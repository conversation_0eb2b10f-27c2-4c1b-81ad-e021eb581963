<!--APP应用系统配置-->
<?xml version="1.0" encoding="UTF-8"?>
<system>
    
    <catalog name="system" description="系统配置">
        <item name="isDevelop" value="0" description="是否开发环境(0:否,1:是)，默认是0" />
        <item name="isLinstenGateway" value="0" description="是否对BusConfig.xml的服务器进行启动检测(0:否,1:是)，默认是0" />
        <item name="isListenScreenTouch" value="0" description="是否监听全局屏幕触发(0:否,1:是)，默认是0" />
        <item name="isBlurBackground" value="0" description="是否在切换到后台时候模糊背景(0:否,1:是)，默认是1" />
        <item name="pluginPath" value="SystemPlugin.xml|OpenPlugin.xml|FinancialPlugin.xml" description="系统插件配置文件地址,多个用|分割" />
    </catalog>
    
    <catalog name="ignoreWebViewCookie" description="webView忽略同步原生Cookie项配置">
        <item name=".huajinsc.cn" value="openPhoneAuthToken|openAccountAuthToken" description="当前域名忽略的cookie项配置，多个用|分割" />
    </catalog>
    
    <catalog name="systemRightAlert" description="权限申请自定义框">
        <item name="title" value="权限用途说明" description="弹框标题描述" />
        <item name="button" value="我知道了" description="弹框底部按钮描述" />
        <item name="header" value="&lt;font color='#505050'&gt;我们深知个人信息对您的重要性，会尽全力保护您的个人信息安全。为向您提供优质的服务，我们需要获取以下权限及信息。&lt;/font&gt;&lt;br/&gt;" description="内容头部描述，支持Html格式" />
        <item name="footer" value="&lt;font color='#505050'&gt;系统将弹框请求，请允许中山赢者APP获取相关权限。&lt;/font&gt;&lt;br/&gt;" description="内容底部描述，支持Html格式" />
        <item name="audio" value="&lt;b&gt;&lt;font color='#363636'&gt;麦克风权限&lt;/font&gt;&lt;/b&gt;&lt;br/&gt;&lt;font color='#6f6f6f'&gt;用于视频通话。&lt;/font&gt;&lt;br/&gt;" description="麦克风权限描述，支持Html格式" />
        <item name="photo" value="&lt;b&gt;&lt;font color='#363636'&gt;相册&lt;/font&gt;&lt;/b&gt;&lt;br/&gt;&lt;font color='#6f6f6f'&gt;用于从相册中选择照片上传，包括开户、业务办理、编辑头像时的相片上传。&lt;/font&gt;&lt;br/&gt;" description="相册权限描述，支持Html格式" />
        <item name="camera" value="&lt;b&gt;&lt;font color='#363636'&gt;相机&lt;/font&gt;&lt;/b&gt;&lt;br/&gt;&lt;font color='#6f6f6f'&gt;用于拍摄上传照片，包括开户及业务办理中的证件照、人像照片和修改头像时的拍照功能。&lt;/font&gt;&lt;br/&gt;" description="相机权限描述，支持Html格式" />
        <item name="addressBook" value="&lt;b&gt;&lt;font color='#363636'&gt;通讯录权限&lt;/font&gt;&lt;/b&gt;&lt;br/&gt;&lt;font color='#6f6f6f'&gt;用于选择联系人。&lt;/font&gt;&lt;br/&gt;" description="通讯录权限描述，支持Html格式" />
        <item name="location" value="&lt;b&gt;&lt;font color='#363636'&gt;位置信息&lt;/font&gt;&lt;/b&gt;&lt;br/&gt;&lt;font color='#6f6f6f'&gt;用于查看营业网点时向您提供定位附近的营业部位置，拒绝授权后，您将无法匹配到附近营业部的信息。&lt;/font&gt;&lt;br/&gt;" description="定位权限描述，支持Html格式" />
    </catalog>
    
    <catalog name="keyborad" description="键盘配置">
        <item name="theme" value="0" description="原生键盘主题类型(0:浅色,1:深色)，默认是0" />
        <item name="isNoCutScreen" value="0" description="原生键盘是否支持防止截屏(0:不处理，1：处理)，默认0，处理的时候键盘会去掉点击背景色" />
    </catalog>
    
    <catalog name="networkChange" description="网络切换相关配置">
        <item name="isShowNetChange" value="0" description="是否显示网络连接提醒(0:否,1:是)，默认是1" />
        <item name="showNetChangeMode" value="1" description="显示网络连接提醒的模式(0:底部提示,1:通知栏提示),默认是0" />
    </catalog>
    
    <catalog name="noteBar" description="通知栏提示层配置">
        <item name="barColor" value="#f00ddd" description="背景色" />
        <item name="textColor" value="#00ff00" description="字体颜色" />
    </catalog>
    
    <catalog name="networkRequest" description="网络请求相关配置">
        <item name="isShowErrorPrefix" value="1" description="是否对网络异常提示显示前缀(0:否,1:是)默认是1" />
        <item name="isRequestURLEncode" value="1" description="是否对请求入参进行URL编码(0:否,1:是)默认是1" />
        <item name="isRequestURLSign" value="0" description="是否对请求入参进行签名(0:否,1:是)默认是0" />
        <item name="requestSignKey" value="uFSCpZUT24jPeofBtkFTEXIKNZAKWw1vXD/ABnpapSUeT9CVhloh0e0kmq2PagBU" description="请求签名的Key" />
        <item name="requestSignAppId" value="default" description="请求签名的APPId" />
        <item name="isRequestURLEncrypt" value="0" description="是否对请求入参进行加密(0:否,1:是)默认是0" />
        <item name="requestEncryptMode" value="des" description="加密的类型" />
        <item name="requestEncryptKey" value="qumPZ40oEK2Cdyiwz0tV7P18icupyh9SAq220g9l7aEeT9CVhloh0e0kmq2PagBU" description="请求加密的Key" />
        <item name="isRestFull" value="0" description="是否走微服务RestFull接口" />
    </catalog>
    
    <catalog name="networkErrorInfo" description="请求常见的错误的提示信息配置">
        <item name="-100000" value="亲，网络很不给力哦~" description="请求断网错误" />
        <item name="-100001" value="亲，服务器不能正常访问~" description="请求服务器异常" />
        <item name="-100002" value="亲，请求超时，请稍后重试~" description="请求超时错误" />
        <item name="-100003" value="亲，请求已中断，请稍后重试~" description="请求中断错误" />
        <item name="-100004" value="亲，请求地址不能正常访问~" description="请求地址错误" />
        <item name="-100005" value="亲，DNS域名解析失败~" description="DNS域名解析错误" />
        <item name="-100006" value="亲，您正在通话中~" description="通话中请求数据错误" />
    </catalog>
    
    <catalog name="webViewPool" description="webView链接池">
        <item name="isUse" value="0" description="是否开启(0:否,1:是)" />
        <item name="inspectable" value="0" description="是否开启WK调试(0:否,1:是)默认是0" />
        <item name="htmlLoadMode" value="0" description="html的加载模式(0:从安全沙箱加载,1:从安装包加载)，默认是0" />
        <item name="poolInitSize" value="3" description="池子初始大小" />
        <item name="poolMaxSize" value="3" description="池子最多大小" />
        <item name="isCheckH5" value="0" description="是否检测H5加载完成（1:检测，只有H5通知原生加载完成以后,才可以回调H5的方法 0:不检查,直接回调H5）默认是0，不检测" />
        <item name="isShowLoading" value="0" description="是否显示WebView加载过渡效果(0:否,1:是)默认是0" />
    </catalog>
    
    <catalog name="update" description="版本管理配置">
        <item name="isOpen" value="0" description="是否启动检测版本更新(0:否,1:是)，默认是0" />
    </catalog>
    
    <catalog name="log" description="日志的配置">
        <item name="logLevel" value="debug" description="off|error|warn|info|debug|all" />
        <item name="logAsync" value="1" description="是否异步写日志(0:同步，1:异步)" />
        <item name="logType" value="console" description="console:控制台，file:文件，server:服务器，多种日志模式用|分割，如console|file|server" />
        <item name="fileLogRollingFrequency" value="1440" description="文件日志滚动的时间间隔，单位分钟，超过就新建文件" />
        <item name="fileLogRollingSize" value="1" description="文件日志滚动的文件大小间隔，单位M，超过就新建文件" />
        <item name="fileLogMaxNum" value="7" description="文件日志的最大个数，超过就删除历史文件" />
        <item name="fileLogMaxSize" value="20" description="文件日志的存储空间限制，单位M，超过就删除历史文件" />
        <item name="serverLogSaveInterval" value="2" description="服务器日志的发送时间间隔，单位秒" />
        <item name="serverLogSaveThreshold" value="10" description="服务器日志的发送累计条数间隔" />
        <item name="serverLogSaveUrl" value="" description="服务器日志的地址" />
    </catalog>
    
    <!-- 统计配置文件-->
    <catalog name="traffic" description="思迪统计配置">
        <item name="isOpen" value="0" description="是否开启(0:否,1:是)默认0" />
        <item name="appKey" value="Y7tg6rPjhjBb5GQTbiej3brnpHwIqSsWI4CpEB7yoaEeT9CVhloh0e0kmq2PagBU" description="统计SDK授权签名Key(加密后)" />
        <item name="appCode" value="23b543b07b5a4e158e670569949bcc79" description="统计SDK授权商户号" />
        <item name="appId" value="hjyj.app" description="应用APP编号" />
        <item name="policy" value="1" description="发送策略(0:实时发送-只在模拟器测试模式下有效,真机自动转成4,1:启动发送,2:最小间隔发送,3:最小数目发送,4:最小间隔或数目发送,5:进入后台时发送)，默认0" />
        <item name="channelId" value="appStore" description="发送渠道" />
        <item name="encryptEnabled" value="0" description="设置是否对上传的埋点数据进行加密(0:否,1:是)默认0" />
        <item name="encryptKey" value="Y7tg6rPjhjBb5GQTbiej3brnpHwIqSsWI4CpEB7yoaEeT9CVhloh0e0kmq2PagBU" description="埋点数据的加密Key" />
        <item name="compressEnabled" value="0" description="设置是否对上传的埋点数据进行压缩(0:否,1:是)默认0" />
        <item name="sendInterval" value="90" description="统计发送的时间间隔(单位:秒)，默认是90秒" />
        <item name="sendNum" value="10" description="统计发送的数目间隔，默认是10条" />
        <item name="crashReportEnabled" value="1" description="设置是否统计崩溃信息(0:否,1:是)默认1" />
        <item name="backgroundTaskEnabled" value="1" description="设置是否支持后台任务模式(0:否,1:是)默认1" />
        <item name="onlyWifiEnabled" value="0" description="设置是否仅WIFI模式下发送(0:否,1:是)默认0" />
        <item name="cacheMaxNum" value="2000" description="本地缓存的最大数据数目，默认是2000" />
        <item name="restartAppTime" value="30" description="进入后台重算启动时间间隔(单位:秒)，默认是30" />
        <item name="uploadTime" value="09:00-10:00|23:10-23:20" description="上传时间段,例如：09:00-10:00|14:00-15:00,为空代表不限制" />
        <item name="startAction" value="1" description="启动事件动作" />
        <item name="pageAction" value="2" description="页面访问事件动作" />
        <item name="appAction" value="3" description="APP周期事件动作" />
        <item name="exceptionAction" value="4" description="APP异常事件动作" />
        <item name="reqErrorAction" value="5" description="请求异常事件动作" />
        <item name="reqErrorTimeOut" value="5000" description="请求异常超时时间(单位:毫秒)默认5000" />
        <item name="isShowTip" value="0" description="是否发送请求的时候显示tip提示(0:否,1:是)默认0" />
        <item name="isEditor" value="0" description="是否是埋点的编辑人员(0:否,1:是)默认0" />
        <item name="isTestin" value="0" description="是否云测模式(0:否,1:是)默认0" />
        <item name="url" value="" description="统计的生产服务器URL地址" />
        <item name="isAutoGetStampId" value="1" description="是否自动获取StampId等信息(0:否,1:是)默认1" />
        <item name="ipKey" value="KfZAnJoqEDtmwhTjxZ+C3JhhR1r9/yRrLlDXduxkb/YeT9CVhloh0e0kmq2PagBU" description="高德根据IP获取地理位置服务授权key(加密后)" />
    </catalog>
    
    <catalog name="CPAuth" description="银联认证相关配置">
        <item name="environment" value="test" description="运行环境(生产环境:product,测试环境:test)" />
        <item name="appSysId" value="" description="系统编号" />
        <item name="privateKey" value="" description="密钥" />
    </catalog>
    
    <catalog name="checkScreenShotRecord" description="防截屏录屏攻击配置">
        <item name="isOpen" value="1" description="是否检测截屏录屏攻击(0:否,1:是)，默认是0" />
        <item name="screenCaptureTip" value="当前界面正在被截屏，请注意您的安全！" description="防截屏提示语" />
        <item name="screenRecordingTip" value="当前界面正在被录屏，请注意您的安全！" description="防录屏提示语" />
    </catalog>
    
</system>
