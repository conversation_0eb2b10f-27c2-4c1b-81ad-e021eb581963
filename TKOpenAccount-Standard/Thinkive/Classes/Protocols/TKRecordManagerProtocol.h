//
//  TKRecordManagerProtocol.h
//  TKOpenAccount-Standard
//
//  Created by <PERSON> on 2022/3/29.
//  Copyright © 2022 thinkive. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

typedef enum : NSUInteger {
    TKChatVideoRecordNetworkStatusUnknown = 0,
    TKChatVideoRecordNetworkStatusVeryGood,
    TKChatVideoRecordNetworkStatusGood,
    TKChatVideoRecordNetworkStatusBad,
    TKChatVideoRecordNetworkStatusVeryBad,
} TKChatVideoRecordNetworkStatus;

@protocol TKRecordManagerDelegate <NSObject>

@optional
// 语音合成开始回调
- (void)speechSynthesisDidStart;

/// 多段语音合成开始回调
/// - Parameters:
///   - index: 当前正在合成的语音索引
///   - synthesisArray: 要合成的语音数组
- (void)speechSynthesisDidStartWithIndex:(int)index synthesisArray:(NSArray *)synthesisArray;

// 语音合成结束回调
// @param text 合成语音的文本
// @param speechSynthesisFilePath 语音合成后保存的文件路径
//- (void)speechSynthesisDidComplete:(NSString *)text speechSynthesisFilePath:(NSString *)speechSynthesisFilePath;

/// 语音合成失败
- (void)speechSynthesisDidFail:(NSString *)errorMsg;

// 语音合成播放结束回调
- (void)speechSynthesisDidPlayDone;

/// 多段语音合成播放结束回调
/// - Parameter index: 当前已合成的语音索引
- (void)speechSynthesisDidPlayDoneWithIndex:(int)index synthesisArray:(NSArray *)synthesisArray;

/// 语音识别开始回调
- (void)speechRecognizeDidStart;

/// 每个语音包分片识别结果
/// @param result 一段语音每次识别结果
- (void)speechRecognizeOnSliceRecognize:(NSString *)result;

/// 一次语音识别结果
/// @param reslut 一次识别结果字符串
- (void)speechRecognizeDidRecognize:(NSString *)result;

/// 语音识别结束回调
- (void)speechRecognizeDidComplete;

/// 语音识别失败回调
- (void)speechRecognizeDidFail:(NSString *)errorMsg;

/// 连接视频服务器开始回调
- (void)connectServerRoomDidStart;

/// 连接视频服务器回调
- (void)connectServerRoomDidComplete:(BOOL)success errorMsg:(NSString *)errorMsg;

/// 断开视频服务器回调
- (void)disconnectServerRoomDidComplete:(BOOL)success errorMsg:(NSString *)errorMsg;

// tchat回到前台
- (void)TChatBecomeActive;

/// 视频流回调
- (void)OnVideoDataCallBack:(UIImage *)image;

/// 音频流回调
- (void)OnAudioDataCallBack:(NSData*)buf;

/// 开始录制回调
- (void)recordStartCallBack;

/// 结束录制回调
- (void)recordStopCallBack:(NSString *)filePath fullFilePath:(NSString *)fullFilePath videoLenth:(int)videoLenth catonLength:(int)catonLength;

/// 结束录制出错回调
- (void)recordStopErrocCallBack:(NSString *)errorMsg;

@end

@protocol TKRecordManagerProtocol <NSObject>

/// 构造方法
/// @param configParam 初始化参数字典
- (instancetype)initWithConfig:(NSDictionary *)configParam;

/// 代码
@property (nonatomic, readwrite, weak) id<TKRecordManagerDelegate> delegate;

/// 配置字典
@property (nonatomic, readwrite, copy) NSDictionary *configParam;

/// 展示界面的contentView
@property (nonatomic, readwrite, weak) UIView *contentView;

/// 数字人录制界面客户端视频y坐标
@property (nonatomic, readwrite, assign) int contentViewY;

/// 禁用麦克风，不录制音频。目前仅TChat活体需要改为YES。默认为NO
@property (nonatomic, readwrite, assign) BOOL disableMicrophone;

@property (atomic, readwrite, assign) int requestID; // tts/虚拟人的事件ID
///是否是前置相机
@property (nonatomic, assign) BOOL  isFrontCamera;

// 获取本地视频第一帧
- (UIImage *)getLocalVideoPreViewImage:(NSString *)filePath;

// 请求视频房间号
- (void)bootDevcie:(BOOL)isFirst;

/// 断开视频服务器并销毁释放资源
- (void)stopDevice:(BOOL)needRelease;

/// 开始录制视频
- (void)startRecord;

/// 停止录制视频
- (void)stopRecord;

/// 语音播报
/// @param text 播报的内容
- (void)syntheticAndPlay:(NSString *)text tipSpeed:(NSString *)tipSpeed;
- (void)syntheticAndPlayContents:(NSArray *)texts tipSpeed:(NSString *)tipSpeed;  // 播报多段语音

/// 播放远程文件
/// - Parameters:
///   - url: 资源地址
///   - tipSpeed: 播放速度
///   - needRequestToken: 是否需要先申请token
- (void)playRemoteUrl:(NSString *)url tipSpeed:(NSString *)tipSpeed needRequestToken:(BOOL)needRequestToken;

/// 停止语音播报
- (void)stopSyntheticAndPlay;

/// 播放音频
/// @param flag 播放音频索引。和后端保持一致
- (void)playVideo:(NSString *)flag;

/// 停止播放音频
- (void)stopPlayVideo;

/// 开始语音识别
- (void)startRecognize;

/// 停止语音识别
- (void)stopRecognize;




@optional
/// 展示坐席/虚拟人界面的contentView
@property (nonatomic, readwrite, weak) UIView *remoteContentView;

/// 视频文件路径
@property (nonatomic, readwrite, copy) NSString *videoFilePath;

/// 是否横屏录制
@property (nonatomic, readwrite, assign) BOOL isLandscape;

// 字幕滚动速度
@property (nonatomic, assign) float subtitlesScrollSpeed;

/// 激活设备
- (void)activeDevice;

/// 休眠设备
- (void)deactiveDevice;

// 获取视频第一帧
- (UIImage*)getVideoPreViewImage:(NSURL *)path;

//切换摄像头
-(void)tkSwitchVideoCamera:(BOOL)isFrontCamera;

/// 收音、消音设置
/// @param mute 是否消音
- (void)enableMute:(BOOL)mute;

/// 创建字幕，根据展示时间滚动
/// - Parameters:
///   - text: 字幕内容
///   - showTime: 展示时间
- (void)createSubtitles:(NSString *)text showTime:(NSString *)showTime questionOneWordSpeed:(NSString *)questionOneWordSpeed;

@end

NS_ASSUME_NONNULL_END
