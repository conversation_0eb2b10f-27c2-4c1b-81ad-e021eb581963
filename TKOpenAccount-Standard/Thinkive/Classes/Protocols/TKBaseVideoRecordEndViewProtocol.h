//
//  TKBaseVideoRecordEndViewProtocol.h
//  TKOpenAccount-Standard
//
//  Created by <PERSON> on 2022/3/24.
//  Copyright © 2022 thinkive. All rights reserved.
//


#import <Foundation/Foundation.h>
#import "TKPlayerControlView.h"

NS_ASSUME_NONNULL_BEGIN

@protocol TKBaseVideoRecordEndViewDelegate <NSObject>
/**
 <AUTHOR> 2019年04月13日14:18:27
 @单向视频结果页返回
 */
- (void)endGoBack;

/**
 <AUTHOR> 2019年04月13日14:20:28
 @单向视频结果页重试
 */
- (void)endReste;

/**
 <AUTHOR> 2019年04月13日14:21:40
 @单向视频结果页提交
 */
- (void)endSubmit;

@end


typedef enum {
    TKOneWayVideoEndTypeNormal=0,//正常走完流程
    TKOneWayVideoEndTypeNoVoiceError, //语音识别失败,没声音
    TKOneWayVideoEndTypeWrongAnswerError, //语音识别失败，回答错误
    TKOneWayVideoEndTypeFaceDetectError,  //人脸识别失败
    TKOneWayVideoEndTypeFaceCompareError, //人脸比对失败
    TKOneWayVideoEndTypeFaceNumberError //多人脸失败
}TKOneWayVideoEndType;


@protocol TKBaseVideoRecordEndViewProtocol

@property (nonatomic, strong) UIImageView * _Nullable videoShowImgView;//视频展示人像视图
@property (nonatomic, strong) UIImage *videoShowImg;//视频展示示例图
@property (nonatomic, weak) id<TKBaseVideoRecordEndViewDelegate> delegate;
@property (nonatomic, assign) TKOneWayVideoEndType endType;//单向视频结果类型
@property (nonatomic, readwrite, weak) TKPlayerControlView *playerControlView;
@property (nonatomic, readwrite, strong) NSString *failureString;   // 失败展示文案
@property (nonatomic, readwrite, strong) NSString *errorTitle;   // 失败展示标题

/// 构造方法
/// @param frame frame
/// @param param 参数
- (instancetype)initWithFrame:(CGRect)frame withParam:(NSMutableDictionary *)param;


@optional
/// 根据tip更新UI
/// @param tipArr tip数组。格式是：@[@{@"tipImage" : @"", @"tipContent" : @""}];
- (void)updateTipViewWithTipArr:(NSArray *)tipArr;


@property (nonatomic, readwrite, assign) BOOL isLandscape;   // 是否横屏录制

@end

NS_ASSUME_NONNULL_END
