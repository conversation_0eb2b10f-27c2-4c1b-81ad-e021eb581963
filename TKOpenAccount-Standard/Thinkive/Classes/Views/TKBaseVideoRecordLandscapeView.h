//
//  TKBaseVideoRecordLandscapeView.h
//  TKOpenAccount-Standard
//
//  Created by <PERSON> on 2022/11/11.
//  Copyright © 2022 thinkive. All rights reserved.
//

#import <UIKit/UIKit.h>
#import "TKBaseVideoRecordViewProtocol.h"

NS_ASSUME_NONNULL_BEGIN

@interface TKBaseVideoRecordLandscapeView : UIView<TKBaseVideoRecordViewProtocol>
{
    UIButton *_takeBtn;
    UILabel *_warningLabel;
    UILabel *_recordTimeLabel;
}

@property (nonatomic, strong) NSString *mainColorString;//按钮图片主色调颜色值，h5没有传默认：#2F85FF（用于图片和按钮底色调整）

@property (nonatomic, strong) UIView *topView,*bottomView,*leftView,*rightView;//顶部遮罩层,底部遮罩层,左部遮罩层，右部遮罩层

@property (nonatomic, strong) UIButton *backBtn;//返回按钮
@property (nonatomic, strong) UIButton *switchCameraBtn;//切换摄像头按钮
@property (nonatomic, strong)  UIView *bottomShowTipLineView;//底部文档等提示展示区域横线
@property (nonatomic, strong) UIView *bottomShowTipRecordLineView;//录制时长占比展示线
@property (nonatomic, strong) UIView *bottomShowTipView;//底部文档等提示展示区域
@property (nonatomic, strong) UITextView *bottomShowLabel;//底部文字展示
@property (nonatomic, strong) UIImageView *serviceBg;//小机器人图标背景图
@property (nonatomic, strong) TKGIFImageView *serviceGifView;//小机器人动作图
@property (nonatomic, strong) UILabel *recordTimeLabel;//录制时长
@property (nonatomic, strong) UILabel *countDownLabel;//回答倒计时展示label

/// 创建view方法，支持子类重写
- (void)viewInit;

NS_ASSUME_NONNULL_END

@end
