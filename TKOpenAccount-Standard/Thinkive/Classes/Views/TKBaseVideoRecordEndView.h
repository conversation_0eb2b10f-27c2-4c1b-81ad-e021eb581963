//
//  TKBaseVideoRecordEndView.h
//  TKOpenAccount-Standard
//
//  Created by <PERSON> on 2022/9/2.
//  Copyright © 2022 thinkive. All rights reserved.
//

#import <UIKit/UIKit.h>
#import "TKBaseVideoRecordEndViewProtocol.h"

NS_ASSUME_NONNULL_BEGIN

@interface TKBaseVideoRecordEndView : UIView<TKBaseVideoRecordEndViewProtocol>
{
    UIImage *_videoShowImg;
    UILabel *_warningTipLabel;
    UIButton *_warningBackBtn;
    NSString *_mainColorString;
    UIButton *_resetBtn;
    UIButton *_submitBtn;
}

@property (nonatomic, strong) NSMutableDictionary *requestParam;//请求参数
@property (nonatomic, strong) UIImage * _Nullable videoShowImg; //视频展示人像
@property (nonatomic, strong) UIImageView * _Nullable videoShowImgView;//视频展示人像视图
@property (nonatomic, strong) UILabel *titleLabel; // 标题文本
@property (nonatomic, strong) UILabel *warningTipLabel;//识别失败提示语
@property (nonatomic, strong) UIButton *warningBackBtn;//识别失败返回按钮
@property (nonatomic, strong) NSString *mainColorString;//按钮图片主色调颜色值，h5没有传默认：#2F85FF（用于图片和按钮底色调整）
@property (nonatomic, strong) UIButton *resetBtn;//重新录制按钮
@property (nonatomic, strong) UIButton *submitBtn;//确认提交按钮
@property (nonatomic, strong) UIButton *backBtn;//返回按钮
@property (nonatomic, strong) UIImageView *voiceWarningImgView;//语音识别失败警告图
@property (nonatomic, strong) UILabel *warningTitleTipLabel;//识别失败标题



/**
 <AUTHOR>
 @初始化单向视频正常走完流程结果页面
 */
- (void)viewInit;

/**
 <AUTHOR>
 @初始化单向视频失败界面
 */
-(void)viewErrorInit:(NSString *)errorMsg;

/// 根据tip更新UI
/// @param tipArr tip数组。格式是：@[@{@"tipImage" : @"", @"tipContent" : @""}];
- (void)updateTipViewWithTipArr:(NSArray *)tipArr;

@end

NS_ASSUME_NONNULL_END
