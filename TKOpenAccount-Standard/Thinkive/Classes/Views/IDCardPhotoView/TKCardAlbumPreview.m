//
//  TKCardAlbumPreview.m
//  TKOpenAccount-Standard
//
//  Created by 夏博文 on 2025/7/2.
//  Copyright © 2025 thinkive. All rights reserved.
//

#import "TKCardAlbumPreview.h"
#define TKCARD_AspectRatio 0.631915 //身份证高/宽的比例

@interface TKCardAlbumPreview()
@property (nonatomic, strong) NSMutableDictionary *requestParams;//h5传来的请求参数
@property (nonatomic, strong) NSString *mainColorString;//按钮图片主色调颜色值，h5没有传默认：#2F85FF（用于图片和按钮底色调整）
@property(nonatomic,strong) UIButton *backBtn;//返回按钮
@property (nonatomic, strong) UIButton *cancelBtn;//重拍重选按钮
@property (nonatomic, strong) UIButton *submitBtn;//提交按钮
@property (nonatomic, assign) BOOL isPhotoAlbum;//是否相册图


@end

@implementation TKCardAlbumPreview
/**
 *<AUTHOR> 2025年07月02日15:24:25
 *@初始化相册预览页面
 *@param frame frame
 *@param requestParams 初始化页面用到的参数
 */
-(instancetype)initWithFrame:(CGRect)frame requestParams:(NSMutableDictionary *)requestParams{
    self=[super initWithFrame:frame];
    if (self) {
        [self setBackgroundColor:[TKUIHelper colorWithHexString:@"#0D0D0D"]];
        self.requestParams=requestParams;
        if ([TKStringHelper isEmpty:self.requestParams[@"mainColor"]]) {
            self.mainColorString=@"#EE393E";
        }else{
            self.mainColorString=self.requestParams[@"mainColor"];
        }
        [self viewInit];
    }
    return self;
}


/**
 <AUTHOR> 2025年07月02日15:25:51
 @初始化相册预览页面
 */
-(void)viewInit{

}

#pragma mark -初始化证件预览界面带有示例模块
- (void)previewCardNeedSampleView:(UIImage*)image isPhotoAlbum:(BOOL)isPhotoAlbum isFrontCard:(BOOL)isFrontCard{
    self.isPhotoAlbum=isPhotoAlbum;
    

    
    //头部提示语
    float cardLabelY=20;
    if (ISIPHONEX) {
        cardLabelY=52;
    }
    float cardLabelWdith=108;
    float cardLabelHeight=20;
    float cardLabelX=(self.TKWidth-cardLabelWdith)/2.0f;
    UILabel *cardLabel = [[UILabel alloc] init];
    cardLabel.textAlignment=NSTextAlignmentCenter;
    cardLabel.frame = CGRectMake(cardLabelX, cardLabelY, cardLabelWdith, cardLabelHeight);
    cardLabel.text = @"上传确认";
    cardLabel.font = [UIFont fontWithName:@"PingFang SC" size:14];
    cardLabel.textColor = [UIColor whiteColor];
    [self addSubview:cardLabel];
    [self addSubview:self.backBtn];

    //预览图
    float cardIVX=20;
    float cardIVY=cardLabel.TKBottom+30;
    UIImageView *cardIV;
    float cardIVWidth=self.TKWidth-cardIVX*2;
    float cardIVHeight=cardIVWidth*TKCARD_AspectRatio;
    cardIV =[[UIImageView alloc] init];
    cardIV.frame=CGRectMake(cardIVX, cardIVY, cardIVWidth, cardIVHeight);
    cardIV.contentMode = UIViewContentModeScaleAspectFit;
    [cardIV setImage:image];
    [self addSubview:cardIV];
    cardIV.tag=6001310;
    

    
    //身份证示例图
    float tipImgWidth=150;
    float tipImgHeight=tipImgWidth*0.631915;
    float tipImgY=cardIV.TKBottom+46;
    float tipImgX=(self.TKWidth-tipImgWidth)/2.0f;
    UIImageView *tipImgView=[[UIImageView alloc] initWithFrame:CGRectMake(tipImgX, tipImgY, tipImgWidth, tipImgHeight)];
    if (isFrontCard) {
        //是否复印件示例图
        if (self.requestParams[@"isCopies"] && ![self.requestParams[@"isCopies"] integerValue]) {//是否身份证复印件
            [tipImgView setImage:[UIImage imageNamed:[NSString stringWithFormat:@"%@.bundle/Resources/TKOpenPlugin60013/front_tip5.1UI_icon_down.png", TK_OPEN_RESOURCE_NAME]]];
        }else{
            [tipImgView setImage:[UIImage imageNamed:[NSString stringWithFormat:@"%@.bundle/Resources/TKOpenPlugin60013/front_tip5.1UI_icon_down.png", TK_OPEN_RESOURCE_NAME]]];
        }
        
        //是否h5传了身份证正面示例图，传了以h5为准
        if([TKStringHelper isNotEmpty:self.requestParams[@"previewSamplePicFront"]]){
            [tipImgView setImage:[UIImage imageWithData:[TKBase64Helper dataWithDecodeBase64String:self.requestParams[@"previewSamplePicFront"]]]];
        }

    }else{
        //是否复印件示例图
        if (self.requestParams[@"isCopies"] && ![self.requestParams[@"isCopies"] integerValue]) {//是否身份证复印件
            [tipImgView setImage:[UIImage imageNamed:[NSString stringWithFormat:@"%@.bundle/Resources/TKOpenPlugin60013/reverse_tip5.1UI_icon_down.png", TK_OPEN_RESOURCE_NAME]]];
        }else{
            [tipImgView setImage:[UIImage imageNamed:[NSString stringWithFormat:@"%@.bundle/Resources/TKOpenPlugin60013/reverse_tip5.1UI_icon_down.png", TK_OPEN_RESOURCE_NAME]]];
        }

        //是否h5传了身份证反面示例图，传了以h5为准
        if([TKStringHelper isNotEmpty:self.requestParams[@"previewSamplePicBack"]]){
            [tipImgView setImage:[UIImage imageWithData:[TKBase64Helper dataWithDecodeBase64String:self.requestParams[@"previewSamplePicBack"]]]];
        }
    }
    tipImgView.contentMode = UIViewContentModeScaleToFill;
    [self addSubview:tipImgView];
    
    //检查照片的提示语
    UILabel *checkTipLabel = [[UILabel alloc] init];
    checkTipLabel.numberOfLines=0;
    checkTipLabel.backgroundColor=[UIColor clearColor];
    float checkTipLabelWidth=240;
    float checkTipLabelHeight=44;
    float checkTipLabelLeft=(self.TKWidth-checkTipLabelWidth)/2.0f;
    checkTipLabel.frame = CGRectMake(checkTipLabelLeft,tipImgView.TKBottom+5.0f, checkTipLabelWidth, checkTipLabelHeight);
    NSString *tipStr= @"请核对示例,确保身份证照片\n文字清晰、边角完整、光线均匀。";
    NSRange tipRange=[tipStr rangeOfString:@"文字清晰、边角完整、光线均匀。"];
    //顶部提示用富文本
    NSMutableAttributedString *tipAttribut=[[NSMutableAttributedString alloc] initWithString:tipStr];
    // 2.添加属性
    [tipAttribut addAttribute:NSFontAttributeName value:[UIFont fontWithName:@"PingFang SC" size:16] range:NSMakeRange(0, tipStr.length)];
    [tipAttribut addAttribute:NSForegroundColorAttributeName value:[TKUIHelper colorWithHexString:@"#BBBBBB"] range:NSMakeRange(0, tipStr.length)];
    [tipAttribut addAttribute:NSForegroundColorAttributeName value:[UIColor orangeColor] range:tipRange];
    checkTipLabel.attributedText=tipAttribut;
    checkTipLabel.textAlignment = NSTextAlignmentCenter;
    CGSize lableSize = [checkTipLabel sizeThatFits:CGSizeMake(240, MAXFLOAT)];
    [checkTipLabel setTKHeight:lableSize.height];
    [self addSubview:checkTipLabel];

    if(_cancelBtn){
        [_cancelBtn removeFromSuperview];
        _cancelBtn=nil;
    }
    [self addSubview:self.cancelBtn];
    
    if(_submitBtn){
        [_submitBtn removeFromSuperview];
        _submitBtn=nil;
    }
    [self addSubview:self.submitBtn];
}

//预览界面图片变动
-(void)changePreviewImg:(UIImage*)image{
    UIImageView *cardImgView=(UIImageView *)[self viewWithTag:6001310];
    [cardImgView setImage:image];
}

#pragma mark 按钮事件
-(void)retryAction:(id)sender{
    if (self.delegate&&[self.delegate respondsToSelector:@selector(previewRetryAction:)]) {
        [self.delegate previewRetryAction:sender];
    }
}

-(void)submitAction:(id)sender{
    if (self.delegate&&[self.delegate respondsToSelector:@selector(previewSubmitAction:)]) {
        [self.delegate previewSubmitAction:sender];
    }
}

-(void)backAction:(id)sender{
    if (self.delegate&&[self.delegate respondsToSelector:@selector(previewBackAction:)]) {
        [self.delegate previewBackAction:sender];
    }
}

#pragma mark lazyloading
/**
 <AUTHOR> 2025年07月03日17:05:19
 @初始化懒加载backBtn
 @return backBtn
 */
-(UIButton *)backBtn{
    if (!_backBtn) {
        //头部提示语
        float y=20;
        if (ISIPHONEX) {
            y=52;
        }
        _backBtn= [UIButton buttonWithType:UIButtonTypeCustom];
        _backBtn.tag = 102;
        [_backBtn setImage:[UIImage imageNamed:[NSString stringWithFormat:@"%@.bundle/Resources/TKOpenPlugin60013/back_album_preview_btn.png", TK_OPEN_RESOURCE_NAME]] forState:UIControlStateNormal];

        float bBtnWidth=24;
        _backBtn.frame = CGRectMake(self.TKWidth-bBtnWidth-20, y, bBtnWidth, bBtnWidth);
        _backBtn.layer.cornerRadius=_backBtn.frame.size.width/2;
        [_backBtn setBackgroundColor:[TKUIHelper colorWithHexString:@"#666666" alpha:0.7f]];
        [_backBtn setContentHorizontalAlignment:UIControlContentHorizontalAlignmentCenter];
        [_backBtn setContentVerticalAlignment:UIControlContentVerticalAlignmentCenter];
        
        [_backBtn addTarget:self action:@selector(backAction:) forControlEvents:UIControlEventTouchUpInside];
    }
    return _backBtn;
}


/**
 <AUTHOR> 2025年07月03日16:31:12
 @初始化懒加载重拍重选按钮
 @return 重拍重选按钮
 */
-(UIButton *)cancelBtn{
    if(!_cancelBtn){
        
        float btnHeight=44;
        float btnY=self.TKHeight-btnHeight-40;
        float btnX=32;
        float btnWidht=(self.TKWidth-2*btnX-15)/2.0f ;
        
        //重拍按钮
        _cancelBtn= [UIButton buttonWithType:UIButtonTypeCustom];
        if (self.isPhotoAlbum) {
            [_cancelBtn setTitle:@"重选" forState:UIControlStateNormal];
        }else{
            [_cancelBtn setTitle:@"重拍" forState:UIControlStateNormal];
        }
        if ([TKOpenViewStyleHelper shareInstance].isElder) {
            _cancelBtn.titleLabel.font = [UIFont fontWithName:@"PingFang SC" size:22];
        }else{
            _cancelBtn.titleLabel.font = [UIFont fontWithName:@"PingFang SC" size:16];
        }
        [_cancelBtn setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];
        [_cancelBtn setBackgroundColor:[TKUIHelper colorWithHexString:@"#666666" alpha:0.7f]];
        [_cancelBtn.layer setCornerRadius:btnHeight/2.0f];
        _cancelBtn.frame =CGRectMake(btnX, btnY, btnWidht,btnHeight);
        [_cancelBtn setContentHorizontalAlignment:UIControlContentHorizontalAlignmentCenter];
        [_cancelBtn setContentVerticalAlignment:UIControlContentVerticalAlignmentCenter];
        [_cancelBtn addTarget:self action:@selector(retryAction:) forControlEvents:UIControlEventTouchUpInside];
    }
    return _cancelBtn;
}
    
/**
 <AUTHOR> 2025年07月03日16:31:17
 @初始化懒加载提交按钮
 @return 提交按钮
 */
-(UIButton *)submitBtn{
    if(!_submitBtn){
        //提交按钮
        _submitBtn = [UIButton buttonWithType:UIButtonTypeCustom];
        [_submitBtn setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];
        [_submitBtn setTitle:@"上传照片" forState:UIControlStateNormal];
        [_submitBtn setBackgroundColor:[TKUIHelper colorWithHexString:self.mainColorString]];
        if ([TKOpenViewStyleHelper shareInstance].isElder) {
            _submitBtn.titleLabel.font = [UIFont fontWithName:@"PingFang SC" size:22];
        }else{
            _submitBtn.titleLabel.font = [UIFont fontWithName:@"PingFang SC" size:16];
        }
        [_submitBtn.layer setCornerRadius:self.cancelBtn.TKHeight/2.0f];
        _submitBtn.frame =CGRectMake(self.TKRight-self.cancelBtn.TKWidth-self.cancelBtn.TKLeft, self.cancelBtn.TKTop, self.cancelBtn.TKWidth,self.cancelBtn.TKHeight);
        [_submitBtn setContentHorizontalAlignment:UIControlContentHorizontalAlignmentCenter];
        [_submitBtn setContentVerticalAlignment:UIControlContentVerticalAlignmentCenter];
        [_submitBtn addTarget:self action:@selector(submitAction:) forControlEvents:UIControlEventTouchUpInside];
    }
    return _submitBtn;
}
    
@end
