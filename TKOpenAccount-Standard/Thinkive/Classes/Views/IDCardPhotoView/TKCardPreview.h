//
//  TKCardPreview.h
//  TKOpenAccount-Standard
//
//  Created by 夏博文 on 2023/1/13.
//  Copyright © 2023 thinkive. All rights reserved.
//

@protocol TKCardPreviewDelegate <NSObject>

//重拍重选事件
- (void)previewRetryAction:(id)sender;

//提交事件
- (void)previewSubmitAction:(id)sender;

//返回事件
- (void)previewBackAction:(id)sender;
@end


@interface TKCardPreview : UIView

@property (nonatomic, weak) id<TKCardPreviewDelegate>delegate;

/**
 *<AUTHOR> 2023年01月13日15:13:05
 *@初始化证件预览图
 *@param frame frame
 *@param requestParams 初始化页面用到的参数
 */
-(instancetype)initWithFrame:(CGRect)frame requestParams:(NSMutableDictionary *)requestParams;

//#pragma mark -初始化证件预览界面,无示例图--（废弃新版只支持示例图预览）
//- (void)previewCardView:(UIImage*)image isPhotoAlbum:(BOOL)isPhotoAlbum;

#pragma mark -初始化证件预览界面带有示例模块
- (void)previewCardNeedSampleView:(UIImage*)image isPhotoAlbum:(BOOL)isPhotoAlbum isFrontCard:(BOOL)isFrontCard;

//预览界面图片变动
-(void)changePreviewImg:(UIImage*)image;
@end


