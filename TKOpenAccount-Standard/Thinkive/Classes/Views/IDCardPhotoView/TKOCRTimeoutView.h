//
//  TKEXIDTimeoutView.h
//  HrSec
//  身份证OCR超时提示视图
//  Created by <PERSON>ie on 2019/9/24.
//  Copyright © 2019 刘任朋. All rights reserved.
//

@protocol TKOCRTimeoutViewDelegate <NSObject>

//超时提示按钮事件代理
- (void)doTipAction:(id)sender;

@end



@interface TKOCRTimeoutView : UIView
@property (nonatomic, strong) UILabel *titleLabel;//提示标题文本
@property (nonatomic, strong) UIImageView *bkwzImgView,*nwsjImgView,*gxhsImgView;//边框完整提示图,拿稳手机提示图,光线合适提示图
@property (nonatomic, strong) UILabel *bkwzLabel,*nwsjLabel,*gxhsLabel;//边框完整提示文本,拿稳手机文本,光线合适文本
@property (nonatomic, strong) UILabel *describeLabel;//提示描述文本
@property (nonatomic, strong) UIButton *continueScanBtn;//继续扫描按钮
@property (nonatomic, strong) UIButton *takeBtn;//拍照排队按钮
@property (nonatomic, weak) id<TKOCRTimeoutViewDelegate>delegate;
/**
 *<AUTHOR> 2019年09月24日15:58:25
 *@初始化身份证OCR超时提示视图
 *@param frame frame
 *@param requestParams 初始化页面用到的参数
 */
-(instancetype)initWithFrame:(CGRect)frame requestParams:(NSMutableDictionary *)requestParams;

/**
 <AUTHOR> 2022年05月23日164829
 @是否是上传失败的弹出
 @param flag是否是上传失败的弹出
 */
-(void)isUploadFile:(BOOL)flag;

//设置单独按钮,无拍照场景时候
-(void)setOnlyBtn;
@end


