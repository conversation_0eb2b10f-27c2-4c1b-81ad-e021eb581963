//
//  TKBankCardPhotoView.m
//  TKOpenAccount-Standard
//  银行卡拍照扫描界面，竖屏布局
//  Created by 夏博文 on 2025/7/28.
//  Copyright © 2025 thinkive. All rights reserved.
//

#import "TKBankCardPhotoView.h"
#define TKCARD_AspectRatio 0.631915 //身份证高/宽的比例

@interface TKBankCardPhotoView()
@property (nonatomic, strong) NSMutableDictionary *requestParams;//h5传来的请求参数
@property (nonatomic, strong) NSString *mainColorString;//按钮图片主色调颜色值，h5没有传默认：#2F85FF（用于图片和按钮底色调整）
@property(nonatomic,strong) UIImageView *idAreaView;//指定扫描区域对准框视图
@property(nonatomic,strong) UIView *topView,*leftView,*rightView,*bottomView;//顶部遮罩层,左侧遮罩层,右侧遮罩层,底部遮罩层
@property(nonatomic,strong) UIButton *backBtn;//返回按钮
//@property(nonatomic,strong) UIImageView *idTipImgView;//证件提示信息图
@property(nonatomic,strong) UILabel *topTipLabel;//头部提示文本
@property(nonatomic,strong) UILabel *bottomTipLabel;//底部提示文本
@property(nonatomic,strong) UIButton *takeBtn;//拍照按钮
@property(nonatomic,strong) UILabel *takeLabel;//拍照提示文本
@property(nonatomic,strong) UIButton *albumBtn;//相册按钮
//@property(nonatomic,strong) UILabel *albumLabel;//相册提示文本
//@property(nonatomic,strong) UIButton *recognizeSwitchBtn;//切换扫描识别按钮
//@property(nonatomic,strong) UILabel *recognizeSwitchLabel;//切换扫描提示文本
//@property(nonatomic,strong) UIButton *takeSwitchBtn;//切换拍照按钮
//@property(nonatomic,strong) UILabel *takeSwitchLabel;//切换拍照提示文本
//@property(nonatomic,strong) UIButton *recognizeBtn;//自动扫描按钮
//@property(nonatomic,strong) UILabel *recognizeLabel;//自动扫描提示文本
@property(nonatomic,strong) UIImageView *scanLineImgView;//扫描动画线条
@property(nonatomic,strong) UILabel *qualityTipsLabel;//质检提示文本
@end

@implementation TKBankCardPhotoView

/**
 *<AUTHOR> 2025年07月28日11:04:13
 *@初始化身份证拍照扫描界面
 *@param frame frame
 *@param requestParams 初始化页面用到的参数
 */
-(instancetype)initWithFrame:(CGRect)frame requestParams:(NSMutableDictionary *)requestParams{
    self=[super initWithFrame:frame];
    if (self) {
        [self setBackgroundColor:[UIColor clearColor]];
        self.requestParams=requestParams;
        if ([TKStringHelper isEmpty:self.requestParams[@"mainColor"]]) {
            self.mainColorString=TKCARD_MAIN_COLOR;
        }else{
            self.mainColorString=self.requestParams[@"mainColor"];
        }
        [self viewInit];
    }
    return self;
}
/**
 <AUTHOR> 2025年07月28日11:04:24
 @初始化身份证拍照扫描界面
 */
-(void)viewInit{

    [self.idAreaView removeFromSuperview];
    _idAreaView=nil;
    float areaX=17;//卡片指定区域X坐标
    float areaWidth=self.TKWidth-2*areaX;//卡片指定区域宽度
    float areaHeight=areaWidth*TKCARD_AspectRatio;
    float areaY=(self.TKHeight-areaHeight)/2.0f;//卡片指定区域Y坐标
    
    
    
    self.idRect=CGRectMake(areaX, areaY, areaWidth, areaHeight);
    [self addSubview:self.idAreaView];
    [self addSubview:self.topView];
    [self addSubview:self.leftView];
    [self addSubview:self.rightView];
    [self addSubview:self.bottomView];
    [self addSubview:self.backBtn];
//    [self addSubview:self.idTipImgView];
    [self addSubview:self.topTipLabel];
    [self addSubview:self.bottomTipLabel];
    [self addSubview:self.takeLabel];
    [self addSubview:self.takeBtn];
//    [self addSubview:self.recognizeBtn];
//    [self addSubview:self.recognizeLabel];
//    [self addSubview:self.recognizeSwitchLabel];
//    [self addSubview:self.recognizeSwitchBtn];
    [self addSubview:self.scanLineImgView];

    
    if (self.requestParams[@"isAlbum"] && [self.requestParams[@"isAlbum"] integerValue] == 1) {
        [self addSubview:self.albumBtn];
    }
    
//    if (self.requestParams[@"isTake"] && [self.requestParams[@"isTake"] integerValue] == 1) {
//        [self addSubview:self.takeSwitchBtn];
//        [self addSubview:self.takeSwitchLabel];
//    }


}


/**
 <AUTHOR> 2025年07月11日09:08:32
 @质检提示
 @param tipString提示内容
 */
-(void)qualityTips:(NSString *)tipString{
    if (_qualityTipsLabel) {
        [_qualityTipsLabel removeFromSuperview];
        _qualityTipsLabel=nil;
    }
    
    if ([TKStringHelper isEmpty:tipString]) {
        return;
    }
    tipString=[NSString stringWithFormat:@" %@",tipString];
    NSMutableAttributedString *attri =[[NSMutableAttributedString alloc] initWithString:tipString];
    [attri addAttribute:NSForegroundColorAttributeName value:[TKUIHelper colorWithHexString:@"#FFFFFF"] range:NSMakeRange(0, tipString.length)];
    [attri addAttribute:NSFontAttributeName value:[UIFont fontWithName:@"PingFang SC" size:16] range:NSMakeRange(0, tipString.length)];
    // 创建图片图片附件
    NSTextAttachment *attach = [[NSTextAttachment alloc] init];
    attach.image = [UIImage imageNamed:[NSString stringWithFormat:@"%@.bundle/Resources/TKOpenGeneral/tk_quality_tips_img.png", TK_OPEN_RESOURCE_NAME]];;
    attach.bounds = CGRectMake(0, -3.5, 16, 16);
    NSAttributedString *attachImg = [NSAttributedString attributedStringWithAttachment:attach];
    //将图片插入到合适的位置
    [attri insertAttributedString:attachImg atIndex:0];
    
    self.qualityTipsLabel.attributedText = attri;
    self.qualityTipsLabel.textAlignment=NSTextAlignmentCenter;
    float height=36;
    CGSize lableSize = [self.qualityTipsLabel sizeThatFits:CGSizeMake(UISCREEN_HEIGHT, height)];
    float width=lableSize.width;
    self.qualityTipsLabel.frame=CGRectMake(0, 0, width+20, height);
//    [self.qualityTipsLabel sizeToFit];
    self.qualityTipsLabel.center=self.center;
    self.qualityTipsLabel.backgroundColor=[TKUIHelper colorWithHexString:@"#FF8D07" alpha:0.6f];
    self.qualityTipsLabel.layer.cornerRadius=height/2.0f;
    self.qualityTipsLabel.layer.masksToBounds=YES;
    [self addSubview:self.qualityTipsLabel];
}


#pragma mark setting getting
//-(void)setIsUploadPositiveCard:(BOOL)isUploadPositiveCard{
//    CGFloat width=self.TKWidth;
//    CGFloat height=self.TKHeight;
//    if (self.TKHeight>self.TKWidth) {
//        width=self.TKHeight;
//        height=self.TKWidth;
//    }
//    
//    
//    _isUploadPositiveCard=isUploadPositiveCard;
//    
//    [_idAreaView removeFromSuperview];
//    _idAreaView=nil;
//    [_idTipImgView removeFromSuperview];
//    _idTipImgView=nil;
//    [_topTipLabel removeFromSuperview];
//    _topTipLabel=nil;
//    [self addSubview:self.idAreaView];
//    [self addSubview:self.idTipImgView];
//    [self.idTipImgView setTKTop:(height-self.idTipImgView.TKHeight)/2.0f];
//    [self addSubview:self.topTipLabel];
//    [self.topTipLabel setTKLeft:(width-self.topTipLabel.TKWidth)/2.0f];
//}

-(void)setCurrentBankCardPhotoViewStatus:(TKBankCardPhotoViewStatus)currentBankCardPhotoViewStatus{
    _currentBankCardPhotoViewStatus=currentBankCardPhotoViewStatus;
    switch (_currentBankCardPhotoViewStatus) {
        case TKBankCardPhotoViewStatusOnlyTake://只有拍照
        {
//            [self.takeSwitchBtn setHidden:YES];
//            [self.takeSwitchLabel setHidden:YES];
//            [self.recognizeBtn setHidden:YES];
//            [self.recognizeLabel setHidden:YES];
//            [self.recognizeSwitchLabel setHidden:YES];
//            [self.recognizeSwitchBtn setHidden:YES];
            [self.scanLineImgView setHidden:YES];
            [self.takeBtn setHidden:NO];
            [self.takeLabel setHidden:NO];
            
        }
            break;
        case TKBankCardPhotoViewStatusTake://扫描界面的拍照状态
        {
//            [self.takeSwitchBtn setHidden:YES];
//            [self.takeSwitchLabel setHidden:YES];
//            [self.recognizeBtn setHidden:YES];
//            [self.recognizeLabel setHidden:YES];
            [self.scanLineImgView setHidden:YES];
//            [self.recognizeSwitchLabel setHidden:NO];
//            [self.recognizeSwitchBtn setHidden:NO];
            [self.takeBtn setHidden:NO];
            [self.takeLabel setHidden:NO];
            self.topTipLabel.text=@"请确保银行卡完整置于拍摄区域";

        }
            break;
        case TKBankCardPhotoViewStatusRecognize://扫描界面的扫描识别状态
        {
//            [self.takeSwitchBtn setHidden:NO];
//            [self.takeSwitchLabel setHidden:NO];
//            [self.recognizeBtn setHidden:NO];
//            [self.recognizeLabel setHidden:NO];
            [self.scanLineImgView setHidden:NO];
//            [self.recognizeSwitchLabel setHidden:YES];
//            [self.recognizeSwitchBtn setHidden:YES];
            [self.takeBtn setHidden:YES];
            [self.takeLabel setHidden:YES];
            self.topTipLabel.text=@"将卡片边缘对齐方框以便扫描";
        }
            break;
        default:
            break;
    }

}


#pragma mark 按钮事件
//点击相册
-(void)albumAction:(id)sender{
    if (self.delegate&&[self.delegate respondsToSelector:@selector(bankPhotoViewAlbumAction:)]) {
        [self.delegate bankPhotoViewAlbumAction:sender];
    }
}
//点击拍照
-(void)takeAction:(id)sender{
    if (self.delegate&&[self.delegate respondsToSelector:@selector(bankPhotoViewTakeAction:)]) {
        [self.delegate bankPhotoViewTakeAction:sender];
    }
}
//点击返回
-(void)backAction:(id)sender{
    if (self.delegate&&[self.delegate respondsToSelector:@selector(bankPhotoViewBackAction:)]) {
        [self.delegate bankPhotoViewBackAction:sender];
    }
}

//点击切换到扫描
-(void)switchRecognizeAction:(id)sender{
    if (self.delegate&&[self.delegate respondsToSelector:@selector(bankPhotoViewSwitchRecognizeAction:)]) {
        [self.delegate bankPhotoViewSwitchRecognizeAction:sender];
    }
}

#pragma mark
//点击切换到拍照
-(void)switchTakeAction:(id)sender{
    if (self.delegate&&[self.delegate respondsToSelector:@selector(bankPhotoViewSwitchTakeAction:)]) {
        [self.delegate bankPhotoViewSwitchTakeAction:sender];
    }
}

//扫描线框动画
- (void)addScanLineAnimation{
//    // 1. 创建动画对象
//    CABasicAnimation *moveAnimation = [CABasicAnimation animationWithKeyPath:@"position.x"];
//
//    // 2. 设置动画属性
//    moveAnimation.fromValue = @(self.scanLineImgView.layer.position.x); // 起始X位置
//    moveAnimation.toValue = @(self.scanLineImgView.layer.position.x +self.idAreaView.TKWidth); // 结束X位置
//    moveAnimation.duration = 1.5; // 动画持续时间
//    moveAnimation.timingFunction = [CAMediaTimingFunction functionWithName:kCAMediaTimingFunctionEaseInEaseOut]; // 动画节奏
//
//    // 3. 保持动画结束状态
//    moveAnimation.fillMode = kCAFillModeForwards;
//    moveAnimation.removedOnCompletion = NO;
//
//    // 4. 实际改变图层位置（可选）
//    self.scanLineImgView.layer.position = CGPointMake(self.scanLineImgView.layer.position.x + 200, self.scanLineImgView.layer.position.y);
//
//    // 5. 添加动画到图层
//    [self.scanLineImgView.layer addAnimation:moveAnimation forKey:@"horizontalMoveAnimation"];
    
    /*------------开始设置动画------------------------*/
    CGPoint originalPosition = self.scanLineImgView.layer.position;    //创建动画对象
    CABasicAnimation *basicAni = [CABasicAnimation animation];
    //设置动画属性
//    basicAni.fromValue = [NSValue valueWithCGPoint:originalPosition];
//    basicAni.toValue =[NSValue valueWithCGPoint:CGPointMake(self.TKWidth, originalPosition.y)];
    
//    [basicAni setFromValue:[NSNumber numberWithFloat:0]];
//    [basicAni setToValue:[NSNumber numberWithFloat:self.TKWidth]];
    
    basicAni.keyPath = @"position";

    basicAni.fromValue = [NSValue valueWithCGPoint:originalPosition];
    basicAni.toValue = [NSValue valueWithCGPoint:CGPointMake(self.idAreaView.TKRight-self.scanLineImgView.TKWidth*2.5f, originalPosition.y)];
    //设置动画的起始位置。也就是动画从哪里到哪里
//    basicAni.fromValue = [NSValue valueWithCGPoint:originalPosition];
//    basicAni.toValue =[NSValue valueWithCGPoint:CGPointMake(self.scanLineImgView.center.x+self.idAreaView.TKWidth-30, originalPosition.x)];

//    [NSValue valueWithCGPoint:CGPointMake(self.idAreaView.TKRight-self.scanLineImgView.TKWidth, originalPosition.x)];

    //动画持续时间
    basicAni.duration = 2.50f;
    //动画填充模式
    basicAni.fillMode = kCAFillModeForwards;
    //动画完成不删除
    basicAni.removedOnCompletion = NO;
    //动画重复次数
    basicAni.repeatCount = CGFLOAT_MAX;
    basicAni.timingFunction = [CAMediaTimingFunction functionWithName:kCAMediaTimingFunctionLinear];
      //把动画添加到要作用的Layer上面
    [self.scanLineImgView.layer addAnimation:basicAni forKey:@"translate"];

    
//    // 1. 创建动画对象
//    CABasicAnimation *moveAnimation = [CABasicAnimation animationWithKeyPath:@"position.x"];
//
//    // 2. 设置动画属性
//    moveAnimation.fromValue = @(self.scanLineImgView.layer.position.x); // 起始X位置
//    moveAnimation.toValue = @(self.scanLineImgView.layer.position.x + self.idAreaView.TKWidth); // 结束X位置
//    moveAnimation.duration = 2.5; // 动画持续时间
//    moveAnimation.timingFunction = [CAMediaTimingFunction functionWithName:kCAMediaTimingFunctionEaseInEaseOut]; // 动画节奏
//    moveAnimation.repeatCount = CGFLOAT_MAX;
//    // 3. 保持动画结束状态
//    moveAnimation.fillMode = kCAFillModeForwards;
//    moveAnimation.removedOnCompletion = NO;
//
////    // 4. 实际改变图层位置（可选）
////    yourView.layer.position = CGPointMake(yourView.layer.position.x + 200, yourView.layer.position.y);
//
//    // 5. 添加动画到图层
//    [self.scanLineImgView.layer addAnimation:moveAnimation forKey:@"horizontalMoveAnimation"];
    
    
    //开始平移动画
    
//        NSLog(@"开始执行平移动画");
//        CABasicAnimation *animation1 = [CABasicAnimation animation];
//        //注意：不能使用transform.position.x,那样的话动画无效
//        animation1.keyPath = @"position.x";
//        animation1.duration = 1.0f;
//        animation1.byValue = @(self.id);
//        animation1.beginTime = 0.f;
//        //动画维持结束后的状态,如果不加这两句代码，动画运行结束后会恢复最初的动画状态
//        animation1.removedOnCompletion = NO;
//        animation1.fillMode = kCAFillModeForwards;
//        [self.scanLineImgView.layer addAnimation:animation1 forKey:@"animation1"];
    
}

#pragma mark - 公共动画控制接口

/**
 * <AUTHOR> 2025年07月29日
 * @brief 启动扫描线动画
 * @discussion 统一的扫描线动画控制接口，替代控制器直接操作内部视图
 */
- (void)startScanLineAnimation {
    // 确保扫描线可见
    self.scanLineImgView.hidden = NO;

    // 移除可能存在的旧动画
    [self.scanLineImgView.layer removeAllAnimations];

    // 启动水平扫描动画
    [self addScanLineAnimation];

    NSLog(@"[TKBankCardPhotoView] 启动扫描线动画");
}

/**
 * <AUTHOR> 2025年07月29日
 * @brief 停止扫描线动画
 * @discussion 停止并移除扫描线动画
 */
- (void)stopScanLineAnimation {
    // 移除所有动画
    [self.scanLineImgView.layer removeAllAnimations];

    // 隐藏扫描线
    self.scanLineImgView.hidden = YES;

    NSLog(@"[TKBankCardPhotoView] 停止扫描线动画");
}

#pragma mark lazyloading
/**
 <AUTHOR> 2025年07月03日09:44:34
 @初始化懒加载idAreaView
 @return idAreaView
 */
-(UIImageView *)idAreaView{
    if (!_idAreaView) {
        _idAreaView=[[UIImageView alloc] initWithFrame:self.idRect];
        UIImage *idAreaImg;
        idAreaImg=[UIImage imageNamed:[NSString stringWithFormat:@"%@.bundle/Resources/TKOpenPlugin60013/tk_card_frame_5.1UI.png", TK_OPEN_RESOURCE_NAME]];
        //对准框不做主题色变化
        [_idAreaView setImage:idAreaImg];
    }
    return _idAreaView;
}

/**
 <AUTHOR> 2025年07月03日09:59:15
 @初始化懒加载topView
 @return topView
 */
-(UIView *)topView{
    if (!_topView) {
        _topView=[[UIView alloc] initWithFrame:CGRectMake(0, 0, self.TKWidth, self.idRect.origin.y)];
        _topView.backgroundColor=[TKUIHelper colorWithHexString:@"#000000" alpha:0.6];
    }
    return _topView;
}

/**
 <AUTHOR> 2025年07月03日10:00:19
 @初始化懒加载leftView
 @return leftView
 */
-(UIView *)leftView{
    if (!_leftView) {
        _leftView=[[UIView alloc] initWithFrame:CGRectMake(0, self.idRect.origin.y, self.idRect.origin.x, self.idRect.size.height)];
        _leftView.backgroundColor=[TKUIHelper colorWithHexString:@"#000000" alpha:0.6];
    }
    return _leftView;
}

/**
 <AUTHOR> 2025年07月03日10:00:26
 @初始化懒加载rightView
 @return rightView
 */
-(UIView *)rightView{
    if (!_rightView) {
        _rightView=[[UIView alloc] initWithFrame:CGRectMake(self.idRect.size.width+self.idRect.origin.x, self.idRect.origin.y, self.idRect.origin.x, self.idRect.size.height)];
        _rightView.backgroundColor=[TKUIHelper colorWithHexString:@"#000000" alpha:0.6];
    }
    return _rightView;
}

/**
 <AUTHOR> 2025年07月03日10:00:32
 @初始化懒加载bottomView
 @return bottomView
 */
-(UIView *)bottomView{
    if (!_bottomView) {
        _bottomView=[[UIView alloc] initWithFrame:CGRectMake(0, self.idRect.size.height+self.idRect.origin.y, self.TKWidth, self.TKHeight-self.idRect.origin.y-self.idRect.size.height)];
        _bottomView.backgroundColor=[TKUIHelper colorWithHexString:@"#000000" alpha:0.6];
    }
    return _bottomView;
}

/**
 <AUTHOR> 2025年07月03日10:09:26
 @初始化懒加载backBtn
 @return backBtn
 */
-(UIButton *)backBtn{
    if (!_backBtn) {
        _backBtn= [UIButton buttonWithType:UIButtonTypeCustom];
        _backBtn.tag = 102;
        [_backBtn setImage:[UIImage imageNamed:[NSString stringWithFormat:@"%@.bundle/Resources/TKOpenPlugin60013/back_btn_icon_new.png", TK_OPEN_RESOURCE_NAME]] forState:UIControlStateNormal];
        [_backBtn setImageEdgeInsets:UIEdgeInsetsMake(9, 10, 9, 14)];

        float bBtnWidth=36;
        _backBtn.frame = CGRectMake(16, STATUSBAR_HEIGHT, bBtnWidth, bBtnWidth);
        _backBtn.layer.cornerRadius=_backBtn.frame.size.width/2;
        [_backBtn setBackgroundColor:[TKUIHelper colorWithHexString:@"#666666" alpha:0.7f]];
        [_backBtn setContentHorizontalAlignment:UIControlContentHorizontalAlignmentCenter];
        [_backBtn setContentVerticalAlignment:UIControlContentVerticalAlignmentCenter];
        [_backBtn addTarget:self action:@selector(backAction:) forControlEvents:UIControlEventTouchUpInside];
    }
    return _backBtn;
}



///**
// <AUTHOR> 2025年07月03日10:28:53
// @初始化懒加载idTipImgView
// @return idTipImgView
// */
//-(UIImageView *)idTipImgView{
//    if (!_idTipImgView) {
//        float width=80;
//        float height=50;
//        float y=(self.TKHeight-height)/2.0f;
//        float x=(self.idRect.origin.x-width)/2.0f;
//        _idTipImgView=[[UIImageView alloc] initWithFrame:CGRectMake(x, y, width, height)];
//        UIImage *idTipImg;
//        if (self.isUploadPositiveCard) {
//            idTipImg=[UIImage imageNamed:[NSString stringWithFormat:@"%@.bundle/Resources/TKOpenPlugin60013/tk_card_frame_5.1UI_small_front.png", TK_OPEN_RESOURCE_NAME]];
//        }else{
//            idTipImg=[UIImage imageNamed:[NSString stringWithFormat:@"%@.bundle/Resources/TKOpenPlugin60013/tk_card_frame_5.1UI_small_reverse.png", TK_OPEN_RESOURCE_NAME]];
//        }
//     
//        [_idTipImgView setImage:idTipImg];
//    }
//    return _idTipImgView;
//}

/**
 <AUTHOR> 2025年07月03日10:41:26
 @初始化懒加载topTipLabel
 @return topTipLabel
 */
-(UILabel *)topTipLabel{
    if (!_topTipLabel) {
        
        _topTipLabel=[[UILabel alloc] init];
        _topTipLabel.textColor = [UIColor whiteColor];
        _topTipLabel.text=@"请确保银行卡完整置于拍摄区域";
        _topTipLabel.textAlignment = NSTextAlignmentCenter;
        _topTipLabel.font = [UIFont fontWithName:@"PingFang SC" size:14];
        _topTipLabel.textColor = [TKUIHelper colorWithHexString:@"#ffffff"];
        float height=20;
        CGSize lableSize = [_topTipLabel sizeThatFits:CGSizeMake(self.TKWidth, height)];
        _topTipLabel.frame=CGRectMake((self.TKWidth-lableSize.width)/2.0f, self.idAreaView.TKTop-height-29, lableSize.width, height);

    }
    return _topTipLabel;
}

/**
 <AUTHOR> 2025年07月03日10:56:08
 @初始化懒加载bottomTipLabel
 @return bottomTipLabel
 */
-(UILabel *)bottomTipLabel{
    if (!_bottomTipLabel) {
        
        _bottomTipLabel=[[UILabel alloc] init];
        _bottomTipLabel.numberOfLines=0;
        _bottomTipLabel.textAlignment = NSTextAlignmentCenter;
        NSString *name=self.requestParams[@"userName"];
        NSString *string=[NSString stringWithFormat:@"为了你的账户资金安全，\n只能绑定持卡人（%@）本人的银行卡。",name];
        if ([TKStringHelper isEmpty:name]) {
            string=@"为了你的账户资金安全，\n只能绑定持卡人本人的银行卡。";
        }
        NSMutableAttributedString *attri =[[NSMutableAttributedString alloc] initWithString:string];
        [attri addAttribute:NSForegroundColorAttributeName value:[TKUIHelper colorWithHexString:@"#FFFFFF"] range:NSMakeRange(0, string.length)];
        
        if ([TKStringHelper isNotEmpty:name]) {
            NSRange tipRange;
            tipRange=[string rangeOfString:name];
            [attri addAttribute:NSForegroundColorAttributeName value:[UIColor orangeColor] range:tipRange];
        }

        
        [attri addAttribute:NSFontAttributeName value:[UIFont fontWithName:@"PingFang SC" size:14] range:NSMakeRange(0, string.length)];
        // 创建图片图片附件
        NSTextAttachment *attach = [[NSTextAttachment alloc] init];
        attach.image = [UIImage imageNamed:[NSString stringWithFormat:@"%@.bundle/Resources/TKOpenGeneral/tk_quality_tips_bgwhite_img.png", TK_OPEN_RESOURCE_NAME]];;
        attach.bounds = CGRectMake(0, -3.5, 16, 16);

        NSAttributedString *attachImg = [NSAttributedString attributedStringWithAttachment:attach];
        //将图片插入到合适的位置
        [attri insertAttributedString:attachImg atIndex:0];
        
        _bottomTipLabel.attributedText = attri;
        
        float height=44;
        CGSize lableSize = [_bottomTipLabel sizeThatFits:CGSizeMake(self.TKWidth, height)];
        _bottomTipLabel.frame=CGRectMake((self.TKWidth-lableSize.width)/2.0f, self.idRect.origin.y+self.idRect.size.height+29, lableSize.width, height);

    }
    return _bottomTipLabel;
}

/**
 <AUTHOR> 2025年07月03日13:01:48
 @初始化懒加载takeBtn
 @return takeBtn
 */
-(UIButton *)takeBtn{
    if (!_takeBtn) {
        _takeBtn= [UIButton buttonWithType:UIButtonTypeCustom];
        _takeBtn.tag = 101;
        [_takeBtn setBackgroundImage:[UIImage imageNamed:[NSString stringWithFormat:@"%@.bundle/Resources/TKOpenPlugin60013/tk_photo_btn_new.png", TK_OPEN_RESOURCE_NAME]] forState:UIControlStateNormal];
        [_takeBtn setBackgroundImage:[UIImage imageNamed:[NSString stringWithFormat:@"%@.bundle/Resources/TKOpenPlugin60013/tk_photo_selected_btn_new.png", TK_OPEN_RESOURCE_NAME]] forState:UIControlStateHighlighted];
        float width=48;
        _takeBtn.frame = CGRectMake((self.TKWidth-width)/2.0f, self.takeLabel.TKTop-width, width, width);
        
        [_takeBtn setContentHorizontalAlignment:UIControlContentHorizontalAlignmentCenter];
        
        [_takeBtn setContentVerticalAlignment:UIControlContentVerticalAlignmentCenter];
        
        [_takeBtn addTarget:self action:@selector(takeAction:) forControlEvents:UIControlEventTouchUpInside];
        
        
    }
    return _takeBtn;
}

/**
 <AUTHOR> 2025年07月03日13:22:09
 @初始化懒加载takeLabel
 @return takeLabel
 */
-(UILabel *)takeLabel{
    if (!_takeLabel) {
        
        _takeLabel=[[UILabel alloc] init];
        _takeLabel.textColor = [UIColor whiteColor];
        if ([TKOpenViewStyleHelper shareInstance].isElder) {
            _takeLabel.font=[UIFont fontWithName:@"PingFang SC" size:18];
        }else{
            _takeLabel.font=[UIFont fontWithName:@"PingFang SC" size:14];
        }
        _takeLabel.text=@"拍照";
        _takeLabel.textAlignment = NSTextAlignmentCenter;
        float height=20;
        CGSize lableSize = [_takeLabel sizeThatFits:CGSizeMake(self.TKWidth, height)];
        _takeLabel.frame=CGRectMake((self.TKWidth-lableSize.width)/2.0f, self.TKHeight-height-IPHONEX_BUTTOM_HEIGHT-29, lableSize.width, height);

    }
    return _takeLabel;
}

///**
// <AUTHOR> 2025年07月03日13:36:06
// @初始化懒加载albumLabel
// @return albumLabel
// */
//-(UILabel *)albumLabel{
//    if (!_albumLabel) {
//        
//        _albumLabel=[[UILabel alloc] init];
//        _albumLabel.textColor = [UIColor whiteColor];
//        if ([TKOpenViewStyleHelper shareInstance].isElder) {
//            _albumLabel.font=[UIFont fontWithName:@"PingFang SC" size:18];
//        }else{
//            _albumLabel.font=[UIFont fontWithName:@"PingFang SC" size:14];
//        }
//        _albumLabel.text=@"相册";
//        _albumLabel.textAlignment = NSTextAlignmentCenter;
//        float height=20;
//        CGSize lableSize = [_albumLabel sizeThatFits:CGSizeMake(self.idRect.origin.x, height)];
//        _albumLabel.frame=CGRectMake((self.idRect.origin.x - lableSize.width)/2+self.idRect.origin.x+self.idRect.size.width, self.idRect.origin.y+self.idRect.size.height-height, lableSize.width, height);
//
//    }
//    return _albumLabel;
//}

/**
 <AUTHOR> 2025年07月03日13:36:01
 @初始化懒加载albumBtn
 @return albumBtn
 */
-(UIButton *)albumBtn{
    if (!_albumBtn) {
        _albumBtn= [UIButton buttonWithType:UIButtonTypeCustom];
        _albumBtn.tag = 100;

        [_albumBtn setImage:[UIImage imageNamed:[NSString stringWithFormat:@"%@.bundle/Resources/TKOpenPlugin60013/album_btn_icon_new.png", TK_OPEN_RESOURCE_NAME]] forState:UIControlStateNormal];
        [_albumBtn setImageEdgeInsets:UIEdgeInsetsMake(9, 9, 9, 9)];

        float width=36;
        _albumBtn.frame = CGRectMake(self.TKWidth-width-16, STATUSBAR_HEIGHT, width, width);
        _albumBtn.layer.cornerRadius=_backBtn.frame.size.width/2;
        [_albumBtn setBackgroundColor:[TKUIHelper colorWithHexString:@"#666666" alpha:0.7f]];
        [_albumBtn setContentHorizontalAlignment:UIControlContentHorizontalAlignmentCenter];
        [_albumBtn setContentVerticalAlignment:UIControlContentVerticalAlignmentCenter];
        
        [_albumBtn addTarget:self action:@selector(albumAction:) forControlEvents:UIControlEventTouchUpInside];
        
        
    }
    return _albumBtn;
}

///**
// <AUTHOR> 2025年07月04日09:43:30
// @初始化懒加载recognizeSwitchBtn
// @return recognizeSwitchBtn
// */
//-(UIButton *)recognizeSwitchBtn{
//    if (!_recognizeSwitchBtn) {
//        _recognizeSwitchBtn= [UIButton buttonWithType:UIButtonTypeCustom];
//        _recognizeSwitchBtn.tag = 106;
//        [_recognizeSwitchBtn setImage:[UIImage imageNamed:[NSString stringWithFormat:@"%@.bundle/Resources/TKOpenPlugin60013/scan_btn_icon_new.png", TK_OPEN_RESOURCE_NAME]] forState:UIControlStateNormal];
//        [_recognizeSwitchBtn setImageEdgeInsets:UIEdgeInsetsMake(9, 9, 9, 9)];
//
//        float width=36;
//        _recognizeSwitchBtn.frame = CGRectMake((self.idRect.origin.x - width)/2+self.idRect.origin.x+self.idRect.size.width, self.idAreaView.TKTop, width, width);
//        _recognizeSwitchBtn.layer.cornerRadius=_backBtn.frame.size.width/2;
//        [_recognizeSwitchBtn setBackgroundColor:[TKUIHelper colorWithHexString:@"#666666" alpha:0.7f]];
//        [_recognizeSwitchBtn setContentHorizontalAlignment:UIControlContentHorizontalAlignmentCenter];
//        [_recognizeSwitchBtn setContentVerticalAlignment:UIControlContentVerticalAlignmentCenter];
//        
//        [_recognizeSwitchBtn addTarget:self action:@selector(switchRecognizeAction:) forControlEvents:UIControlEventTouchUpInside];
//        
//        
//    }
//    return _recognizeSwitchBtn;
//}

///**
// <AUTHOR> 2025年07月04日10:34:00
// @初始化懒加载recognizeSwitchLabel
// @return recognizeSwitchLabel
// */
//-(UILabel *)recognizeSwitchLabel{
//    if (!_recognizeSwitchLabel) {
//        
//        _recognizeSwitchLabel=[[UILabel alloc] init];
//        _recognizeSwitchLabel.textColor = [UIColor whiteColor];
//        if ([TKOpenViewStyleHelper shareInstance].isElder) {
//            _recognizeSwitchLabel.font=[UIFont fontWithName:@"PingFang SC" size:18];
//        }else{
//            _recognizeSwitchLabel.font=[UIFont fontWithName:@"PingFang SC" size:14];
//        }
//        _recognizeSwitchLabel.text=@"自动识别";
//        _recognizeSwitchLabel.textAlignment = NSTextAlignmentCenter;
//        float height=20;
//        CGSize lableSize = [_recognizeSwitchLabel sizeThatFits:CGSizeMake(self.idRect.origin.x, height)];
//        _recognizeSwitchLabel.frame=CGRectMake((self.idRect.origin.x - lableSize.width)/2+self.idRect.origin.x+self.idRect.size.width, self.recognizeSwitchBtn.TKBottom, lableSize.width, height);
//
//    }
//    return _recognizeSwitchLabel;
//}

///**
// <AUTHOR> 2025年07月04日10:03:01
// @初始化懒加载takeSwitchBtn
// @return takeSwitchBtn
// */
//-(UIButton *)takeSwitchBtn{
//    if (!_takeSwitchBtn) {
//        _takeSwitchBtn= [UIButton buttonWithType:UIButtonTypeCustom];
//        _takeSwitchBtn.tag = 105;
//        [_takeSwitchBtn setImage:[UIImage imageNamed:[NSString stringWithFormat:@"%@.bundle/Resources/TKOpenPlugin60013/take_btn_icon_new.png", TK_OPEN_RESOURCE_NAME]] forState:UIControlStateNormal];
//        [_takeSwitchBtn setImageEdgeInsets:UIEdgeInsetsMake(9, 9, 9, 9)];
//
//        float width=36;
//        _takeSwitchBtn.frame = CGRectMake((self.idRect.origin.x - width)/2+self.idRect.origin.x+self.idRect.size.width, self.idAreaView.TKTop, width, width);
//        _takeSwitchBtn.layer.cornerRadius=_backBtn.frame.size.width/2;
//        [_takeSwitchBtn setBackgroundColor:[TKUIHelper colorWithHexString:@"#666666" alpha:0.7f]];
//        [_takeSwitchBtn setContentHorizontalAlignment:UIControlContentHorizontalAlignmentCenter];
//        [_takeSwitchBtn setContentVerticalAlignment:UIControlContentVerticalAlignmentCenter];
//        
//        [_takeSwitchBtn addTarget:self action:@selector(switchTakeAction:) forControlEvents:UIControlEventTouchUpInside];
//        
//        
//    }
//    return _takeSwitchBtn;
//}

///**
// <AUTHOR> 2025年07月04日10:35:20
// @初始化懒加载takeSwitchLabel
// @return takeSwitchLabel
// */
//-(UILabel *)takeSwitchLabel{
//    if (!_takeSwitchLabel) {
//        
//        _takeSwitchLabel=[[UILabel alloc] init];
//        _takeSwitchLabel.textColor = [UIColor whiteColor];
//        if ([TKOpenViewStyleHelper shareInstance].isElder) {
//            _takeSwitchLabel.font=[UIFont fontWithName:@"PingFang SC" size:18];
//        }else{
//            _takeSwitchLabel.font=[UIFont fontWithName:@"PingFang SC" size:14];
//        }
//        _takeSwitchLabel.text=@"手动拍照";
//        _takeSwitchLabel.textAlignment = NSTextAlignmentCenter;
//        float height=20;
//        CGSize lableSize = [_takeSwitchLabel sizeThatFits:CGSizeMake(self.idRect.origin.x, height)];
//        _takeSwitchLabel.frame=CGRectMake((self.idRect.origin.x - lableSize.width)/2+self.idRect.origin.x+self.idRect.size.width, self.recognizeSwitchBtn.TKBottom, lableSize.width, height);
//
//    }
//    return _takeSwitchLabel;
//}


///**
// <AUTHOR> 2025年07月04日10:54:05
// @初始化懒加载recognizeBtn
// @return recognizeBtn
// */
//-(UIButton *)recognizeBtn{
//    if (!_recognizeBtn) {
//        _recognizeBtn= [UIButton buttonWithType:UIButtonTypeCustom];
//        [_recognizeBtn setImage:[UIImage imageNamed:[NSString stringWithFormat:@"%@.bundle/Resources/TKOpenPlugin60013/scan_btn_icon_new.png", TK_OPEN_RESOURCE_NAME]] forState:UIControlStateNormal];
//        [_recognizeBtn setImageEdgeInsets:UIEdgeInsetsMake(9, 11, 9, 11)];
//        float width=48;
//        _recognizeBtn.frame = CGRectMake((self.idRect.origin.x - width)/2+self.idRect.origin.x+self.idRect.size.width, (self.TKHeight-72)/2, width, width);
//        [_recognizeBtn setBackgroundColor:[TKUIHelper colorWithHexString:self.mainColorString]];
//        _recognizeBtn.layer.cornerRadius=width/2.0f;
//        [_recognizeBtn setContentHorizontalAlignment:UIControlContentHorizontalAlignmentCenter];
//        
//        [_recognizeBtn setContentVerticalAlignment:UIControlContentVerticalAlignmentCenter];
//                
//        
//    }
//    return _recognizeBtn;
//}

///**
// <AUTHOR> 2025年07月04日10:57:35
// @初始化懒加载recognizeLabel
// @return recognizeLabel
// */
//-(UILabel *)recognizeLabel{
//    if (!_recognizeLabel) {
//        
//        _recognizeLabel=[[UILabel alloc] init];
//        _recognizeLabel.textColor=[TKUIHelper colorWithHexString:self.mainColorString];
//
//        if ([TKOpenViewStyleHelper shareInstance].isElder) {
//            _recognizeLabel.font=[UIFont fontWithName:@"PingFang SC" size:18];
//        }else{
//            _recognizeLabel.font=[UIFont fontWithName:@"PingFang SC" size:14];
//        }
//        _recognizeLabel.text=@"自动识别";
//        _recognizeLabel.textAlignment = NSTextAlignmentCenter;
//        float height=20;
//        CGSize lableSize = [_recognizeLabel sizeThatFits:CGSizeMake(self.idRect.origin.x, height)];
//        _recognizeLabel.frame=CGRectMake((self.idRect.origin.x - lableSize.width)/2+self.idRect.origin.x+self.idRect.size.width, self.recognizeBtn.TKBottom, lableSize.width, height);
//
//    }
//    return _recognizeLabel;
//}

/**
 <AUTHOR> 2025年07月04日15:03:55
 @初始化懒加载scanLineImgView
 @return scanLineImgView
 */
-(UIImageView *)scanLineImgView{
    if (!_scanLineImgView) {
        _scanLineImgView = [[UIImageView alloc] initWithFrame:CGRectMake(self.idAreaView.TKLeft-15, self.idAreaView.TKTop, 30, self.idAreaView.TKHeight)];
        UIImage *scanLineImg= [UIImage imageNamed:[NSString stringWithFormat:@"%@.bundle/Resources/TKOpenPlugin60013/scan_line_new.png", TK_OPEN_RESOURCE_NAME]];
//        if (![self.mainColorString isEqualToString:TKCARD_MAIN_COLOR]) {
//            scanLineImg = [scanLineImg imageWithRenderingMode:UIImageRenderingModeAlwaysTemplate];
//            [_scanLineImgView setTintColor:[TKUIHelper colorWithHexString:self.mainColorString]];
//        }
        _scanLineImgView.image =scanLineImg;
        
        _scanLineImgView.contentMode = UIViewContentModeScaleAspectFill;
        _scanLineImgView.backgroundColor = [UIColor clearColor];
        // 注意：不在懒加载时自动启动动画，由外部通过 startScanLineAnimation 控制

    }
    return _scanLineImgView;
}



/**
 <AUTHOR> 2025年07月11日09:53:06
 @初始化懒加载qualityTipsLabel
 @return qualityTipsLabel
 */
-(UILabel *)qualityTipsLabel{
    if (!_qualityTipsLabel) {
        _qualityTipsLabel=[[UILabel alloc] init];
    }
    return _qualityTipsLabel;
}
@end
