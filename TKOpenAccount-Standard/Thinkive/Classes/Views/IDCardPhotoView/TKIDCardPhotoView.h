//
//  TKIDCardPhotoView.h
//  TKOpenAccount-Standard
//  身份证拍照扫描界面，横屏布局
//  Created by 夏博文 on 2025/7/2.
//  Copyright © 2025 thinkive. All rights reserved.
//

typedef enum : NSUInteger {
    TKIDCardPhotoViewStatusOnlyTake = 0,//只有拍照
    TKIDCardPhotoViewStatusTake,//扫描界面的拍照状态
    TKIDCardPhotoViewStatusRecognize,//扫描界面的扫描识别状态
} TKIDCardPhotoViewStatus;


@protocol TKIDCardPhotoViewDelegate <NSObject>
//打开相册
- (void)photoViewAlbumAction:(id)sender;

//拍摄照片
- (void)photoViewTakeAction:(id)sender;

//点击返回
- (void)photoViewBackAction:(id)sender;

//点击切换到扫描
- (void)photoViewSwitchRecognizeAction:(id)sender;

//点击切换到拍照
- (void)photoViewSwitchTakeAction:(id)sender;
@end

@interface TKIDCardPhotoView : UIView
@property (nonatomic, weak) id<TKIDCardPhotoViewDelegate>delegate;
@property(nonatomic,assign) CGRect  idRect;//照片裁剪指定卡片区域,手机屏幕区域
@property(nonatomic,assign) BOOL isUploadPositiveCard;//是否证件正面
@property (nonatomic, assign) TKIDCardPhotoViewStatus currentIDCardPhotoViewStatus;


/**
 *<AUTHOR> 2025年07月02日15:53:53
 *@初始化相册预览页面
 *@param frame frame
 *@param requestParams 初始化页面用到的参数
 */
-(instancetype)initWithFrame:(CGRect)frame requestParams:(NSMutableDictionary *)requestParams;

/**
 <AUTHOR> 2025年07月11日09:08:32
 @质检提示
 @param tipString提示内容
 */
-(void)qualityTips:(NSString *)tipString;
@end

