//
//  TKEXIDTimeoutView.m
//  HrSec
//
//  Created by <PERSON>ie on 2019/9/24.
//  Copyright © 2019 刘任朋. All rights reserved.
//

#import "TKOCRTimeoutView.h"
@interface TKOCRTimeoutView()
@property (nonatomic, strong) UIView *tipBgView;//提示窗口背景视图


@property (nonatomic, strong) NSMutableDictionary *requestParams;//h5传来的请求参数
@end
@implementation TKOCRTimeoutView
/**
 *<AUTHOR> 2019年09月24日15:58:25
 *@初始化身份证OCR超时提示视图
 *@param frame frame
 *@param requestParams 初始化页面用到的参数
 */
-(instancetype)initWithFrame:(CGRect)frame requestParams:(NSMutableDictionary *)requestParams{
    self=[super initWithFrame:frame];
    if (self) {
        [self setBackgroundColor:[UIColor colorWithRed:128/255.0 green:128/255.0 blue:128/255.0 alpha:0.6]];
        self.requestParams=requestParams;
        if ([TKStringHelper isEmpty:self.requestParams[@"mainColor"]]) {
            self.requestParams[@"mainColor"]=nil;
        }
        [self viewInit];
    }
    return self;
}
/**
 <AUTHOR> 2019年09月24日15:58:32
 @初始化身份证OCR超时提示视图
 */
-(void)viewInit{
    [self addSubview:self.tipBgView];
    [self.tipBgView addSubview:self.titleLabel];
    [self.tipBgView addSubview:self.bkwzImgView];
    [self.tipBgView addSubview:self.nwsjImgView];
    [self.tipBgView addSubview:self.gxhsImgView];
    [self.tipBgView addSubview:self.bkwzLabel];
    [self.tipBgView addSubview:self.nwsjLabel];
    [self.tipBgView addSubview:self.gxhsLabel];
    [self.tipBgView addSubview:self.describeLabel];
    [self.tipBgView addSubview:self.continueScanBtn];
    [self.tipBgView addSubview:self.takeBtn];
}

-(void)isUploadFile:(BOOL)flag{
    self.takeBtn.tag=107;
    self.continueScanBtn.tag=108;
    [self.bkwzImgView removeFromSuperview];
    [self.nwsjImgView removeFromSuperview];
    [self.gxhsImgView removeFromSuperview];
    [self.bkwzLabel removeFromSuperview];
    [self.nwsjLabel removeFromSuperview];
    [self.gxhsLabel removeFromSuperview];
    [self.tipBgView setTKHeight:168];
    [self.tipBgView setTKTop:(self.TKHeight-168)/2.0f];
    [self.describeLabel setTKTop:self.titleLabel.TKBottom];
    [self.takeBtn setTKTop:self.tipBgView.TKHeight-40-24];
    [self.continueScanBtn setTKTop:self.takeBtn.TKTop];
}

//设置单独按钮,无拍照场景时候
-(void)setOnlyBtn{
    [self.takeBtn removeFromSuperview];
    [self.continueScanBtn setTKLeft:(self.tipBgView.TKWidth-self.continueScanBtn.TKWidth)/2.0f];
}


#pragma mark 按钮事件
-(void)btnAction:(id)sender{
    if (self.delegate&&[self.delegate respondsToSelector:@selector(doTipAction:)]) {
        [self.delegate doTipAction:sender];
    }
}

#pragma mark lazyloading

/**
 <AUTHOR> 2019年08月23日11:17:19
 @初始化懒加载提示窗口背景视图
 @return 提示窗口背景视图
 */
-(UIView *)tipBgView{
    if (!_tipBgView) {
        float width=300;
        float height;
        height=288;
        float x=(self.TKWidth-width)/2;
        float y=(self.TKHeight-height)/2.0f;
        _tipBgView=[[UIView alloc] initWithFrame:CGRectMake(x, y, width, height)];
        _tipBgView.backgroundColor=[UIColor whiteColor];
        _tipBgView.layer.cornerRadius=8.0f;
        _tipBgView.layer.masksToBounds=YES;
    }
    return _tipBgView;
}



/**
 <AUTHOR> 2019年09月07日13:20:27
 @初始化懒加载titleLabel
 @return titleLabel
 */
-(UILabel *)titleLabel{
    if (!_titleLabel) {
        float x=0;
        float y=24;
        float width=self.tipBgView.TKWidth;
        float height=26;
        _titleLabel=[[UILabel alloc] initWithFrame:CGRectMake(x, y, width, height)];
        _titleLabel.textAlignment=NSTextAlignmentCenter;
        _titleLabel.text = @"扫描未成功";
        _titleLabel.font = [UIFont fontWithName:@"PingFang SC" size:18];
        _titleLabel.textColor = [TKUIHelper colorWithHexString:@"#000000"];
    }
    return _titleLabel;
}



/**
 <AUTHOR> 2023年05月22日10:22:39
 @初始化懒加载bkwzImgView
 @return bkwzImgView
 */
-(UIImageView *)bkwzImgView{
    if (!_bkwzImgView) {
        float x=24;
        float y=self.titleLabel.TKBottom;
        float width=(self.tipBgView.TKWidth-2*x)/3.0f;
        float height=60;
        _bkwzImgView=[[UIImageView alloc] initWithFrame:CGRectMake(x, y, width, height)];
        [_bkwzImgView setImage:[UIImage imageNamed:[NSString stringWithFormat:@"%@.bundle/Resources/TKOpenPlugin60013/tk_bkwz_tip.png", TK_OPEN_RESOURCE_NAME]]];
        _bkwzImgView.contentMode=UIViewContentModeCenter;
    }
    return _bkwzImgView;
}

/**
 <AUTHOR> 2023年05月22日10:53:04
 @初始化懒加载nwsjImgView
 @return nwsjImgView
 */
-(UIImageView *)nwsjImgView{
    if (!_nwsjImgView) {
        float x=self.bkwzImgView.TKRight;
        float y=self.bkwzImgView.TKTop;
        float width=self.bkwzImgView.TKWidth;
        float height=self.bkwzImgView.TKHeight;
        _nwsjImgView=[[UIImageView alloc] initWithFrame:CGRectMake(x, y, width, height)];
        [_nwsjImgView setImage:[UIImage imageNamed:[NSString stringWithFormat:@"%@.bundle/Resources/TKOpenPlugin60013/tk_nwsj_tip.png", TK_OPEN_RESOURCE_NAME]]];
        _nwsjImgView.contentMode=UIViewContentModeCenter;
    }
    return _nwsjImgView;
}

/**
 <AUTHOR> 2023年05月22日10:53:15
 @初始化懒加载gxhsImgView
 @return gxhsImgView
 */
-(UIImageView *)gxhsImgView{
    if (!_gxhsImgView) {
        float x=self.nwsjImgView.TKRight;
        float y=self.bkwzImgView.TKTop;
        float width=self.bkwzImgView.TKWidth;
        float height=self.bkwzImgView.TKHeight;
        _gxhsImgView=[[UIImageView alloc] initWithFrame:CGRectMake(x, y, width, height)];
        [_gxhsImgView setImage:[UIImage imageNamed:[NSString stringWithFormat:@"%@.bundle/Resources/TKOpenPlugin60013/tk_gxhs_tip.png", TK_OPEN_RESOURCE_NAME]]];
        _gxhsImgView.contentMode=UIViewContentModeCenter;
    }
    return _gxhsImgView;
}

/**
 <AUTHOR> 2023年05月22日10:23:13
 @初始化懒加载bkwzLabel
 @return bkwzLabel
 */
-(UILabel *)bkwzLabel{
    if (!_bkwzLabel) {
        float x=self.bkwzImgView.TKLeft;
        float y=self.bkwzImgView.TKBottom;
        float width=self.bkwzImgView.TKWidth;
        float height=26;
        _bkwzLabel=[[UILabel alloc] initWithFrame:CGRectMake(x, y, width, height)];
        _bkwzLabel.textAlignment=NSTextAlignmentCenter;
        _bkwzLabel.text=@"边框完整";
        _bkwzLabel.textColor=[TKUIHelper colorWithHexString:@"#666666"];
        _bkwzLabel.font=[UIFont fontWithName:@"PingFang SC" size:14.0f];
    }
    return _bkwzLabel;
}

/**
 <AUTHOR> 2023年05月22日10:57:46
 @初始化懒加载nwsjLabel
 @return nwsjLabel
 */
-(UILabel *)nwsjLabel{
    if (!_nwsjLabel) {
        float x=self.bkwzLabel.TKRight;
        float y=self.bkwzLabel.TKTop;
        float width=self.bkwzLabel.TKWidth;
        float height=self.bkwzLabel.TKHeight;
        _nwsjLabel=[[UILabel alloc] initWithFrame:CGRectMake(x, y, width, height)];
        _nwsjLabel.textAlignment=NSTextAlignmentCenter;
        _nwsjLabel.text=@"拿稳手机";
        _nwsjLabel.textColor=[TKUIHelper colorWithHexString:@"#666666"];
        _nwsjLabel.font=[UIFont fontWithName:@"PingFang SC" size:14.0f];
    }
    return _nwsjLabel;
}

/**
 <AUTHOR> 2023年05月22日10:59:25
 @初始化懒加载gxhsLabel
 @return gxhsLabel
 */
-(UILabel *)gxhsLabel{
    if (!_gxhsLabel) {
        float x=self.nwsjLabel.TKRight;
        float y=self.bkwzLabel.TKTop;
        float width=self.bkwzLabel.TKWidth;
        float height=self.bkwzLabel.TKHeight;
        _gxhsLabel=[[UILabel alloc] initWithFrame:CGRectMake(x, y, width, height)];
        _gxhsLabel.textAlignment=NSTextAlignmentCenter;
        _gxhsLabel.text=@"光线合适";
        _gxhsLabel.textColor=[TKUIHelper colorWithHexString:@"#666666"];
        _gxhsLabel.font=[UIFont fontWithName:@"PingFang SC" size:14.0f];
    }
    return _gxhsLabel;
}

/**
 <AUTHOR> 2019年09月24日19:16:45
 @初始化懒加载describeLabel
 @return describeLabel
 */
-(UILabel *)describeLabel{
    if (!_describeLabel) {
        float x=24;
        float height=40;
        float width=self.tipBgView.TKWidth-2*x;
        float y=self.bkwzLabel.TKBottom+16;
        _describeLabel=[[UILabel alloc] initWithFrame:CGRectMake(x, y, width, height)];
        _describeLabel.numberOfLines=0;
        _describeLabel.textColor = [TKUIHelper colorWithHexString:@"#666666"];
        _describeLabel.font=[UIFont fontWithName:@"PingFang SC" size:13];
        NSString *tipStr= @"如果长时间扫描未成功，可选择手动拍照模 式。(把身份证放在深色背景上更容易识别)";
        NSRange tipRange=[tipStr rangeOfString:@"(把身份证放在深色背景上更容易识别)"];
        if (self.requestParams[@"isTake"] && [self.requestParams[@"isTake"] integerValue] == 0) {
            //无拍照场景时候
           tipStr= @"如果长时间扫描未成功，请将身份证放在深色背景上重新识别";
           tipRange=[tipStr rangeOfString:@"深色背景"];
        }
        
        //提示用富文本
        NSMutableAttributedString *tipAttribut=[[NSMutableAttributedString alloc] initWithString:tipStr];
        
        // 2.添加属性
        [tipAttribut addAttribute:NSForegroundColorAttributeName value:[UIColor orangeColor] range:tipRange];
        [tipAttribut addAttribute:NSFontAttributeName value:[UIFont systemFontOfSize:13.0f] range:NSMakeRange(0, tipStr.length)];

        _describeLabel.attributedText=tipAttribut;
        
        _describeLabel.textAlignment=NSTextAlignmentLeft;
    }
    return _describeLabel;
}

/**
 <AUTHOR> 2019年09月24日19:37:51
 @初始化懒加载takeBtn
 @return continueBtn
 */
-(UIButton *)takeBtn{
    if (!_takeBtn) {

        float width=120;
        float height=40;
        float x=24;
        float y=self.tipBgView.TKHeight-height-24;
        _takeBtn=[[UIButton alloc] initWithFrame:CGRectMake(x, y, width, height)];



        [_takeBtn setTitleColor:[TKUIHelper colorWithHexString:@"#222222"] forState:UIControlStateNormal];
        _takeBtn.layer.borderColor=[TKUIHelper colorWithHexString:@"#D8DCE0"].CGColor;
        _takeBtn.layer.borderWidth=1.0f;
        [_takeBtn setTitle:@"手动拍照" forState:UIControlStateNormal];
        _takeBtn.titleLabel.font=[UIFont fontWithName:@"PingFang SC" size:14];

        _takeBtn.layer.cornerRadius=height/2.0f;
        _takeBtn.tag=105;
        [_takeBtn addTarget:self action:@selector(btnAction:) forControlEvents:UIControlEventTouchUpInside];
    }
    return _takeBtn;
}

/**
 <AUTHOR> 2019年09月24日19:37:43
 @初始化懒加载continueScanBtn
 @return continueScanBtn
 */
-(UIButton *)continueScanBtn{
    if (!_continueScanBtn) {


        float width=self.takeBtn.TKWidth;
        float height=self.takeBtn.TKHeight;
        float x=self.tipBgView.TKWidth-width-24;
        float y=self.takeBtn.TKTop;
        _continueScanBtn=[[UIButton alloc] initWithFrame:CGRectMake(x, y, width, height)];
        
        if (self.requestParams[@"mainColor"]) {
            [_continueScanBtn setBackgroundColor:[TKUIHelper colorWithHexString:self.requestParams[@"mainColor"]]];
        }else{
            [_continueScanBtn setBackgroundColor:[TKUIHelper colorWithHexString:@"#EE393E"]];

        }

        [_continueScanBtn setTitle:@"再扫一次" forState:UIControlStateNormal];

        _continueScanBtn.titleLabel.font=[UIFont fontWithName:@"PingFang SC" size:14];

        _continueScanBtn.layer.cornerRadius=height/2.0f;
        _continueScanBtn.tag=106;
        [_continueScanBtn addTarget:self action:@selector(btnAction:) forControlEvents:UIControlEventTouchUpInside];
    }
    return _continueScanBtn;
}


@end
