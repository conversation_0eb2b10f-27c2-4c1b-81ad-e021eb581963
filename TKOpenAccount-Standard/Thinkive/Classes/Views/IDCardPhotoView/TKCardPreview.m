//
//  TKCardPreview.m
//  TKOpenAccount-Standard
//
//  Created by 夏博文 on 2023/1/13.
//  Copyright © 2023 thinkive. All rights reserved.
//

#import "TKCardPreview.h"
@interface TKCardPreview(){
    CGFloat viewWidth,viewHeight;
}
@property (nonatomic, strong) NSMutableDictionary *requestParams;//h5传来的请求参数
@property (nonatomic, strong) NSString *mainColorString;//按钮图片主色调颜色值，h5没有传默认：#2F85FF（用于图片和按钮底色调整）
@property (nonatomic, strong) UIButton *cancelBtn;//重拍重选按钮
@property (nonatomic, strong) UIButton *submitBtn;//提交按钮
@property (nonatomic, assign) BOOL isPhotoAlbum;//是否相册图
@end

@implementation TKCardPreview

/**
 *<AUTHOR> 2023年01月13日15:13:05
 *@初始化证件预览图
 *@param frame frame
 *@param requestParams 初始化页面用到的参数
 */
-(instancetype)initWithFrame:(CGRect)frame requestParams:(NSMutableDictionary *)requestParams{
    self=[super initWithFrame:frame];
    if (self) {
        [self setBackgroundColor:[TKUIHelper colorWithHexString:@"#0D0D0D"]];
        self.requestParams=requestParams;
        if ([TKStringHelper isEmpty:self.requestParams[@"mainColor"]]) {
            self.mainColorString=@"#EE393E";
        }else{
            self.mainColorString=self.requestParams[@"mainColor"];
        }
        viewWidth = self.TKWidth;
        viewHeight = self.TKHeight;
        if (viewWidth<viewHeight) {
            viewWidth=self.TKHeight;
            viewHeight=self.TKWidth;
        }
        [self viewInit];
    }
    return self;
}


/**
 <AUTHOR> 2023年01月13日15:20:26
 @初始化证件预览图
 */
-(void)viewInit{

}

//#pragma mark -初始化证件预览界面,无示例图
//- (void)previewCardView:(UIImage*)image isPhotoAlbum:(BOOL)isPhotoAlbum{
//    self.isPhotoAlbum=isPhotoAlbum;
//    float leftWidth=20;
//    float centerHeight=20;
//    if (ISIPHONEX) {
//        leftWidth=60;
//        centerHeight=49;
//    }
//    //预览图
//    UIImageView *cardIV;
//    float cardIVWidth=viewWidth-leftWidth*2;
//    float cardIVHeight=viewHeight-44-13*2-7*2-25;
//    cardIV =[[UIImageView alloc] init];
//    cardIV.frame=CGRectMake(leftWidth, 40, cardIVWidth, cardIVHeight);
//    cardIV.contentMode = UIViewContentModeScaleAspectFit;
//    [cardIV setImage:image];
//    [self addSubview:cardIV];
//    cardIV.tag=6001310;
//    //头部提示语
//    float cardLabelWdith=300;
//    float cardLabelHeight=25;
//    
//    UILabel *cardLabel = [[UILabel alloc] init];
//    cardLabel.frame = CGRectMake(((cardIV.TKWidth-cardLabelWdith)/2+cardIV.TKLeft), cardIV.TKTop-8-cardLabelHeight, cardLabelWdith, cardLabelHeight);
//    NSString *tipStr= @"请确保证件照片 文字清晰、边角完整";
//
//    NSRange tipRange=[tipStr rangeOfString:@"文字清晰、边角完整"];
//    //顶部提示用富文本
//    NSMutableAttributedString *tipAttribut=[[NSMutableAttributedString alloc] initWithString:tipStr];
//    // 2.添加属性
//    [tipAttribut addAttribute:NSFontAttributeName value:[UIFont fontWithName:@"PingFang SC" size:18] range:NSMakeRange(0, tipStr.length)];
//    [tipAttribut addAttribute:NSForegroundColorAttributeName value:[TKUIHelper colorWithHexString:@"#ffffff"] range:NSMakeRange(0, tipStr.length)];
//    [tipAttribut addAttribute:NSForegroundColorAttributeName value:[UIColor orangeColor] range:tipRange];
//    cardLabel.attributedText=tipAttribut;
//    [self addSubview:cardLabel];
//    
//    
//    
//    
//    if(_cancelBtn){
//        [_cancelBtn removeFromSuperview];
//        _cancelBtn=nil;
//    }
//    [self addSubview:self.cancelBtn];
//    
//    
//    if(_submitBtn){
//        [_submitBtn removeFromSuperview];
//        _submitBtn=nil;
//    }
//    [self addSubview:self.submitBtn];
//}


#pragma mark -初始化证件预览界面带有示例模块
- (void)previewCardNeedSampleView:(UIImage*)image isPhotoAlbum:(BOOL)isPhotoAlbum isFrontCard:(BOOL)isFrontCard{
    self.isPhotoAlbum=isPhotoAlbum;
    float cardIVX=20;
    float cardIVY=20;
    if (ISIPHONEX) {
        cardIVX=52;
        cardIVY=40;
    }
    
    //预览图
    UIImageView *cardIV;
    float cardIVWidth=viewWidth-cardIVX*2-225;
    float cardIVHeight=viewHeight-2*cardIVY;
    cardIV =[[UIImageView alloc] init];
    cardIV.frame=CGRectMake(cardIVX, cardIVY, cardIVWidth, cardIVHeight);
    cardIV.contentMode = UIViewContentModeScaleAspectFit;
    [cardIV setImage:image];
    [self addSubview:cardIV];
    cardIV.tag=6001310;
    
//    //头部提示语
//    float cardLabelWdith=108;
//    float cardLabelHeight=25;
//    UILabel *cardLabel = [[UILabel alloc] init];
//    cardLabel.frame = CGRectMake(((cardIV.TKWidth-cardLabelWdith)/2+cardIV.TKLeft), cardIV.TKTop-8-cardLabelHeight, cardLabelWdith, cardLabelHeight);
//    cardLabel.text = @"请您核对照片";
//    cardLabel.font = [UIFont fontWithName:@"PingFang SC" size:18];
//    cardLabel.textColor = [UIColor whiteColor];
//    [self addSubview:cardLabel];
    
    //身份证示例图
    float tipImgWidth=150;
    float tipImgHeight=tipImgWidth*0.631915;
    float tipImgY=40;
    float tipImgX=cardIV.TKRight+30;
    UIImageView *tipImgView=[[UIImageView alloc] initWithFrame:CGRectMake(tipImgX, tipImgY, tipImgWidth, tipImgHeight)];
    if (isFrontCard) {
        //是否复印件示例图
        if (self.requestParams[@"isCopies"] && ![self.requestParams[@"isCopies"] integerValue]) {//是否身份证复印件
            [tipImgView setImage:[UIImage imageNamed:[NSString stringWithFormat:@"%@.bundle/Resources/TKOpenPlugin60013/front_tip5.1UI_icon_down.png", TK_OPEN_RESOURCE_NAME]]];
        }else{
            [tipImgView setImage:[UIImage imageNamed:[NSString stringWithFormat:@"%@.bundle/Resources/TKOpenPlugin60013/front_tip5.1UI_icon_down.png", TK_OPEN_RESOURCE_NAME]]];
        }
        
        //是否h5传了身份证正面示例图，传了以h5为准
        if([TKStringHelper isNotEmpty:self.requestParams[@"previewSamplePicFront"]]){
            [tipImgView setImage:[UIImage imageWithData:[TKBase64Helper dataWithDecodeBase64String:self.requestParams[@"previewSamplePicFront"]]]];
        }

    }else{
        //是否复印件示例图
        if (self.requestParams[@"isCopies"] && ![self.requestParams[@"isCopies"] integerValue]) {//是否身份证复印件
            [tipImgView setImage:[UIImage imageNamed:[NSString stringWithFormat:@"%@.bundle/Resources/TKOpenPlugin60013/reverse_tip5.1UI_icon_down.png", TK_OPEN_RESOURCE_NAME]]];
        }else{
            [tipImgView setImage:[UIImage imageNamed:[NSString stringWithFormat:@"%@.bundle/Resources/TKOpenPlugin60013/reverse_tip5.1UI_icon_down.png", TK_OPEN_RESOURCE_NAME]]];
        }

        //是否h5传了身份证反面示例图，传了以h5为准
        if([TKStringHelper isNotEmpty:self.requestParams[@"previewSamplePicBack"]]){
            [tipImgView setImage:[UIImage imageWithData:[TKBase64Helper dataWithDecodeBase64String:self.requestParams[@"previewSamplePicBack"]]]];
        }
    }
    tipImgView.contentMode = UIViewContentModeScaleToFill;
    [self addSubview:tipImgView];
    
    //检查照片的提示语
    UILabel *checkTipLabel = [[UILabel alloc] init];
    checkTipLabel.numberOfLines=0;
    checkTipLabel.backgroundColor=[UIColor clearColor];
    float checkTipLabelWidth=210;
    float checkTipLabelHeight=66;
    checkTipLabel.frame = CGRectMake(tipImgView.TKLeft,tipImgView.TKBottom+5.0f, checkTipLabelWidth, checkTipLabelHeight);
    NSString *tipStr= @"请核对示例,确保身份证照片\n文字清晰、边角完整、光线均匀。";
    NSRange tipRange=[tipStr rangeOfString:@"文字清晰、边角完整、光线均匀。"];
    //顶部提示用富文本
    NSMutableAttributedString *tipAttribut=[[NSMutableAttributedString alloc] initWithString:tipStr];
    // 2.添加属性
    [tipAttribut addAttribute:NSFontAttributeName value:[UIFont fontWithName:@"PingFang SC" size:16] range:NSMakeRange(0, tipStr.length)];
    [tipAttribut addAttribute:NSForegroundColorAttributeName value:[TKUIHelper colorWithHexString:@"#BBBBBB"] range:NSMakeRange(0, tipStr.length)];
    [tipAttribut addAttribute:NSForegroundColorAttributeName value:[UIColor orangeColor] range:tipRange];
    checkTipLabel.attributedText=tipAttribut;
    checkTipLabel.textAlignment = NSTextAlignmentLeft;
    CGSize lableSize = [checkTipLabel sizeThatFits:CGSizeMake(210, MAXFLOAT)];
    [checkTipLabel setTKHeight:lableSize.height];
    [self addSubview:checkTipLabel];

    if(_cancelBtn){
        [_cancelBtn removeFromSuperview];
        _cancelBtn=nil;
    }
    [self addSubview:self.cancelBtn];
    [self.cancelBtn setTKLeft:tipImgX];
    
    if(_submitBtn){
        [_submitBtn removeFromSuperview];
        _submitBtn=nil;
    }
    [self addSubview:self.submitBtn];
}

//预览界面图片变动
-(void)changePreviewImg:(UIImage*)image{
    UIImageView *cardImgView=(UIImageView *)[self viewWithTag:6001310];
    [cardImgView setImage:image];
}

#pragma mark 按钮事件
-(void)retryAction:(id)sender{
    if (self.delegate&&[self.delegate respondsToSelector:@selector(previewRetryAction:)]) {
        [self.delegate previewRetryAction:sender];
    }
}

-(void)submitAction:(id)sender{
    if (self.delegate&&[self.delegate respondsToSelector:@selector(previewSubmitAction:)]) {
        [self.delegate previewSubmitAction:sender];
    }
}

#pragma mark lazyloading


/**
 <AUTHOR> 2023年01月16日11:15:29
 @初始化懒加载重拍重选按钮
 @return 重拍重选按钮
 */
-(UIButton *)cancelBtn{
    if(!_cancelBtn){
        float btnWidht=200;
        float btnHeight=40;
        float btnY=viewHeight-btnHeight-40;
        float btnX=0;
        
        //重拍按钮
        _cancelBtn= [UIButton buttonWithType:UIButtonTypeCustom];
        if (self.isPhotoAlbum) {
            [_cancelBtn setTitle:@"重选" forState:UIControlStateNormal];
        }else{
            [_cancelBtn setTitle:@"重拍" forState:UIControlStateNormal];
        }
        if ([TKOpenViewStyleHelper shareInstance].isElder) {
            _cancelBtn.titleLabel.font = [UIFont fontWithName:@"PingFang SC" size:22];
        }else{
            _cancelBtn.titleLabel.font = [UIFont fontWithName:@"PingFang SC" size:16];
        }
        [_cancelBtn setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];
        [_cancelBtn setBackgroundColor:[TKUIHelper colorWithHexString:@"#666666" alpha:0.7f]];
        [_cancelBtn.layer setCornerRadius:btnHeight/2.0f];
        _cancelBtn.frame =CGRectMake(btnX, btnY, btnWidht,btnHeight);
        [_cancelBtn setContentHorizontalAlignment:UIControlContentHorizontalAlignmentCenter];
        [_cancelBtn setContentVerticalAlignment:UIControlContentVerticalAlignmentCenter];
        [_cancelBtn addTarget:self action:@selector(retryAction:) forControlEvents:UIControlEventTouchUpInside];
    }
    return _cancelBtn;
}
    
/**
 <AUTHOR> 2023年01月16日11:16:26
 @初始化懒加载提交按钮
 @return 提交按钮
 */
-(UIButton *)submitBtn{
    if(!_submitBtn){
        //提交按钮
        _submitBtn = [UIButton buttonWithType:UIButtonTypeCustom];
        [_submitBtn setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];
        [_submitBtn setTitle:@"提交" forState:UIControlStateNormal];
        [_submitBtn setBackgroundColor:[TKUIHelper colorWithHexString:self.mainColorString]];
        if ([TKOpenViewStyleHelper shareInstance].isElder) {
            _submitBtn.titleLabel.font = [UIFont fontWithName:@"PingFang SC" size:22];
        }else{
            _submitBtn.titleLabel.font = [UIFont fontWithName:@"PingFang SC" size:16];
        }
        [_submitBtn.layer setCornerRadius:self.cancelBtn.TKHeight/2.0f];
        _submitBtn.frame =CGRectMake(self.cancelBtn.TKLeft, self.cancelBtn.TKTop-20-self.cancelBtn.TKHeight, self.cancelBtn.TKWidth,self.cancelBtn.TKHeight);
        [_submitBtn setContentHorizontalAlignment:UIControlContentHorizontalAlignmentCenter];
        [_submitBtn setContentVerticalAlignment:UIControlContentVerticalAlignmentCenter];
        [_submitBtn addTarget:self action:@selector(submitAction:) forControlEvents:UIControlEventTouchUpInside];
    }
    return _submitBtn;
}
    
@end
