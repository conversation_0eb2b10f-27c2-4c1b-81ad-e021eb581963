//
//  TKBankCardPhotoView.h
//  TKOpenAccount-Standard
//  银行卡拍照扫描界面，竖屏布局
//  Created by 夏博文 on 2025/7/28.
//  Copyright © 2025 thinkive. All rights reserved.
//

typedef enum : NSUInteger {
    TKBankCardPhotoViewStatusOnlyTake = 0,//只有拍照
    TKBankCardPhotoViewStatusTake,//扫描界面的拍照状态
    TKBankCardPhotoViewStatusRecognize,//扫描界面的扫描识别状态
} TKBankCardPhotoViewStatus;


@protocol TKBankCardPhotoViewDelegate <NSObject>
//打开相册
- (void)bankPhotoViewAlbumAction:(id)sender;

//拍摄照片
- (void)bankPhotoViewTakeAction:(id)sender;

//点击返回
- (void)bankPhotoViewBackAction:(id)sender;

//点击切换到扫描
- (void)bankPhotoViewSwitchRecognizeAction:(id)sender;

//点击切换到拍照
- (void)bankPhotoViewSwitchTakeAction:(id)sender;
@end

@interface TKBankCardPhotoView : UIView
@property (nonatomic, weak) id<TKBankCardPhotoViewDelegate>delegate;
@property(nonatomic,assign) CGRect  idRect;//照片裁剪指定卡片区域,手机屏幕区域
@property(nonatomic,assign) BOOL isUploadPositiveCard;//是否证件正面
@property (nonatomic, assign) TKBankCardPhotoViewStatus currentBankCardPhotoViewStatus;


/**
 *<AUTHOR> 2025年07月28日11:04:13
 *@初始化相册预览页面
 *@param frame frame
 *@param requestParams 初始化页面用到的参数
 */
-(instancetype)initWithFrame:(CGRect)frame requestParams:(NSMutableDictionary *)requestParams;

/**
 * <AUTHOR> 2025年07月29日
 * @brief 启动扫描线动画
 * @discussion 统一的扫描线动画控制接口，替代控制器直接操作内部视图
 */
- (void)startScanLineAnimation;

/**
 * <AUTHOR> 2025年07月29日
 * @brief 停止扫描线动画
 * @discussion 停止并移除扫描线动画
 */
- (void)stopScanLineAnimation;

/**
 <AUTHOR> 2025年07月11日09:08:32
 @质检提示
 @param tipString提示内容
 */
-(void)qualityTips:(NSString *)tipString;
@end

