//
//  TKReadingView.h
//  TKOpenAccount-Standard
//
//  Created by <PERSON> on 2023/7/28.
//  Copyright © 2023 thinkive. All rights reserved.
//

#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

typedef enum : NSInteger {
    TKReadingTypeReadAloud,  // 朗读
    TKReadingTypeRead,        // 阅读
} TKReadingType;

@class TKReadingView;
@protocol TKReadingViewDelegate <NSObject>

/**
 点击关闭按钮
 */
- (void)closeBtnDidClicked:(TKReadingView *)readingView;

/**
 点击确认按钮
 */
- (void)confirmBtnDidClicked:(TKReadingView *)readingView;


/**
 双向视频回发坐席的消息内容
 */
- (void)msgToServerClicked:(NSString *)msg;
@end


@interface TKReadingView : UIView

@property (nonatomic, weak) id<TKReadingViewDelegate> delegate;
@property (nonatomic, readwrite, assign) TKReadingType type;
@property (nonatomic, readwrite, strong) NSString *readPromptBtnTitle;
@property (nonatomic, readwrite, strong) NSString *readConfirmBtnTitle;
@property (nonatomic, readwrite, strong) NSString *readTitle;

@property (nonatomic, assign) float wordSpeedDuration; // 文字滚动速度
@property (nonatomic, assign) BOOL isReadAll;//是否完整阅读，即是否滚动到底部

// 是否自动滚动
@property (nonatomic, assign) int autoScroll;

/**
*  <AUTHOR> 2023年12月07日10:01:20
*  @brief  初始化
*  @param frame
*  @param param
*  @param oneWordSpeed 多少秒滚动一个字
*  @return
*/
-(instancetype)initWithFrame:(CGRect)frame param:(NSMutableDictionary *)param;

/// 展示阅读文案
/// - Parameters:
///   - content: 阅读内容
///   - countdownTime: 倒计时时长
- (void)showWithContent:(NSString *)content countdownTime:(NSInteger)countdownTime type:(TKReadingType)type readTitle:(NSString *)readTitle readConfirmBtnTitle:(NSString *)readConfirmBtnTitle oneWordSpeed:(NSString *)oneWordSpeed;

@end

NS_ASSUME_NONNULL_END
