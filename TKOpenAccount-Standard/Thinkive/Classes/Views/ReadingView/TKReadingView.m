//
//  TKReadingView.m
//  TKOpenAccount-Standard
//
//  Created by <PERSON> on 2023/7/28.
//  Copyright © 2023 thinkive. All rights reserved.
//

#import "TKReadingView.h"

@interface TKReadingView () <UITextViewDelegate>

@property (nonatomic, strong) UIView *backgroundView;
@property (nonatomic, strong) UIView *contentView;
@property (nonatomic, strong) UILabel *titleLabel;
@property (nonatomic, strong) UITextView *textView;
//@property (nonatomic, strong) UIProgressView *progressView;
@property (nonatomic, strong) UIButton *closeButton;
@property (nonatomic, strong) UIButton *confirmButton;
@property (nonatomic, strong) NSTimer *countdownTimer;
@property (nonatomic, assign) NSInteger countdownTime;
@property (nonatomic, assign) BOOL scrolledToBottom;
@property (nonatomic, assign) BOOL countdownEnd;
@property (nonatomic, assign) int changeReadTextViewWords; // 变色的字数
@property (nonatomic, readwrite, strong) NSTimer *changeReadTextViewOffSetTimer; // testView滚动定时器

@property (nonatomic, readwrite, strong) TKLayerView *layerView;
@property (nonatomic, strong) NSMutableDictionary *requestParam;
@property (nonatomic, strong) NSString *mainColorString;//按钮图片主色调颜色值，h5没有传默认：#2F85FF（用于图片和按钮底色调整）
@property (nonatomic, assign) int instructionNo;//坐席发过来的指令编号，没有的话就不是双向视频需要向坐席回发消息


@end

@implementation TKReadingView

-(instancetype)initWithFrame:(CGRect)frame param:(NSMutableDictionary *)param{
    self=[super initWithFrame:frame];
    if (self) {
        self.backgroundColor=[UIColor clearColor];
        self.requestParam=param;
        if ([TKStringHelper isEmpty:param[@"mainColor"]]) {
            self.mainColorString=@"#2F85FF";
        }else{
            self.mainColorString=param[@"mainColor"];
        }
        self.instructionNo=[self.requestParam getIntWithKey:@"instructionNo"];
        self.isReadAll=NO;
        if([self.requestParam getIntWithKey:@"readAll"]==1){
            self.isReadAll=YES;
        }
        [self setupViews];
    }
    return self;
}

- (instancetype)initWithFrame:(CGRect)frame {
    if (self = [super initWithFrame:frame]) {
        [self setupViews];
    }
    
    return self;
}

- (instancetype)init {
    self = [super init];
    if (self) {
        [self setupViews];
    }
    return self;
}

- (void)setupViews {
    // Background view
    float x=20;
    float width=self.TKWidth-2*x;
    float height=228;
    float y=self.TKHeight-IPHONEX_BUTTOM_HEIGHT-height-10;
    self.backgroundView = [[UIView alloc] initWithFrame:CGRectMake(x, y, width, height)];
    self.backgroundView.backgroundColor = [TKUIHelper colorWithHexString:@"#ffffff"];
    self.backgroundView.hidden = YES;
    [self addSubview:self.backgroundView];

    // Content view
    self.contentView = [[UIView alloc] initWithFrame:self.backgroundView.frame];
    self.contentView.backgroundColor = [TKUIHelper colorWithHexString:@"#ffffff"];
    self.contentView.layer.cornerRadius=10.0f;
    [self addSubview:self.contentView];

    
    // title label
    self.titleLabel = [[UILabel alloc] initWithFrame:CGRectMake(0, 12, width, 24)];
    self.titleLabel.font = [UIFont fontWithName:@"PingFang SC" size:18];
    self.titleLabel.textColor = [TKUIHelper colorWithHexString:@"#222222"];
    self.titleLabel.textAlignment = NSTextAlignmentCenter;
    [self.contentView addSubview:self.titleLabel];

    // Progress view
//    self.progressView = [[UIProgressView alloc] initWithFrame:CGRectMake(20, 80, CGRectGetWidth(self.contentView.bounds) - 40, 10)];
//    self.progressView.progressTintColor = [UIColor whiteColor];
//    [self.contentView addSubview:self.progressView];

    


    // Confirm button
    self.confirmButton = [UIButton buttonWithType:UIButtonTypeCustom];
    self.confirmButton.frame = CGRectMake(16, self.contentView.TKHeight - 10 - 44, CGRectGetWidth(self.contentView.bounds) - 40, 44);
    [self.confirmButton addTarget:self action:@selector(confirmButtonTapped) forControlEvents:UIControlEventTouchUpInside];
    [self.contentView addSubview:self.confirmButton];
    [self.confirmButton setTitle:[NSString stringWithFormat:@"%@", self.readPromptBtnTitle] forState:UIControlStateNormal];
    
    // Close button
    if([TKStringHelper isNotEmpty:self.requestParam[@"cancel"]]){
        float btnWidth=(self.contentView.TKWidth-40)/2.0f;
        self.closeButton = [UIButton buttonWithType:UIButtonTypeCustom];
        self.closeButton.frame = CGRectMake(10, self.confirmButton.TKTop, btnWidth, 44);
        [self.closeButton setTitle:self.requestParam[@"cancel"] forState:UIControlStateNormal];
        [self.closeButton setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];
        
        [self.closeButton setTitleColor:[TKUIHelper colorWithHexString:@"#222222"] forState:UIControlStateNormal];
        [self.closeButton setBackgroundColor:[TKUIHelper colorWithHexString:@"#ffffff"]];
        self.closeButton.layer.borderWidth=1.0f;
        self.closeButton.layer.borderColor=[TKUIHelper colorWithHexString:@"#D8DCE0"].CGColor;
        [self.closeButton addTarget:self action:@selector(closeButtonTapped) forControlEvents:UIControlEventTouchUpInside];
        self.closeButton.layer.cornerRadius = self.closeButton.TKHeight / 2.0f;
        [self.contentView addSubview:self.closeButton];
        
        [self.confirmButton setTKWidth:btnWidth];
        [self.confirmButton setTKLeft:self.closeButton.TKRight+20];
    }

    
    
    CGFloat testViewTop = 48;
    self.textView = [[UITextView alloc] initWithFrame:CGRectMake(16, testViewTop, CGRectGetWidth(self.contentView.bounds) - 16 * 2, self.confirmButton.TKTop - 10 - testViewTop)];
    self.textView.textColor = [TKUIHelper colorWithHexString:@"#222222"];
    self.textView.backgroundColor = [UIColor clearColor];
    self.textView.font = [UIFont fontWithName:@"PingFang SC" size:15];
    self.textView.showsVerticalScrollIndicator = YES;
    self.textView.indicatorStyle = UIScrollViewIndicatorStyleDefault;
    self.textView.delegate = self;
    self.textView.editable = NO;
    [self.contentView addSubview:self.textView];
    
    // 检测是否到底，到底则按钮可点击
    [self enableBtn:self.confirmButton isEnable:[self checkScrollViewScrollToBottom:self.textView] || self.countdownEnd];
    

}

- (void)showWithContent:(NSString *)content countdownTime:(NSInteger)countdownTime type:(TKReadingType)type readTitle:(NSString *)readTitle readConfirmBtnTitle:(NSString *)readConfirmBtnTitle  oneWordSpeed:(NSString *)oneWordSpeed {
    
    self.type = type;
    
    if ([TKStringHelper isNotEmpty:readTitle]) self.readTitle = readTitle;
    self.titleLabel.text = self.readTitle;
    
//    self.textView.text = content;
    content = [TKCommonUtil switchLabelToSpan:content];
    // 生成富文本
    NSMutableAttributedString *attStr = [self convertTextToHtmlString:content textColor:@"#222222"];
    self.textView.attributedText = attStr;
    

    //高度最高半屏，超出支持滚动
    float changeHeight;
    CGSize lableSize = [self.textView sizeThatFits:CGSizeMake(self.textView.TKWidth, MAXFLOAT)];
    if (lableSize.height<self.textView.TKHeight) {
        changeHeight=self.textView.TKHeight-lableSize.height;
        [self.textView setTKHeight:self.textView.TKHeight-changeHeight];
        [self.contentView setTKHeight:self.contentView.TKHeight-changeHeight];
    }else{
        changeHeight=lableSize.height-self.textView.TKHeight;
        if ((self.contentView.TKHeight+changeHeight)>self.TKHeight/2.0f) {
            changeHeight=self.TKHeight/2.0f-self.contentView.TKHeight;
        }
        [self.textView setTKHeight:self.textView.TKHeight+changeHeight];
        [self.contentView setTKHeight:self.contentView.TKHeight+changeHeight];
    }
    [self.confirmButton setTKTop:self.contentView.TKHeight - 10 - 44];
    [self.closeButton setTKTop:self.contentView.TKHeight - 10 - 44];
    self.TKHeight = self.contentView.TKHeight;
    self.TKTop = UISCREEN_HEIGHT - self.TKHeight-IPHONEX_BUTTOM_HEIGHT-10;
    [self.contentView setTKTop:0];
    
    // 先暂停滚动
    [self stopTextViewScroll];
    
    self.countdownTime = countdownTime;
    
    if ([TKStringHelper isNotEmpty:readConfirmBtnTitle]) self.readConfirmBtnTitle = readConfirmBtnTitle;
    [self.confirmButton setTitle:[NSString stringWithFormat:@"%@(%ds)", self.readConfirmBtnTitle, countdownTime] forState:UIControlStateNormal];
    
    [self enableBtn:self.confirmButton isEnable:NO];

    // Reset progress view and scrolled flag
//    self.progressView.progress = 0;
    self.scrolledToBottom = NO;
    self.countdownEnd = NO;
    
    // 开始滚动
    float durationTime = 0.19f;  // 0.19s/1个字
    if (countdownTime > 0) {
        
        durationTime = countdownTime * 1.0f / attStr.length;
    } else if ([TKStringHelper isNotEmpty:oneWordSpeed]) {
    
        durationTime = oneWordSpeed.floatValue;    // 优先使用外层配置的
    }
    self.wordSpeedDuration = durationTime;

    // Show the view
//    self.backgroundView.hidden = NO;
    [UIView animateWithDuration:0.3 animations:^{
        self.contentView.frame =  self.contentView.frame;
    } completion:^(BOOL finished) {
        if(self.autoScroll){
            // 开始滚动
            [self changeReadTextViewOffSet];
        }

    }];
    
    // Start countdown
    [self startCountdownTimer];
}



- (NSMutableAttributedString *)convertTextToHtmlString:(NSString *)text textColor:(NSString *)colorString
{
    NSString *tempDivString = [NSString stringWithFormat:@"<span style=\"font-size:%ipx;color:%@;font-family:PingFang SC\">%@</span>", (int)self.textView.font.pointSize, colorString, text];
   NSData *tempData = [tempDivString dataUsingEncoding:NSUnicodeStringEncoding];
   NSDictionary *options = @{NSDocumentTypeDocumentAttribute: NSHTMLTextDocumentType,
                             NSCharacterEncodingDocumentAttribute:[NSNumber numberWithInt:NSUTF8StringEncoding]
   };
   NSMutableAttributedString *tempattStr = [[NSMutableAttributedString alloc]initWithData:tempData options:options documentAttributes:nil error:nil];
    
    return tempattStr;
}

- (void)stopTextViewScroll
{
    self.changeReadTextViewWords = 0;
    [self.changeReadTextViewOffSetTimer invalidate];
    self.changeReadTextViewOffSetTimer = nil;
}

/**
@Auther Vie 2021年07月29日13:56:44
改变阅读文本空间显示
*/
-(void)changeReadTextViewOffSet{
   
   // 防止页面不显示的时候，定时器还在反复调用该功能
   if (!self.window) {
       
       [self stopTextViewScroll];
       
//        TKLogDebug(@"changeReadTextViewOffSet timer invalidate");
       return;
   }
   
   //计算是否要滚动换行
   //多久渐变一个字   
   NSTimeInterval repeactTime = 1.0f;
    if (self.wordSpeedDuration <= 0.05) repeactTime = 0.1;    // 滚动速度变大，刷新频率也更新
   int wordPerSecond = ceilf(repeactTime / self.wordSpeedDuration);

   if (self.changeReadTextViewWords < self.textView.attributedText.string.length) {
       // -1是防止到最后一个字再滚动的话会有点显示问题。会一整行滚动一次，又展示
//        TKLogDebug(@"思迪文案滚动动画：repeactTime = %.2f, wordPerSecond = %i, self.changeReadTextViewWords = %i, self.bottomShowLabel.attributedText.string.length = %i", repeactTime, wordPerSecond, self.changeReadTextViewWords, self.textView.attributedText.string.length);
       
       NSRange rang = NSMakeRange(self.changeReadTextViewWords, wordPerSecond);
       [self.textView scrollRangeToVisible:rang];
       
       //计算是否要滚动换行
//        CGSize labelSize=[self.bottomShowLabel sizeThatFits:CGSizeMake(self.bottomShowLabel.TKWidth, MAXFLOAT)];
//        float diffY=labelSize.height-self.bottomShowLabel.TKHeight;
//        if (diffY>0) {
//            NSRange rang=NSMakeRange(self.changeReadTextViewWords, wordPerSecond);
//            [self.bottomShowLabel scrollRangeToVisible:rang];
//        }
       
       self.changeReadTextViewWords = self.changeReadTextViewWords + wordPerSecond;

//        TKLogDebug(@"思迪文案滚动动画：创建定时器");
       if (self.changeReadTextViewOffSetTimer == nil) {
           
           self.changeReadTextViewOffSetTimer = [NSTimer timerWithTimeInterval:repeactTime target:self selector:@selector(changeReadTextViewOffSet) userInfo:nil repeats:YES];
           [[NSRunLoop mainRunLoop] addTimer:self.changeReadTextViewOffSetTimer forMode:NSRunLoopCommonModes];
       }
   } else {
       
//        TKLogDebug(@"思迪文案滚动动画：销毁定时器");
       [self stopTextViewScroll];
   }
}


- (void)closeButtonTapped {
    [self hide];
    
    if(self.instructionNo==1006){
        NSString *msg=[NSString stringWithFormat:@"RET:1006:%@",[TKDataHelper dictionaryToJson:@{@"action":@"cancel",@"taskId":self.requestParam[@"taskId"]}]];
        // 调用代理方法
        if ([self.delegate respondsToSelector:@selector(msgToServerClicked:)]) {
            [self.delegate msgToServerClicked:msg];
        }
    }else if(self.instructionNo==1007){
        NSString *msg=[NSString stringWithFormat:@"RET:1007:%@",[TKDataHelper dictionaryToJson:@{@"action":@"cancel",@"taskId":self.requestParam[@"taskId"]}]];
        // 调用代理方法
        if ([self.delegate respondsToSelector:@selector(msgToServerClicked:)]) {
            [self.delegate msgToServerClicked:msg];
        }
    }else{
        // 调用代理方法
        if ([self.delegate respondsToSelector:@selector(closeBtnDidClicked:)]) {
            [self.delegate closeBtnDidClicked:self];
        }
    }
    

}

- (void)confirmButtonTapped {
    if (![self checkScrollViewScrollToBottom:self.textView] && self.isReadAll) {
        [self.layerView showTip:[NSString stringWithFormat:@"请完整%@", self.type == TKReadingTypeRead ? @"阅读" : @"朗读"] position:TKLayerPosition_Center textColor:@"#ffffff" bgColor:@"#00000099"];
        return;
    }
    
    [self hide];
    if(self.instructionNo==1006){
        NSString *msg=[NSString stringWithFormat:@"RET:1006:%@",[TKDataHelper dictionaryToJson:@{@"action":@"confirm",@"taskId":self.requestParam[@"taskId"]}]];
        // 调用代理方法
        if ([self.delegate respondsToSelector:@selector(msgToServerClicked:)]) {
            [self.delegate msgToServerClicked:msg];
        }
    }else if(self.instructionNo==1007){
        NSString *msg=[NSString stringWithFormat:@"RET:1007:%@",[TKDataHelper dictionaryToJson:@{@"action":@"confirm",@"taskId":self.requestParam[@"taskId"]}]];
        // 调用代理方法
        if ([self.delegate respondsToSelector:@selector(msgToServerClicked:)]) {
            [self.delegate msgToServerClicked:msg];
        }
    }else{
        // 调用代理方法
        if ([self.delegate respondsToSelector:@selector(confirmBtnDidClicked:)]) {
            [self.delegate confirmBtnDidClicked:self];
        }
    }

}

- (void)startCountdownTimer {
    [self stopCountdownTimer];
    
    self.countdownTimer = [NSTimer scheduledTimerWithTimeInterval:1.0 target:self selector:@selector(updateCountdown) userInfo:nil repeats:YES];
    [[NSRunLoop mainRunLoop] addTimer:self.countdownTimer forMode:NSRunLoopCommonModes];
}

- (void)updateCountdown {
    self.countdownTime--;
    if (self.countdownTime > 0) {

        [self.confirmButton setTitle:[NSString stringWithFormat:@"%@(%ds)", self.readConfirmBtnTitle, (int)self.countdownTime] forState:UIControlStateNormal];
        
    } else {
        [self.confirmButton setTitle:[NSString stringWithFormat:@"%@", self.readConfirmBtnTitle] forState:UIControlStateNormal];
        [self stopCountdownTimer];
        self.countdownEnd = YES;
        
//        if (self.type == TKReadingTypeRead) {
//            [self.layerView showTip:@"请完整阅读" position:TKLayerPosition_Center textColor:@"#ffffff" bgColor:@"#00000099"];
//        }
//        else {
//            [self.layerView showTip:@"请完整朗读" position:TKLayerPosition_Center textColor:@"#ffffff" bgColor:@"#00000099"];
//        }

        // 检测是否到底，到底则按钮可点击
        [self enableBtn:self.confirmButton isEnable:[self checkScrollViewScrollToBottom:self.textView]||self.countdownEnd];
        
//        self.confirmButton.enabled = YES;
//        self.confirmButton.backgroundColor = [UIColor redColor];
    }
}

- (void)stopCountdownTimer {
    [self.countdownTimer invalidate];
    self.countdownTimer = nil;

}

- (void)hide {
    self.backgroundView.hidden = YES;
    [UIView animateWithDuration:0.3 animations:^{
        self.contentView.frame = CGRectMake(20, CGRectGetHeight(self.bounds)+IPHONEX_BUTTOM_HEIGHT+10, self.contentView.TKWidth, self.contentView.TKHeight);
    }];
    [self stopCountdownTimer];
}

- (void)setButtonBackgroundColor:(UIButton *)button alpha:(CGFloat)alpha
{
//    if (button.window) {
        // 修改按钮渐变色
        [button setBackgroundColor:[TKUIHelper colorWithHexString:self.requestParam[@"mainColor"] alpha:alpha]];
        button.layer.cornerRadius = button.TKHeight / 2.0f;
//    }
}

/// 设置是否可以点击确认
/// @param isEnable 是否可以点击
- (void)enableBtn:(UIButton *)btn isEnable:(BOOL)isEnable {
    
    btn.enabled = isEnable;
    if (isEnable) {

        [self setButtonBackgroundColor:btn alpha:1.0f];
    } else {
        
        [self setButtonBackgroundColor:btn alpha:0.3f];
    }
}

#pragma mark - UIScrollViewDelegate
- (void)scrollViewWillBeginDragging:(UIScrollView *)scrollView {
    [self stopTextViewScroll];
}

- (void)scrollViewDidScroll:(UIScrollView *)scrollView {
    [self checkScrollViewScrollToBottom:scrollView];
}

- (BOOL)checkScrollViewScrollToBottom:(UIScrollView *)scrollView
{
    CGFloat contentOffsetY = scrollView.contentOffset.y;
    CGFloat contentHeight = scrollView.contentSize.height;
    CGFloat scrollViewHeight = CGRectGetHeight(scrollView.bounds);
    CGFloat bottomOffset = scrollView.contentInset.bottom;

    // Check if scrolled to the bottom
    if (contentOffsetY >= contentHeight - scrollViewHeight - bottomOffset - 25) {
        self.scrolledToBottom = YES;
        // Enable the confirm button when scrolled to the bottom
        [self enableBtn:self.confirmButton isEnable:self.scrolledToBottom && self.countdownEnd];

        
//        TKLogDebug(@"已滚动到底部");
    } else {
//        self.scrolledToBottom = NO;
        // Disable the confirm button when not scrolled to the bottom
//        self.confirmButton.enabled = NO;
//        TKLogDebug(@"未滚动到底部");
    }
    
    return self.scrolledToBottom;
}

#pragma mark - Setter && Getter
- (NSString *)readPromptBtnTitle
{
    if ([TKStringHelper isNotEmpty:_readPromptBtnTitle]) {
        return _readPromptBtnTitle;
    } else {
        return self.type == TKReadingTypeRead ? @"开始阅读" : @"开始朗读";
    }
}

- (NSString *)readConfirmBtnTitle
{
    if ([TKStringHelper isNotEmpty:_readConfirmBtnTitle]) {
        return _readConfirmBtnTitle;
    } else {
        return self.type == TKReadingTypeRead ? @"我已阅读" : @"我已朗读";
    }
}

- (NSString *)readTitle
{
    if ([TKStringHelper isNotEmpty:_readTitle]) {
        return _readTitle;
    } else {
        return self.type == TKReadingTypeRead ? @"请完整阅读" : @"请使用普通话朗读";
    }
}

- (TKLayerView *)layerView{
    if (!_layerView) {
        _layerView = [[TKLayerView alloc] initContentView:self withBtnTextColor:@"#ffffff" cancelBtnTextColor:nil withWidth:0 withFont:[UIFont fontWithName:@"PingFang SC" size:16]];
    }
    return _layerView;
}

@end

