//
//  TKFaceDectTipView.h
//  TKOpenAccount-Standard
//
//  Created by <PERSON> on 2022/12/28.
//  Copyright © 2022 thinkive. All rights reserved.
//

#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

@interface TKFaceDectTipView : UIView

/// 是否增强提示效果。控件四周亮起红框。默认NO
@property (nonatomic, readwrite, assign) BOOL isEnhance;

/// 展示质检提示
/// - Parameters:
///   - warningSting: 提示内容
///   - forceDisplay: 是否强制展示不管之前是否已经在展示
///   - displayTime: 强制展示时长
///   - maxWidth: 最大宽度
///   - y: 提示框y值
- (void)showWarning:(NSString *)warningSting forceDisplay:(BOOL)forceDisplay displayTime:(NSTimeInterval)displayTime maxWidth:(CGFloat)maxWidth y:(CGFloat)y;

@end

NS_ASSUME_NONNULL_END
