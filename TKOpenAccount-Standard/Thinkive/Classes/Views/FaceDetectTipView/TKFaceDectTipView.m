//
//  TKFaceDectTipView.m
//  TKOpenAccount-Standard
//
//  Created by <PERSON> on 2022/12/28.
//  Copyright © 2022 thinkive. All rights reserved.
//

#import "TKFaceDectTipView.h"

@interface TKFaceDectTipView()

@property (nonatomic, strong) UIImageView *errorImgView; // 红框imageview
@property (nonatomic, strong) UILabel *warningLabel; // 提示文本
@property (nonatomic, readwrite, assign) BOOL isDisplayWarning; // 是否正在显示警告，该字段为YES时，一般的警告都不会再展示，直接强制展示的时间结束
@property (nonatomic, readwrite, strong) NSTimer *warningTipsTimer; // 警告提示超时定时器

@end

@implementation TKFaceDectTipView

#pragma mark - Init && Dealloc
- (instancetype)initWithFrame:(CGRect)frame {
    if (self = [super initWithFrame:frame]) {
        [self setupUI];
    }
    return self;
}

#pragma mark - Selector
- (void)setupUI
{
    [self addSubview:self.warningLabel];
    [self addSubview:self.errorImgView];
}

- (void)showWarning:(NSString *)warningSting forceDisplay:(BOOL)forceDisplay displayTime:(NSTimeInterval)displayTime maxWidth:(CGFloat)maxWidth y:(CGFloat)y {

    if (self.isDisplayWarning && forceDisplay == NO) {
        
        return;
    } else {
        
        if ([TKStringHelper isEmpty:warningSting]) {
            self.hidden = YES;
            return;
        } else {
            self.hidden = NO;
        }
        
        // 计算文案宽高
        maxWidth = maxWidth == 0 ? self.TKWidth : maxWidth;
        
        CGFloat horizontalMargin = 19;
        CGFloat verticalMargin = 6;
        CGSize size = [warningSting boundingRectWithSize:CGSizeMake(maxWidth - (horizontalMargin) * 2, CGFLOAT_MAX)
                                                 options:
                          NSStringDrawingUsesLineFragmentOrigin |
                          NSStringDrawingUsesFontLeading
                                                       attributes:@{ NSFontAttributeName: self.warningLabel.font}
                                                 context:nil].size;
        CGFloat width = size.width > 0 ? (size.width + horizontalMargin * 2) : 0;
        CGFloat height = size.height > 0 ? (size.height + verticalMargin * 2) : 0;
        self.warningLabel.text = warningSting;
        self.warningLabel.frame = CGRectMake(0, 0, width, height);
        self.warningLabel.center = CGPointMake(self.frame.size.width * 0.5, self.frame.size.height * 0.5);
        if (y > 0) self.warningLabel.TKTop = y;
        
        // 增加强制展示定制器
        if (forceDisplay) {
            
            [self.warningTipsTimer invalidate];
            self.warningTipsTimer = nil;
            
            displayTime = displayTime < 1.0 ? 1.0 : displayTime;
            
            // 定时隐藏
            self.warningTipsTimer = [NSTimer timerWithTimeInterval:displayTime target:self selector:@selector(hideWarningTip:) userInfo:@{@"warningSting" : warningSting} repeats:NO];
            [[NSRunLoop mainRunLoop] addTimer:self.warningTipsTimer forMode:NSRunLoopCommonModes];
            
            self.isDisplayWarning = YES;
        }
    }
}

/// 展示警告提示
/// @param string string
- (void)hideWarningTip:(NSTimer *)timer
{
    [self.warningTipsTimer invalidate];
    self.warningTipsTimer = nil;
    self.warningLabel.TKWidth = 0;
    self.warningLabel.text = nil;
    self.isDisplayWarning = NO;
}


#pragma mark - Setter && Getter
- (UILabel *)warningLabel{
    if (!_warningLabel) {
        _warningLabel = [UILabel new];
        _warningLabel.backgroundColor = [TKUIHelper colorWithHexString:@"#FD4D43" alpha:0.9];
        _warningLabel.textColor = UIColor.whiteColor;
        _warningLabel.layer.cornerRadius = 20.0f;
        _warningLabel.layer.masksToBounds = YES;
        _warningLabel.textAlignment = NSTextAlignmentCenter;
        _warningLabel.font = [UIFont fontWithName:@"PingFangSC-Semibold" size:22];
        _warningLabel.numberOfLines = 0;
    }
    return _warningLabel;
}

- (UIImageView *)errorImgView {
    if (!_errorImgView) {
        _errorImgView = [[UIImageView alloc] initWithFrame:self.bounds];
        _errorImgView.image = [UIImage imageNamed:[NSString stringWithFormat:@"%@.bundle/Resources/TKOpenGeneral/tk_face_detect_error.png", TK_OPEN_RESOURCE_NAME]];//设置图片
        _errorImgView.hidden = YES;
    }
    
    return _errorImgView;
}

- (void)setIsEnhance:(BOOL)isEnhance {
    _isEnhance = isEnhance;
    
    _errorImgView.hidden = !isEnhance;
}

@end
