//
//  TKBaseVideoRecordView.m
//  TKOpenAccount-Standard
//
//  Created by <PERSON> on 2022/8/31.
//  Copyright © 2022 thinkive. All rights reserved.
//

#import "TKBaseVideoRecordView.h"
#import "TKReadingView.h"

#define TK_ONEVIEW_PIECE_COLOR  [UIColor colorWithRed:0/255.0 green:13/255.0 blue:41/255.0 alpha:0.85/1.0]
#define TK_ONEVIEW_TIP_LABEL_COLOR [TKUIHelper colorWithHexString:@"#3CCDFF"]
#define TK_WAIT_COUNT_DOWN 0.9f
#define BottomShowLabelLineHeight  4.0f

@interface TKBaseVideoRecordView()<TKReadingViewDelegate>


@property (nonatomic, strong) TKLayerView  *layerView;//提示layer
@property (nonatomic, strong) UILabel *waitTipLabel;//开始结束提示文本


@property (nonatomic, assign) float answerCount;//回答倒计时默认5秒
@property (nonatomic, assign) int recordCountDown;//回答倒计时3秒

@property (nonatomic, assign) BOOL isFinished;//是否回答了问题



@property (nonatomic, strong) NSTimer *recordDownTimer;
@property (nonatomic, assign) BOOL badgeViewHidden;//记录红点显示隐藏状态
@property (nonatomic, assign) float wordSpeedDuration; // 文字滚动速度
@property (nonatomic, readwrite, assign) TKCountDownType countDownType; // 倒计时类型
@property (nonatomic, readwrite, assign) BOOL hasAsrResult; // 是否已有asr结果
@property (nonatomic, strong) UITextView *readLable;//阅读文本
@property (nonatomic, readwrite, strong) NSTimer *startAutoRecordTimer; // 开始自动录制定时器，防止不做录制占用时间过长
@property (nonatomic, readwrite, assign) int startAutoRecordCountDown; // 开始自动录制倒计时

@property (nonatomic, readwrite, strong) TKReadingView *readingView;

@property (nonatomic, strong) UILabel *questionProgressLabel;//问题进度提示文本
@property (nonatomic, strong) UILabel *videoWitnessLabel;//视频见证中提示文本
@property (nonatomic, strong) UILabel *dontLeaveLabel;//请勿离开文本提示
@property (nonatomic, strong) UIImageView *boxTipImgView;   //对准框中间提示图标
@property (nonatomic, strong) UILabel *boxTipLabel;//对准框中间提示文本
@end

@implementation TKBaseVideoRecordView
@synthesize boxRect = _boxRect;
@synthesize requestParam;
@synthesize takeBtn = _takeBtn;
@synthesize delegate = _delegate;
@synthesize answerPromptImgBg = _answerPromptImgBg;
@synthesize answerPromptLabel = _answerPromptLabel;
@synthesize nextBtn = _nextBtn;
@synthesize avPreviewView = _avPreviewView;
@synthesize isReadVideo = _isReadVideo;
@synthesize currentDisplayPrompt = _currentDisplayPrompt;


- (void)dealloc {
    [self stopAutoStartRecordTimer];
}

-(instancetype)initWithFrame:(CGRect)frame withParam:(NSMutableDictionary *)param{
    self=[super initWithFrame:frame];
    if (self) {
        self.requestParam=param;
        
        if ([TKStringHelper isEmpty:param[@"mainColor"]]) {
            self.mainColorString=@"#2772FE";
        }else{
            self.mainColorString=param[@"mainColor"];
        }
    
        if([TKStringHelper isNotEmpty:param[@"isReadVideo"]]){
            self.isReadVideo=[param[@"isReadVideo"] intValue]==1?true:false;
        }
        
        [self viewInit];
    }
    return self;
}

/**
 <AUTHOR> 2019年04月03日09:13:34
 @初始化单向视频默认界面
 */
- (void)viewInit{

    [self addSubview:self.boxImgBackgroundView];
    [self addSubview:self.boxImgView];
//    [self addSubview:self.topView];
//    [self addSubview:self.bottomView];
//    [self addSubview:self.leftView];
//    [self addSubview:self.rightView];
    if (self.requestParam[@"isShowHeadRect"]&&[self.requestParam[@"isShowHeadRect"] integerValue] == 0) {
        //不需要头像框的UI场景
        [self.boxImgBackgroundView setHidden:YES];
        [self.boxImgView setHidden:YES];
//        [self.topView setHidden:YES];
//        [self.bottomView setHidden:YES];
//        [self.leftView setHidden:YES];
//        [self.rightView setHidden:YES];
        [self addSubview:self.topBgView];
        [self addSubview:self.bottomBgView];
    }
    
    [self addSubview:self.dontLeaveLabel];
    [self addSubview:self.backBtn];
    [self addSubview:self.videoWitnessLabel];
    

    [self addSubview:self.badgeView];
    [self addSubview:self.recordTimeLabel];
    [self.recordTimeLabel setHidden:YES];//进来先不展示录制倒计时
    [self.badgeView setHidden:YES];//进来先不展示录制倒计时
    self.badgeViewHidden=YES;
    
    [self addSubview:self.takeBtn];
    
    [self.boxImgBackgroundView addSubview:self.boxTipImgView];
    [self.boxImgBackgroundView addSubview:self.boxTipLabel];
    
    self.requestParam[@"readString"]=self.requestParam[@"readString"]?self.requestParam[@"readString"]:@"我自愿开立证券账户。";
    self.colorWordsNum=0;
    
    // 默认隐藏下一步按钮
    [self showNextBtn:NO btnTitle:nil];
    
    [self showTakeRecordBtn:YES];
}


#pragma mark 事件方法

/**
 @Auther Vie 2020年02月27日17:45:23
 @param sender 拍照事件
 */
-(void)takeAction:(UIButton *)sender{
    
    [self stopAutoStartRecordTimer];
    
    if(self.isReadVideo){
        //朗读单向界面跳转
        // 开始录制之后，5秒内不可点击完成录制
        [self enableFinishTakeRecord:NO];
        //显示录制倒计时
        _recordTimeLabel.hidden=NO;
        [self.dontLeaveLabel setHidden:YES];
        if (self.delegate&&[self.delegate respondsToSelector:@selector(takeRecord)]) {
            [_takeBtn removeFromSuperview];
            _takeBtn = nil;
            [self addSubview:self.endBtn];
            [self.delegate takeRecord];
            [self showReadTextView];
        }
    }else{
        // 智能语音单向界面跳转
        [self addSubview:self.bottomShowTipLineView];
        [self.bottomShowTipLineView addSubview:self.bottomShowTipRecordLineView];
        [self.bottomShowTipRecordLineView addSubview:self.questionProgressLabel];
        
        [self.boxTipImgView setHidden:YES];
        [self.boxTipLabel setHidden:YES];
        [self.dontLeaveLabel setHidden:YES];
        
        if (self.delegate&&[self.delegate respondsToSelector:@selector(takeRecord)]) {
            [_takeBtn removeFromSuperview];
            _takeBtn = nil;
            [self.delegate takeRecord];
        }
    }
}

/**
 @Auther Vie 2020年02月28日10:47:55
 @param sender 结束拍照事件
 */
- (void)endAction:(UIButton *)sender{
    if (self.delegate&&[self.delegate respondsToSelector:@selector(endRecord)]) {
        [self.delegate endRecord];
    }
}


/**
 @Auther Vie 2019年04月08日10:46:51

 @param sender 返回按钮点击事件
 */
-(void)backAction:(UIButton *)sender{
    if (self.delegate&&[self.delegate respondsToSelector:@selector(goBack)]) {
        [self.delegate goBack];
    }
}


/**
 @Auther Vie 2020年02月27日17:45:23
 @param sender 拍照事件
 */
-(void)nextAction:(UIButton *)sender{
    
    [self.nextBtn removeFromSuperview];
    self.nextBtn = nil;
    
    if (self.delegate && [self.delegate respondsToSelector:@selector(nextAction)]) {
        
        [self.delegate nextAction];
    }
}


/**
 <AUTHOR> 2019年05月23日13:37:30
 @活体完成话术播报等待界面
 */
-(void)liveEndWait{
    if (self.isReadVideo) {
        [self liveEndWait:@"请您正对手机屏幕。如您已经准备好，请点击屏幕下方的开始录制按钮，根据提示内容进行朗读。"];
    }else{
        [self liveEndWait:@"请您正对手机屏幕。如您已经准备好，请点击屏幕下方的开始录制按钮，根据提示回答问题。"];
    }
    
}

- (void)liveEndWait:(NSString *)str {
    
    [self addSubview:self.bottomShowTipView];
    [self.bottomShowTipView addSubview:self.bottomShowLabel];
    _bottomShowLabel.textContainerInset = UIEdgeInsetsMake(18.3f, 12, 18.3f, 12);
    _bottomShowLabel.font = [UIFont fontWithName:@"PingFang SC" size:16];
    self.bottomShowLabel.text = str;
    
    //文本计算高度
    CGSize lableSize = [self.bottomShowLabel sizeThatFits:CGSizeMake(self.bottomShowTipView.TKWidth, MAXFLOAT)];
    [self.bottomShowLabel setTKHeight:lableSize.height];
    [self.bottomShowTipView setTKHeight:lableSize.height];
    
//    self.countDownNum=3;
//    self.countDownLabel.text=[NSString stringWithFormat:@"%ld",(long)self.countDownNum];
//    self.countDownLabel.font = [UIFont fontWithName:@"PingFangSC-Medium" size:80];
//    [self addSubview:self.countDownLabel];
//    [self performSelector:@selector(countDownChangeView) withObject:nil afterDelay:TK_WAIT_COUNT_DOWN];
}


/**
 <AUTHOR> 2019年04月15日15:15:19
 @活体警告
 */
-(void)liveWarning:(NSString *)warningSting{

    [self liveWarning:warningSting forceDisplay:NO];
}

/**
 <AUTHOR> 2019年04月15日15:15:19
 @活体警告
 */
-(void)liveWarning:(NSString *)warningSting forceDisplay:(BOOL)forceDisplay {

//    if (!_faceDectTipView) {
//        [self addSubview:self.faceDectTipView];
//    }
//
//    NSTimeInterval faceDetectInterval = 1.0;
//    if ([self.requestParam[@"faceDetectInterval"] doubleValue] > 0) {
//        faceDetectInterval = [self.requestParam[@"faceDetectInterval"] doubleValue];
//    }
//
//    CGFloat maxWidth = self.avPreviewView.TKWidth;
//    [self.faceDectTipView showWarning:warningSting forceDisplay:forceDisplay displayTime:faceDetectInterval maxWidth:maxWidth y:0];

    if ([TKStringHelper isEmpty:warningSting]) {
        [self.boxTipImgView setImage:[UIImage imageNamed:[NSString stringWithFormat:@"%@.bundle/Resources/TKOpenPlugin60026/tk_one_video_wait.png", TK_OPEN_RESOURCE_NAME]]];
        self.boxTipLabel.text = @"请保持人脸在视频框内";
        self.boxTipLabel.textColor=[TKUIHelper colorWithHexString:@"#ffffff"];
        if (_takeBtn&&!_takeBtn.hidden) {
            [self.boxTipImgView setHidden:YES];
            [self.boxTipLabel setHidden:YES];
        }
        //非警告状态
        UIImage *img=[UIImage imageNamed:[NSString stringWithFormat:@"%@.bundle/Resources/TKOpenPlugin60005/video_head_box_5.1UI.png", TK_OPEN_RESOURCE_NAME]];
        img = [img imageWithRenderingMode:UIImageRenderingModeAlwaysTemplate];
        [self.boxImgView setTintColor:[TKUIHelper colorWithHexString:@"#396DE3"]];
        [self.boxImgView setImage:img];
    }else{
        [self.boxTipImgView setHidden:NO];
        [self.boxTipLabel setHidden:NO];
        [self.boxTipImgView setImage:[UIImage imageNamed:[NSString stringWithFormat:@"%@.bundle/Resources/TKOpenPlugin60026/tk_one_video_wait_warning.png", TK_OPEN_RESOURCE_NAME]]];
        self.boxTipLabel.text = warningSting;
        self.boxTipLabel.textColor=[TKUIHelper colorWithHexString:@"#FF4848"];
        //警告状态下对准框改为红色
        UIImage *img=[UIImage imageNamed:[NSString stringWithFormat:@"%@.bundle/Resources/TKOpenPlugin60005/video_head_box_5.1UI.png", TK_OPEN_RESOURCE_NAME]];
        img = [img imageWithRenderingMode:UIImageRenderingModeAlwaysTemplate];
        [self.boxImgView setTintColor:[TKUIHelper colorWithHexString:@"#FF4848"]];
        [self.boxImgView setImage:img];
    }
}


/**
 <AUTHOR> 2019年04月15日15:22:00
 @活体继续识别
 */
- (void)liveContinue:(NSString *)string isOneLineShow:(BOOL)isOneLineShow isHtmlString:(BOOL)htmlFlag questionOneWordSpeed:(NSString *)questionOneWordSpeed {
  
    if ([self.currentDisplayPrompt isEqualToString:string]) {
        
        NSMutableAttributedString *attributedStr=[[NSMutableAttributedString alloc]initWithAttributedString:self.bottomShowLabel.attributedText];
        [attributedStr addAttribute:NSForegroundColorAttributeName value:[TKUIHelper colorWithHexString:@"#396DE3"] range:NSMakeRange(0, self.colorWordsNum)];
        self.bottomShowLabel.attributedText = attributedStr;
        [self changeReadTextViewOffSet];
    }else{
        self.bottomShowTipView.hidden = [TKStringHelper isEmpty:string];
        
        [self updateTipLabel:[NSString stringWithFormat:@"%@",string] textColor:@"#222222" cornerRadius:20.0f isOneLineShow:isOneLineShow isHtmlString:htmlFlag questionOneWordSpeed:questionOneWordSpeed];
    }
    self.isFinished=NO;
}


/**
 <AUTHOR> 18:13:29
 @视频录制中播放的语音要修改图界面展示波动图
 */
- (void)startRecorderVideoPlay:(NSString *)questionString isOneLineShow:(BOOL)isOneLineShow isHtmlString:(BOOL)htmlFlag questionOneWordSpeed:(NSString *)questionOneWordSpeed {
    
    [self.answerPromptLabel removeFromSuperview];
    [self.answerPromptImgBg removeFromSuperview];
    [self.countDownLabel removeFromSuperview];
    self.countDownLabel = nil;
    self.answerPromptLabel = nil;
    self.answerPromptImgBg = nil;
    self.isFinished = YES;
    self.countDownType = TKCountDownTypeUnknown;
    
    self.bottomShowTipView.hidden = [TKStringHelper isEmpty:questionString];
    
    if ([TKStringHelper isNotEmpty:self.currentDisplayPrompt]) {
        questionString=[NSString stringWithFormat:@"%@<br/>%@",questionString,self.currentDisplayPrompt];
    }
    self.colorWordsNum=0;
    [self updateTipLabel:questionString textColor:@"#222222" cornerRadius:20.0f isOneLineShow:NO isHtmlString:htmlFlag questionOneWordSpeed:questionOneWordSpeed];
    
}


/**
 <AUTHOR> 2019年04月26日19:12:30
 @回答完毕（识别到了是或否）
 */
- (void)answered:(BOOL)isAnswered displayTime:(int)displayTime {
    
    self.isFinished=YES;
    
    // 回答了YES
    if (isAnswered) {

        // 延迟1s是为了展示语音识别结果
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(displayTime * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            
            self.answerPromptLabel.hidden = YES;
            self.answerPromptImgBg.hidden = YES;
        });
    }
}


/**
<AUTHOR> 2020年01月07日19:02:54
@语音问题播放完成停止波动动画
*/
-(void)playEndVoiceView{
//    [self.fluctuateGifView removeFromSuperview];
//    self.fluctuateGifView=nil;

}


/**
 <AUTHOR> 2019年04月16日20:09:51
 @语音合成播放完成，修改界面
 */
- (void)playEndView:(int)waitTime prompt:(NSString *)string noAnswerPromptTime:(int)noAnswerPromptTime {
    
    if (waitTime<=0) {
        waitTime=5;
    }
    
    self.answerCount=waitTime;
    self.hasAsrResult = NO;
    [self updateCountDownLabelText:waitTime countDownType:TKCountDownTypeAnswer];
    [self addSubview:self.countDownLabel];
    
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1.0f * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        [self answerCountDown:waitTime noAnswerPromptTime:noAnswerPromptTime];
    });
}


/**
 <AUTHOR> 2019年04月26日18:39:08
 @问题回答倒计时
 */
- (void)answerCountDown:(int)waitTime noAnswerPromptTime:(int)noAnswerPromptTime {
    
    __weak typeof(self) weakSelf = self;
    
    if (!self.isFinished) {
        self.answerCount = self.answerCount-1;
        
        noAnswerPromptTime = noAnswerPromptTime <= 0 ? 3 : noAnswerPromptTime;
        if ((waitTime - self.answerCount >= noAnswerPromptTime) && self.hasAsrResult == NO) {
            [self showNoAnswerPrompt];
        }
       
        [self updateCountDownLabelText:self.answerCount countDownType:TKCountDownTypeAnswer];
        
        if (self.answerCount>=3) {
            
            dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1.0f * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                [weakSelf answerCountDown:waitTime noAnswerPromptTime:noAnswerPromptTime];
            });

        }else if(self.answerCount<3&&self.answerCount>0){

            dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1.0f * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                [weakSelf answerCountDown:waitTime noAnswerPromptTime:noAnswerPromptTime];
            });
        }else{
            
            if (self.delegate&&[self.delegate respondsToSelector:@selector(answerCountDownEnd)]) {
                [self.delegate answerCountDownEnd];
            }

//            [self.countDownLabel removeFromSuperview];
        }
    }else{

//        [self.countDownLabel removeFromSuperview];
    }
    
}

- (void)recordLongTime:(int)recordTime startRecord:(BOOL)startRecord {
    NSDate *d = [NSDate dateWithTimeIntervalSince1970:recordTime];
    NSDateFormatter *formatter = [[NSDateFormatter alloc] init];
    formatter.locale = [[NSLocale alloc] initWithLocaleIdentifier:@"zh_CN"];
    if (recordTime/3600 >= 1) {
        [formatter setDateFormat:@"HH:mm:ss"];
    } else {
          [formatter setDateFormat:@"mm:ss"];
    }
    
    // 大于最短录制时间可以点击完成录制
    if (recordTime >= [self.requestParam[@"shortestTime"] intValue]) {
        [self enableFinishTakeRecord:YES];
    }

    if (recordTime!=0) {
        
        [UIView animateWithDuration:0.3 animations:^{
            
            self.badgeView.alpha = 0;
        } completion:^(BOOL finished) {
            if (finished) {
                
                [UIView animateWithDuration:0.3 delay:0.4 options:UIViewAnimationOptionCurveLinear animations:^{
                    
                    self.recordTimeLabel.text = [formatter stringFromDate:d];
                    self.badgeView.alpha = 1;
                } completion:nil];
            }
        }];
    } else {
        self.recordTimeLabel.text = [formatter stringFromDate:d];
        self.badgeView.alpha = 1;
    }
}

/**
@Auther Vie 2020年02月28日12:48:01
@param recordTime 当前录制时间
@param longestTime 最长录制时间
*/
-(void)recordTime:(int)recordTime longestTime:(int)longestTime{
    
    NSDateFormatter *formatter = [[NSDateFormatter alloc] init];
    formatter.locale = [[NSLocale alloc] initWithLocaleIdentifier:@"zh_CN"];

    int remaining = longestTime - recordTime; // 剩余时间
    if (remaining / 3600 >= 1) {
        [formatter setDateFormat:@"HH:mm:ss"];
    } else {
        [formatter setDateFormat:@"mm:ss"];
    }
    
    NSDate *d = [NSDate dateWithTimeIntervalSince1970:remaining];

    
    // 大于最短录制时间可以点击完成录制
    if (recordTime >= [self.requestParam[@"shortestTime"] intValue]) {
        [self enableFinishTakeRecord:YES];
    }
    
    if (recordTime!=0) {
        
        [UIView animateWithDuration:0.3 animations:^{
            
            self.badgeView.alpha = 0;
        } completion:^(BOOL finished) {
            if (finished) {
                
                [UIView animateWithDuration:0.3 delay:0.4 options:UIViewAnimationOptionCurveLinear animations:^{
                    
                    self.recordTimeLabel.text = [formatter stringFromDate:d];
                    self.badgeView.alpha = 1;
                } completion:nil];
            }
        }];
    } else {
        self.recordTimeLabel.text = [formatter stringFromDate:d];
        self.badgeView.alpha = 1;
    }

}

/**
@Auther Vie 2020年03月10日14:43:37
@param showWaitTip 中间准备提示文本
*/
-(void)showWaitTip:(NSString *)string{
    self.waitTipLabel.text=string;
    [self addSubview:self.waitTipLabel];
}

/**
@Auther Vie 2020年03月10日14:46:52
@隐藏中间准备提示文本
*/
-(void)hideWaitTip{
    [self.waitTipLabel removeFromSuperview];
    [self.recordTimeLabel setHidden:NO];//展示录制倒计时
    [self.badgeView setHidden:NO];//展示录制倒计时
    self.badgeViewHidden=NO;
}

/// 设置是否可以点击开始录制
/// @param isEnable 是否可以点击
- (void)enableTakeRecord:(BOOL)isEnable {
    
    self.takeBtn.enabled = isEnable;
    if (isEnable) {
        _takeBtn.layer.borderWidth=0.0f;
        _takeBtn.layer.borderColor=[UIColor clearColor].CGColor;
        [_takeBtn setTitleColor:[TKUIHelper colorWithHexString:@"#FFFFFF"] forState:UIControlStateNormal];
        _takeBtn.backgroundColor=[TKUIHelper colorWithHexString:[self.mainColorString isEqualToString:@"#2772FE"]?@"#EE393E":self.mainColorString];
    } else {
        _takeBtn.layer.borderWidth=0.0f;
        _takeBtn.layer.borderColor=[UIColor clearColor].CGColor;
        [_takeBtn setTitleColor:[TKUIHelper colorWithHexString:@"#FFFFFF"] forState:UIControlStateNormal];
        _takeBtn.backgroundColor=[TKUIHelper colorWithHexString:[self.mainColorString isEqualToString:@"#2772FE"]?[TKCommonUtil blendWithWhite:@"#F79DA0" ratio:0.28f]:self.mainColorString];
    }
}

/// 设置是否可以点击结束录制
/// @param isEnable 是否可以点击
- (void)enableFinishTakeRecord:(BOOL)isEnable {
    self.endBtn.enabled = isEnable;
    if (isEnable) {
        _endBtn.layer.borderWidth=0.0f;
        _endBtn.layer.borderColor=[UIColor clearColor].CGColor;
        [_endBtn setTitleColor:[TKUIHelper colorWithHexString:@"#FFFFFF"] forState:UIControlStateNormal];
        _endBtn.backgroundColor=[TKUIHelper colorWithHexString:[self.mainColorString isEqualToString:@"#2772FE"]?@"#EE393E":self.mainColorString];
    } else {
        _endBtn.layer.borderWidth=0.0f;
        _endBtn.layer.borderColor=[UIColor clearColor].CGColor;
        [_endBtn setTitleColor:[TKUIHelper colorWithHexString:@"#FFFFFF"] forState:UIControlStateNormal];
        _endBtn.backgroundColor=[TKUIHelper colorWithHexString:[self.mainColorString isEqualToString:@"#2772FE"]?[TKCommonUtil blendWithWhite:@"#F79DA0" ratio:0.28f]:self.mainColorString];
    }
}

/**
<AUTHOR> 2021年07月08日10:30:58
@修改提示语问题话术
@string 文本
@colorString 文本颜色
@cornerRadius 背景框圆角
@flag 是否是播放语音话术（y坐标不一样要调整）
@flag 是否是html文本
@return 顶部遮罩层
*/
-(void)updateTipLabel:(NSString *)string textColor:(NSString *)colorString cornerRadius:(CGFloat)cornerRadius isOneLineShow:(BOOL)isOneLineShow isHtmlString:(BOOL)htmlFlag questionOneWordSpeed:(NSString *)questionOneWordSpeed {
    
//    TKLogDebug(@"---------oneLineWidth ponitSize = %.2f, string= %@", self.bottomShowLabel.font.pointSize, string);
    string=[TKCommonUtil switchLabelToSpan:string];
    // 生成富文本
    NSMutableAttributedString *attStr = [self convertTextToHtmlString:string textColor:colorString];
    
    // 不重复处理
    if ([self.bottomShowLabel.text isEqualToString:string]
        || [self.bottomShowLabel.attributedText isEqualToAttributedString:attStr]) {
//        TKLogDebug(@"---------oneLineWidth 传入的string = %@重复,不重复处理", string);
        return;
    }
    
    // 先暂停滚动
    [self stopTextViewScroll];

    self.bottomShowLabel.attributedText = nil;
//    self.bottomShowLabel.textContainerInset = UIEdgeInsetsMake(0, 0, 0, 0); // 旧版UI会设置UIEdgeInsetsMake(5, 0, 5, 0).这里要改回来
    
    // 根据富文本调整frame
    [self updateTextViewFrame:string textColor:colorString isOneLineShow:isOneLineShow];
    self.bottomShowLabel.textContainerInset = UIEdgeInsetsMake(0, 12, 0, 12);
    self.bottomShowLabel.font = [UIFont fontWithName:@"PingFang SC" size:16];
   self.bottomShowLabel.attributedText = attStr;
   [self addSubview:self.bottomShowTipView];
   [self.bottomShowTipView addSubview:self.bottomShowLabel];

   if (!isOneLineShow) {
       //计算是否要滚动换行
       //多久渐变一个字
       float durationTime = 0.19f;
        if ([TKStringHelper isNotEmpty:questionOneWordSpeed]) durationTime = questionOneWordSpeed.floatValue;
       if (durationTime == 0) durationTime = 0.19f;
       self.wordSpeedDuration = durationTime;
       
       //问题播放需要判断是否走
       [self changeReadTextViewOffSet];
   }
}

- (void)updateTextViewFrame:(NSString *)string textColor:(NSString *)colorString isOneLineShow:(BOOL)isOneLineShow
{

    
    self.bottomShowLabel.textContainer.lineBreakMode =  NSLineBreakByWordWrapping;

    // 生成富文本
    
    // 计算一行的宽、高度(中文高度包括行高,需要清除换行再计算)
    NSString *tempStr = [string stringByReplacingOccurrencesOfString:@"<br/>" withString:@""];
    NSMutableAttributedString *tempattStr = [self convertTextToHtmlString:tempStr textColor:colorString];
    CGFloat gap=18.3;
    // 如果不是播放问题，展示多行问题文本
    if (isOneLineShow == NO) {

        // 采用固定高度，不再动态计算
        self.bottomShowTipView.TKHeight = 104;
        [self.bottomShowLabel setTKHeight:104-gap*2];
        [self.bottomShowLabel setTKTop:gap];
        
        self.bottomShowLabel.textContainer.maximumNumberOfLines = INT_MAX;
        
    } else {    // 如果是播放问题，需要展示多行（>1  问题文本 + 倒计时文本 + 识别结果文本）

        self.bottomShowTipView.TKHeight = 104;
        [self.bottomShowLabel setTKHeight:104-gap*2];
        [self.bottomShowLabel setTKTop:gap];

    }
}

- (NSMutableAttributedString *)convertTextToHtmlString:(NSString *)text textColor:(NSString *)colorString
{
    NSString *tempDivString = [NSString stringWithFormat:@"<span style=\"font-size:%ipx;color:%@;font-family:PingFang SC\">%@</span>", (int)self.bottomShowLabel.font.pointSize, colorString, text];
   NSData *tempData = [tempDivString dataUsingEncoding:NSUnicodeStringEncoding];
   NSDictionary *options = @{NSDocumentTypeDocumentAttribute: NSHTMLTextDocumentType,
                             NSCharacterEncodingDocumentAttribute:[NSNumber numberWithInt:NSUTF8StringEncoding]
   };
   NSMutableAttributedString *tempattStr = [[NSMutableAttributedString alloc]initWithData:tempData options:options documentAttributes:nil error:nil];
    
    return tempattStr;
}

/**
计算html字符串高度

@param str html 未处理的字符串
@param font 字体设置
@param lineSpacing 行高设置
@param width 容器宽度设置
@return 富文本高度
*/
- (CGSize)getHTMLHeightByStr:(NSMutableAttributedString *)str width:(CGFloat)width
{
   CGSize contextSize = [str boundingRectWithSize:(CGSize){width, CGFLOAT_MAX} options:NSStringDrawingUsesLineFragmentOrigin | NSStringDrawingUsesFontLeading context:nil].size;
   return contextSize;

}

/**
@Auther Vie 2021年07月29日13:56:44
改变阅读文本空间显示
*/
-(void)changeReadTextViewOffSet {
   
   // 防止页面不显示的时候，定时器还在反复调用该功能
   if (!self.window) {
       
       [self stopTextViewScroll];
       
//        TKLogDebug(@"changeReadTextViewOffSet timer invalidate");
       return;
   }
    
    //计算是否要滚动换行
    CGSize labelSize=[self.bottomShowLabel sizeThatFits:CGSizeMake(self.bottomShowLabel.TKWidth, self.bottomShowLabel.TKHeight)];
    float diffY=labelSize.height-self.bottomShowLabel.TKHeight;
    if (diffY>0) {
        NSRange rang=NSMakeRange(self.colorWordsNum, 1);
        [self.bottomShowLabel scrollRangeToVisible:rang];
    }
    
    if (self.colorWordsNum < (self.bottomShowLabel.attributedText.string.length-self.currentDisplayPrompt.length)) {
        self.colorWordsNum++;
        if (self.colorWordsNum >=self.bottomShowLabel.attributedText.string.length) {
            self.colorWordsNum =(int)self.bottomShowLabel.attributedText.string.length;
        }
        //多久渐变一个字
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(self.wordSpeedDuration * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            NSMutableAttributedString *attributedStr=[[NSMutableAttributedString alloc]initWithAttributedString:self.bottomShowLabel.attributedText];
            [attributedStr addAttribute:NSForegroundColorAttributeName value:[TKUIHelper colorWithHexString:@"#396DE3"] range:NSMakeRange(0, self.colorWordsNum)];
            self.bottomShowLabel.attributedText = attributedStr;
            [self changeReadTextViewOffSet];
        });
    }

    else {
       
//        TKLogDebug(@"思迪文案滚动动画：销毁定时器");
       [self stopTextViewScroll];
    }
    
}


- (void)stopTextViewScroll
{
//    self.changeReadTextViewWords = 0;
//    [self.changeReadTextViewOffSetTimer invalidate];
//    self.changeReadTextViewOffSetTimer = nil;
}

/**
 <AUTHOR> 2021年07月08日16:15:38
 @语音识别过程中识别到的回答小字提示
 @param回答正确，无用回答
 */
-(void)answerPromptType:(BOOL)flag identifyString:(NSString *)string{
    if (!flag) {
        string=@"不是/其他内容";
    }
    string=[NSString stringWithFormat:@" %@",string];
    NSMutableAttributedString *attri =[[NSMutableAttributedString alloc] initWithString:string];
    [attri addAttribute:NSForegroundColorAttributeName value:[TKUIHelper colorWithHexString:@"#FFFFFF"] range:NSMakeRange(0, string.length)];
    [attri addAttribute:NSFontAttributeName value:[UIFont fontWithName:@"PingFang SC" size:14] range:NSMakeRange(0, string.length)];
    // 创建图片图片附件
    NSTextAttachment *attach = [[NSTextAttachment alloc] init];
    if (flag) {
        attach.image = [UIImage imageNamed:[NSString stringWithFormat:@"%@.bundle/Resources/TKOpenPlugin60026/tk_answer_ok_5.1UI.png", TK_OPEN_RESOURCE_NAME]];
        attach.bounds = CGRectMake(0, -3.5, 16, 16);
    }else{
        attach.image = [UIImage imageNamed:[NSString stringWithFormat:@"%@.bundle/Resources/TKOpenGeneral/tk_quality_tips_img.png", TK_OPEN_RESOURCE_NAME]];;
        attach.bounds = CGRectMake(0, -3.5, 16, 16);
    }

    
    NSAttributedString *attachImg = [NSAttributedString attributedStringWithAttachment:attach];
    //将图片插入到合适的位置
    [attri insertAttributedString:attachImg atIndex:0];
    
    self.countDownLabel.attributedText = attri;
    self.countDownLabel.textAlignment=NSTextAlignmentCenter;
    float height=36;
    CGSize lableSize = [self.countDownLabel sizeThatFits:CGSizeMake(UISCREEN_HEIGHT, height)];
    float width=lableSize.width+20;
    float x=(self.TKWidth-width)/2.0f;
    float y=self.bottomShowTipView.TKBottom+6;
    self.countDownLabel.frame=CGRectMake(x, y, width, height);
    if (flag) {
        self.countDownLabel.backgroundColor=[TKUIHelper colorWithHexString:@"#398DE3" alpha:0.6f];
    }else{
        self.countDownLabel.backgroundColor=[TKUIHelper colorWithHexString:@"#FF8D07" alpha:0.6f];
    }

    
    self.countDownLabel.layer.cornerRadius=height/2.0f;
    self.countDownLabel.layer.masksToBounds=YES;
    
 
    // 更新识别倒计时文案
    self.hasAsrResult = YES;
    
//    if (flag) {
//        
//    }else{
//        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.5f * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
//            [self updateCountDownLabelText:self.answerCount countDownType:TKCountDownTypeAnswer];
//        });
//    }

 
}

/**
@Auther Vie 2021年07月28日16:40:17
@param currentNum 当前进度，
@param allNum 总进度数目；问题n，结束语1；n+1
*/
-(void)currentNum:(int)currentNum allNum:(int)allNum{
    if (allNum == 0) return;
    currentNum++;
    if (currentNum>allNum) {
        return;
    }
    self.bottomShowTipRecordLineView.hidden = NO;
    
    float width = currentNum* 1.0f / allNum * self.bottomShowTipLineView.TKWidth;
    [self.bottomShowTipRecordLineView setFrameWidth:width > self.bottomShowTipLineView.TKWidth ? self.bottomShowTipLineView.TKWidth : width];
    
    self.questionProgressLabel.text=[NSString stringWithFormat:@"进度%d/%d",currentNum,allNum];
    
    float progressX=self.bottomShowTipRecordLineView.TKWidth-self.questionProgressLabel.TKWidth/2.0f;
    if (progressX<0) {
        progressX=0;
    }else if ((progressX+self.questionProgressLabel.TKWidth)>self.bottomShowTipRecordLineView.TKWidth) {
        progressX=self.bottomShowTipRecordLineView.TKWidth-self.questionProgressLabel.TKWidth;
    }
    [self.questionProgressLabel setTKLeft:progressX];
    
    //新设计这里不展示进度条
    self.bottomShowTipLineView.hidden = YES;
    self.bottomShowTipRecordLineView.hidden = YES;
    self.questionProgressLabel.hidden = YES;
}


///// 是否展示录制按钮
///// @param isShow 是否展示
/// @param btnTitle 按钮文案，传Nil展示默认的“继续播报”
- (void)showNextBtn:(BOOL)isShow btnTitle:(NSString *)btnTitle {
    if (self.nextBtn.superview == nil) [self addSubview:self.nextBtn];
    [self bringSubviewToFront:self.nextBtn];
    self.nextBtn.hidden = !isShow;
    [self.nextBtn setBackgroundColor:[TKUIHelper colorWithHexString:[self.mainColorString isEqualToString:@"#2772FE"]?@"#EE393E":self.mainColorString]];
    if([TKStringHelper isEmpty:btnTitle]){
        [self.nextBtn setTitle:@"继续播报" forState:UIControlStateNormal];
    }else{
        [self.nextBtn setTitle:btnTitle forState:UIControlStateNormal];
    }
}

/// 展示用户动作提示文案，等待用户做动作
/// @param originString 待展示的文案
/// @param htmlFlag 是否是html标签
/// @param waitTime 等待时间
- (void)showUserActionPrompt:(NSString *)originString isHtmlString:(BOOL)htmlFlag waitTime:(int)waitTime questionOneWordSpeed:(NSString *)questionOneWordSpeed {
    if (waitTime <= 0) {
        waitTime = 5;
    }
    
    self.isFinished = NO;
    
    // 防止等待时间文本闪烁，先隐藏，后显示
    self.countDownLabel.hidden = YES;
    [self updateCountDownLabelText:waitTime countDownType:TKCountDownTypeUserAction];
    [self updateTipLabel:[NSString stringWithFormat:@"%@",originString] textColor:@"#222222" cornerRadius:20.0f isOneLineShow:YES isHtmlString:htmlFlag questionOneWordSpeed:questionOneWordSpeed];
    self.countDownLabel.hidden = NO;
    
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1.0f * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        [self userActionCountDown:waitTime];
    });
}


/**
 <AUTHOR> 2019年04月26日18:39:08
 @强制用户做动作等待倒计时
 */
-(void)userActionCountDown:(int)waitTime {
    
    __weak typeof(self) weakSelf = self;
    
    // 其他业务异常语音打断倒计时
    if (!self.isFinished) {
        
        waitTime--;
    
        [self updateCountDownLabelText:waitTime countDownType:TKCountDownTypeUserAction];
        
        if (waitTime > 0) {
            
            dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1.0f * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                [self userActionCountDown:waitTime];
            });
        }else{
            
            if (self.delegate && [self.delegate respondsToSelector:@selector(userActionCountDownEnd)]) {
                [self.delegate userActionCountDownEnd];
            }
        }
    }else{
        
    }
}

- (void)updateCountDownLabelText:(int)answerCount countDownType:(TKCountDownType)countDownType
{
    if (self.hasAsrResult) {
        //有识别结果了就不再展示请回答
        return;
    }
    self.countDownType = countDownType;
    
    [self addSubview:self.countDownLabel];
    
    NSString *tempStr = nil;
    if (countDownType == TKCountDownTypeAnswer) {
        if (self.hasAsrResult == YES) {
            tempStr =[NSString stringWithFormat:@"请回答(%ds)",(int)answerCount] ;
        } else {
            tempStr =[NSString stringWithFormat:@"请回答(%ds)",(int)answerCount] ;
        }
    } else {
        tempStr = [NSString stringWithFormat:@"(%ds)",(int)answerCount];
    }
    

    tempStr=[NSString stringWithFormat:@" %@",tempStr];
    NSMutableAttributedString *attri =[[NSMutableAttributedString alloc] initWithString:tempStr];
    [attri addAttribute:NSForegroundColorAttributeName value:[TKUIHelper colorWithHexString:@"#FFFFFF"] range:NSMakeRange(0, tempStr.length)];
    [attri addAttribute:NSFontAttributeName value:[UIFont fontWithName:@"PingFang SC" size:14] range:NSMakeRange(0, tempStr.length)];
    // 创建图片图片附件
    NSTextAttachment *attach = [[NSTextAttachment alloc] init];
    attach.image = [UIImage imageNamed:[NSString stringWithFormat:@"%@.bundle/Resources/TKOpenPlugin60026/tk_one_micphone.png", TK_OPEN_RESOURCE_NAME]];;
    attach.bounds = CGRectMake(0, -3, 12, 18);
    NSAttributedString *attachImg = [NSAttributedString attributedStringWithAttachment:attach];
    //将图片插入到合适的位置
    [attri insertAttributedString:attachImg atIndex:0];
    
    self.countDownLabel.attributedText = attri;
    self.countDownLabel.textAlignment=NSTextAlignmentCenter;
    float height=36;
    CGSize lableSize = [self.countDownLabel sizeThatFits:CGSizeMake(UISCREEN_HEIGHT, height)];
    float width=lableSize.width+20;
    float x=(self.TKWidth-width)/2.0f;
    float y=self.bottomShowTipView.TKBottom+6;
    self.countDownLabel.frame=CGRectMake(x, y, width, height);
    self.countDownLabel.backgroundColor=[TKUIHelper colorWithHexString:@"#398DE3" alpha:0.6f];
    self.countDownLabel.layer.cornerRadius=height/2.0f;
    self.countDownLabel.layer.masksToBounds=YES;
}

- (void)showNoAnswerPrompt
{
//    self.answerPromptLabel.text = @"未检测到您的回答，请再回答一次";
//    self.answerPromptLabel.backgroundColor = [TKUIHelper colorWithHexString:@"#FFFFFF"];
//    
//    float y = self.countDownLabel.TKBottom + 6;
//    self.answerPromptLabel.font = [UIFont fontWithName:@"PingFang SC" size:16];
//    CGSize lableSize = [self.answerPromptLabel sizeThatFits:CGSizeMake(self.bottomShowLabel.TKWidth, self.answerPromptLabel.font.lineHeight)];
//    float width = lableSize.width + 12;
//    float x = self.bottomShowLabel.TKLeft;
//    self.answerPromptLabel.frame=CGRectMake(x, y, width, lableSize.height);
//    
//    if (![self.mainColorString isEqualToString:@"#2772FE"]) {
//        self.answerPromptLabel.textColor=[TKUIHelper colorWithHexString:self.mainColorString];
//    }else{
//        self.answerPromptLabel.textColor=[TKUIHelper colorWithHexString:@"#2F85FF"];
//    }
//    
//    [self.bottomShowTipView addSubview:self.answerPromptLabel];
}

/**
 <AUTHOR> 2023年06月27日16:57:36
 @朗读单向场景标题文案
 @return 朗读单向场景标题文案
 */
-(void)changeToReadVideoLabel{
    [self addSubview:self.countDownLabel];
    
    NSString *tempStr = @"请使用普通话朗读";

    tempStr=[NSString stringWithFormat:@" %@",tempStr];
    NSMutableAttributedString *attri =[[NSMutableAttributedString alloc] initWithString:tempStr];
    [attri addAttribute:NSForegroundColorAttributeName value:[TKUIHelper colorWithHexString:@"#FFFFFF"] range:NSMakeRange(0, tempStr.length)];
    [attri addAttribute:NSFontAttributeName value:[UIFont fontWithName:@"PingFang SC" size:14] range:NSMakeRange(0, tempStr.length)];
    // 创建图片图片附件
    NSTextAttachment *attach = [[NSTextAttachment alloc] init];
    attach.image = [UIImage imageNamed:[NSString stringWithFormat:@"%@.bundle/Resources/TKOpenPlugin60026/tk_one_micphone.png", TK_OPEN_RESOURCE_NAME]];;
    attach.bounds = CGRectMake(0, -3, 12, 18);
    NSAttributedString *attachImg = [NSAttributedString attributedStringWithAttachment:attach];
    //将图片插入到合适的位置
    [attri insertAttributedString:attachImg atIndex:0];
    
    self.countDownLabel.attributedText = attri;
    self.countDownLabel.textAlignment=NSTextAlignmentCenter;
    float height=36;
    CGSize lableSize = [self.countDownLabel sizeThatFits:CGSizeMake(UISCREEN_HEIGHT, height)];
    float width=lableSize.width+20;
    float x=(self.TKWidth-width)/2.0f;
    float y=self.bottomShowTipView.TKBottom+6;
    self.countDownLabel.frame=CGRectMake(x, y, width, height);
    self.countDownLabel.backgroundColor=[TKUIHelper colorWithHexString:@"#398DE3" alpha:0.6f];
    self.countDownLabel.layer.cornerRadius=height/2.0f;
    self.countDownLabel.layer.masksToBounds=YES;
}

/**
 <AUTHOR> 2023年06月26日14:25:22
 @初始化单向视频朗读默认界面
 */
-(void)showReadTextView{
    [self.bottomShowLabel removeFromSuperview];
    _bottomShowLabel=nil;
    
    
    
    CGSize readLableSize;
    if ([TKStringHelper isEmpty:self.requestParam[@"longReadString"]]) {
        NSString *text;
        if ([TKStringHelper isNotEmpty:self.requestParam[@"readHtmlString"]]) {
            text=[TKCommonUtil switchLabelToSpan:self.requestParam[@"readHtmlString"] ];
        }else{
            text=self.requestParam[@"readString"];
        }
        self.readLable.textContainer.lineBreakMode =  NSLineBreakByWordWrapping;

        // 生成富文本
        
        // 计算一行的宽、高度(中文高度包括行高,需要清除换行再计算)
        NSMutableAttributedString *tempStr = [self convertTextToHtmlString:text textColor:@"#222222"];
        CGFloat gap=18.3;
        // 如果不是播放问题，展示多行问题文本
        // 采用固定高度，不再动态计算
        self.bottomShowTipView.TKHeight = 104;
        [self.readLable setTKHeight:104-gap*2];
        [self.readLable setTKTop:gap];
        self.readLable.textContainer.maximumNumberOfLines = INT_MAX;
        self.readLable.textContainerInset = UIEdgeInsetsMake(0, 12, 0, 12);
        self.readLable.font = [UIFont fontWithName:@"PingFang SC" size:16];
        self.readLable.attributedText = tempStr;
       
    

    }else{
        NSString *text=[TKCommonUtil switchLabelToSpan:self.requestParam[@"longReadString"] ];
        NSMutableAttributedString *tempStr = [self convertTextToHtmlString:text textColor:@"#222222"];
        self.readLable.textContainer.maximumNumberOfLines = INT_MAX;
        self.readLable.textContainerInset = UIEdgeInsetsMake(0, 12, 0, 12);
        self.readLable.font = [UIFont fontWithName:@"PingFang SC" size:16];
        self.readLable.attributedText = tempStr;
        readLableSize=CGSizeMake(self.bottomShowTipView.TKWidth, UISCREEN_HEIGHT/3.0f);
        
        CGFloat gap=18.3;
        // 如果不是播放问题，展示多行问题文本
        // 采用固定高度，不再动态计算
        self.bottomShowTipView.TKHeight = readLableSize.height+gap*2;
        [self.readLable setTKHeight:readLableSize.height];
        [self.readLable setTKTop:gap];
    }
    [self.readLable setTKWidth:self.bottomShowTipView.TKWidth];
    [self.bottomShowTipView addSubview:self.readLable];
    [self changeToReadVideoLabel];
}

/**
@Auther Vie 2021年03月08日13:19:01
@阅读文本引导
*/
-(void)readingGuideTip{
    NSString *readText;
    if ([TKStringHelper isNotEmpty:self.requestParam[@"readHtmlString"]]) {
        readText= [NSString stringWithFormat:@"%@", self.requestParam[@"readHtmlString"]];
    }else{
        readText= [NSString stringWithFormat:@"%@", self.requestParam[@"readString"]];
    }
    if ([TKStringHelper isEmpty:self.requestParam[@"longReadString"]]) {
        if ([TKStringHelper isNotEmpty:self.requestParam[@"readHtmlString"]]) {
            NSData *data = [readText dataUsingEncoding:NSUnicodeStringEncoding];
            NSDictionary *options = @{NSDocumentTypeDocumentAttribute: NSHTMLTextDocumentType};
            NSMutableAttributedString *html = [[NSMutableAttributedString alloc]initWithData:data options:options documentAttributes:nil error:nil];
            [html addAttribute:NSForegroundColorAttributeName value:[TKUIHelper colorWithHexString:@"#FCC006"] range:NSMakeRange(0, self.colorWordsNum)];
            self.readLable.attributedText = html;
        }else{
            NSMutableAttributedString *attributedStr = [[NSMutableAttributedString alloc]initWithString:readText];
            [attributedStr addAttribute:NSFontAttributeName value:[UIFont fontWithName:@"PingFang SC" size:16] range:NSMakeRange(0, readText.length)];
            [attributedStr addAttribute:NSForegroundColorAttributeName value:[TKUIHelper colorWithHexString:@"#396DE3"] range:NSMakeRange(0, self.colorWordsNum)];
            self.readLable.attributedText = attributedStr;
        }
        
        //计算是否要滚动换行
        CGSize labelSize=[self.readLable sizeThatFits:CGSizeMake(self.readLable.TKWidth, MAXFLOAT)];
        float diffY=labelSize.height-self.readLable.TKHeight;
        if (diffY>0) {
            NSRange rang=NSMakeRange(self.colorWordsNum, 1);
            [self.readLable scrollRangeToVisible:rang];
        }
        
        if (self.colorWordsNum < self.readLable.attributedText.string.length) {
            self.colorWordsNum++;
            //多久渐变一个字
            float colorWordTime=self.requestParam[@"colorWordTime"]?[self.requestParam[@"colorWordTime"] floatValue]:0.3f;
            dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(colorWordTime * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                [self readingGuideTip];
            });
        }

    }else{
//        //html长文本滚动不做处理
//        NSData *data = [self.requestParam[@"longReadString"] dataUsingEncoding:NSUnicodeStringEncoding];
//        NSDictionary *options = @{NSDocumentTypeDocumentAttribute: NSHTMLTextDocumentType};
//        NSAttributedString *html = [[NSAttributedString alloc]initWithData:data
//                                                                   options:options
//                                                        documentAttributes:nil
//                                                                     error:nil];
//        self.readLable.attributedText = html;
    }
    

    
}


/// 展示阅读文案
/// - Parameters:
///   - content: 阅读内容
///   - countdownTime: 倒计时时长
- (void)showWithContent:(NSString *)content countdownTime:(NSInteger)countdownTime type:(TKReadingType)type readTitle:(NSString *)readTitle readConfirmBtnTitle:(NSString *)readConfirmBtnTitle oneWordSpeed:(NSString *)oneWordSpeed {
    [self addSubview:self.readingView];
    [self.readingView showWithContent:content countdownTime:countdownTime type:type readTitle:readTitle readConfirmBtnTitle:readConfirmBtnTitle  oneWordSpeed:oneWordSpeed];
}

///// 是否展示录制按钮
///// @param isShow 是否展示
- (void)showTakeRecordBtn:(BOOL)isShow {
    if (self.takeBtn.superview == nil) [self addSubview:self.takeBtn];
    [self bringSubviewToFront:self.takeBtn];
    self.takeBtn.hidden = !isShow;
}

- (void)atuoStartTakeRecord:(int)countDown
{
    [self createAutoStartRecordTimer:countDown];
    [self updateTakeBtnWithCountDown:countDown];
}

- (void)updateTakeBtnWithCountDown:(int)countDown
{
    NSString *btnTitle;

    btnTitle=@"开始录制";
    
    if (countDown > 0) {
        btnTitle = [btnTitle stringByAppendingFormat:@"(%i)",countDown];
    }
    [_takeBtn setTitle:btnTitle forState:UIControlStateNormal];
}

- (void)createAutoStartRecordTimer:(NSTimeInterval)interval
{
    [self stopAutoStartRecordTimer];
    
    if (interval <= 0) return;
    
    // 设置开始录制超时定时器
    self.startAutoRecordTimer = [NSTimer timerWithTimeInterval:1 target:self selector:@selector(startAutoRecord:) userInfo:nil repeats:YES];
    [[NSRunLoop mainRunLoop] addTimer:self.startAutoRecordTimer forMode:NSRunLoopCommonModes];
    
    self.startAutoRecordCountDown = interval;
}

- (void)stopAutoStartRecordTimer {
    if (_startAutoRecordTimer) {
        [self.startAutoRecordTimer invalidate];
        self.startAutoRecordTimer = nil;
    }
}

- (void)startAutoRecord:(NSTimer *)timer
{
    TKLogInfo(@"思迪录制日志：就绪后达到时间，自动开始录制(%i)", self.startAutoRecordCountDown);
    if (self.startAutoRecordCountDown > 1) {
        self.startAutoRecordCountDown--;
        [self updateTakeBtnWithCountDown:self.startAutoRecordCountDown];
    } else {
        [self stopAutoStartRecordTimer];
        
        [self takeAction:self.takeBtn];
    }
}


#pragma mark - TKReadingViewDelegate
/**
 点击关闭按钮
 */
- (void)closeBtnDidClicked:(TKReadingView *)readingView{
    if (self.delegate&&[self.delegate respondsToSelector:@selector(goBack)]) {
        [self.readingView removeFromSuperview];
        self.readingView = nil;
        
        [self.delegate goBack];
    }
}

/**
 点击确认按钮
 */
- (void)confirmBtnDidClicked:(TKReadingView *)readingView {
    if (self.delegate && [self.delegate respondsToSelector:@selector(nextAction)]) {
        [self.readingView removeFromSuperview];
        self.readingView = nil;
        [self.delegate nextAction];
    }
}

#pragma mark lazyloading

/**
 <AUTHOR> 2019年04月03日10:47:55
 @初始化懒加载人像取景框矩阵
 @return 人像取景框矩阵
 */
-(CGRect)boxRect{
    if (CGRectIsEmpty(_boxRect)) {
        //因为是多问题，对准框需要固定不变位置，按文本4行高度117弄y坐标
        float boxRectX =12;
        float gap=64;
        //5s等小屏幕机型
        if (UISCREEN_WIDTH==320) {
            gap=54;
        }
        float boxRectWidth = self.TKWidth-boxRectX*2.0f;
        float boxRectHeight = boxRectWidth;
        float boxRectY = self.backBtn.TKBottom + gap + 104;
        _boxRect=CGRectMake(boxRectX, boxRectY, boxRectWidth, boxRectHeight);
    }
    return _boxRect;
}

///**
// <AUTHOR> 2019年04月03日10:57:34
// @初始化懒加载顶部遮罩层
// @return 顶部遮罩层
// */
//-(UIView *)topView{
//    if (!_topView) {
//        _topView=[[UIView alloc] initWithFrame:CGRectMake(0, 0, self.TKWidth, self.boxRect.origin.y)];
//        _topView.backgroundColor = [TKUIHelper colorWithHexString:@"#000000" alpha:0.7];
//    }
//    return _topView;
//}
//
//
///**
// <AUTHOR> 2019年04月03日11:20:41
// @初始化懒加载底部遮罩层
// @return 底部遮罩层
// */
//-(UIView *)bottomView{
//    if (!_bottomView) {
//        _bottomView=[[UIView alloc] initWithFrame:CGRectMake(0, self.boxRect.size.height+self.boxRect.origin.y, self.TKWidth, self.TKHeight-self.boxRect.origin.y-self.boxRect.size.height)];
//        _bottomView.backgroundColor = [TKUIHelper colorWithHexString:@"#000000" alpha:0.7];
//    }
//    return _bottomView;
//}
//
///**
// <AUTHOR> 2019年04月03日10:57:34
// @初始化懒加左部遮罩层
// @return 左部遮罩层
// */
//-(UIView *)leftView{
//    if (!_leftView) {
//        _leftView=[[UIView alloc] initWithFrame:CGRectMake(0, self.boxRect.origin.y, self.boxRect.origin.x, self.boxRect.size.height)];
//        _leftView.backgroundColor = [TKUIHelper colorWithHexString:@"#000000" alpha:0.7];
//    }
//    return _leftView;
//}
//
///**
// <AUTHOR> 2019年04月03日10:57:34
// @初始化懒加右部遮罩层
// @return 右部遮罩层
// */
//-(UIView *)rightView{
//    if (!_rightView) {
//        _rightView=[[UIView alloc] initWithFrame:CGRectMake(self.boxRect.origin.x+self.boxRect.size.width, self.boxRect.origin.y, self.TKWidth - CGRectGetMaxX(self.boxRect), self.boxRect.size.height)];
//        _rightView.backgroundColor = [TKUIHelper colorWithHexString:@"#000000" alpha:0.7];
//    }
//    return _rightView;
//}

/**
 <AUTHOR> 2019年12月30日22:05:40
 @初始化懒加载底部文档等提示展示区域
 @return 底部文档等提示展示区域
 */
-(UIView *)bottomShowTipView{
    if (!_bottomShowTipView) {

        _bottomShowTipView=[[UIView alloc] init];
        //子视图是否局限于视图的边界。
        _bottomShowTipView.clipsToBounds=YES;
        [_bottomShowTipView setBackgroundColor:[TKUIHelper colorWithHexString:@"#FFFFFF" alpha:0.9f]];
        
        float gap =6;
        float bottomShowTipViewX=12;

        float bottomShowTipViewY=self.backBtn.TKBottom+gap;
        float bottomShowTipViewWidth=self.TKWidth-2*bottomShowTipViewX;//左右留白15;
        _bottomShowTipView.layer.cornerRadius=10.0f;
        _bottomShowTipView.layer.maskedCorners = kCALayerMaxXMinYCorner|kCALayerMinXMaxYCorner|kCALayerMaxXMaxYCorner; // 只对左上和右上角的圆角有效
        _bottomShowTipView.frame=CGRectMake(bottomShowTipViewX, bottomShowTipViewY, bottomShowTipViewWidth, 104);
    }
    return _bottomShowTipView;
}


/**
 <AUTHOR> 2019年12月30日22:20:29
 @初始化懒加载底部文档等提示展示区域横线
 @return 底部文档等提示展示区域横线
 */
-(UIView *)bottomShowTipLineView{
    if (!_bottomShowTipLineView) {
        
        float width=255;
        float height=4;
        float y=self.TKHeight-50-height;
        float x=(self.TKWidth-width)/2.0f;
        _bottomShowTipLineView=[[UIView alloc] initWithFrame:CGRectMake(x, y, width, height)];
        _bottomShowTipLineView.backgroundColor=[TKUIHelper colorWithHexString:@"#FFFFFF " alpha:0.2f];
        _bottomShowTipLineView.layer.cornerRadius=height/2.0f;
    }
    return _bottomShowTipLineView;
}

/**
 <AUTHOR> 2019年12月30日22:20:29
 @初始化懒加载bottomShowTipRecordLineView
 @return bottomShowTipRecordLineView
 */
-(UIView *)bottomShowTipRecordLineView{
    if (!_bottomShowTipRecordLineView) {
        _bottomShowTipRecordLineView=[[UIView alloc] initWithFrame:CGRectMake(0, 0, 0, self.bottomShowTipLineView.TKHeight)];
        
        _bottomShowTipRecordLineView.layer.cornerRadius=self.bottomShowTipLineView.TKHeight/2.0f;
        if (![self.mainColorString isEqualToString:@"#2772FE"]) {
            
            [_bottomShowTipRecordLineView setBackgroundColor:[TKUIHelper colorWithHexString:self.mainColorString alpha:0.5f]];
        }else{
            
            [_bottomShowTipRecordLineView setBackgroundColor:[TKUIHelper colorWithHexString:@"#2772FE" alpha:0.5f]];
        }
    }
    return _bottomShowTipRecordLineView;
}


/**
 <AUTHOR> 2019年12月31日13:48:11
 @初始化懒加载底部文字展示
 @return 底部文字展示
 */
-(UITextView *)bottomShowLabel{
    if (!_bottomShowLabel) {
        
        _bottomShowLabel=[[UITextView alloc] init];
        _bottomShowLabel.font = [UIFont fontWithName:@"PingFang SC" size:16];
        _bottomShowLabel.textColor=[TKUIHelper colorWithHexString:@"#222222"];
        _bottomShowLabel.textAlignment=NSTextAlignmentLeft;
        _bottomShowLabel.clipsToBounds=YES;//子视图是否局限于视图的边界。

        _bottomShowLabel.backgroundColor=[UIColor clearColor];
        _bottomShowLabel.frame=CGRectMake(0, 0, self.bottomShowTipView.TKWidth, self.bottomShowTipView.TKHeight);
        _bottomShowLabel.backgroundColor=[UIColor clearColor];
        _bottomShowLabel.textContainerInset = UIEdgeInsetsMake(0, 0, 0, 0);
        [_bottomShowLabel setEditable:false];
        _bottomShowLabel.showsVerticalScrollIndicator = NO;
        _bottomShowLabel.showsHorizontalScrollIndicator = NO;
    }
    return _bottomShowLabel;
}


/**
 <AUTHOR> 2019年12月31日13:48:11
 @初始化懒加载提示layer
 @return 提示layer
 */
-(TKLayerView *)layerView{
    if (!_layerView) {
        _layerView=[[TKLayerView alloc] initContentView:self withBtnTextColor:nil cancelBtnTextColor:nil withWidth:self.boxRect.size.width withFont:[UIFont fontWithName:@"PingFangSC-Semibold" size:22]];
        
        [_layerView setShowTipDuration:0.6];
    }
    return _layerView;
}


/**
 <AUTHOR> 2019年04月01日14:48:01
 @初始化懒加载UIImageView人像取景框
 @return UIImageView人像取景框
 */
-(UIImageView *)boxImgView{
    if (!_boxImgView) {
        
        _boxImgView=[[UIImageView alloc] initWithFrame:self.boxRect];
        UIImage *img=[UIImage imageNamed:[NSString stringWithFormat:@"%@.bundle/Resources/TKOpenPlugin60005/video_head_box_5.1UI.png", TK_OPEN_RESOURCE_NAME]];
        
        //        if (![self.mainColorString isEqualToString:@"#2772FE"]) {
        //            img = [img imageWithRenderingMode:UIImageRenderingModeAlwaysTemplate];
        //            [_boxImgView setTintColor:[TKUIHelper colorWithHexString:self.mainColorString]];
        //        }
        [_boxImgView setImage:img];
    }
    return _boxImgView;
}

/**
 @初始化懒加载UIImageView人像取景背景框(外层的黑色边框)
 @return UIImageView人像取景背景框
 */
-(UIImageView *)boxImgBackgroundView{
    if (!_boxImgBackgroundView) {
        
        _boxImgBackgroundView = [[UIImageView alloc] initWithFrame:self.boxRect];
        [_boxImgBackgroundView setImage:[UIImage imageNamed:[NSString stringWithFormat:@"%@.bundle/Resources/TKOpenPlugin60005/video_head_box_5.1UI.png", TK_OPEN_RESOURCE_NAME]]];
    }
    return _boxImgBackgroundView;
}

/**
 <AUTHOR> 2019年04月03日14:52:07
 @初始化懒加载返回按钮
 @return 返回按钮
 */
-(UIButton *)backBtn{
    if (!_backBtn) {
        
        float backBtnWidth=32;
        float backBtnheight=32;
        float backBtnX=20.0f;
        float backBtnY=6+STATUSBAR_HEIGHT;
        _backBtn=[[UIButton alloc] initWithFrame:CGRectMake(backBtnX, backBtnY, backBtnWidth, backBtnheight)];
        _backBtn.clipsToBounds = YES;
        [_backBtn setImage:[UIImage imageNamed:[NSString stringWithFormat:@"%@.bundle/Resources/TKOpenGeneral/tk_open_back.png", TK_OPEN_RESOURCE_NAME]] forState:UIControlStateNormal];
        [_backBtn setImageEdgeInsets:UIEdgeInsetsMake(0, -2, 0, 2)]; // 图片往右偏了，需要往左偏回来
        
        [_backBtn setBackgroundColor:[TKUIHelper colorWithHexString:@"#666666" alpha:0.3f]];
        _backBtn.layer.cornerRadius = backBtnWidth/2.0f;
        
        [_backBtn addTarget:self action:@selector(backAction:) forControlEvents:UIControlEventTouchUpInside];
    }
    return _backBtn;
}





/**
 <AUTHOR> 2019年04月26日18:55:02
 @初始化懒加载回答倒计时展示label
 @return 回答倒计时展示label
 */
-(UILabel *)countDownLabel{
    if (!_countDownLabel) {
        _countDownLabel=[[UILabel alloc] init];
    }
    return _countDownLabel;
}

/**
 <AUTHOR> 2020年08月26日10:52:52
 @初始化懒加载takeBtn
 @return takeBtn
 */
-(UIButton *)takeBtn{
    if (!_takeBtn) {
        float x=32;
        float height=44;
        float width=self.TKWidth-2*x;
        float y=self.TKHeight-height-10-IPHONEX_BUTTOM_HEIGHT;
        
        
        _takeBtn=[[UIButton alloc] initWithFrame:CGRectMake(x, y, width, height)];
        [self updateTakeBtnWithCountDown:0];
        
        _takeBtn.titleLabel.font=[UIFont fontWithName:@"PingFang SC" size:18];
        [_takeBtn addTarget:self action:@selector(takeAction:) forControlEvents:UIControlEventTouchUpInside];
        _takeBtn.layer.cornerRadius=height/2.0f;
        
        // 默认一开始不可用
        [self enableTakeRecord:NO];
    }
    return _takeBtn;
}


- (UIButton *)nextBtn{
    if (!_nextBtn) {
        
        _nextBtn = [[UIButton alloc] initWithFrame:self.takeBtn.frame];
        NSString *nextBtnTitle = @"继续播报";
        [_nextBtn setTitle:nextBtnTitle forState:UIControlStateNormal];
        
        _nextBtn.titleLabel.font = self.takeBtn.titleLabel.font;
        [_nextBtn addTarget:self action:@selector(nextAction:) forControlEvents:UIControlEventTouchUpInside];
        _nextBtn.layer.cornerRadius = self.takeBtn.TKHeight * 0.5;
        
        _nextBtn.layer.borderWidth = 0.0f;
        _nextBtn.layer.borderColor = [ UIColor clearColor].CGColor;
        [_nextBtn setTitleColor:[TKUIHelper colorWithHexString:@"#FFFFFF"] forState:UIControlStateNormal];
        _nextBtn.backgroundColor = [TKUIHelper colorWithHexString:[self.mainColorString isEqualToString:@"#2772FE"]?@"#EE393E":self.mainColorString];
    }
    return _nextBtn;
}

/**
 <AUTHOR> 2020年08月26日10:52:52
 @初始化懒加载语音回答结果提示
 @return 语音回答结果提示
 */
- (UILabel *)answerPromptLabel{
    if (!_answerPromptLabel) {
        _answerPromptLabel = [[UILabel alloc] init];
        _answerPromptLabel.textAlignment=NSTextAlignmentCenter;
        _answerPromptLabel.font=[UIFont fontWithName:@"PingFang SC" size:22];
    }
    return _answerPromptLabel;
}

- (UIView *)badgeView {
    if (!_badgeView) {
        NSInteger const pointWidth = 6; //小红点的宽高
        CGRect frame = CGRectMake(self.recordTimeLabel.TKLeft - pointWidth, self.recordTimeLabel.TKTop + (self.recordTimeLabel.TKHeight - pointWidth) * 0.5, pointWidth, pointWidth);
        _badgeView = [[UILabel alloc] initWithFrame:frame];
        _badgeView.backgroundColor = [TKUIHelper colorWithHexString:@"ff5153"];
        //圆角为宽度的一半
        _badgeView.layer.cornerRadius = pointWidth / 2;
        //确保可以有圆角
        _badgeView.layer.masksToBounds = YES;
        
    }
    
    return _badgeView;
}

/**
 <AUTHOR> 2019年04月03日14:52:07
 @初始化懒加载recordTimeLabel
 @return recordTimeLabel
 */
-(UILabel *)recordTimeLabel{
    if (!_recordTimeLabel) {
        float y=6+STATUSBAR_HEIGHT;
        //        float width=96;
        float width=43;
        float height=32;
        float x=(self.frame.size.width-width)/2;
        _recordTimeLabel=[[UILabel alloc] initWithFrame:CGRectMake(x, y, width, height)];
        _recordTimeLabel.text=@"00:00";
        _recordTimeLabel.textColor=[TKUIHelper colorWithHexString:@"#FFFFFF"];
        _recordTimeLabel.textAlignment=NSTextAlignmentCenter;
        _recordTimeLabel.font = [UIFont fontWithName:@"PingFang SC" size:14];;
        
    }
    return _recordTimeLabel;
}


- (void)setAvPreviewView:(UIView *)avPreviewView {
    _avPreviewView = avPreviewView;
    [self insertSubview:avPreviewView atIndex:0];
}

- (TKFaceDectTipView *)faceDectTipView {
    if (!_faceDectTipView) {
        _faceDectTipView = [[TKFaceDectTipView alloc] initWithFrame:self.boxRect];
//        _faceDectTipView.isEnhance = YES;
    }
    
    return _faceDectTipView;
}

/**
 <AUTHOR> 2020年02月28日10:50:51
 @初始化懒加载结束录制按钮
 @return 结束录制按钮
 */
-(UIButton *)endBtn{
    if (!_endBtn) {
        float x=32;
        float height=44;
        float width=self.TKWidth-2*x;
        float y=self.TKHeight-height-10-IPHONEX_BUTTOM_HEIGHT;
        
        
        _endBtn=[[UIButton alloc] initWithFrame:CGRectMake(x, y, width, height)];
        
        [_endBtn setTitle:@"完成录制" forState:UIControlStateNormal];
        [_endBtn setTitleColor:[TKUIHelper colorWithHexString:@"#FFFFFF" alpha:0.7f] forState:UIControlStateNormal];
 
        _endBtn.layer.cornerRadius=height/2.0f;
        _endBtn.layer.borderWidth=0.0f;
        _endBtn.layer.borderColor=[UIColor clearColor].CGColor;
        [_endBtn setTitleColor:[TKUIHelper colorWithHexString:@"#FFFFFF"] forState:UIControlStateNormal];
        _endBtn.backgroundColor=[TKUIHelper colorWithHexString:[self.mainColorString isEqualToString:@"#2772FE"]?[TKCommonUtil blendWithWhite:@"#F79DA0" ratio:0.28f]:self.mainColorString];
        [_endBtn addTarget:self action:@selector(endAction:) forControlEvents:UIControlEventTouchUpInside];
    }
    return _endBtn;
}

/**
 <AUTHOR> 2020年01月02日10:16:18
 @初始化懒加载倒计时文本
 @return 倒计时文本
 */
-(UILabel *)waitTipLabel{
    if (!_waitTipLabel) {
        float width=152;
        _waitTipLabel=[[UILabel alloc] initWithFrame:CGRectMake(0, 0, width, width)];
        _waitTipLabel.layer.cornerRadius=width/2;
        _waitTipLabel.backgroundColor=[TKUIHelper colorWithHexString:@"#000000" alpha:0.5];
        _waitTipLabel.textColor=[UIColor whiteColor];
        _waitTipLabel.center=self.boxImgView.center;
        _waitTipLabel.textAlignment=NSTextAlignmentCenter;
        _waitTipLabel.font=[UIFont fontWithName:@"PingFangSC-Medium" size:40];
        _waitTipLabel.clipsToBounds = YES;
    }
    return _waitTipLabel;
}

/**
 <AUTHOR> 2020年02月27日21:21:26
 @初始化懒加载阅读文本
 @return 阅读文本
 */
-(UITextView *)readLable{
    if (!_readLable) {
        _readLable=[[UITextView alloc] init];
        _readLable.font =[UIFont fontWithName:@"PingFang SC" size:16];
        _readLable.textAlignment=NSTextAlignmentLeft;
        _readLable.backgroundColor=[UIColor clearColor];
        _readLable.textColor =[TKUIHelper colorWithHexString:[self.mainColorString isEqualToString:@"#2772FE"]?@"#51B4FE":self.mainColorString];
        _readLable.textContainerInset=UIEdgeInsetsMake(5, -5, 0, -5);
        [_readLable setEditable:false];
    }
    return _readLable;
}


/**
 *  <AUTHOR> 2023年07月10日13:30:12
 *  @初始化懒加载topBgView
 *  @return  topBgView
 */
-(UIView *)topBgView{
    if (!_topBgView) {
        float gap =31;
        //5s等小屏幕机型
        if (UISCREEN_WIDTH==320) {
            gap=22;
        }
        float height=self.backBtn.TKBottom+gap;
        float width=self.TKWidth;
        float x=0;
        float y=0;
        _topBgView=[[UIView alloc] initWithFrame:CGRectMake(x, y, width, height)];
        NSMutableArray *btoColors = [NSMutableArray arrayWithObjects:(id)[TKUIHelper colorWithHexString:@"#000000" alpha:0.5f].CGColor,(id)[TKUIHelper colorWithHexString:@"#000000" alpha:0.00f].CGColor, nil];
        CAGradientLayer *btoGradientLayer = [CAGradientLayer layer];

        btoGradientLayer.frame = CGRectMake(0, 0, width, height);
        btoGradientLayer.startPoint = CGPointMake(0.5, 0);
        btoGradientLayer.endPoint = CGPointMake(0.5, 1);
        [btoGradientLayer setColors:[NSArray arrayWithArray:btoColors]];
        [_topBgView.layer addSublayer:btoGradientLayer]; //设置颜色渐变
    }
    return _topBgView;
}


/**
 *  <AUTHOR> 2023年07月10日13:30:12
 *  @初始化懒加载bottomBgView
 *  @return  bottomBgView
 */
-(UIView *)bottomBgView{
    if (!_bottomBgView) {
        float gap =31;
        //5s等小屏幕机型
        if (UISCREEN_WIDTH==320) {
            gap=22;
        }
        float height=self.TKHeight-self.bottomShowTipLineView.TKTop+20;
        float width=self.TKWidth;
        float x=0;
        float y=self.TKHeight-height;
        _bottomBgView=[[UIView alloc] initWithFrame:CGRectMake(x, y, width, height)];
        NSMutableArray *btoColors = [NSMutableArray arrayWithObjects:(id)[TKUIHelper colorWithHexString:@"#000000" alpha:0.00f].CGColor,(id)[TKUIHelper colorWithHexString:@"#000000" alpha:0.5f].CGColor, nil];
        CAGradientLayer *btoGradientLayer = [CAGradientLayer layer];

        btoGradientLayer.frame = CGRectMake(0, 0, width, height);
        btoGradientLayer.startPoint = CGPointMake(0.5, 0);
        btoGradientLayer.endPoint = CGPointMake(0.5, 1);
        [btoGradientLayer setColors:[NSArray arrayWithArray:btoColors]];
        [_bottomBgView.layer addSublayer:btoGradientLayer]; //设置颜色渐变
    }
    return _bottomBgView;
}

- (TKReadingView *)readingView {
    if (!_readingView) {
        NSMutableDictionary *param=[[NSMutableDictionary alloc] init];
        param[@"readAll"]=@"1";
        TKReadingView *readingView = [[TKReadingView alloc] initWithFrame:self.bounds param:param];
        _readingView = readingView;
        _readingView.delegate = self;
//        _readingView.autoScroll = YES;
    }
    return _readingView;
}



/**
 <AUTHOR> 2024年08月29日10:04:03
 @初始化懒问题进度提示文本
 @return 问题进度提示文本
 */
-(UILabel *)questionProgressLabel{
    if (!_questionProgressLabel) {
        float height=18;
        float width=52;
        float x=0;
        float y=-(height-self.bottomShowTipLineView.TKHeight)/2.0f;
        _questionProgressLabel=[[UILabel alloc] initWithFrame:CGRectMake(x, y, width, height)];
        _questionProgressLabel.font =[UIFont fontWithName:@"PingFang SC" size:10];
        _questionProgressLabel.textAlignment=NSTextAlignmentCenter;
        if (![self.mainColorString isEqualToString:@"#2772FE"]) {
            
            [_questionProgressLabel setBackgroundColor:[TKUIHelper colorWithHexString:self.mainColorString]];
        }else{
            
            [_questionProgressLabel setBackgroundColor:[TKUIHelper colorWithHexString:@"#2772FE"]];
        }
        _questionProgressLabel.textColor =[TKUIHelper colorWithHexString:@"#ffffff"];
        _questionProgressLabel.layer.cornerRadius=11;
        _questionProgressLabel.layer.masksToBounds=YES;
    }
    return _questionProgressLabel;
}


/**
 *  <AUTHOR> 2025年07月14日10:11:47
 *  @初始化懒加载videoWitnessLabel
 *  @return  videoWitnessLabel
 */
-(UILabel *)videoWitnessLabel{
    if (!_videoWitnessLabel) {
        float x=self.backBtn.TKRight+16;
        float y=STATUSBAR_HEIGHT;
        float width=75;
        float height=NAVBAR_HEIGHT;
        _videoWitnessLabel = [[UILabel alloc] initWithFrame:CGRectMake(x, y, width, height)];
        _videoWitnessLabel.text= @"视频见证中";
        _videoWitnessLabel.font= [UIFont fontWithName:@"PingFang SC" size:14];
        _videoWitnessLabel.textColor = [TKUIHelper colorWithHexString:@"#ffffff"];
        _videoWitnessLabel.textAlignment=NSTextAlignmentLeft;
    }
    return _videoWitnessLabel;
}

/**
 *  <AUTHOR> 2025年07月14日10:07:23
 *  @初始化懒加载dontLeaveLabel
 *  @return  dontLeaveLabel
 */
-(UILabel *)dontLeaveLabel{
    if (!_dontLeaveLabel) {
        float y=STATUSBAR_HEIGHT;
        float width=60;
        float height=NAVBAR_HEIGHT;
        float x=(self.topBgView.TKWidth-width)/2.0f;
        _dontLeaveLabel = [[UILabel alloc] initWithFrame:CGRectMake(x, y, width, height)];
        _dontLeaveLabel.text= @"请勿离开";
        _dontLeaveLabel.font= [UIFont fontWithName:@"PingFang SC" size:14];
        _dontLeaveLabel.textColor = [TKUIHelper colorWithHexString:@"#ffffff"];
        _dontLeaveLabel.textAlignment=NSTextAlignmentCenter;
    }
    return _dontLeaveLabel;
}


/**
 <AUTHOR> 2025年07月16日13:59:35
 @初始化懒加载UIImageView对准框中间提示图标
 @return UIImageView对准框中间提示图标
 */
-(UIImageView *)boxTipImgView{
    if (!_boxTipImgView) {
        float width=120;
        float height=120;
        float y=60;
        float x=(self.boxRect.size.width-width)/2.0f;
        _boxTipImgView = [[UIImageView alloc] initWithFrame:CGRectMake(x, y, width, height)];
        [_boxTipImgView setImage:[UIImage imageNamed:[NSString stringWithFormat:@"%@.bundle/Resources/TKOpenPlugin60026/tk_one_video_wait.png", TK_OPEN_RESOURCE_NAME]]];
        _boxTipImgView.contentMode=UIViewContentModeScaleAspectFit;
    }
    return _boxTipImgView;
}


/**
 * <AUTHOR> 2025年07月16日13:59:46
 *  @初始化懒加载对准框中间提示文本
 *  @return  对准框中间提示文本
 */
-(UILabel *)boxTipLabel{
    if (!_boxTipLabel) {
        
        float width = self.boxRect.size.width;
        float x = 0;
        float height = 30;
        float y=self.boxTipImgView.TKBottom+10;
        _boxTipLabel=[[UILabel alloc] initWithFrame:CGRectMake(x, y, width, height)];
        _boxTipLabel.text = @"请保持人脸在视频框内";
        _boxTipLabel.textColor=[TKUIHelper colorWithHexString:@"#ffffff"];
        _boxTipLabel.font = [UIFont fontWithName:@"PingFang SC" size:20];
        _boxTipLabel.numberOfLines = 0;
        _boxTipLabel.textAlignment = NSTextAlignmentCenter;
    }
    return _boxTipLabel;
}
@end
