//
//  TKBaseVideoRecordLandscapeView.m
//  TKOpenAccount-Standard
//
//  Created by <PERSON> on 2022/11/11.
//  Copyright © 2022 thinkive. All rights reserved.
//

#import "TKBaseVideoRecordLandscapeView.h"
#import "TKFaceDectTipView.h"


#define TK_ONEVIEW_PIECE_COLOR  [UIColor colorWithRed:0/255.0 green:13/255.0 blue:41/255.0 alpha:0.85/1.0]
#define TK_ONEVIEW_TIP_LABEL_COLOR [TKUIHelper colorWithHexString:@"#3CCDFF"]
#define TK_WAIT_COUNT_DOWN 0.9f
#define BottomShowLabelLineHeight  4.0f

@interface TKBaseVideoRecordLandscapeView()

@property (nonatomic, strong) TKLayerView  *layerView;//提示layer

@property (nonatomic, assign) float answerCount;//回答倒计时默认5秒
@property (nonatomic, assign) int recordCountDown;//回答倒计时3秒

@property (nonatomic, assign) BOOL isFinished;//是否回答了问题

@property (nonatomic, strong) NSTimer *recordDownTimer;

@property (nonatomic, assign) int changeReadTextViewWords; // 变色的字数
@property (nonatomic, assign) float wordSpeedDuration; // 文字滚动速度
@property (nonatomic, readwrite, strong) NSTimer *changeReadTextViewOffSetTimer; // testView滚动定时器

@property (nonatomic, readwrite, strong) TKFaceDectTipView *faceDectTipView; // 质检错误提示框
@property (nonatomic, readwrite, assign) TKCountDownType countDownType; // 倒计时类型
@property (nonatomic, readwrite, assign) BOOL hasAsrResult; // 是否已有asr结果

@property (nonatomic , strong) UIImageView *ltImageView;//左上角
@property (nonatomic , strong) UIImageView *rtImageView;//右上角
@property (nonatomic , strong) UIImageView *lbImageView;//左下角
@property (nonatomic , strong) UIImageView *rbImageView;//右下角

@property (nonatomic, readwrite, strong) NSTimer *startAutoRecordTimer; // 开始自动录制定时器，防止不做录制占用时间过长
@property (nonatomic, readwrite, assign) int startAutoRecordCountDown; // 开始自动录制倒计时

@end

@implementation TKBaseVideoRecordLandscapeView
@synthesize boxRect = _boxRect;
@synthesize requestParam;
@synthesize takeBtn = _takeBtn;
@synthesize delegate = _delegate;
@synthesize answerPromptImgBg = _answerPromptImgBg;
@synthesize answerPromptLabel = _answerPromptLabel;
@synthesize nextBtn = _nextBtn;
@synthesize avPreviewView = _avPreviewView;

-(instancetype)initWithFrame:(CGRect)frame withParam:(NSMutableDictionary *)param{
    self=[super initWithFrame:frame];
    if (self) {
        self.requestParam=param;
        
        if ([TKStringHelper isEmpty:param[@"mainColor"]]) {
            self.mainColorString=@"#2772FE";
        }else{
            self.mainColorString=param[@"mainColor"];
        }
        
        [self viewInit];
    }
    return self;
}

/**
 <AUTHOR> 2019年04月03日09:13:34
 @初始化单向视频默认界面
 */
- (void)viewInit{

    [self addSubview:self.topView];
    [self addSubview:self.bottomView];
    [self addSubview:self.leftView];
    [self addSubview:self.rightView];
    [self addSubview:self.backBtn];
    
    [self addSubview:self.ltImageView];
    [self addSubview:self.rtImageView];
    [self addSubview:self.lbImageView];
    [self addSubview:self.rbImageView];

    [self addSubview:self.recordTimeLabel];
    if([self.requestParam getIntWithKey:@"isNeedSwitchCamera"]==1){
        [self addSubview:self.switchCameraBtn];
    }
    
//    [self addSubview:self.bottomShowTipView];
//    [self.bottomShowTipView addSubview:self.bottomShowLabel];
    [self addSubview:self.takeBtn];
    
    
    // 默认隐藏下一步按钮
    [self showNextBtn:NO btnTitle:nil];
}


#pragma mark 事件方法

/**
 @Auther Vie 2020年02月27日17:45:23
 @param sender 拍照事件
 */
-(void)takeAction:(UIButton *)sender{
    [self addSubview:self.bottomShowTipLineView];
    [self.bottomShowTipLineView addSubview:self.bottomShowTipRecordLineView];
    
    if (self.delegate&&[self.delegate respondsToSelector:@selector(takeRecord)]) {
        [self.takeBtn removeFromSuperview];
        self.takeBtn = nil;
        [self.delegate takeRecord];
    }
}


/**
 @Auther Vie 2019年04月08日10:46:51

 @param sender 返回按钮点击事件
 */
-(void)backAction:(UIButton *)sender{
    if (self.delegate&&[self.delegate respondsToSelector:@selector(goBack)]) {
        [self.delegate goBack];
    }
}


/**
 @Auther Vie 2020年02月27日17:45:23
 @param sender 拍照事件
 */
-(void)nextAction:(UIButton *)sender{
    
    if (self.delegate && [self.delegate respondsToSelector:@selector(nextAction)]) {
        [self.nextBtn removeFromSuperview];
        self.nextBtn = nil;
        [self.delegate nextAction];
    }
}

/**
 @Auther Vie 2023年02月17日17:02:43

 @param sender 切换摄像头
 */
-(void)switchCameraAction:(UIButton *)sender{
    if (self.delegate&&[self.delegate respondsToSelector:@selector(switchVideoCamera)]) {
        [self.delegate switchVideoCamera];
    }
}


/**
 <AUTHOR> 2019年05月23日13:37:30
 @活体完成话术播报等待界面
 */
-(void)liveEndWait{
    
    [self liveEndWait:@"请您保持全脸在人像框内。"];
}

- (void)liveEndWait:(NSString *)str {
    
    [self addSubview:self.bottomShowTipView];
    [self.bottomShowTipView addSubview:self.bottomShowLabel];
    _bottomShowLabel.textContainerInset = UIEdgeInsetsMake(5, 0, 5, 0);
    _bottomShowLabel.font = [UIFont fontWithName:@"PingFang SC" size:22];
//    self.bottomShowLabel.text=@"非常好，视频录制马上开始。";
    self.bottomShowLabel.text = str;
    
}


/**
 <AUTHOR> 2019年04月15日15:15:19
 @活体警告
 */
-(void)liveWarning:(NSString *)warningSting{

    [self liveWarning:warningSting forceDisplay:NO];
}

/**
 <AUTHOR> 2019年04月15日15:15:19
 @活体警告
 */
-(void)liveWarning:(NSString *)warningSting forceDisplay:(BOOL)forceDisplay {
    
    if (!_faceDectTipView) {
        [self addSubview:self.faceDectTipView];
    }

    NSTimeInterval faceDetectInterval = 1.0;
    if ([self.requestParam[@"faceDetectInterval"] doubleValue] > 0) {
        faceDetectInterval = [self.requestParam[@"faceDetectInterval"] doubleValue];
    }
    
    CGFloat maxWidth = self.TKWidth - (self.bottomShowTipView.TKLeft - self.avPreviewView.TKLeft) * 2;
    [self.faceDectTipView showWarning:warningSting forceDisplay:forceDisplay displayTime:faceDetectInterval maxWidth:maxWidth y:0];
}


/**
 <AUTHOR> 2019年04月15日15:22:00
 @活体继续识别
 */
- (void)liveContinue:(NSString *)string isHtmlString:(BOOL)htmlFlag questionOneWordSpeed:(NSString *)questionOneWordSpeed {
    //    [self.spectrumView stop];

    self.bottomShowTipView.hidden = [TKStringHelper isEmpty:string];
    
//    [self addSubview:self.answerRecordView];
    [self updateTipLabel:[NSString stringWithFormat:@"%@",string] textColor:[self.mainColorString isEqualToString:@"#2772FE"]?@"#51B4FE":self.mainColorString cornerRadius:20.0f isOneLineShow:YES isHtmlString:htmlFlag questionOneWordSpeed:questionOneWordSpeed];
    self.isFinished=NO;
}


/**
 <AUTHOR> 18:13:29
 @视频录制中播放的语音要修改图界面展示波动图
 */
- (void)startRecorderVideoPlay:(NSString *)questionString isOneLineShow:(BOOL)isOneLineShow isHtmlString:(BOOL)htmlFlag questionOneWordSpeed:(NSString *)questionOneWordSpeed{
    
    [self.answerPromptLabel removeFromSuperview];
    [self.answerPromptImgBg removeFromSuperview];
    [self.countDownLabel removeFromSuperview];
    self.countDownLabel = nil;
    self.answerPromptLabel = nil;
    self.answerPromptImgBg = nil;
    self.answerCount = 0;
    self.isFinished = YES;
    self.countDownType = TKCountDownTypeUnknown;
    
    self.bottomShowTipView.hidden = [TKStringHelper isEmpty:questionString];
    
    [self updateTipLabel:questionString textColor:[self.mainColorString isEqualToString:@"#2772FE"]?@"#51B4FE":self.mainColorString cornerRadius:20.0f isOneLineShow:NO isHtmlString:htmlFlag questionOneWordSpeed:questionOneWordSpeed];
    
    [self.serviceGifView setImageByName:[NSString stringWithFormat:@"%@.bundle/Resources/TKOpenPlugin60026/serviceSpeak.gif", TK_OPEN_RESOURCE_NAME]];
}


/**
 <AUTHOR> 2019年04月26日19:12:30
 @回答完毕（识别到了是或否）
 */
- (void)answered:(BOOL)isAnswered displayTime:(int)displayTime {
    
    self.isFinished=YES;
    
    // 回答了YES
    if (isAnswered) {

        // 延迟1s是为了展示语音识别结果
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(displayTime * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            
            self.answerPromptLabel.hidden = YES;
            self.answerPromptImgBg.hidden = YES;
        });
    }
}


/**
<AUTHOR> 2020年01月07日19:02:54
@语音问题播放完成停止波动动画
*/
-(void)playEndVoiceView{
//    [self.fluctuateGifView removeFromSuperview];
//    self.fluctuateGifView=nil;

}


/**
 <AUTHOR> 2019年04月16日20:09:51
 @语音合成播放完成，修改界面
 */
- (void)playEndView:(int)waitTime prompt:(NSString *)string noAnswerPromptTime:(int)noAnswerPromptTime {
    if (waitTime<=0) {
        waitTime=5;
    }
    
    [self.serviceGifView setImageByName:[NSString stringWithFormat:@"%@.bundle/Resources/TKOpenPlugin60026/serviceListen.gif", TK_OPEN_RESOURCE_NAME]];
    
    self.answerCount=waitTime;
    self.hasAsrResult = NO;
    [self updateCountDownLabelText:waitTime countDownType:TKCountDownTypeAnswer];
    [self.bottomShowTipView addSubview:self.countDownLabel];
    
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1.0f * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        [self answerCountDown:waitTime noAnswerPromptTime:noAnswerPromptTime];
    });
}


/**
 <AUTHOR> 2019年04月26日18:39:08
 @问题回答倒计时
 */
- (void)answerCountDown:(int)waitTime noAnswerPromptTime:(int)noAnswerPromptTime {
    
    __weak typeof(self) weakSelf = self;
    
    if (!self.isFinished) {
        self.answerCount = self.answerCount-1;
        
        noAnswerPromptTime = noAnswerPromptTime <= 0 ? 3 : noAnswerPromptTime;
        if ((waitTime - self.answerCount >= noAnswerPromptTime) && self.hasAsrResult == NO) {
            [self showNoAnserPrompt];
        }
       
        [self updateCountDownLabelText:self.answerCount countDownType:TKCountDownTypeAnswer];
        
        if (self.answerCount>=3) {
            
            dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1.0f * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                [weakSelf answerCountDown:waitTime noAnswerPromptTime:noAnswerPromptTime];
            });

        }else if(self.answerCount<3&&self.answerCount>0){

            dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1.0f * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                [weakSelf answerCountDown:waitTime noAnswerPromptTime:noAnswerPromptTime];
            });
        }else{
            
            if (self.delegate&&[self.delegate respondsToSelector:@selector(answerCountDownEnd)]) {
                [self.delegate answerCountDownEnd];
            }

//            [self.countDownLabel removeFromSuperview];
        }
    }else{

//        [self.countDownLabel removeFromSuperview];
    }
}

- (void)recordLongTime:(int)recordTime startRecord:(BOOL)startRecord {
    
    self.recordTimeLabel. backgroundColor = startRecord ? [TKUIHelper colorWithHexString:@"#FF4848"] : [UIColor clearColor];
    NSDateFormatter *formatter = [[NSDateFormatter alloc] init];
    formatter.locale = [[NSLocale alloc] initWithLocaleIdentifier:@"zh_CN"];
    if (recordTime/3600 >= 1) {
        [formatter setDateFormat:@"HH:mm:ss"];
    } else {
          [formatter setDateFormat:@"mm:ss"];
    }

    NSDate *d = [NSDate dateWithTimeIntervalSince1970:recordTime];
    self.recordTimeLabel.text = [formatter stringFromDate:d];
    
    // 大于最短录制时间可以点击完成录制
    if (recordTime >= [self.requestParam[@"shortestTime"] intValue]) {
        [self enableFinishTakeRecord:YES];
    }
}

/**
@Auther Vie 2020年02月28日12:48:01
@param recordTime 当前录制时间
@param longestTime 最长录制时间
*/
-(void)recordTime:(int)recordTime longestTime:(int)longestTime {
    self.recordTimeLabel. backgroundColor =recordTime!=0?[TKUIHelper colorWithHexString:@"#FF4848"]: [UIColor clearColor];
    NSDateFormatter *formatter = [[NSDateFormatter alloc] init];
    formatter.locale = [[NSLocale alloc] initWithLocaleIdentifier:@"zh_CN"];

    int remaining = longestTime - recordTime; // 剩余时间
    if (remaining / 3600 >= 1) {
        [formatter setDateFormat:@"HH:mm:ss"];
    } else {
        [formatter setDateFormat:@"mm:ss"];
    }
    
    NSDate *d = [NSDate dateWithTimeIntervalSince1970:remaining];
    self.recordTimeLabel.text = [formatter stringFromDate:d];
    
    // 大于最短录制时间可以点击完成录制
    if (recordTime >= [self.requestParam[@"shortestTime"] intValue]) {
        [self enableFinishTakeRecord:YES];
    }
    
}


/**
@Auther Vie 2020年03月10日14:46:52
@隐藏中间准备提示文本
*/
-(void)hideWaitTip{

    [self.recordTimeLabel setHidden:NO];//展示录制倒计时
}

/// 设置是否可以点击开始录制
/// @param isEnable 是否可以点击
- (void)enableTakeRecord:(BOOL)isEnable {
    
    self.takeBtn.enabled = isEnable;
    if (isEnable) {
        _takeBtn.layer.borderWidth=0.0f;
        _takeBtn.layer.borderColor=[UIColor clearColor].CGColor;
        [_takeBtn setTitleColor:[TKUIHelper colorWithHexString:@"#FFFFFF"] forState:UIControlStateNormal];
        _takeBtn.backgroundColor=[TKUIHelper colorWithHexString:[self.mainColorString isEqualToString:@"#2772FE"]?@"#2772FE":self.mainColorString];
    } else {
        _takeBtn.layer.borderWidth=1.0f;
        _takeBtn.layer.borderColor=[TKUIHelper colorWithHexString:@"#BDDBFA" alpha:1.0f].CGColor;
        [_takeBtn setTitleColor:[TKUIHelper colorWithHexString:@"#BDDBFA" alpha:1.0f] forState:UIControlStateNormal];
    }
}

/**
<AUTHOR> 2021年07月08日10:30:58
@修改提示语问题话术
@string 文本
@colorString 文本颜色
@cornerRadius 背景框圆角
@flag 是否一行展示
@flag 是否是html文本
@return 顶部遮罩层
*/
-(void)updateTipLabel:(NSString *)string textColor:(NSString *)colorString cornerRadius:(CGFloat)cornerRadius isOneLineShow:(BOOL)isOneLineShow isHtmlString:(BOOL)htmlFlag questionOneWordSpeed:(NSString *)questionOneWordSpeed {
    
//    TKLogDebug(@"---------oneLineWidth ponitSize = %.2f, string= %@", self.bottomShowLabel.font.pointSize, string);
    string = [TKCommonUtil switchLabelToSpan:string];
    
    // 生成富文本
    NSMutableAttributedString *attStr = [self convertTextToHtmlString:string textColor:colorString];
    
    // 不重复处理
    if ([self.bottomShowLabel.text isEqualToString:string]
        || [self.bottomShowLabel.attributedText isEqualToAttributedString:attStr]) {
//        TKLogDebug(@"---------oneLineWidth 传入的string = %@重复,不重复处理", string);
        return;
    }
    
    // 先暂停滚动
    [self stopTextViewScroll];
    
    if (!_serviceBg) {
       [self.bottomShowTipView addSubview:self.serviceBg];
       [self.serviceBg addSubview:self.serviceGifView];
       // 此时文案未更新高度，先不展示
       self.serviceBg.hidden = YES;
       self.serviceGifView.hidden = YES;
    }
    
    self.bottomShowLabel.attributedText = nil;
    self.bottomShowLabel.textContainerInset = UIEdgeInsetsMake(0, 0, 0, 0); // 旧版UI会设置UIEdgeInsetsMake(5, 0, 5, 0).这里要改回来
    
    // 根据富文本调整frame
    [self updateTextViewFrame:string textColor:colorString isOneLineShow:isOneLineShow];

//    self.bottomShowLabel.text = string; // 在11系统，需要设置好frame之后再赋值
   self.bottomShowLabel.attributedText = attStr;
   [self addSubview:self.bottomShowTipView];
   [self.bottomShowTipView addSubview:self.bottomShowLabel];
    // 此时文案已更新高度，展示
    self.serviceBg.hidden = NO;
    self.serviceGifView.hidden = NO;
       
   if (!isOneLineShow) {
       //计算是否要滚动换行
       //多久渐变一个字
       float durationTime = 0.19f;
        if ([TKStringHelper isNotEmpty:questionOneWordSpeed]) durationTime = questionOneWordSpeed.floatValue;
       if (durationTime == 0) durationTime = 0.19f;
       self.wordSpeedDuration = durationTime;
       
       //问题播放需要判断是否走
       [self changeReadTextViewOffSet];
   }
}

- (void)updateTextViewFrame:(NSString *)string textColor:(NSString *)colorString isOneLineShow:(BOOL)isOneLineShow
{
    CGFloat margin = 15;
    CGFloat bottomShowLabelX = _serviceBg.TKRight + margin;
    CGFloat bottomShowLabelY = 5;
    CGFloat bottomShowLabelWidth = self.bottomShowTipView.TKWidth - _serviceBg.TKRight - margin * 2;

    self.bottomShowLabel.textContainer.lineBreakMode =  NSLineBreakByWordWrapping;
    
    // 生成富文本
    NSMutableAttributedString *attStr = [self convertTextToHtmlString:string textColor:colorString];
    
    // 计算一行的宽、高度(中文高度包括行高,需要清除换行再计算)
    NSString *tempStr = [string stringByReplacingOccurrencesOfString:@"<br/>" withString:@""];
    NSMutableAttributedString *tempattStr = [self convertTextToHtmlString:tempStr textColor:colorString];
    CGFloat oneLineHeight = [self getHTMLHeightByStr:tempattStr width:CGFLOAT_MAX].height;

    if (isOneLineShow == NO) { // 如果是播放问题，需要展示多行（>1  问题文本 + 倒计时文本 + 识别结果文本）
        // 计算富文本高度，1行则1行显示，最多显示3行。仅针对文案播报
       // 计算文本的总高度
       CGFloat htmlHeight = [self getHTMLHeightByStr:attStr width:bottomShowLabelWidth].height;
        
        CGFloat extHeight = self.bottomShowLabel.textContainerInset.top  + self.bottomShowLabel.textContainerInset.bottom + bottomShowLabelY * 2 + self.bottomShowTipView.layer.borderWidth * 2;
        
        // 少于最大行数（如2行）展示全部
        CGFloat maxTotalHeight = oneLineHeight * 2;
        if (htmlHeight < maxTotalHeight) {
            
            // 展示超过播报图案的高度，展示两行
            self.bottomShowTipView.TKHeight = oneLineHeight * 2 + extHeight;
            
        } else {
            // 展示最大高度（>2）
            self.bottomShowTipView.TKHeight = maxTotalHeight + extHeight;
        }

        //文本左右保持留白15
       self.bottomShowLabel.frame = CGRectMake(bottomShowLabelX, bottomShowLabelY, bottomShowLabelWidth, self.bottomShowTipView.TKHeight - 2 * bottomShowLabelY);
        self.bottomShowLabel.textContainer.maximumNumberOfLines = INT_MAX;
        
    } else {
        
        // 展示1行高度
        CGFloat oneLineWidth = [self getHTMLHeightByStr:attStr width:CGFLOAT_MAX].width + 16;
        oneLineWidth = oneLineWidth > bottomShowLabelWidth ? bottomShowLabelWidth : oneLineWidth;
        self.bottomShowLabel.frame = CGRectMake(bottomShowLabelX, bottomShowLabelY, oneLineWidth, oneLineHeight * 1);
        self.bottomShowLabel.textContainer.maximumNumberOfLines = 1;
        
        // countDownLabel在bottomShowLabel(第1行)后拼接展示
        CGFloat margin2 = 0;
//        CGFloat countDownLabelHeight = self.countDownLabel.TKHeight;
//        [self.countDownLabel sizeToFit];
//        self.countDownLabel.TKHeight = countDownLabelHeight;
        
        self.countDownLabel.TKLeft = self.bottomShowLabel.TKRight + margin2;
        if ((self.countDownLabel.TKRight + margin) > self.bottomShowTipView.TKWidth) {   // countDownLabel到bottomShowTipView右侧的距离是15
            
            self.bottomShowLabel.textContainer.lineBreakMode =  NSLineBreakByTruncatingTail;
            
            self.countDownLabel.TKRight = self.bottomShowTipView.TKWidth - margin;
            self.bottomShowLabel.TKWidth = self.countDownLabel.TKLeft - margin2 - self.bottomShowLabel.TKLeft;
        }
        
        // 要和-(void)answerPromptType:(BOOL)flag identifyString:(NSString *)string中的高度对应
        // 展示最大高度
        self.bottomShowTipView.TKHeight = MAX(self.countDownLabel.TKBottom, self.bottomShowLabel.TKBottom) + 6 + self.answerPromptLabel.font.lineHeight + self.bottomShowLabel.textContainerInset.bottom + bottomShowLabelY + self.bottomShowTipView.layer.borderWidth;
        
        self.countDownLabel.center = CGPointMake(self.countDownLabel.center.x, self.bottomShowLabel.center.y);
    }
}

- (NSMutableAttributedString *)convertTextToHtmlString:(NSString *)text textColor:(NSString *)colorString
{
    NSString *tempDivString = [NSString stringWithFormat:@"<span style=\"font-size:%ipx;color:%@;font-family:PingFang SC\">%@</span>", (int)self.bottomShowLabel.font.pointSize, colorString, text];
   NSData *tempData = [tempDivString dataUsingEncoding:NSUnicodeStringEncoding];
   NSDictionary *options = @{NSDocumentTypeDocumentAttribute: NSHTMLTextDocumentType,
                             NSCharacterEncodingDocumentAttribute:[NSNumber numberWithInt:NSUTF8StringEncoding]
   };
   NSMutableAttributedString *tempattStr = [[NSMutableAttributedString alloc]initWithData:tempData options:options documentAttributes:nil error:nil];
    
    return tempattStr;
}

/**
计算html字符串高度

@param str html 未处理的字符串
@param font 字体设置
@param lineSpacing 行高设置
@param width 容器宽度设置
@return 富文本高度
*/
- (CGSize)getHTMLHeightByStr:(NSMutableAttributedString *)str width:(CGFloat)width
{
   CGSize contextSize = [str boundingRectWithSize:(CGSize){width, CGFLOAT_MAX} options:NSStringDrawingUsesLineFragmentOrigin | NSStringDrawingUsesFontLeading context:nil].size;
   return contextSize;

}

/**
@Auther Vie 2021年07月29日13:56:44
改变阅读文本空间显示
*/
-(void)changeReadTextViewOffSet {
   
   // 防止页面不显示的时候，定时器还在反复调用该功能
   if (!self.window) {
       
       [self stopTextViewScroll];
       
//        TKLogDebug(@"changeReadTextViewOffSet timer invalidate");
       return;
   }
    
    NSTimeInterval repeactTime = 1.0f;
    if (self.wordSpeedDuration <= 0.05) repeactTime = 0.1;    // 滚动速度变大，刷新频率也更新
    int wordPerSecond = ceilf(repeactTime / self.wordSpeedDuration);

   if (self.changeReadTextViewWords < self.bottomShowLabel.attributedText.string.length) {
       // -1是防止到最后一个字再滚动的话会有点显示问题。会一整行滚动一次，又展示
//        TKLogDebug(@"思迪文案滚动动画：durationTime = %.2f, wordPerSecond = %i, self.changeReadTextViewWords = %i, self.bottomShowLabel.attributedText.string.length = %i", durationTime, wordPerSecond, self.changeReadTextViewWords, self.bottomShowLabel.attributedText.string.length);
       
       NSRange rang = NSMakeRange(self.changeReadTextViewWords, wordPerSecond);
       [self.bottomShowLabel scrollRangeToVisible:rang];
       
       //计算是否要滚动换行
//        CGSize labelSize=[self.bottomShowLabel sizeThatFits:CGSizeMake(self.bottomShowLabel.TKWidth, MAXFLOAT)];
//        float diffY=labelSize.height-self.bottomShowLabel.TKHeight;
//        if (diffY>0) {
//            NSRange rang=NSMakeRange(self.changeReadTextViewWords, self.wordPerSecond);
//            [self.bottomShowLabel scrollRangeToVisible:rang];
//        }
       
       self.changeReadTextViewWords = self.changeReadTextViewWords + wordPerSecond;

//        TKLogDebug(@"思迪文案滚动动画：创建定时器");
       if (self.changeReadTextViewOffSetTimer == nil) {
           
           self.changeReadTextViewOffSetTimer = [NSTimer timerWithTimeInterval:repeactTime target:self selector:@selector(changeReadTextViewOffSet) userInfo:nil repeats:YES];
           [[NSRunLoop mainRunLoop] addTimer:self.changeReadTextViewOffSetTimer forMode:NSRunLoopCommonModes];
       }
   } else {
       
//        TKLogDebug(@"思迪文案滚动动画：销毁定时器");
       [self stopTextViewScroll];
   }
}


- (void)stopTextViewScroll
{
    self.changeReadTextViewWords = 0;
    [self.changeReadTextViewOffSetTimer invalidate];
    self.changeReadTextViewOffSetTimer = nil;
}

/**
 <AUTHOR> 2021年07月08日16:15:38
 @语音识别过程中识别到的回答小字提示
 @param回答正确，无用回答
 */
-(void)answerPromptType:(BOOL)flag identifyString:(NSString *)string{
    [_answerPromptLabel removeFromSuperview];
    _answerPromptLabel=nil;
    [_answerPromptImgBg removeFromSuperview];
    _answerPromptImgBg=nil;
    
    // 更新识别倒计时文案
    self.hasAsrResult = YES;
    [self updateCountDownLabelText:self.answerCount countDownType:TKCountDownTypeAnswer];

    self.answerPromptLabel.text=string;

    // 要和- (void)updateTextViewFrame:(NSString *)string textColor:(NSString *)colorString isOneLineShow:(BOOL)isOneLineShow中的计算对应
    float y = self.countDownLabel.TKBottom + 6;
    
    CGSize lableSize = [self.answerPromptLabel sizeThatFits:CGSizeMake(self.bottomShowLabel.TKWidth, self.answerPromptLabel.font.lineHeight)];
    float width=lableSize.width;
    float x=self.bottomShowLabel.TKLeft;
    self.answerPromptLabel.frame=CGRectMake(x, y, width, lableSize.height);
    [self.bottomShowTipView addSubview:self.answerPromptLabel];
    
    //语音回答旁边小图标
    self.answerPromptImgBg=[[UIView alloc] initWithFrame:CGRectMake(x+width+6, y + 4, 22, 22)];
    self.answerPromptImgBg.layer.cornerRadius=self.answerPromptImgBg.TKWidth/2.0f;
    UIImageView *imgView=[[UIImageView alloc] initWithFrame:CGRectMake(0.0f, 0.0f, 22, 22)];
    [self.answerPromptImgBg addSubview:imgView];
    if (flag) {
        if (![self.mainColorString isEqualToString:@"#2772FE"]) {
            self.answerPromptLabel.textColor=[TKUIHelper colorWithHexString:self.mainColorString];
            self.answerPromptImgBg.backgroundColor=[TKUIHelper colorWithHexString:self.mainColorString];
        }else{
            self.answerPromptLabel.textColor=[TKUIHelper colorWithHexString:@"#2772FE"];
            self.answerPromptImgBg.backgroundColor=[TKUIHelper colorWithHexString:@"#2772FE"];
        }
        

        imgView.image=[UIImage imageNamed:[NSString stringWithFormat:@"%@.bundle/Resources/TKOpenPlugin60007/tk_one_answer_ok.png", TK_OPEN_RESOURCE_NAME]];
    }else{
        self.answerPromptLabel.textColor=[TKUIHelper colorWithHexString:@"#EA4940"];
        self.answerPromptImgBg.backgroundColor=[TKUIHelper colorWithHexString:@"#EA4940"];
        imgView.image=[UIImage imageNamed:[NSString stringWithFormat:@"%@.bundle/Resources/TKOpenPlugin60007/tk_one_answer_unknown.png", TK_OPEN_RESOURCE_NAME]];
    }
    [self.bottomShowTipView addSubview:self.answerPromptImgBg];
}

/**
@Auther Vie 2021年07月28日16:40:17
@param currentNum 当前进度，
@param allNum 总进度数目；问题n，结束语1；n+1
*/
-(void)currentNum:(int)currentNum allNum:(float)allNum{
    if (allNum == 0) return;
        
    self.bottomShowTipRecordLineView.hidden = NO;
    
    float width = currentNum * 1.0f / allNum * self.bottomShowTipLineView.TKWidth;
    [self.bottomShowTipRecordLineView setFrameWidth:width > self.bottomShowTipLineView.TKWidth ? self.bottomShowTipLineView.TKWidth : width];
}


///// 是否展示录制按钮
///// @param isShow 是否展示
/// @param btnTitle 按钮文案，传Nil展示默认的“继续播报”
- (void)showNextBtn:(BOOL)isShow btnTitle:(NSString *)btnTitle {
    
    if ([TKStringHelper isNotEmpty:btnTitle]) [self.nextBtn setTitle:btnTitle forState:UIControlStateNormal];
    if (self.nextBtn.superview == nil) [self addSubview:self.nextBtn];
    [self bringSubviewToFront:self.nextBtn];
    self.nextBtn.hidden = !isShow;
}

/// 展示用户动作提示文案，等待用户做动作
/// @param originString 待展示的文案
/// @param htmlFlag 是否是html标签
/// @param waitTime 等待时间
- (void)showUserActionPrompt:(NSString *)originString isHtmlString:(BOOL)htmlFlag waitTime:(int)waitTime questionOneWordSpeed:(NSString *)questionOneWordSpeed {
    if (waitTime <= 0) {
        waitTime = 5;
    }
    
    self.isFinished = NO;
    
    // 防止等待时间文本闪烁，先隐藏，后显示
    self.countDownLabel.hidden = YES;
    [self updateCountDownLabelText:waitTime countDownType:TKCountDownTypeUserAction];
    [self updateTipLabel:[NSString stringWithFormat:@"%@",originString] textColor:[self.mainColorString isEqualToString:@"#2772FE"] ? @"#51B4FE":self.mainColorString cornerRadius:20.0f isOneLineShow:YES isHtmlString:htmlFlag questionOneWordSpeed:questionOneWordSpeed];
    self.countDownLabel.hidden = NO;
    
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1.0f * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        [self userActionCountDown:waitTime];
    });
}


/**
 <AUTHOR> 2019年04月26日18:39:08
 @强制用户做动作等待倒计时
 */
-(void)userActionCountDown:(int)waitTime {
    
    __weak typeof(self) weakSelf = self;
    
    // 其他业务异常语音打断倒计时
    if (!self.isFinished) {
        
        waitTime--;
    
        [self updateCountDownLabelText:waitTime countDownType:TKCountDownTypeUserAction];
        
        if (waitTime > 0) {
            
            dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1.0f * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                [self userActionCountDown:waitTime];
            });
        }else{
            
            if (self.delegate && [self.delegate respondsToSelector:@selector(userActionCountDownEnd)]) {
                [self.delegate userActionCountDownEnd];
            }
        }
    }else{
        
    }
}

- (void)updateCountDownLabelText:(int)answerCount countDownType:(TKCountDownType)countDownType
{
    self.countDownType = countDownType;
    [self.bottomShowTipView addSubview:self.countDownLabel];
    self.countDownLabel.text = [NSString stringWithFormat:@"(%d)",(int)answerCount] ;
    [self.countDownLabel sizeToFit];
    self.countDownLabel.TKHeight = 22;
}


- (void)showNoAnserPrompt
{
    self.answerPromptLabel.text = @"未检测到您的回答，请再回答一次";
    self.answerPromptLabel.backgroundColor = [TKUIHelper colorWithHexString:@"#FFFFFF"];
    
    float y = self.countDownLabel.TKBottom + 6;
    self.answerPromptLabel.font = [UIFont fontWithName:@"PingFang SC" size:16];
    CGSize lableSize = [self.answerPromptLabel sizeThatFits:CGSizeMake(self.bottomShowLabel.TKWidth, self.answerPromptLabel.font.lineHeight)];
    float width = lableSize.width + 12;
    float x = self.bottomShowLabel.TKLeft;
    self.answerPromptLabel.frame=CGRectMake(x, y, width, lableSize.height);
    
    if (![self.mainColorString isEqualToString:@"#2772FE"]) {
        self.answerPromptLabel.textColor=[TKUIHelper colorWithHexString:self.mainColorString];
    }else{
        self.answerPromptLabel.textColor=[TKUIHelper colorWithHexString:@"#2F85FF"];
    }
    
    [self.bottomShowTipView addSubview:self.answerPromptLabel];
}

- (void)atuoStartTakeRecord:(int)countDown
{
    [self createAutoStartRecordTimer:countDown];
    [self updateTakeBtnWithCountDown:countDown];
}

- (void)updateTakeBtnWithCountDown:(int)countDown
{
    NSString *btnTitle = @"◉  开始录制";
    if (countDown > 0) {
        btnTitle = [btnTitle stringByAppendingFormat:@"(%i)",countDown];
    }
    [_takeBtn setTitle:btnTitle forState:UIControlStateNormal];
}

- (void)createAutoStartRecordTimer:(NSTimeInterval)interval
{
    [self stopAutoStartRecordTimer];
    
    if (interval <= 0) return;
    
    // 设置开始录制超时定时器
    self.startAutoRecordTimer = [NSTimer timerWithTimeInterval:1 target:self selector:@selector(startAutoRecord:) userInfo:nil repeats:YES];
    [[NSRunLoop mainRunLoop] addTimer:self.startAutoRecordTimer forMode:NSRunLoopCommonModes];
    
    self.startAutoRecordCountDown = interval;
}

- (void)stopAutoStartRecordTimer {
    if (_startAutoRecordTimer) {
        [self.startAutoRecordTimer invalidate];
        self.startAutoRecordTimer = nil;
    }
}

- (void)startAutoRecord:(NSTimer *)timer
{
    TKLogInfo(@"思迪录制日志：就绪后达到时间，自动开始录制");
    if (self.startAutoRecordCountDown > 0) {
        [self updateTakeBtnWithCountDown:self.startAutoRecordCountDown];
        self.startAutoRecordCountDown--;
    } else {
        [self stopAutoStartRecordTimer];
        
        [self takeAction:self.takeBtn];
    }
}


#pragma mark lazyloading
/**
 <AUTHOR> 2019年04月03日10:47:55
 @初始化懒加载人像取景框矩阵
 @return 人像取景框矩阵
 */
-(CGRect)boxRect{
    if (CGRectIsEmpty(_boxRect)) {
        CGFloat topMargin = IPHONEX_SAFEAREAINSETS_TOP;
        if (topMargin == 0) {
            topMargin = 20;
            // X以下机型，如果隐藏状态栏，IPHONEX_SAFEAREAINSETS_TOP为0，需要加上状态高度
        }
        
        _boxRect = CGRectMake(topMargin + 63, 0, self.TKWidth - (topMargin + 63) - IPHONEX_BUTTOM_HEIGHT, self.TKHeight);
    }
    return _boxRect;
}

/**
 <AUTHOR> 2019年04月03日10:57:34
 @初始化懒加载顶部遮罩层
 @return 顶部遮罩层
 */
-(UIView *)topView{
    if (!_topView) {
        _topView=[[UIView alloc] initWithFrame:CGRectMake(0, 0, self.TKWidth, self.boxRect.origin.y)];
        _topView.backgroundColor = [TKUIHelper colorWithHexString:@"#000000" alpha:1];
    }
    return _topView;
}


/**
 <AUTHOR> 2019年04月03日11:20:41
 @初始化懒加载底部遮罩层
 @return 底部遮罩层
 */
-(UIView *)bottomView{
    if (!_bottomView) {
        _bottomView=[[UIView alloc] initWithFrame:CGRectMake(0, self.boxRect.size.height+self.boxRect.origin.y, self.TKWidth, self.TKHeight-self.boxRect.origin.y-self.boxRect.size.height)];
        _bottomView.backgroundColor = [TKUIHelper colorWithHexString:@"#000000" alpha:1];
    }
    return _bottomView;
}

/**
 <AUTHOR> 2019年04月03日10:57:34
 @初始化懒加左部遮罩层
 @return 左部遮罩层
 */
-(UIView *)leftView{
    if (!_leftView) {
        _leftView=[[UIView alloc] initWithFrame:CGRectMake(0, self.boxRect.origin.y, self.boxRect.origin.x, self.boxRect.size.height)];
        _leftView.backgroundColor = [TKUIHelper colorWithHexString:@"#000000" alpha:1];
    }
    return _leftView;
}

/**
 <AUTHOR> 2019年04月03日10:57:34
 @初始化懒加右部遮罩层
 @return 右部遮罩层
 */
-(UIView *)rightView{
    if (!_rightView) {
        _rightView=[[UIView alloc] initWithFrame:CGRectMake(self.boxRect.origin.x+self.boxRect.size.width, self.boxRect.origin.y, self.TKWidth - CGRectGetMaxX(self.boxRect), self.boxRect.size.height)];
        _rightView.backgroundColor = [TKUIHelper colorWithHexString:@"#000000" alpha:1];
    }
    return _rightView;
}

/**
 <AUTHOR> 2019年12月30日22:05:40
 @初始化懒加载底部文档等提示展示区域
 @return 底部文档等提示展示区域
 */
-(UIView *)bottomShowTipView{
    if (!_bottomShowTipView) {
        
        _bottomShowTipView=[[UIView alloc] init];
        _bottomShowTipView.layer.borderWidth = 1;
        //子视图是否局限于视图的边界。
        _bottomShowTipView.clipsToBounds=YES;
        
        if (![self.mainColorString isEqualToString:@"#2772FE"]) {
            _bottomShowTipView.layer.borderColor=[TKUIHelper colorWithHexString:self.mainColorString alpha:0.2f].CGColor;
        }else{
            _bottomShowTipView.layer.borderColor=[TKUIHelper colorWithHexString:@"#498FD5" alpha:0.2f].CGColor;
        }
        [_bottomShowTipView setBackgroundColor:[TKUIHelper colorWithHexString:@"#040D16" alpha:0.5f]];
        
        float bottomShowTipViewX = self.boxRect.origin.x + 20;
        float bottomShowTipViewY = self.boxRect.origin.y + 30;
        float bottomShowTipViewWidth = self.boxRect.size.width - 20 * 2;
        self.bottomShowTipView.layer.cornerRadius = 13.0f;
        self.bottomShowTipView.frame = CGRectMake(bottomShowTipViewX, bottomShowTipViewY, bottomShowTipViewWidth, 42);
    }
    return _bottomShowTipView;
}


/**
 <AUTHOR> 2019年12月30日22:20:29
 @初始化懒加载底部文档等提示展示区域横线
 @return 底部文档等提示展示区域横线
 */
-(UIView *)bottomShowTipLineView{
    if (!_bottomShowTipLineView) {
        
        float x = self.boxRect.origin.x + 35;
        float y = self.boxRect.origin.y + 14;
        float width = self.boxRect.size.width - 35 * 2;
        float height = 4;
        _bottomShowTipLineView = [[UIView alloc] initWithFrame:CGRectMake(x, y, width, height)];
        _bottomShowTipLineView.backgroundColor = [TKUIHelper colorWithHexString:@"#333333 " alpha:0.8f];
        _bottomShowTipLineView.layer.cornerRadius = height/2.0f;
    }
    return _bottomShowTipLineView;
}

/**
 <AUTHOR> 2019年12月30日22:20:29
 @初始化懒加载bottomShowTipRecordLineView
 @return bottomShowTipRecordLineView
 */
-(UIView *)bottomShowTipRecordLineView{
    if (!_bottomShowTipRecordLineView) {
        _bottomShowTipRecordLineView=[[UIView alloc] initWithFrame:CGRectMake(0, 0, 0, self.bottomShowTipLineView.TKHeight)];
        
        _bottomShowTipRecordLineView.layer.cornerRadius=self.bottomShowTipLineView.TKHeight/2.0f;
        if (![self.mainColorString isEqualToString:@"#2772FE"]) {
            
            [_bottomShowTipRecordLineView setBackgroundColor:[TKUIHelper colorWithHexString:self.mainColorString alpha:0.5f]];
        }else{
            
            [_bottomShowTipRecordLineView setBackgroundColor:[TKUIHelper colorWithHexString:@"#2772FE" alpha:0.5f]];
        }
    }
    return _bottomShowTipRecordLineView;
}


/**
 <AUTHOR> 2019年12月31日13:48:11
 @初始化懒加载底部文字展示
 @return 底部文字展示
 */
-(UITextView *)bottomShowLabel{
    if (!_bottomShowLabel) {
        
        _bottomShowLabel=[[UITextView alloc] init];
//        _bottomShowLabel.numberOfLines=0;
        _bottomShowLabel.font = [UIFont fontWithName:@"PingFangSC-Medium" size:20];
        _bottomShowLabel.textColor=[UIColor whiteColor];
        _bottomShowLabel.textAlignment=NSTextAlignmentCenter;
        _bottomShowLabel.clipsToBounds=YES;//子视图是否局限于视图的边界。

//        _bottomShowLabel.text=@"请您保持全脸在人像框内。";
        _bottomShowLabel.backgroundColor=[UIColor clearColor];
        _bottomShowLabel.frame=CGRectMake(0, 0, self.bottomShowTipView.TKWidth, self.bottomShowTipView.TKHeight);
        _bottomShowLabel.backgroundColor=[UIColor clearColor];
        _bottomShowLabel.textContainerInset = UIEdgeInsetsMake(0, 0, 0, 0);
        [_bottomShowLabel setEditable:false];
        _bottomShowLabel.showsVerticalScrollIndicator = NO;
        _bottomShowLabel.showsHorizontalScrollIndicator = NO;
    }
    return _bottomShowLabel;
}


/**
 <AUTHOR> 2019年12月31日13:48:11
 @初始化懒加载提示layer
 @return 提示layer
 */
-(TKLayerView *)layerView{
    if (!_layerView) {
        _layerView=[[TKLayerView alloc] initContentView:self withBtnTextColor:nil cancelBtnTextColor:nil withWidth:self.boxRect.size.width withFont:[UIFont fontWithName:@"PingFangSC-Semibold" size:22]];
        
        [_layerView setShowTipDuration:0.6];
    }
    return _layerView;
}


/**
 <AUTHOR> 2019年04月03日14:52:07
 @初始化懒加载返回按钮
 @return 返回按钮
 */
-(UIButton *)backBtn{
    if (!_backBtn) {
        
        float backBtnWidth = 32;
        float backBtnheight = 32;
        float backBtnX = self.boxRect.origin.x - 63;
        float backBtnY = 18;
        _backBtn=[[UIButton alloc] initWithFrame:CGRectMake(backBtnX, backBtnY, backBtnWidth, backBtnheight)];
        _backBtn.clipsToBounds = YES;
        [_backBtn setImage:[UIImage imageNamed:[NSString stringWithFormat:@"%@.bundle/Resources/TKOpenGeneral/tk_open_back.png", TK_OPEN_RESOURCE_NAME]] forState:UIControlStateNormal];
        [_backBtn setImageEdgeInsets:UIEdgeInsetsMake(0, -2, 0, 2)]; // 图片往右偏了，需要往左偏回来
        
        [_backBtn setBackgroundColor:[TKUIHelper colorWithHexString:@"#ffffff" alpha:0.2f]];
        _backBtn.layer.cornerRadius = backBtnWidth/2.0f;
        
        [_backBtn addTarget:self action:@selector(backAction:) forControlEvents:UIControlEventTouchUpInside];
    }
    return _backBtn;
}


/**
 <AUTHOR> 2019年04月26日18:55:02
 @初始化懒加载回答倒计时展示label
 @return 回答倒计时展示label
 */
-(UILabel *)countDownLabel{
    if (!_countDownLabel) {
        float width=30;
        float height=22;
        float x=self.bottomShowLabel.TKLeft;
        float y=40;
        _countDownLabel= [[UILabel alloc] initWithFrame:CGRectMake(x, y, width, height)];
        _countDownLabel.font =  [UIFont fontWithName:@"PingFang SC" size:22];;
        _countDownLabel.textAlignment=NSTextAlignmentLeft;
        _countDownLabel.textColor = [TKUIHelper colorWithHexString:@"#FFFFFF" alpha:0.7f];
    }
    return _countDownLabel;
}


/**
 <AUTHOR> 2020年02月28日10:50:22
 @初始化懒加载开始录制按钮
 @return 开始录制按钮
 */
- (UIButton *)takeBtn{
    if (!_takeBtn) {
        
        float takeBtnHeight = 44;
        float takeBtnWidth = 175;
        float takeBtnX = self.boxRect.origin.x + (self.boxRect.size.width - takeBtnWidth) * 0.5;
        float gap=13;
        //5s等小屏幕机型
        if (UISCREEN_WIDTH==320) {
            gap=5;
        }
        float takeBtnY = self.TKHeight - takeBtnHeight-gap - IPHONEX_BUTTOM_HEIGHT;
        _takeBtn=[[UIButton alloc] initWithFrame:CGRectMake(takeBtnX, takeBtnY, takeBtnWidth, takeBtnHeight)];
        
//        [_takeBtn setTitle:@"◉  开始录制" forState:UIControlStateNormal];
        [self updateTakeBtnWithCountDown:0];
        _takeBtn.layer.cornerRadius=takeBtnHeight/2.0f;
        if ([self respondsToSelector:NSSelectorFromString(@"takeAction:")]) {
            
            [_takeBtn addTarget:self action:NSSelectorFromString(@"takeAction:") forControlEvents:UIControlEventTouchUpInside];
        }
        [self enableTakeRecord:NO];
      
    }
    return _takeBtn;
}


- (UIButton *)nextBtn{
    if (!_nextBtn) {
        
        _nextBtn = [[UIButton alloc] initWithFrame:self.takeBtn.frame];
        NSString *nextBtnTitle = @"继续播报";
        [_nextBtn setTitle:nextBtnTitle forState:UIControlStateNormal];
        
        _nextBtn.titleLabel.font = self.takeBtn.titleLabel.font;
        [_nextBtn addTarget:self action:@selector(nextAction:) forControlEvents:UIControlEventTouchUpInside];
        _nextBtn.layer.cornerRadius = self.takeBtn.TKHeight * 0.5;
        
        _nextBtn.layer.borderWidth=0.0f;
        _nextBtn.layer.borderColor=[UIColor clearColor].CGColor;
        [_nextBtn setTitleColor:[TKUIHelper colorWithHexString:@"#FFFFFF"] forState:UIControlStateNormal];
        _nextBtn.backgroundColor=[TKUIHelper colorWithHexString:[self.mainColorString isEqualToString:@"#2772FE"]?@"#2772FE":self.mainColorString];
    }
    return _nextBtn;
}

/**
 <AUTHOR> 2020年08月26日10:52:52
 @初始化懒加载语音回答结果提示
 @return 语音回答结果提示
 */
- (UILabel *)answerPromptLabel{
    if (!_answerPromptLabel) {
        _answerPromptLabel = [[UILabel alloc] init];
        _answerPromptLabel.textAlignment=NSTextAlignmentCenter;
        _answerPromptLabel.font=[UIFont fontWithName:@"PingFang SC" size:22];
    }
    return _answerPromptLabel;
}

/**
 <AUTHOR> 2019年04月03日14:52:07
 @初始化懒加载recordTimeLabel
 @return recordTimeLabel
 */
-(UILabel *)recordTimeLabel{
    if (!_recordTimeLabel) {
        float x = self.backBtn.TKLeft - 10;
        float y = (self.TKHeight - 20) * 0.5;
        float width = 53;
        float height = 20;
        _recordTimeLabel=[[UILabel alloc] initWithFrame:CGRectMake(x, y, width, height)];
        _recordTimeLabel.text=@"00:00";
        _recordTimeLabel.textColor=[TKUIHelper colorWithHexString:@"#FFFFFF"];
        _recordTimeLabel.font = [UIFont fontWithName:@"PingFang SC" size:14];
        _recordTimeLabel.hidden = NO;
        _recordTimeLabel.layer.cornerRadius = 4;
        _recordTimeLabel.clipsToBounds = YES;
        _recordTimeLabel.textAlignment = NSTextAlignmentCenter;
    }
    return _recordTimeLabel;
}

/**
 *  <AUTHOR> 2019年09月08日14:32:34
 *  @初始化懒加载switchCameraBtn
 *  @return  switchCameraBtn
 */
-(UIButton *)switchCameraBtn{
    if (!_switchCameraBtn) {
        float width=32;
        float height=32;
        float x=self.backBtn.TKLeft;
        float y=self.TKHeight - height - 18-IPHONEX_BUTTOM_HEIGHT;
        _switchCameraBtn=[[UIButton alloc] initWithFrame:CGRectMake(x, y, width, height)];
        _switchCameraBtn.clipsToBounds = YES;
        [_switchCameraBtn setImage:[UIImage imageNamed:[NSString stringWithFormat:@"%@.bundle/Resources/TKOpenGeneral/tk_switchCamera_btn.png", TK_OPEN_RESOURCE_NAME]] forState:UIControlStateNormal];
        [_switchCameraBtn setImageEdgeInsets:UIEdgeInsetsMake(6, 6, 8, 6)];

        [_switchCameraBtn setBackgroundColor:[TKUIHelper colorWithHexString:@"#ffffff" alpha:0.2f]];
        _switchCameraBtn.layer.cornerRadius = width/2.0f;
        
        [_switchCameraBtn addTarget:self action:@selector(switchCameraAction:)
          forControlEvents:UIControlEventTouchUpInside];
    }
    return _switchCameraBtn;
}

/**
 <AUTHOR> 2021年09月15日15:44:25
 @初始化懒加载serviceBg
 @return serviceBg
 */
-(UIImageView *)serviceBg{
    if (!_serviceBg) {
        float width=36;
        float height=36;
        float x=15;
        float y=15;
        _serviceBg=[[UIImageView alloc] initWithFrame:CGRectMake(x, y, width, height)];
        [_serviceBg setImage:[UIImage imageNamed:[NSString stringWithFormat:@"%@.bundle/Resources/TKOpenPlugin60026/serviceBg.png", TK_OPEN_RESOURCE_NAME]]];
    }
    return _serviceBg;
}

/**
 <AUTHOR> 2021年09月15日15:44:34
 @初始化懒加载serviceGifView
 @return serviceGifView
 */
-(TKGIFImageView *)serviceGifView{
    if (!_serviceGifView) {
        float height=20;
        float width=28;
        float x=(self.serviceBg.TKWidth-width)/2.0f;
        float y=(self.serviceBg.TKHeight-height)/2.0f;
        _serviceGifView=[[TKGIFImageView alloc] initWithFrame:CGRectMake(x, y, width, height)];
        [_serviceGifView setImageByName:[NSString stringWithFormat:@"%@.bundle/Resources/TKOpenPlugin60026/serviceSpeak.gif", TK_OPEN_RESOURCE_NAME]];
    }
    return _serviceGifView;
}


- (void)setAvPreviewView:(UIView *)avPreviewView {
    _avPreviewView = avPreviewView;
    [self insertSubview:avPreviewView atIndex:0];
}

- (TKFaceDectTipView *)faceDectTipView {
    if (!_faceDectTipView) {
        _faceDectTipView = [[TKFaceDectTipView alloc] initWithFrame:self.boxRect];
        _faceDectTipView.isEnhance = YES;
    }
    
    return _faceDectTipView;
}


- (UIImageView *)ltImageView {
    if (!_ltImageView) {
        float rectImgWidth=14.0f;
        _ltImageView = [[UIImageView alloc] initWithFrame:CGRectMake(self.leftView.TKRight, self.topView.TKBottom, rectImgWidth, rectImgWidth)];
        UIImage *img=[UIImage imageNamed:[NSString stringWithFormat:@"%@.bundle/Resources/TKOpenPlugin60077/tk_open_lt.png", TK_OPEN_RESOURCE_NAME]];
        if (self.requestParam[@"mainColor"]) {
            img = [img imageWithRenderingMode:UIImageRenderingModeAlwaysTemplate];
            [_ltImageView setTintColor:[TKUIHelper colorWithHexString:self.requestParam[@"mainColor"]]];
        }
        [_ltImageView setImage:img];
    }
    return _ltImageView;
}

- (UIImageView *)rtImageView {
    if (!_rtImageView) {
        _rtImageView = [[UIImageView alloc] initWithFrame:CGRectMake(self.rightView.TKLeft-self.ltImageView.TKWidth, self.topView.TKBottom, self.ltImageView.TKWidth, self.ltImageView.TKWidth)];
        UIImage *img=[UIImage imageNamed:[NSString stringWithFormat:@"%@.bundle/Resources/TKOpenPlugin60077/tk_open_rt.png", TK_OPEN_RESOURCE_NAME]];
        if (self.requestParam[@"mainColor"]) {
            img = [img imageWithRenderingMode:UIImageRenderingModeAlwaysTemplate];
            [_rtImageView setTintColor:[TKUIHelper colorWithHexString:self.requestParam[@"mainColor"]]];
        }
        [_rtImageView setImage:img];
    }
    return _rtImageView;
}

- (UIImageView *)lbImageView {
    if (!_lbImageView) {
        _lbImageView = [[UIImageView alloc] initWithFrame:CGRectMake(self.leftView.TKRight, self.bottomView.TKTop-self.ltImageView.TKWidth, self.ltImageView.TKWidth, self.ltImageView.TKWidth)];
        UIImage *img=[UIImage imageNamed:[NSString stringWithFormat:@"%@.bundle/Resources/TKOpenPlugin60077/tk_open_lb.png", TK_OPEN_RESOURCE_NAME]];
        if (self.requestParam[@"mainColor"]) {
            img = [img imageWithRenderingMode:UIImageRenderingModeAlwaysTemplate];
            [_lbImageView setTintColor:[TKUIHelper colorWithHexString:self.requestParam[@"mainColor"]]];
        }
        [_lbImageView setImage:img];
    }
    return _lbImageView;
}

- (UIImageView *)rbImageView {
    if (!_rbImageView) {
        _rbImageView = [[UIImageView alloc] initWithFrame:CGRectMake(self.rightView.TKLeft-self.ltImageView.TKWidth, self.bottomView.TKTop-self.ltImageView.TKWidth, self.ltImageView.TKWidth, self.ltImageView.TKWidth)];
        UIImage *img=[UIImage imageNamed:[NSString stringWithFormat:@"%@.bundle/Resources/TKOpenPlugin60077/tk_open_rb.png", TK_OPEN_RESOURCE_NAME]];
        if (self.requestParam[@"mainColor"]) {
            img = [img imageWithRenderingMode:UIImageRenderingModeAlwaysTemplate];
            [_rbImageView setTintColor:[TKUIHelper colorWithHexString:self.requestParam[@"mainColor"]]];
        }
        [_rbImageView setImage:img];
    }
    return _rbImageView;
}
@end
