//
//  TKAVCaptureManager.m
//  TKOpenAccount-Standard
//
//  Created by <PERSON><PERSON> on 2017/7/7.
//  Copyright © 2017年 thinkive. All rights reserved.
//

#import "TKAVCaptureManager.h"
#import "TKCommonUtil.h"
#import <sys/utsname.h>
#import <sys/stat.h>


#define TEMP_MOVE_NAME @"TEMP_FILE.MOV"

@interface TKAVCaptureManager()<AVCaptureFileOutputRecordingDelegate>
{
    AVCaptureSession *mCaptureSession;
    
    AVCaptureStillImageOutput *captureStillImageOutput;
    
    AVCaptureMovieFileOutput *captureMovieFileOutput;
    
    NSInteger dPosition;
    
    NSString *m_nameStr;
    
}

@end

@implementation TKAVCaptureManager

-(AVCaptureDeviceType)getAVDeviceType{
    struct utsname systemInfo;
    uname(&systemInfo);
    NSString *deviceModel = [NSString stringWithCString:systemInfo.machine encoding:NSUTF8StringEncoding];
    AVCaptureDeviceType deviceType = AVCaptureDeviceTypeBuiltInWideAngleCamera;
    if ([deviceModel isEqualToString:@"iPhone15,2"] || [deviceModel isEqualToString:@"iPhone15,3"]) {
        if (@available(iOS 13.0, *)) {
            deviceType = AVCaptureDeviceTypeBuiltInUltraWideCamera;
        }
    } else if ([deviceModel hasPrefix:@"iPhone16,"]) {
        if (@available(iOS 13.0, *)) {
            deviceType = AVCaptureDeviceTypeBuiltInDualWideCamera;
        }
    }
    return deviceType;
}

- (instancetype)initWithPreviewView:(UIView *)previewView withCameraPosition:(AVCaptureDevicePosition)cPosition withCamraOrientation:(AVCaptureVideoOrientation)cOrientation handleBusinessType:(TK_VIDEO_BUSINESS_TYPE)bType
{
    self = [super init];
    
    if (self) {
        
        dPosition = cPosition;
        
        NSError *error;
        
        mCaptureSession = [[AVCaptureSession alloc] init];
        
        AVCaptureDevice *vDevice;
        if (IOS16_OR_LATER&&cPosition==AVCaptureDevicePositionBack) {
           vDevice =[AVCaptureDevice defaultDeviceWithDeviceType:[self getAVDeviceType] mediaType:AVMediaTypeVideo position:cPosition];
        }else{
            vDevice =  [self getCameraDeviceWithPosition:cPosition];
        }
        
        AVCaptureDeviceInput *videoIn = [AVCaptureDeviceInput deviceInputWithDevice:vDevice error:&error];
        
        if (error) {
            TKLogInfo(@"Video input creation failed");
            return nil;
        }
        
        [mCaptureSession beginConfiguration];
        
        if (![mCaptureSession canAddInput:videoIn]) {
            TKLogInfo(@"Video input add-to-session failed");
            return nil;
        }
        
        [mCaptureSession addInput:videoIn];
        
        if (bType == TK_VIDEO_TAKE_PHOTO) {
            
            if (cPosition == AVCaptureDevicePositionFront) {
                
                [self setFrontSessionPreset:mCaptureSession];
                
            }else{
                
                mCaptureSession.sessionPreset = AVCaptureSessionPreset1920x1080;
            }
            
            captureStillImageOutput = [[AVCaptureStillImageOutput alloc] init];
            
            NSDictionary *outputSettings = @{AVVideoCodecKey:AVVideoCodecJPEG};
            
            [captureStillImageOutput setOutputSettings:outputSettings];
            
            if ([mCaptureSession canAddOutput:captureStillImageOutput])
            {
                [mCaptureSession addOutput:captureStillImageOutput];
            }
            
        }else if(bType == TK_VIDEO_RECORD_VIDEO){
            
            mCaptureSession.sessionPreset = AVCaptureSessionPreset352x288;
        
            AVCaptureDevice *aDevice = [AVCaptureDevice defaultDeviceWithMediaType:AVMediaTypeAudio];
            
            //音频输入
            AVCaptureDeviceInput *audioInput = [[AVCaptureDeviceInput alloc] initWithDevice:aDevice error:nil];
            
            if ([mCaptureSession canAddInput:audioInput])
            {
                [mCaptureSession addInput:audioInput];
            }
            
            captureMovieFileOutput = [[AVCaptureMovieFileOutput alloc] init];
            
            if ([mCaptureSession canAddOutput:captureMovieFileOutput]){
                
                [mCaptureSession addOutput:captureMovieFileOutput];
                
            }
            
        }else{
            
            if (cPosition == AVCaptureDevicePositionFront) {
                
                [self setFrontSessionPreset:mCaptureSession];
                
            }else{
                
                mCaptureSession.sessionPreset = AVCaptureSessionPreset1920x1080;
                
            }
        
        }
        
        [mCaptureSession commitConfiguration];
        
        dispatch_async(dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_BACKGROUND, 0), ^{
            [mCaptureSession startRunning];
        });
        
        AVCaptureVideoPreviewLayer* previewLayer = [[AVCaptureVideoPreviewLayer alloc] initWithSession:mCaptureSession];
        
        previewLayer.frame = previewView.layer.bounds;
        
        previewLayer.contentsGravity = kCAGravityResizeAspectFill;
        
        previewLayer.videoGravity = AVLayerVideoGravityResizeAspectFill;
        
        previewLayer.connection.videoOrientation = cOrientation;
        
        [previewView.layer insertSublayer:previewLayer atIndex:0];
        
        // 等待摄像头启动
        sleep(1);
        
    }
    
    return self;
}

- (void)setFrontSessionPreset:(AVCaptureSession *)mCaptureSession {
    if ([mCaptureSession canSetSessionPreset:AVCaptureSessionPreset1920x1080]) {
        mCaptureSession.sessionPreset = AVCaptureSessionPreset1920x1080;
    } else if ([mCaptureSession canSetSessionPreset:AVCaptureSessionPreset1280x720]) {
        mCaptureSession.sessionPreset = AVCaptureSessionPreset1280x720;
    } else {
        mCaptureSession.sessionPreset = AVCaptureSessionPreset640x480;
    }
}

/**
 *  取得指定位置的摄像头
 * *@param position 摄像头位置
 * * @return 摄像头设备
 */
-(AVCaptureDevice *)getCameraDeviceWithPosition:(AVCaptureDevicePosition )position
{
    NSArray *cameras= [AVCaptureDevice devicesWithMediaType:AVMediaTypeVideo];
    
    for (AVCaptureDevice *camera in cameras)
    {
        if ([camera position]==position)
        {
            return camera;
        }
    }
    return nil;
}

#pragma mark -拍摄照片
- (void)takePicture:(TKHandleResultBlock)handerBlock{
    
    //根据设备输出获得连接
    AVCaptureConnection *videoConnection = [captureStillImageOutput connectionWithMediaType:AVMediaTypeVideo];
    
    [captureStillImageOutput captureStillImageAsynchronouslyFromConnection:videoConnection completionHandler:^(CMSampleBufferRef imageDataSampleBuffer, NSError *error)
     {
         if (mCaptureSession && [mCaptureSession isRunning]) {
             dispatch_async(dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_BACKGROUND, 0), ^{
                 [mCaptureSession stopRunning];
             });
             
         }
         
         UIImage *image = nil;
         
         if (imageDataSampleBuffer)
         {
             NSData *imageData = [AVCaptureStillImageOutput jpegStillImageNSDataRepresentation:imageDataSampleBuffer];
             
             image = [UIImage imageWithData:imageData];
             
             if (dPosition == 1) {//证件照横向旋转
                 
                 TKLogInfo(@"device_oritention = %ld", (long)[[UIDevice currentDevice] orientation]);
                 
//                 switch ([[UIDevice currentDevice] orientation]) {//旋转图片的方向
//                         
//                     case UIInterfaceOrientationPortrait:
//                         image = [UIImage imageWithCGImage:image.CGImage scale:1.0f orientation:UIImageOrientationUp];
//                         break;
//                         
//                     case UIInterfaceOrientationPortraitUpsideDown:
//                         image = [UIImage imageWithCGImage:image.CGImage scale:1.0f orientation:UIImageOrientationLeft];
//                         break;
//                         
//                     case UIInterfaceOrientationLandscapeLeft:
//                         image = [UIImage imageWithCGImage:image.CGImage scale:1.0f orientation:UIImageOrientationDown];
//                         break;
//                         
//                     case UIInterfaceOrientationLandscapeRight:
//                         image = [UIImage imageWithCGImage:image.CGImage scale:1.0f orientation:UIImageOrientationUp];
//                         break;
//                         
//                     default:
//                        
//                         break;
//                 }
                 
                 image = [UIImage imageWithCGImage:image.CGImage scale:1.0f orientation:UIImageOrientationUp];
                 
             }else{//人脸照旋转
                 //大头照不镜像
                 image = [TKCommonUtil tkFixOrientation:image];
//                 image = [TKCommonUtil tkFixOrientation:[UIImage imageWithCGImage:image.CGImage scale:1.0f orientation:UIImageOrientationLeftMirrored]];
                 
             }
             
         }
     
         if (handerBlock) {
             handerBlock(image);
         }
     }];
    
}


/**
 *  Description 重拍图片
 */
- (void)reTakePicture
{
    if (mCaptureSession && ![mCaptureSession isRunning]) {
        dispatch_async(dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_BACKGROUND, 0), ^{
            [mCaptureSession startRunning];
        });
        
        // 等待摄像头启动
        sleep(1);
    }
    
}

/**
 *  Description 取消图片拍摄
 */
- (void)cancelTakePicture{
    
    if (mCaptureSession && [mCaptureSession isRunning]) {
        dispatch_async(dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_BACKGROUND, 0), ^{
            [mCaptureSession stopRunning];
        });
        
    }
}

- (void)startRecording
{
    if(mCaptureSession && ![mCaptureSession isRunning]){
        dispatch_async(dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_BACKGROUND, 0), ^{
            [mCaptureSession startRunning];
        });
        
        // 等待摄像头启动
        sleep(1);
    }
    
    if (captureMovieFileOutput && ![captureMovieFileOutput isRecording]) {
        
        NSURL *outputFileUrl = [self tempFileURL] ;
        // 生成缓存文件
        
        AVCaptureConnection *videoConnection = [self connectionWithMediaType:AVMediaTypeVideo fromConnections:captureMovieFileOutput.connections];
        
        if (videoConnection.isActive) {
            
             [captureMovieFileOutput startRecordingToOutputFileURL:outputFileUrl recordingDelegate:self];
        }
    }
}

- (void)stopRecording
{
    if ([captureMovieFileOutput isRecording]) {
        
        [captureMovieFileOutput stopRecording];
    }
    
    if([mCaptureSession isRunning]){
        dispatch_async(dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_BACKGROUND, 0), ^{
            [mCaptureSession stopRunning];
        });
        
    }
    
}

- (void)resetRecording
{
    if ([captureMovieFileOutput isRecording]) {
        
        [captureMovieFileOutput stopRecording];
    }
    
    if(![mCaptureSession isRunning]){
        dispatch_async(dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_BACKGROUND, 0), ^{
            [mCaptureSession startRunning];
        });
        
        // 等待摄像头启动
        sleep(1);
    }
}

- (float)getRecordTime
{
    if (captureMovieFileOutput) {
        
        CGFloat currentSecond = captureMovieFileOutput.recordedDuration.value/captureMovieFileOutput.recordedDuration.timescale;
        
        return currentSecond;
    }
    
    return 0;
    
}

- (BOOL)isRecordingVideo
{
    
    if (captureMovieFileOutput) {
        
        return captureMovieFileOutput.isRecording;
    }
    
    return 0;
    
}


- (void)setVideoFileName:(NSString *)vfName
{
    m_nameStr = vfName;
}


#pragma mark -
#pragma mark AVCaptureFileOutputRecordingDelegate
- (void)captureOutput:(AVCaptureFileOutput *)captureOutput didStartRecordingToOutputFileAtURL:(NSURL *)fileURL fromConnections:(NSArray *)connections {
    TKLogInfo(@"begin");
    
    CGFloat currentSecond = captureOutput.recordedDuration.value/captureOutput.recordedDuration.timescale;// 计算当前在第几秒
    
    if (_rDelegate && [_rDelegate respondsToSelector:@selector(isStartRecording:)]) {
        [_rDelegate isStartRecording:currentSecond];
    }

}
- (void)captureOutput:(AVCaptureFileOutput *)captureOutput didFinishRecordingToOutputFileAtURL:(NSURL *)outputFileURL fromConnections:(NSArray *)connections error:(NSError *)error {
   
    CGFloat currentSecond = captureOutput.recordedDuration.value/captureOutput.recordedDuration.timescale;// 计算当前在第几秒
    
    if (_rDelegate && [_rDelegate respondsToSelector:@selector(isFinishRecording:)]) {
        [_rDelegate isFinishRecording:currentSecond];
    }
    TKLogInfo(@"finish");
    if (!m_nameStr) {
        return ;
    }
    // 进行文件复制
    
    NSString *strTempPath = [NSHomeDirectory() stringByAppendingFormat:@"/Documents/temp.mp4"];
    
    NSString *strMovPath = [NSHomeDirectory() stringByAppendingFormat:@"/Documents/%@.mp4",m_nameStr];
    NSFileManager *fm = [[NSFileManager alloc] init];
    
    [fm removeItemAtURL:[NSURL fileURLWithPath:strTempPath] error:nil];
    
    [fm removeItemAtURL:[NSURL fileURLWithPath:strMovPath] error:nil];
    
    // 是否存在
    BOOL isExistsOk = [fm fileExistsAtPath:[[self tempFileURL] path]];
    TKLogInfo(@"文件 %d 存在",isExistsOk);
    BOOL isCopyOk =YES;
    if (isExistsOk) {
        
        isCopyOk = [fm copyItemAtURL:[self tempFileURL] toURL:[NSURL fileURLWithPath:strMovPath] error:&error];
        // 不管成功还是失败。都删除这个缓存
        BOOL isDeleteOk = [fm removeItemAtURL:[self tempFileURL] error:nil];
        TKLogInfo(@"删除缓存 %d",isDeleteOk);
        
    }
    
}

-(NSURL *)tempFileURL {
    return [NSURL fileURLWithPath:[NSString stringWithFormat:@"%@%@", NSTemporaryDirectory(), TEMP_MOVE_NAME]];
}

-(AVCaptureConnection *)connectionWithMediaType:(NSString *)mediaType fromConnections:(NSArray *)connections
{
    for ( AVCaptureConnection *connection in connections ) {
        for ( AVCaptureInputPort *port in [connection inputPorts] ) {
            if ( [[port mediaType] isEqual:mediaType] ) {
                return connection;
            }
        }
    }
    return nil;
}

- (void)dealloc
{
    mCaptureSession = nil;
    
    captureStillImageOutput = nil;
    
    captureMovieFileOutput = nil;
    
    TKLogInfo(@"%@%s",NSStringFromClass([self class]), __FUNCTION__);
}
@end
