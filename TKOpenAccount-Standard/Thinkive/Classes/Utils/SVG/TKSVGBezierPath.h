//
//  TKSVGBezierPath.h
//  TKOpenAccount-Standard
//
//  Created by <PERSON> on 2021/8/30.
//  Copyright © 2021 thinkive. All rights reserved.
//



NS_ASSUME_NONNULL_BEGIN

@class TKSVGBezierPath;

FOUNDATION_EXTERN CGRect TKSVGAdjustCGRectForContentsGravity(CGRect aRect, CGSize aSize, NSString *aGravity);
FOUNDATION_EXTERN CGRect TKSVGBoundingRectForPaths(NSArray<TKSVGBezierPath*> *paths);
FOUNDATION_EXTERN void TKSVGDrawPaths(NSArray<TKSVGBezierPath*> *paths, CGContextRef ctx, CGRect rect,
                                    __nullable CGColorRef defaultFillColor,
                                    __nullable CGColorRef defaultStrokeColor);
FOUNDATION_EXTERN void TKSVGDrawPathsWithBlock(NSArray<TKSVGBezierPath*> * const paths,
                                             CGContextRef const ctx,
                                             CGRect rect,
                                             void (^drawingBlock)(TKSVGBezierPath *path));

/// A NSBezierPath or UIBezierPath subclass that represents an SVG path
/// and its SVG attributes
@interface TKSVGBezierPath : UIBezierPath


/*!
 * @brief A set of paths and their attributes.
 *
 */
@property(nonatomic, readonly) NSDictionary<NSString*, id> *svgAttributes;


/*!
 * @brief The string representation of an SVG.
 *
 */
@property(nonatomic, readonly) NSString *SVGRepresentation;



/*!
 * @brief Returns an array of SVGBezierPaths given an SVG's URL.
 *
 * @param aURL The URL from which to load an SVG.
 *
 */
+ (NSArray<TKSVGBezierPath*> *)pathsFromSVGAtURL:(NSURL *)aURL;


/*!
 * @brief Returns an array of paths given the XML string of an SVG.
 *
 */
+ (NSArray<TKSVGBezierPath*> *)pathsFromSVGString:(NSString *)svgString;

/*!
 * @brief Returns a new path with the values of `attributes` added to `svgAttributes`
 *
 * @param attributes A dictionary of SVG attributes to set.
 *
 */
- (TKSVGBezierPath *)pathBySettingSVGAttributes:(NSDictionary *)attributes;

/*!
 * @brief The value of the SVG's viewBox attribute, expressed as a CGRect. If there is
 * no viewBox attribute, this property will be CGRect.null
 *
 */
@property(nonatomic, readonly) CGRect viewBox;

#if !TARGET_OS_IPHONE
@property(nonatomic, readonly) CGPathRef CGPath;
#endif


+ (void)resetCache;


#if !TARGET_OS_IPHONE
- (void)applyTransform:(CGAffineTransform)transform;
#endif
@end
NS_ASSUME_NONNULL_END

