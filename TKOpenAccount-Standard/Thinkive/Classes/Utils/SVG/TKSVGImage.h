//
//  TKSVGImage.h
//  TKOpenAccount-Standard
//
//  Created by <PERSON> on 2022/11/24.
//  Copyright © 2022 thinkive. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

/// 根据UIImage做api 适配
@interface TKSVGImage : NSObject

+ (nullable instancetype)imageNamed:(NSString *)name;

//  根据imageName转换结果
@property(nonatomic, copy) IBInspectable NSString *svgName;

@property(nonatomic, copy) IBInspectable UIColor *fillColor;

/// 仿UIImage API，实际是空实现
/// - Parameter renderingMode: UIImageRenderingMode
- (TKSVGImage *)imageWithRenderingMode:(UIImageRenderingMode)renderingMode;

@end

NS_ASSUME_NONNULL_END
