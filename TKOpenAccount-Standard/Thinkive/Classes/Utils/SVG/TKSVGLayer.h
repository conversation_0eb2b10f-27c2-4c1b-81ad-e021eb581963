//
//  TKSVGLayer.h
//  TKOpenAccount-Standard
//
//  Created by <PERSON> on 2021/8/30.
//  Copyright © 2021 thinkive. All rights reserved.
//

#import <QuartzCore/QuartzCore.h>

NS_ASSUME_NONNULL_BEGIN

@class TKSVGBezierPath;

/*!
 * @brief A CALayer subclass that renders an SVG file.
 *
 */
@interface TKSVGLayer : CALayer


/*!
 * @brief Initialises a layer that renders the SVG file at the URL.
 *
 * @param url The URL of the SVG file.
 *
 */
- (instancetype)initWithContentsOfURL:(NSURL *)url;


/*!
 * @brief The SVG paths the layer should draw.
 *
 */
@property (nonatomic, copy) NSArray<TKSVGBezierPath*> *paths;


/*!
 * @brief A color to fill the SVG shape with.
 *
 * @discussion Setting this property solidly fills the shape formed by the SVG path with the given color.
 *
 */
@property(nonatomic) CGColorRef fillColor;



/*!
 * @brief The color to stroke the path with.
 *
 * @discussion Setting this property solidly colors the path generated by the SVG file.
 *
 */
@property(nonatomic) CGColorRef strokeColor;


/*!
 * @brief Specifies whether line thickness should be scaled when scaling paths.
 *
 */
@property(nonatomic) BOOL scaleLineWidth;


/*!
 * @brief The value of the SVG's viewBox attribute, expressed as a CGRect. If there is
 * no viewBox attribute, this property will be CGRect.null
 *
 */
@property(nonatomic, readonly) CGRect viewBox;

@end
NS_ASSUME_NONNULL_END
