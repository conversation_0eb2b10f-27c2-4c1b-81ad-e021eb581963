//
//  TKSVGImage.m
//  TKOpenAccount-Standard
//
//  Created by <PERSON> on 2022/11/24.
//  Copyright © 2022 thinkive. All rights reserved.
//

#import "TKSVGImage.h"

@interface TKSVGImage()


@end


@implementation TKSVGImage

#pragma mark - 构造方法
+ (nullable instancetype)imageNamed:(NSString *)name {
    return [[self alloc] initWithImageName:name];
}

- (nullable instancetype)initWithImageName:(NSString *)name {
    if (self = [super init]) {
    
        self.svgName = name;
    }
    
    return self;
}


#pragma mark - Public selector
/// 仿UIImage API，实际是空实现
/// - Parameter renderingMode: UIImageRenderingMode
- (TKSVGImage *)imageWithRenderingMode:(UIImageRenderingMode)renderingMode {
    return self;
}

@end
