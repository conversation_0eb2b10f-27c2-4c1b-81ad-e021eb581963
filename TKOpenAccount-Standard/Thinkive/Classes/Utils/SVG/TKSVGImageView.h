//
//  TKSVGImageView.h
//  TKOpenAccount-Standard
//
//  Created by <PERSON> on 2021/8/30.
//  Copyright © 2021 thinkive. All rights reserved.
//


#import "TKSVGImage.h"

NS_ASSUME_NONNULL_BEGIN

@class TKSVGBezierPath;


/*!
 * @brief A view that renders an SVG file.
 *
 */
IB_DESIGNABLE
@interface TKSVGImageView : UIView

/*!
 * @brief Initialises a view that renders the provided SVG.
 * @param url The URL of the SVG file.
 * @code let url = NSBundle.mainBundle().URLForResource("svg_file_name", withExtension: "svg")!
 let TKSVGImageView = TKSVGImageView(contentsOfURL: url)
 *
 */
- (instancetype)initWithContentsOfURL:(NSURL *)url;

// Allows for setting the SVG via IB
@property(nonatomic, copy) IBInspectable NSString *svgName;

/*!
 * @discussion The SVG paths the view should draw.
 *
 */
@property (nonatomic, copy) NSArray<TKSVGBezierPath*> *paths;


/*!
 * @brief A color to fill the SVG shape with.
 * @discussion Setting this property solidly fills the shape formed by the SVG path with the given color.
 *
 */
@property(nonatomic, copy) IBInspectable UIColor *fillColor;


/*!
 * @brief The color to stroke the path with.
 * @discussion Setting this property solidly colors the path generated by the SVG file.
 *
 */
@property(nonatomic, copy) IBInspectable UIColor *strokeColor;


/*!
 * @brief Specifies whether line thickness should be scaled when scaling paths.
 *
 */
@property(nonatomic) IBInspectable BOOL scaleLineWidth;

/*!
 * @brief The value of the SVG's viewBox attribute, expressed as a CGRect. If there is
 * no viewBox attribute, this property will be CGRect.null
 *
 */
@property(nonatomic, readonly) CGRect viewBox;

/// 仿UIImageView api。设置SVG Image
@property(nonatomic, copy) TKSVGImage *image;

/// 仿UIImageView api。设置tintColor
@property(nonatomic,strong)   UIColor     *tintColor;

@end
NS_ASSUME_NONNULL_END
