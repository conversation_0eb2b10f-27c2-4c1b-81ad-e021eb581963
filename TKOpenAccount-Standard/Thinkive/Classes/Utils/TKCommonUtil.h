//
//  TKCommonUtil.h
//  TKOpenAccount-Standard
//
//  Created by <PERSON><PERSON> on 2016/12/9.
//  Copyright © 2016年 thinkive. All rights reserved.
//

#import <Foundation/Foundation.h>

typedef void(^TKCommonUtilIsMuteBlock)(BOOL flag);//是否静音

@interface TKCommonUtil : NSObject
/**
 *<AUTHOR> 2019年11月20日10:39:32
 *@TKCommonUtil单例
 */
+(TKCommonUtil *)shareInstance;

+ (BOOL)isCurrentViewControllerVisible:(UIViewController *)viewController;

+ (UIViewController *)getCurrentVisibleVC;


/**
 *
 *  @description 设备是否插入耳机
 *
 *  @method isHeadsetPluggedIn
 */
+ (BOOL)isHeadsetPluggedIn;

/**
 *
 *  @description 对字符串做网络编码
 *
 *  @method encodeToPercentEscapeString:
 *  @param  input 需编码字符串
 */
+ (NSString *)encodeToPercentEscapeString:(NSString *)input;

/**
 *
 *  @description 将字典对象转成json自负串
 *
 *  @method wraperDicToJsonStr:
 *  @param  rParams 字典对象
 */
+ (NSString *)wraperDicToJsonStr:(NSDictionary*)rParams;

/**
 *
 *  @description 判断是不是ipv6网络
 *
 *  @method isIpv6
 */
+ (BOOL)isIpv6;

/**
 *
 *  @description 旋转图片的方向
 *
 *  @method tkFixOrientation:
 *  @param  image 图片对象
 */
+ (UIImage *)tkFixOrientation:(UIImage*)image;

/**
 *
 *  @description 获取大数据标识
 *
 *  @method fetchAppStatisticsMarker
 */
+ (NSString *)fetchAppStatisticsMarker;

/**
 *
 *  @description 将视图指定区域转换为图片
 *
 *  @method fetchAppStatisticsMarker
 */
+ (UIImage *)imageCropFromView : (UIView *)cView withRect:(CGRect)cRect;

//获取当前设备是否静音，iOS5以后使用
-(void)getMute:(TKCommonUtilIsMuteBlock)flagBlock;

#pragma mark 裁剪图片大小
+ (UIImage*)imageByScalingNotCroppingForSize:(UIImage*)anImage toSize:(CGSize)frameSize;

/**
 *  @description 自动设置音频播放工具
 */
+ (void)autoHeadsetState;

/// 签名单向视频文件
/// @param path 文件路径
+ (NSString *)signOneWayVideo:(NSString *)path;


/**
 *  @description 字符串同音字替换要求的字
 *  @convertParams  同音字的音标和对应字
 *  @word  需要转换的文本
 */
+(NSString *)convertWords:(NSMutableDictionary *)convertParams withWord:(NSString *)word;

//url拼接参数，兼容各版本H5框架
+(NSString *)url:(NSString *)url appendingParamStr:(NSString *)paramStr;


//切换div&p标签转换为为span标签
+(NSString *)switchLabelToSpan:(NSString *)string;


//屏幕旋转
//allowRotation yes横屏，No竖屏
+ (void)switchInterfaceOrientationWithAllowRotation:(BOOL)allowRotation vc:(UIViewController *)vc;

/**
 * 将指定颜色与白色进行混合，比例由ratio决定。
 * @param colorHex 原色
 * @param ratio ratio: 0.0~1.0，0=原色，1=白色
 * @return 混合后的颜色
 * 使用示例：
 * [TKCommonUtil blendWithWhite:@"#1061FF" ratio:0.72f]
 */
+ (NSString *)blendWithWhite:(NSString *)colorHex ratio:(float)ratio;
@end
