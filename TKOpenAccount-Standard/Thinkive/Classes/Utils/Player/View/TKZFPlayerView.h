//
//  TKZFPlayerView.h
//  TKOpenAccount-Standard
//
//  Created by <PERSON> on 2022/12/7.
//  Copyright © 2022 thinkive. All rights reserved.
//

#import <UIKit/UIKit.h>
#import "TKZFPlayerConst.h"

NS_ASSUME_NONNULL_BEGIN

@interface TKZFPlayerView : UIView

/// 播放器内容视图
@property (nonatomic, strong) UIView *playerView;

/// 决定内容如何缩放以适应视图
@property (nonatomic, assign) TKZFPlayerScalingMode scalingMode;

/// 视频大小
@property (nonatomic, assign) CGSize presentationSize;

/// playerView的封面
@property (nonatomic, strong, readonly) UIImageView *coverImageView;

@end

NS_ASSUME_NONNULL_END
