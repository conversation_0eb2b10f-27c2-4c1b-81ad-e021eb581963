//
//  TKZFVolumeBrightnessView.h
//  
//
//
//


#import <UIKit/UIKit.h>

typedef NS_ENUM(NSInteger, TKZFVolumeBrightnessType) {
    TKZFVolumeBrightnessTypeVolume,       // volume
    TKZFVolumeBrightnessTypeumeBrightness // brightness
};

@interface TKZFVolumeBrightnessView : UIView

@property (nonatomic, assign, readonly) TKZFVolumeBrightnessType volumeBrightnessType;
@property (nonatomic, strong, readonly) UIProgressView *progressView;
@property (nonatomic, strong, readonly) UIImageView *iconImageView;

- (void)updateProgress:(CGFloat)progress withVolumeBrightnessType:(TKZFVolumeBrightnessType)volumeBrightnessType;

/// 添加系统音量view
- (void)addSystemVolumeView;

/// 移除系统音量view
- (void)removeSystemVolumeView;

@end
