//
//  TKSpeedSelectView.m
//  TKOpenAccount-Standard
//
//  Created by <PERSON> on 2023/4/11.
//  Copyright © 2023 thinkive. All rights reserved.
//

#import "TKSpeedSelectView.h"

@interface TKSpeedSelectView () <UITableViewDelegate, UITableViewDataSource>

@property (nonatomic, strong) UIImageView *checkmarkImageView;
@property (nonatomic, strong) UIImageView *triangleImageView;
@property (nonatomic, strong) UITableView *tableView;

@property (nonatomic, strong) NSMutableArray<NSNumber *> *speeds;
@property (nonatomic, assign) CGFloat tableWidth;
@property (nonatomic, readwrite, assign) int selectedSpeedIndex;


@end

@implementation TKSpeedSelectView

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        // 设置选项倍速
        self.speeds = [NSMutableArray arrayWithObjects:@(2.0), @(1.5), @(1.0), @(0.5), nil];
        // 设置默认选中的倍速
        self.selectedSpeed = 1.0;
        // 设置tableview宽度
        self.tableWidth = 130;
        
        // 初始化tableview
        [self addSubview:self.tableView];
        [self addSubview:self.checkmarkImageView];
        [self addSubview:self.triangleImageView];
    }
    return self;
}

- (void)layoutSubviews {
    [super layoutSubviews];
    
    // 重新计算tableview的高度
    CGFloat height = self.speeds.count * self.tableView.rowHeight;
    self.tableView.frame = CGRectMake(0, 0, self.tableWidth, height);
    [self.tableView reloadData];
    
    CGRect frame = self.frame;
    CGPoint center = self.triangleImageView.center;
    CGFloat x = center.x;
    CGFloat y = center.y;
    if (frame.origin.x < 0) {
        x = 11;
    } else if (frame.origin.x + frame.size.width > self.superview.frame.size.width) {
        x = frame.size.width - 11;
    }
    self.triangleImageView.center = CGPointMake(x, y);
    
    CGFloat triangleWidth = 7;
    CGFloat triangleHeight = 6;
    self.triangleImageView.frame = CGRectMake((self.tableView.TKWidth - triangleWidth) * 0.5, self.tableView.TKBottom, triangleWidth, triangleHeight);
    
    // 更新箭头位置
    self.selectedSpeedIndex = (int)[self.speeds indexOfObject:[NSNumber numberWithDouble:self.selectedSpeed]];
    self.checkmarkImageView.center = CGPointMake(self.checkmarkImageView.center.x, self.selectedSpeedIndex * self.tableView.rowHeight + self.tableView.rowHeight * 0.5);
}

//- (void)drawRect:(CGRect)rect {
//    // 绘制三角形
//    UIBezierPath *path = [UIBezierPath bezierPath];
//    CGFloat width = self.frame.size.width;
//    CGFloat height = self.frame.size.height;
//    CGPoint p1 = CGPointMake(width/2 - 5, height - 10);
//    CGPoint p2 = CGPointMake(width/2, height - 5);
//    CGPoint p3 = CGPointMake(width/2 + 5, height - 10);
//    [path moveToPoint:p1];
//    [path addLineToPoint:p2];
//    [path addLineToPoint:p3];
//    [path closePath];
//    [[UIColor whiteColor] setFill];
//    [path fill];
//}

#pragma mark - UITableViewDelegate & UITableViewDataSource

- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    return self.speeds.count;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    
    UITableViewCell *cell = [tableView dequeueReusableCellWithIdentifier:@"TKSpeedSelectViewCell"];
    if (!cell) {
        cell = [[UITableViewCell alloc] initWithStyle:UITableViewCellStyleDefault reuseIdentifier:@"SpeedSelectCell"];
    }
    cell.textLabel.text = [NSString stringWithFormat:@"    %@X", self.speeds[indexPath.row]];
    cell.backgroundColor = [TKUIHelper colorWithHexString:@"#404040"];
    cell.textLabel.textColor = [UIColor whiteColor];
    cell.textLabel.font = [UIFont fontWithName:@"PingFang SC" size:14];
    
    // 设置cell的textLabel文字阴影
    cell.textLabel.layer.shadowColor = [[UIColor blackColor] CGColor];
    cell.textLabel.layer.shadowOffset = CGSizeMake(0, 0);
    cell.textLabel.layer.shadowOpacity = 1;
    cell.textLabel.layer.shadowRadius = 1;

    return cell;
}

- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath {
    [tableView deselectRowAtIndexPath:indexPath animated:YES];
    NSInteger speedIndex = indexPath.row;

    self.selectedSpeedIndex = (int)indexPath.row;
    self.checkmarkImageView.center = CGPointMake(self.checkmarkImageView.center.x, self.selectedSpeedIndex * self.tableView.rowHeight + self.tableView.rowHeight * 0.5);
    [tableView reloadData];
    if (self.delegate && [self.delegate respondsToSelector:@selector(speedSelectView:didSelectSpeed:)]) {
        [self.delegate speedSelectView:self didSelectSpeed:[self.speeds[speedIndex] floatValue]];
    }
}


#pragma mark - Setter && Getter
- (UITableView *)tableView {
    if (!_tableView) {
        _tableView = [[UITableView alloc] initWithFrame:CGRectMake(0, 0, self.tableWidth, 0) style:UITableViewStylePlain];
        _tableView.delegate = self;
        _tableView.dataSource = self;
        _tableView.rowHeight = 29;
        _tableView.separatorStyle = UITableViewCellSeparatorStyleSingleLine;
        _tableView.separatorColor = [UIColor colorWithWhite:1 alpha:0.15];
        _tableView.separatorInset = UIEdgeInsetsMake(0, -10, 0, 0);
        _tableView.layer.cornerRadius = 6;
        _tableView.clipsToBounds = YES;
        _tableView.backgroundColor = [TKUIHelper colorWithHexString:@"#404040"];
    }
    return _tableView;
}

- (UIImageView *)checkmarkImageView {
    if (!_checkmarkImageView) {
        UIImage *originalImage = [UIImage imageNamed:[NSString stringWithFormat:@"%@.bundle/Resources/TKOpenGeneral/tk_rate_select.png", TK_OPEN_RESOURCE_NAME]]; // 加载原始图片
        UIImage *templateImage = [originalImage imageWithRenderingMode:UIImageRenderingModeAlwaysOriginal]; // 转换为无渲染模式
        _checkmarkImageView = [[UIImageView alloc] initWithImage:templateImage];
        _checkmarkImageView.frame = CGRectMake(14, 6, 16, 16);
    }
    return _checkmarkImageView;
}

- (UIImageView *)triangleImageView {
    if (!_triangleImageView) {
        
        UIImage *originalImage = [UIImage imageNamed:[NSString stringWithFormat:@"%@.bundle/Resources/TKOpenGeneral/tk_rate_triangle.png", TK_OPEN_RESOURCE_NAME]]; // 加载原始图片
        UIImage *templateImage = [originalImage imageWithRenderingMode:UIImageRenderingModeAlwaysOriginal]; // 转换为无渲染模式
        _triangleImageView = [[UIImageView alloc] initWithImage:templateImage];
        
        CGFloat triangleWidth = 7;
        CGFloat triangleHeight = 6;
        _triangleImageView.frame = CGRectMake((self.tableView.TKWidth - triangleWidth) * 0.5, self.tableView.TKBottom, triangleWidth, triangleHeight);
    }
    return _triangleImageView;
}

- (void)setSelectedSpeed:(CGFloat)selectedSpeed {
    if (selectedSpeed <= 0.0) selectedSpeed = 1.0;
    
    _selectedSpeed = selectedSpeed;
    self.selectedSpeedIndex = (int)[self.speeds indexOfObject:[NSNumber numberWithDouble:self.selectedSpeed]];
    self.checkmarkImageView.center = CGPointMake(self.checkmarkImageView.center.x, self.selectedSpeedIndex * self.tableView.rowHeight + self.tableView.rowHeight * 0.5);
    [self.tableView reloadData];
}

@end
