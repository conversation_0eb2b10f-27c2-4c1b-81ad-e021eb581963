//
//  TKFragmentTableViewCell.m
//  TKOpenAccount-Standard
//
//  Created by <PERSON> on 2023/4/14.
//  Copyright © 2023 thinkive. All rights reserved.
//

#import "TKFragmentTableViewCell.h"

@interface TKFragmentTableViewCell()

@property (nonatomic, strong) UIImageView *iconImageView; //图标
@property (nonatomic, strong) UILabel *infoLabel; //视频信息
@property (nonatomic, strong) UILabel *timeLabel; //时间片段

@end

@implementation TKFragmentTableViewCell

- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier {
    self = [super initWithStyle:style reuseIdentifier:reuseIdentifier];
    if (self) {
        //初始化UI控件
        [self setupUI];
    }
    return self;
}

- (void)awakeFromNib {
    [super awakeFromNib];
    
    [self setupUI];
}

- (void)setupUI {
    self.backgroundColor = [UIColor colorWithWhite:0 alpha:0.7];
    
    //图标
    self.iconImageView = [[UIImageView alloc] initWithFrame:CGRectMake(20, 15, 35, 35)];
    [self.contentView addSubview:self.iconImageView];
    
    //视频信息
    self.infoLabel = [[UILabel alloc] initWithFrame:CGRectMake(self.iconImageView.TKRight + 20, 13, 200, 22)];
    self.infoLabel.textColor = [UIColor whiteColor];
    self.infoLabel.font = [UIFont fontWithName:@"PingFang SC" size:16];
    [self.contentView addSubview:self.infoLabel];
    
    //时间片段
    self.timeLabel = [[UILabel alloc] initWithFrame:CGRectMake(self.infoLabel.TKLeft, self.infoLabel.TKBottom, 200, 17)];
    self.timeLabel.textColor = [TKUIHelper colorWithHexString:@"#BBBBBB"];
    self.timeLabel.font = [UIFont fontWithName:@"PingFang SC" size:12];
    [self.contentView addSubview:self.timeLabel];
    
}

- (void)setModel:(TKVideoFragmentModel *)model {
    _model = model;
    
    self.infoLabel.TKTop = 13;
    
    //根据视频类型设置图标和文字
    switch (model.fragmentType) {
        case TKFragmentTypeNone:
            self.iconImageView.image = [UIImage imageNamed:[NSString stringWithFormat:@"%@.bundle/Resources/TKOpenGeneral/tk_player_fragment_all.png", TK_OPEN_RESOURCE_NAME]];
            self.infoLabel.text = @"观看完整视频";
            self.timeLabel.text = @"";
            self.infoLabel.center = CGPointMake(self.infoLabel.center.x, self.iconImageView.center.y);
            break;
        case TKFragmentTypeStartAsr:
            self.iconImageView.image = [UIImage imageNamed:[NSString stringWithFormat:@"%@.bundle/Resources/TKOpenGeneral/tk_player_fragment_start_asr.png", TK_OPEN_RESOURCE_NAME]];
            self.infoLabel.text = [NSString stringWithFormat:@"%@", model.fragmentRemark];
            self.timeLabel.text = model.videoTime;
            break;
        case TKFragmentTypeFaceDetectErrorStart:
            self.iconImageView.image = [UIImage imageNamed:[NSString stringWithFormat:@"%@.bundle/Resources/TKOpenGeneral/tk_player_fragment_face_error.png", TK_OPEN_RESOURCE_NAME]];
            self.infoLabel.text = [NSString stringWithFormat:@"%@", model.fragmentRemark];
            self.timeLabel.text = model.videoTime;
            break;
        case TKFragmentTypePause:
            self.iconImageView.image = [UIImage imageNamed:[NSString stringWithFormat:@"%@.bundle/Resources/TKOpenGeneral/tk_player_fragment_start_asr.png", TK_OPEN_RESOURCE_NAME]];
            self.infoLabel.text = [NSString stringWithFormat:@"%@", model.fragmentRemark];
            self.timeLabel.text = model.videoTime;
            break;
        default:
            break;
    }
    
    if (model.isSelect) {
        self.infoLabel.textColor = [TKUIHelper colorWithHexString:@"#1061FF"];
        self.timeLabel.textColor = [TKUIHelper colorWithHexString:@"#1061FF"];
//        self.iconImageView.layer.borderColor = [TKUIHelper colorWithHexString:@"#1061FF"].CGColor;
//        self.iconImageView.layer.borderWidth = 1;
    } else {
        self.infoLabel.textColor = [TKUIHelper colorWithHexString:@"#FFFFFF"];
        self.timeLabel.textColor = [TKUIHelper colorWithHexString:@"#BBBBBB"];
//        self.iconImageView.layer.borderWidth = 0;
    }
}

@end
