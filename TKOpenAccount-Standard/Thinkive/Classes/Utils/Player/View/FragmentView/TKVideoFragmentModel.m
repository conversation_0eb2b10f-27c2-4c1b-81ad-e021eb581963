//
//  TKVideoFragmentModel.m
//  TKOpenAccount-Standard
//
//  Created by <PERSON> on 2023/4/14.
//  Copyright © 2023 thinkive. All rights reserved.
//

#import "TKVideoFragmentModel.h"

@implementation TKVideoFragmentModel

#pragma mark - Init && Dealloc
/// 构造方法
/// @param dic 字典
- (instancetype)initWithDic:(NSDictionary *)dic {
    if (self = [super init]) {
        self.fragmentRemark = [dic getStringWithKey:@"fragmentRemark"];
        self.beginTime = [dic getDoubleWithKey:@"beginTime"];
        self.endTime = [dic getDoubleWithKey:@"endTime"];
        self.fragmentType = [dic getIntWithKey:@"fragmentType"];
        self.videoStartTime = [dic getDoubleWithKey:@"videoStartTime"];
        self.text = [dic getStringWithKey:@"text"];
        self.asrStatus = (TKAsrStatus)[dic getIntWithKey:@"status"];
    }
    
    return self;
}

#pragma mark - Setter && Getter
- (NSString *)videoTime {
    NSTimeInterval seconds = self.beginTime / 1000.0;
    NSInteger minutes = (NSInteger)seconds / 60;
    NSInteger secondsRemainder = (NSInteger)seconds % 60;
    NSString *beginTimeStr = [NSString stringWithFormat:@"%02ld:%02ld", (long)minutes, (long)secondsRemainder];
    
    seconds = self.endTime / 1000.0;
    minutes = (NSInteger)seconds / 60;
    secondsRemainder = (NSInteger)seconds % 60;
    NSString *endTimeStr = [NSString stringWithFormat:@"%02ld:%02ld", (long)minutes, (long)secondsRemainder];
    
    return [NSString stringWithFormat:@"%@ - %@", beginTimeStr, endTimeStr];
}

- (NSString *)frameTime {
    
    return [NSString stringWithFormat:@"%.0f", self.videoStartTime + self.beginTime];
}

- (NSString *)status {
    
    if (self.asrStatus == TKAsrStatusNone) return nil;
    return [NSString stringWithFormat:@"%i", (int)self.asrStatus];
}

- (NSString *)beginFrameDescription {
    
    NSMutableString *description = [NSMutableString string];
    [description appendString:@"{"];
    
    if (self.videoStartTime > 0) [description appendFormat:@"%@:%.0f", @"\"videoStartTime\"", self.videoStartTime];
    if (self.beginTime > 0) [description appendFormat:@",%@:%.0f", @"\"frameTime\"", self.videoStartTime + self.beginTime];
    
    NSString *frameType = self.fragmentType == TKFragmentTypeTTSStart ? @"01" : @"";
    frameType = self.fragmentType == TKFragmentTypeStartAsr ? @"02" : frameType;
    frameType = self.fragmentType == TKFragmentTypeFaceDetectErrorStart ? @"04" : frameType;
    if ([TKStringHelper isNotEmpty:frameType]) [description appendFormat:@",%@:\"%@\"", @"\"frameType\"", frameType];
    
    if ([TKStringHelper isNotEmpty:self.text] && self.fragmentType == TKFragmentTypeTTSStart) [description appendFormat:@",%@:\"%@\"", @"\"text\"", self.text];
//    if ([TKStringHelper isNotEmpty:self.status]) [description appendFormat:@",%@:%@", @"\"status\"", self.status];
    [description appendFormat:@",%@:\"%i\"", @"\"frameGroupId\"", self.frameGroupId];
    
    [description appendString:@"}"];
    return description;
}

- (NSString *)endFrameDescription {
    
    NSMutableString *description = [NSMutableString string];
    [description appendString:@"{"];
    
    if (self.videoStartTime > 0) [description appendFormat:@"%@:%.0f", @"\"videoStartTime\"", self.videoStartTime];
    if (self.endTime > 0) [description appendFormat:@",%@:%.0f", @"\"frameTime\"", self.videoStartTime + self.endTime];
    
    NSString *frameType = self.fragmentType == TKFragmentTypeTTSStart ? @"05" : @"";
    frameType = self.fragmentType == TKFragmentTypeStartAsr ? @"03" : frameType;
    frameType = self.fragmentType == TKFragmentTypeFaceDetectErrorStart ? @"06" : frameType;
    if ([TKStringHelper isNotEmpty:frameType]) [description appendFormat:@",%@:\"%@\"", @"\"frameType\"", frameType];
    
    if ([TKStringHelper isNotEmpty:self.text]) [description appendFormat:@",%@:\"%@\"", @"\"text\"", self.text];
    if ([TKStringHelper isNotEmpty:self.status]) [description appendFormat:@",%@:%@", @"\"status\"", self.status];
    [description appendFormat:@",%@:\"%i\"", @"\"frameGroupId\"", self.frameGroupId];
    
    [description appendString:@"}"];
    return description;
}

@end
