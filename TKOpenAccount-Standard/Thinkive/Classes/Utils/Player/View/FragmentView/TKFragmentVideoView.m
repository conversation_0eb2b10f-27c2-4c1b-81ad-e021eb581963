//
//  TKFragmentVideoView.m
//  TKOpenAccount-Standard
//
//  Created by <PERSON> on 2023/4/14.
//  Copyright © 2023 thinkive. All rights reserved.
//

#import "TKFragmentVideoView.h"
#import "TKFragmentTableViewCell.h"

@interface TKFragmentVideoView ()<UITableViewDelegate, UITableViewDataSource>

@property (nonatomic, readwrite, strong) UITableView *tableView;
@property (nonatomic, readwrite, strong) UIView *gradientView;


@end

@implementation TKFragmentVideoView

- (instancetype)initWithFrame:(CGRect)frame {
    if (self = [super initWithFrame:frame]) {
        [self setupUI];
    }
    
    return self;
}

- (instancetype)init {
    if (self = [super init]) {
        [self setupUI];
    }
    return self;
}

- (void)setupUI {
    self.backgroundColor = [UIColor colorWithWhite:1 alpha:0.7];
    
    [self addSubview:self.gradientView];
    
    self.tableView = [[UITableView alloc] initWithFrame:CGRectZero style:UITableViewStylePlain];
    self.tableView.delegate = self;
    self.tableView.dataSource = self;
    self.tableView.separatorStyle = UITableViewCellSeparatorStyleSingleLine;
    self.tableView.separatorColor = [UIColor colorWithWhite:1 alpha:0.15];
    self.tableView.separatorInset = UIEdgeInsetsMake(0, -10, 0, 0);
    [self.tableView registerClass:[TKFragmentTableViewCell class] forCellReuseIdentifier:@"TKFragmentTableViewCell"];
    self.tableView.backgroundColor = [UIColor colorWithWhite:0 alpha:0.3];
    [self addSubview:self.tableView];
    
    // 设置tableview宽度
    if (self.tableWidth <= 0) self.tableWidth = 130;
    if (self.tableView.rowHeight <= 0) self.tableView.rowHeight = 62.0f;
    self.maxTableHeight = 5 * self.tableView.rowHeight;
}


- (void)layoutSubviews {
    [super layoutSubviews];
    
//    self.gradientView.frame = self.bounds;
    
    // 重新计算tableview的高度
    CGFloat width = self.tableWidth;
    CGFloat height = self.videoList.count * self.tableView.rowHeight;
    if (height > self.maxTableHeight) height = self.maxTableHeight;
//    self.tableView.frame = CGRectMake(self.TKWidth - width, 0, width, self.TKHeight);
    self.tableView.frame = CGRectMake(self.TKWidth - width, 0, width, height);
}

- (void)handleTap:(UITapGestureRecognizer *)gesture
{
    // 隐藏渐变View
//    UIView *gradientView = gesture.view;
//    [UIView animateWithDuration:0.3 animations:^{
//        gradientView.alpha = 0.0;
//    } completion:^(BOOL finished) {
//        // 移除渐变View
//        [gradientView removeFromSuperview];
//    }];
    self.hidden = YES;
}

- (void)updateTableView {
    [self.tableView reloadData];
}

#pragma mark - UITableViewDataSource

- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    return self.videoList.count;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    TKFragmentTableViewCell *cell = [tableView dequeueReusableCellWithIdentifier:@"TKFragmentTableViewCell" forIndexPath:indexPath];
    TKVideoFragmentModel *video = self.videoList[indexPath.row];
    cell.model = video;
    return cell;
}

#pragma mark - UITableViewDelegate

- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath {
    [tableView deselectRowAtIndexPath:indexPath animated:YES];
    
    for (TKVideoFragmentModel *video in self.videoList) {
        video.isSelect = NO;
    }
    TKVideoFragmentModel *video = self.videoList[indexPath.row];
    video.isSelect = YES;
    [tableView reloadData];
    
    if ([self.videoDelegate respondsToSelector:@selector(tableView:didSelectVideo:)]) {
        [self.videoDelegate tableView:tableView didSelectVideo:video];
    }
}

- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath {
    return self.tableView.rowHeight;
}

#pragma mark - Setter && Getter
- (UIView *)gradientView {
    if (!_gradientView) {
        UIView *gradientView = [[UIView alloc] initWithFrame:self.bounds];
//        gradientView.backgroundColor = [UIColor clearColor];
        
        // 创建渐变Layer
        CAGradientLayer *gradientLayer = [CAGradientLayer layer];
        gradientLayer.frame = gradientView.bounds;
        gradientLayer.startPoint = CGPointMake(0, 0.5);
        gradientLayer.endPoint = CGPointMake(1, 0.5);
        gradientLayer.colors = @[(id)[UIColor colorWithWhite:0 alpha:1].CGColor, (id)[UIColor colorWithWhite:0 alpha:0].CGColor];
//        gradientLayer.colors = @[(id)[UIColor redColor].CGColor, (id)[UIColor greenColor].CGColor];
        [gradientView.layer addSublayer:gradientLayer];
        
        // 创建手势
        UITapGestureRecognizer *tapGesture = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(handleTap:)];
        [gradientView addGestureRecognizer:tapGesture];
        
        _gradientView = gradientView;
    }
    
    return _gradientView;
}

@end

