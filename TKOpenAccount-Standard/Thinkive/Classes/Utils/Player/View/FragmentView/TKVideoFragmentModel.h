//
//  TKVideoFragmentModel.h
//  TKOpenAccount-Standard
//
//  Created by <PERSON> on 2023/4/14.
//  Copyright © 2023 thinkive. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

typedef enum : NSUInteger {
    TKFragmentTypeNone,  // 无
    TKFragmentTypeTTSStart,  // 01:语音播报开始
    TKFragmentTypeStartAsr, // 02: 客户开始语音识别
    TKFragmentTypeAsrReslut,    // 03: 识别到客户结果
    TKFragmentTypeFaceDetectErrorStart, // 04:质检异常开始
    TKFragmentTypeTTSEnd,  // 05:语音播报结束
    TKFragmentTypeFaceDetectErrorEnd, // 06:质检异常结束
    TKFragmentTypePause,    // 07:暂停类型
} TKFragmentType;   // 关键帧类型

typedef enum : NSUInteger {
    TKAsrStatusNone,  // 无
    TKAsrStatusSuccess,  // 1：回答正确
    TKAsrStatusFailure, // 2：回答错误
    TKAsrStatusError,    // 3：识别异常
} TKAsrStatus;   // 关键帧类型

@interface TKVideoFragmentModel : NSObject

// 基本数据
@property (nonatomic, copy) NSString *fragmentRemark; //关键帧描述，比如，质检描述："视频中出现2人" ，语音识别描述：" 是的"
@property (nonatomic, assign) NSTimeInterval beginTime; //关键帧开始时间 单位：毫秒
@property (nonatomic, assign) NSTimeInterval endTime; //关键帧结束时间 单位：毫秒
@property (nonatomic, assign) TKFragmentType fragmentType; // 视频类型

@property (nonatomic, assign) NSTimeInterval videoStartTime; //视频开始时间 时间戳
@property (nonatomic, copy) NSString *text; // asr识别客户说话的文字
@property (nonatomic, assign) TKAsrStatus asrStatus; // 客户回答结果   1：回答正确，2：回答错误，3：识别异常
@property (nonatomic, assign) int frameGroupId; //关键帧类型分组ID

// 扩展数据
@property (nonatomic, copy) NSString *videoTime; // 视频时间段 "xx:xx - xx:xx"
@property (nonatomic, copy) NSString *frameTime; // 关键帧时间
@property (nonatomic, copy) NSString *status; // 客户回答结果   1：回答正确，2：回答错误，3：识别异常
@property (nonatomic, readwrite, assign) BOOL isSelect;
- (NSString *)beginFrameDescription;
- (NSString *)endFrameDescription;

/// 构造方法
/// @param dic 字典
- (instancetype)initWithDic:(NSDictionary *)dic;

@end

NS_ASSUME_NONNULL_END
