//
//  TKFragmentVideoView.h
//  TKOpenAccount-Standard
//
//  Created by <PERSON> on 2023/4/14.
//  Copyright © 2023 thinkive. All rights reserved.
//

#import <UIKit/UIKit.h>
#import "TKVideoFragmentModel.h"

NS_ASSUME_NONNULL_BEGIN

@protocol TKFragmentVideoViewDelegate <NSObject>

@optional
- (void)tableView:(UITableView *)tableView didSelectVideo:(TKVideoFragmentModel *)video;

@end

@interface TKFragmentVideoView : UIView

@property (nonatomic, strong) NSArray<TKVideoFragmentModel *> *videoList;
@property (nonatomic, weak) id<TKFragmentVideoViewDelegate> videoDelegate;
@property (nonatomic, assign) CGFloat tableWidth;
@property (nonatomic, assign) CGFloat maxTableHeight;

- (void)updateTableView;

@end

NS_ASSUME_NONNULL_END
