//
//  TKZFLandScapeControlView.m
//
//
// 
//


#import "TKZFLandScapeControlView.h"
#import "UIView+TKZFFrame.h"
#import "TKZFUtilities.h"
#import "TKZFPlayerConst.h"
#import "TKSpeedSelectView.h"
#import "TKFragmentVideoView.h"

@interface TKZFLandScapeControlView () <TKZFSliderViewDelegate, TKSpeedSelectViewDelegate, TKFragmentVideoViewDelegate>


/// 顶部工具栏
@property (nonatomic, strong) UIView *topToolView;
/// 返回按钮
@property (nonatomic, strong) UIButton *backBtn;
/// 标题
@property (nonatomic, strong) UILabel *titleLabel;
/// 底部工具栏
@property (nonatomic, strong) UIView *bottomToolView;
/// 播放或暂停按钮
@property (nonatomic, strong) UIButton *playOrPauseBtn;
/// 播放的当前时间 
@property (nonatomic, strong) UILabel *currentTimeLabel;
/// 滑杆
@property (nonatomic, strong) TKZFSliderView *slider;
/// 视频总时间
@property (nonatomic, strong) UILabel *totalTimeLabel;
/// 锁定屏幕按钮
//@property (nonatomic, strong) UIButton *lockBtn;
/// 时间分隔文本"/"
@property (nonatomic, strong) UILabel *timeSeparatedLabel;

@property (nonatomic, assign) BOOL isShow;
/// 选择视频片段按钮
@property (nonatomic, strong) UIButton *selectFragmentBtn;
/// 倍速选择table
@property (nonatomic, strong) TKSpeedSelectView *rateTableView;
/// 选择播放片段table
@property (nonatomic, strong) TKFragmentVideoView *fragmentVideoTableView;

/// 当前选中的片段模型
@property (nonatomic, readwrite, strong) TKVideoFragmentModel *currentfragmentModel;

/// 提示弹窗
@property (nonatomic, readwrite, strong) TKLayerView *layerView;
/// 是否已显示提示内容
@property (nonatomic, readwrite, assign) BOOL isShowPrompt;

@end

@implementation TKZFLandScapeControlView

- (void)dealloc {
    [[NSNotificationCenter defaultCenter] removeObserver:self name:UIApplicationDidChangeStatusBarFrameNotification object:nil];
}

- (instancetype)initWithFrame:(CGRect)frame {
    if (self = [super initWithFrame:frame]) {
        
        self.isLandscapeRecordPreview = YES;
        
        [self addSubview:self.topToolView];
        [self.topToolView addSubview:self.statusBarView];
        [self.topToolView addSubview:self.backBtn];
        [self.topToolView addSubview:self.titleLabel];
        [self.topToolView addSubview:self.selectFragmentBtn];
        [self addSubview:self.playOrPauseBtn];
        [self addSubview:self.bottomToolView];
        [self.bottomToolView addSubview:self.currentTimeLabel];
        [self.bottomToolView addSubview:self.timeSeparatedLabel];
        
        [self.bottomToolView addSubview:self.slider];
        [self.bottomToolView addSubview:self.totalTimeLabel];
        [self.bottomToolView addSubview:self.rateBtn];
        [self.bottomToolView addSubview:self.fastForwardBtn];
        [self.bottomToolView addSubview:self.fastBackwardBtn];
//        [self addSubview:self.lockBtn];
        
        // 设置子控件的响应事件
        [self makeSubViewsAction];
        [self resetControlView];
    
        
        /// statusBarFrame changed
        [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(layoutControllerViews) name:UIApplicationDidChangeStatusBarFrameNotification object:nil];
    }
    return self;
}

- (void)layoutSubviews {
    [super layoutSubviews];
    
    if (self.isLandscapeRecordPreview) {
        [self layoutLandscapeRecordPreviewLandScapeControlView];
    } else {
        [self layoutPortraitRecordPreviewLandScapeControlView];
    }
}

- (void)layoutLandscapeRecordPreviewLandScapeControlView
{
    CGFloat min_x = 0;
    CGFloat min_y = 0;
    CGFloat min_w = 0;
    CGFloat min_h = 0;
    CGFloat min_view_w = self.bounds.size.width;
    CGFloat min_view_h = self.bounds.size.height;
    
    CGFloat min_margin = 9;
    
    min_x = 0;
    min_y = 0;
    min_w = min_view_w;
    min_h = iPhoneX ? 110 : 80;
    self.topToolView.frame = CGRectMake(min_x, min_y, min_w, min_h);
    
    min_x = 0;
    min_y = 0;
    min_w = min_view_w;
    min_h = 20;
    self.statusBarView.frame = CGRectMake(min_x, min_y, min_w, min_h);

//    min_x = (iPhoneX && UIInterfaceOrientationIsLandscape([UIApplication sharedApplication].statusBarOrientation)) ? 44: 15;
    min_x = 53;
    if (@available(iOS 13.0, *)) {
        if (self.showCustomStatusBar) {
            min_y = self.statusBarView.tkzf_bottom;
        } else {
            min_y = UIInterfaceOrientationIsLandscape([UIApplication sharedApplication].statusBarOrientation) ? 10 : (iPhoneX ? 40 : 20);
        }
    } else {
        min_y = (iPhoneX && UIInterfaceOrientationIsLandscape([UIApplication sharedApplication].statusBarOrientation)) ? 10: (iPhoneX ? 40 : 20);
    }
    min_w = 150;
    min_h = 40;
    self.backBtn.frame = CGRectMake(min_x, min_y, min_w, min_h);
    
    min_x = self.backBtn.tkzf_right + 5;
    min_y = 0;
    min_w = min_view_w - min_x - 15 ;
    min_h = 30;
    self.titleLabel.frame = CGRectMake(min_x, min_y, min_w, min_h);
    self.titleLabel.tkzf_centerY = self.backBtn.tkzf_centerY;
    
    min_w = 158;
    min_h = 32;
    min_x = self.topToolView.TKRight - min_w - 53;
    min_y = 0;
    self.selectFragmentBtn.frame = CGRectMake(min_x, min_y, min_w, min_h);
    self.selectFragmentBtn.tkzf_centerY = self.backBtn.tkzf_centerY;
    
    min_w = 48;
    min_h = min_w;
    min_x = (min_view_w - min_w) * 0.5;
    min_y = (min_view_h - min_h) * 0.5;
    self.playOrPauseBtn.frame = CGRectMake(min_x, min_y, min_w, min_h);
    
    min_h = iPhoneX ? 100 : 73;
    min_x = 0;
    min_y = min_view_h - min_h;
    min_w = min_view_w;
    self.bottomToolView.frame = CGRectMake(min_x, min_y, min_w, min_h);
    
    min_w = 40;
    min_h = 20;
//    min_x = (iPhoneX && UIInterfaceOrientationIsLandscape([UIApplication sharedApplication].statusBarOrientation)) ? 44: 15;
    min_x = 53;
    min_y = self.bottomToolView.TKHeight - min_h - 19;
    self.currentTimeLabel.frame = CGRectMake(min_x, min_y, min_w, min_h);
    
    min_w = self.currentTimeLabel.TKWidth;
//    min_x = self.bottomToolView.tkzf_width - min_w - ((iPhoneX && UIInterfaceOrientationIsLandscape([UIApplication sharedApplication].statusBarOrientation)) ? 44: min_margin);
    min_x = self.bottomToolView.tkzf_width - min_w - 53;
    min_y = 0;
    min_h = self.currentTimeLabel.TKHeight;
    self.totalTimeLabel.frame = CGRectMake(min_x, min_y, min_w, min_h);
    self.totalTimeLabel.tkzf_centerY = self.currentTimeLabel.tkzf_centerY;
    
    min_x = self.currentTimeLabel.tkzf_right + 9;
    min_y = 0;
    min_w = self.totalTimeLabel.tkzf_left - min_x - 9;
    min_h = 30;
    CGRect sliderRect = CGRectMake(min_x, min_y, min_w, min_h);
    if (!CGRectEqualToRect(sliderRect, self.slider.frame)) {
        self.slider.frame = sliderRect;
        self.slider.tkzf_centerY = self.currentTimeLabel.tkzf_centerY;
        [self updateVideoFragmentProgress];
    }
    
    min_w = 32;
    min_h = min_margin;
    min_x = self.totalTimeLabel.TKRight - min_w;
    min_y = self.totalTimeLabel.TKTop - min_h - 20;
    self.rateBtn.frame = CGRectMake(min_x, min_y, min_w, min_h);
    
    min_w = 24;
    min_h = min_w;
    min_x = self.rateBtn.tkzf_left - min_w - 23;
    min_y = 0;
    self.fastForwardBtn.frame = CGRectMake(min_x, min_y, min_w, min_h);
    self.fastForwardBtn.tkzf_centerY = self.rateBtn.tkzf_centerY;
    
    min_w = 24;
    min_h = min_w;
    min_x = self.fastForwardBtn.tkzf_left - min_w - 32;
    min_y = 0;
    self.fastBackwardBtn.frame = CGRectMake(min_x, min_y, min_w, min_h);
    self.fastBackwardBtn.tkzf_centerY = self.rateBtn.tkzf_centerY;
    
//    min_x = (iPhoneX && UIInterfaceOrientationIsLandscape([UIApplication sharedApplication].statusBarOrientation)) ? 50: 18;
//    min_y = 0;
//    min_w = 40;
//    min_h = 40;
//    self.lockBtn.frame = CGRectMake(min_x, min_y, min_w, min_h);
//    self.lockBtn.tkzf_centerY = self.tkzf_centerY;
    
    if (!self.isShow) {
//        self.topToolView.tkzf_y = -self.topToolView.tkzf_height;
//        self.bottomToolView.tkzf_y = self.tkzf_height;
//        self.lockBtn.tkzf_left = iPhoneX ? -82: -47;
    } else {
//        self.lockBtn.tkzf_left = iPhoneX ? 50: 18;
        if (self.player.isLockedScreen) {
//            self.topToolView.tkzf_y = -self.topToolView.tkzf_height;
//            self.bottomToolView.tkzf_y = self.tkzf_height;
        } else {
//            self.topToolView.tkzf_y = 0;
//            self.bottomToolView.tkzf_y = self.tkzf_height - self.bottomToolView.tkzf_height;
        }
    }
}

- (void)layoutPortraitRecordPreviewLandScapeControlView
{
    CGFloat min_x = 0;
    CGFloat min_y = 0;
    CGFloat min_w = 0;
    CGFloat min_h = 0;
    CGFloat min_view_w = self.bounds.size.width;
    CGFloat min_view_h = self.bounds.size.height;
    
    CGFloat min_margin = 9;
    
    min_x = 0;
    min_y = 0;
    min_w = min_view_w;
    min_h = iPhoneX ? 110 : 80;
    self.topToolView.frame = CGRectMake(min_x, min_y, min_w, min_h);
    
    min_x = 0;
    min_y = 0;
    min_w = min_view_w;
    min_h = 20;
    self.statusBarView.frame = CGRectMake(min_x, min_y, min_w, min_h);

//    min_x = (iPhoneX && UIInterfaceOrientationIsLandscape([UIApplication sharedApplication].statusBarOrientation)) ? 44: 15;
    min_x = 9;
    if (@available(iOS 13.0, *)) {
        if (self.showCustomStatusBar) {
            min_y = self.statusBarView.tkzf_bottom;
        } else {
            min_y = UIInterfaceOrientationIsLandscape([UIApplication sharedApplication].statusBarOrientation) ? 10 : (iPhoneX ? 40 : 20);
        }
    } else {
        min_y = (iPhoneX && UIInterfaceOrientationIsLandscape([UIApplication sharedApplication].statusBarOrientation)) ? 10: (iPhoneX ? 40 : 20);
    }
    min_w = 24;
    min_h = 40;
    self.backBtn.frame = CGRectMake(min_x, min_y, min_w, min_h);
    
    min_x = self.backBtn.tkzf_right + 5;
    min_y = 0;
    min_w = min_view_w - min_x - 15 ;
    min_h = 30;
    self.titleLabel.frame = CGRectMake(min_x, min_y, min_w, min_h);
    self.titleLabel.tkzf_centerY = self.backBtn.tkzf_centerY;
    
    min_w = 158;
    min_h = 32;
    min_x = self.topToolView.TKRight - min_w - 9;
    min_y = 0;
    self.selectFragmentBtn.frame = CGRectMake(min_x, min_y, min_w, min_h);
    self.selectFragmentBtn.tkzf_centerY = self.backBtn.tkzf_centerY;
    
    min_w = 48;
    min_h = min_w;
    min_x = (min_view_w - min_w) * 0.5;
    min_y = (min_view_h - min_h) * 0.5;
    self.playOrPauseBtn.frame = CGRectMake(min_x, min_y, min_w, min_h);
    
    min_h = iPhoneX ? 100 : 73;
    min_x = 0;
    min_y = min_view_h - min_h;
    min_w = min_view_w;
    self.bottomToolView.frame = CGRectMake(min_x, min_y, min_w, min_h);
    
    min_w = 40;
    min_h = 20;
//    min_x = (iPhoneX && UIInterfaceOrientationIsLandscape([UIApplication sharedApplication].statusBarOrientation)) ? 44: 15;
    min_x = self.backBtn.TKLeft;
    min_y = 0;
    self.currentTimeLabel.frame = CGRectMake(min_x, min_y, min_w, min_h);
    
    min_x = self.currentTimeLabel.TKRight;
    min_w = 5;
    min_h = self.currentTimeLabel.TKHeight;
    min_y = self.currentTimeLabel.TKTop;
    self.timeSeparatedLabel.frame = CGRectMake(min_x, min_y, min_w, min_h);
    
    min_w = self.currentTimeLabel.TKWidth;
    min_h = self.currentTimeLabel.TKHeight;
    min_x = self.timeSeparatedLabel.TKRight;
    min_y = 0;
    self.totalTimeLabel.frame = CGRectMake(min_x, min_y, min_w, min_h);
    self.totalTimeLabel.tkzf_centerY = self.currentTimeLabel.tkzf_centerY;
    
    min_x = self.currentTimeLabel.tkzf_left;
    min_y = self.currentTimeLabel.TKBottom;
    min_w = min_view_w - min_x * 2;
    min_h = 30;
    CGRect sliderRect = CGRectMake(min_x, min_y, min_w, min_h);
    if (!CGRectEqualToRect(sliderRect, self.slider.frame)) {
        self.slider.frame = sliderRect;
//        self.slider.tkzf_centerY = self.currentTimeLabel.tkzf_centerY;
        [self updateVideoFragmentProgress];
    }
    
    min_w = 32;
    min_h = min_margin;
    min_x = self.slider.TKRight - min_w;
    min_y = 0;
    self.rateBtn.tkzf_centerY = self.currentTimeLabel.tkzf_centerY;
    self.rateBtn.frame = CGRectMake(min_x, min_y, min_w, min_h);
    
    min_w = 24;
    min_h = min_w;
    min_x = self.rateBtn.tkzf_left - min_w - 23;
    min_y = 0;
    self.fastForwardBtn.frame = CGRectMake(min_x, min_y, min_w, min_h);
    self.fastForwardBtn.tkzf_centerY = self.rateBtn.tkzf_centerY;
    
    min_w = 24;
    min_h = min_w;
    min_x = self.fastForwardBtn.tkzf_left - min_w - 32;
    min_y = 0;
    self.fastBackwardBtn.frame = CGRectMake(min_x, min_y, min_w, min_h);
    self.fastBackwardBtn.tkzf_centerY = self.rateBtn.tkzf_centerY;
    
//    min_x = (iPhoneX && UIInterfaceOrientationIsLandscape([UIApplication sharedApplication].statusBarOrientation)) ? 50: 18;
//    min_y = 0;
//    min_w = 40;
//    min_h = 40;
//    self.lockBtn.frame = CGRectMake(min_x, min_y, min_w, min_h);
//    self.lockBtn.tkzf_centerY = self.tkzf_centerY;
    
    if (!self.isShow) {
//        self.topToolView.tkzf_y = -self.topToolView.tkzf_height;
//        self.bottomToolView.tkzf_y = self.tkzf_height;
//        self.lockBtn.tkzf_left = iPhoneX ? -82: -47;
    } else {
//        self.lockBtn.tkzf_left = iPhoneX ? 50: 18;
        if (self.player.isLockedScreen) {
//            self.topToolView.tkzf_y = -self.topToolView.tkzf_height;
//            self.bottomToolView.tkzf_y = self.tkzf_height;
        } else {
//            self.topToolView.tkzf_y = 0;
//            self.bottomToolView.tkzf_y = self.tkzf_height - self.bottomToolView.tkzf_height;
        }
    }
}

- (void)makeSubViewsAction {
    [self.backBtn addTarget:self action:@selector(backBtnClickAction:) forControlEvents:UIControlEventTouchUpInside];
    [self.playOrPauseBtn addTarget:self action:@selector(playPauseButtonClickAction:) forControlEvents:UIControlEventTouchUpInside];
//    [self.lockBtn addTarget:self action:@selector(lockButtonClickAction:) forControlEvents:UIControlEventTouchUpInside];
    [self.rateBtn addTarget:self action:@selector(rateButtonClickAction:) forControlEvents:UIControlEventTouchUpInside];
    [self.fastForwardBtn addTarget:self action:@selector(fastForwardButtonClickAction:) forControlEvents:UIControlEventTouchUpInside];
    [self.fastBackwardBtn addTarget:self action:@selector(fastBackwardButtonClickAction:) forControlEvents:UIControlEventTouchUpInside];
    [self.selectFragmentBtn addTarget:self action:@selector(selectFragmentButtonClickAction:) forControlEvents:UIControlEventTouchUpInside];
}

#pragma mark - action

- (void)layoutControllerViews {
    [self layoutIfNeeded];
    [self setNeedsLayout];
}

- (void)backBtnClickAction:(UIButton *)sender {
//    self.lockBtn.selected = NO;
    self.player.lockedScreen = NO;
//    self.lockBtn.selected = NO;
    [self hideSpeedSelectView];
    [self cancelSelectedVideoFragment];
    
    if (self.player.orientationObserver.supportInterfaceOrientation & TKZFInterfaceOrientationMaskPortrait) {
//        [self.player enterFullScreen:NO animated:YES];
        [self.player enterFullScreen:NO animated:NO];
    }
    if (self.backBtnClickCallback) {
        self.backBtnClickCallback();
    }
}

- (void)playPauseButtonClickAction:(UIButton *)sender {
    [self playOrPause];
}

/// 根据当前播放状态取反
- (void)playOrPause {
    self.playOrPauseBtn.selected = !self.playOrPauseBtn.isSelected;
    self.playOrPauseBtn.isSelected? [self.player.currentPlayerManager play]: [self.player.currentPlayerManager pause];
}

- (void)playBtnSelectedState:(BOOL)selected {
    self.playOrPauseBtn.selected = selected;
}

- (void)lockButtonClickAction:(UIButton *)sender {
    sender.selected = !sender.selected;
    self.player.lockedScreen = sender.selected;
}

- (void)rateButtonClickAction:(UIButton *)sender {
    [self showSpeedSelectView];
}

- (void)fastForwardButtonClickAction:(UIButton *)sender {
    
    NSTimeInterval newTime = self.player.currentTime + 15;
    newTime = newTime > self.player.totalTime ? self.player.totalTime : newTime;
    @tkzf_weakify(self)
    [self.player seekToTime:newTime completionHandler:^(BOOL finished) {
        @tkzf_strongify(self)
//        self.slider.isdragging = NO;
//        if (finished) {
//            if (self.sliderValueChanged) self.sliderValueChanged(value);
//        }
        
        [self.player.currentPlayerManager play];
    }];
}

- (void)fastBackwardButtonClickAction:(UIButton *)sender {
    
    NSTimeInterval newTime = self.player.currentTime - 15;
    newTime = newTime < 0 ? 0 : newTime;
    @tkzf_weakify(self)
    [self.player seekToTime:newTime completionHandler:^(BOOL finished) {
        @tkzf_strongify(self)
//        self.slider.isdragging = NO;
//        if (finished) {
//            if (self.sliderValueChanged) self.sliderValueChanged(value);
//        }
        [self.player.currentPlayerManager play];
    }];
}

- (void)selectFragmentButtonClickAction:(UIButton *)sender {
    
    [self showFragmentSelectView];
}


- (void)showSpeedSelectView {
    
    if (_rateTableView == nil) {
        // 获取倍速按钮在父控件坐标下的位置
        CGRect convertedRect = [self.rateBtn convertRect:self.rateBtn.bounds toView:self];
        
        CGFloat width = 130;
        CGFloat height = 116;
        CGFloat x = CGRectGetMinX(convertedRect) + (convertedRect.size.width - width) * 0.5;
        x = (x + width) > self.TKRight ? self.TKRight - 10 - width : x;
        _rateTableView = [[TKSpeedSelectView alloc] initWithFrame:CGRectMake(x, CGRectGetMinY(convertedRect) - height - 15, width, height)];
        _rateTableView.delegate = self;
        _rateTableView.selectedSpeed = self.player.currentPlayerManager.rate <= 0.0 ? 1.0 : self.player.currentPlayerManager.rate;
        [self addSubview:_rateTableView];
    }
    
    _rateTableView.hidden = NO;
}

- (void)hideSpeedSelectView {
    
    _rateTableView.hidden = YES;
}


- (void)showFragmentSelectView {
    
    if (_fragmentVideoTableView == nil) {
        
        CGFloat width = 300;
        _fragmentVideoTableView = [[TKFragmentVideoView alloc] initWithFrame:self.bounds];
        _fragmentVideoTableView.videoDelegate = self;
        _fragmentVideoTableView.maxTableHeight = self.TKHeight - 20;
        _fragmentVideoTableView.tableWidth = width;
        _fragmentVideoTableView.videoList = self.fragmentModelList;
        [self addSubview:_fragmentVideoTableView];
    }
    
    _fragmentVideoTableView.hidden = NO;
}

- (void)cancelSelectedVideoFragment {
    self.selectFragmentBtn.selected = NO;
    self.selectFragmentBtn.TKWidth = 158;
    self.selectFragmentBtn.TKHeight = 32;
    self.selectFragmentBtn.TKRight = self.topToolView.TKRight - (self.isLandscapeRecordPreview ? 53 : 9);
    // 重置图片和文字的偏移量
    _selectFragmentBtn.imageEdgeInsets = UIEdgeInsetsZero;
    _selectFragmentBtn.titleEdgeInsets = UIEdgeInsetsZero;
    
    self.selectFragmentBtn.titleLabel.textColor = [UIColor whiteColor];
//    [self pause];
//    [self.slider removeVideoFragmentProgress];
    
    self.fragmentVideoTableView.hidden = YES;
}

- (void)selectedVideoFragment:(BOOL)isAll
{
    if (isAll) {
        
        [self cancelSelectedVideoFragment];
        
    } else {
        self.selectFragmentBtn.selected = YES;
        [self.selectFragmentBtn sizeToFit];
        self.selectFragmentBtn.TKWidth = self.selectFragmentBtn.TKWidth + 20;
        self.selectFragmentBtn.TKHeight = 32;
        self.selectFragmentBtn.TKRight = self.topToolView.TKRight - (self.isLandscapeRecordPreview ? 53 : 9);
//        self.selectFragmentBtn.imageEdgeInsets = UIEdgeInsetsMake(0, -8, 0, 0);
//        self.selectFragmentBtn.titleEdgeInsets = UIEdgeInsetsMake(0, 0, 0, -8);
        // 设置图片和文字的偏移量，让图片在右边，文字在左边
        _selectFragmentBtn.imageEdgeInsets = UIEdgeInsetsMake(0, _selectFragmentBtn.titleLabel.intrinsicContentSize.width, 0, -_selectFragmentBtn.titleLabel.intrinsicContentSize.width);
        _selectFragmentBtn.titleEdgeInsets = UIEdgeInsetsMake(0, -_selectFragmentBtn.imageView.intrinsicContentSize.width, 0, _selectFragmentBtn.imageView.intrinsicContentSize.width);
    }
    
    // 切换播放时间
    @tkzf_weakify(self)
    [self.player seekToTime:self.currentfragmentModel.beginTime / 1000 completionHandler:^(BOOL finished) {
        @tkzf_strongify(self)
        if (finished) {
            [self.player.currentPlayerManager play];
        }
    }];
}

- (void)updateVideoFragmentProgress {
    
    // 先移除
    [self removeVideoFragmentProgress];

    // 再添加
    for (TKVideoFragmentModel *fragment in self.fragmentModelList) {
        
        if (fragment.fragmentType == TKFragmentTypeNone) continue;
        
//        TKLogDebug(@"self.player.currentPlayerManager.totalTime = %.2f", self.player.currentPlayerManager.totalTime);
        if ( self.player.currentPlayerManager.totalTime == 0) return;
        
        CGFloat startProgress = fragment.beginTime / 1000.0f / self.player.currentPlayerManager.totalTime;
        CGFloat endProgress = fragment.endTime / 1000.0f /  self.player.currentPlayerManager.totalTime;
        
        switch (fragment.fragmentType) {
            case TKFragmentTypeTTSStart:
                break;
            case TKFragmentTypeStartAsr:
            case TKFragmentTypeAsrReslut:
            case TKFragmentTypePause:
                [self.slider setFragmentProgressViewColor:[TKUIHelper colorWithHexString:@"#1061FF"] withStartProgress:startProgress endProgress:endProgress];
                break;
            case TKFragmentTypeFaceDetectErrorStart:
                [self.slider setFragmentProgressViewColor:[TKUIHelper colorWithHexString:@"#FF4848"] withStartProgress:startProgress endProgress:endProgress];
                break;
            default:
                break;
        }
    }
}
    
- (void)removeVideoFragmentProgress {

    [self.slider removeVideoFragmentProgress];
}

// 根据当前播放时间获取视频类型
- (TKFragmentType)getVideoTypeWithTime:(NSTimeInterval)time {
    for (TKVideoFragmentModel *model in self.fragmentModelList) {
        
        // 过滤全部播放的情况
        if (model.fragmentType == TKFragmentTypeNone) {continue;}
        
        if (time * 1000 >= model.beginTime && time * 1000 <= model.endTime) {
            return model.fragmentType;
        }
    }
    // 播放到结尾处时返回观看完整视频类型
    return TKFragmentTypeNone;
}

- (void)updateRateBtnTitle:(CGFloat)speed
{
    if (speed <= 0.0) speed = 1.0;
    
    if (speed == 1.0) {
        [self.rateBtn setTitle:[NSString stringWithFormat:@"倍速"] forState:UIControlStateNormal];
    } else {
        [self.rateBtn setTitle:[NSString stringWithFormat:@"%.1fX", speed] forState:UIControlStateNormal];
    }
    self.rateTableView.selectedSpeed = speed;
}

#pragma mark - TKZFSliderViewDelegate

- (void)sliderTouchBegan:(float)value {
    self.slider.isdragging = YES;
}

- (void)sliderTouchEnded:(float)value {
    if (self.player.totalTime > 0) {
        self.slider.isdragging = YES;
        if (self.sliderValueChanging) self.sliderValueChanging(value, self.slider.isForward);
        @tkzf_weakify(self)
        [self.player seekToTime:self.player.totalTime*value completionHandler:^(BOOL finished) {
            @tkzf_strongify(self)
            self.slider.isdragging = NO;
            if (finished) {
                if (self.sliderValueChanged) self.sliderValueChanged(value);
                if (self.seekToPlay) {
                    [self.player.currentPlayerManager play];
                }
            }
        }];
    } else {
        self.slider.isdragging = NO;
        self.slider.value = 0;
    }
}

- (void)sliderValueChanged:(float)value {
    if (self.player.totalTime == 0) {
        self.slider.value = 0;
        return;
    }
    self.slider.isdragging = YES;
    NSString *currentTimeString = [TKZFUtilities convertTimeSecond:self.player.totalTime*value];
    self.currentTimeLabel.text = currentTimeString;
    if (self.sliderValueChanging) self.sliderValueChanging(value,self.slider.isForward);
}

- (void)sliderTapped:(float)value {
    [self sliderTouchEnded:value];
    NSString *currentTimeString = [TKZFUtilities convertTimeSecond:self.player.totalTime*value];
    self.currentTimeLabel.text = currentTimeString;
}


#pragma mark - TKSpeedSelectViewDelegate
- (void)speedSelectView:(TKSpeedSelectView *)speedSelectView didSelectSpeed:(CGFloat)speed {
    [self updateRateBtnTitle:speed];
    
    self.player.currentPlayerManager.rate = speed;
    _rateTableView.hidden = YES;
}


#pragma mark - TKFragmentVideoViewDelegate
- (void)tableView:(UITableView *)tableView didSelectVideo:(TKVideoFragmentModel *)fragmentModel {
    
    _fragmentVideoTableView.hidden = YES;
    
    self.currentfragmentModel = fragmentModel;
    
    switch (fragmentModel.fragmentType) {
        case TKFragmentTypeNone:
            [self selectedVideoFragment:YES];
            break;
        case TKFragmentTypeTTSStart:
        case TKFragmentTypeStartAsr:
        case TKFragmentTypeAsrReslut:
        case TKFragmentTypeFaceDetectErrorStart:
        case TKFragmentTypePause:
            // 播放选中片段
            [self selectedVideoFragment:NO];
            break;
        default:
            break;
    }
}


#pragma mark - public method

/// 重置ControlView
- (void)resetControlView {
    self.slider.value                = 0;
    self.slider.bufferValue          = 0;
    self.currentTimeLabel.text       = @"00:00";
    self.totalTimeLabel.text         = @"00:00";
    self.backgroundColor             = [UIColor clearColor];
    self.playOrPauseBtn.selected     = YES;
    self.titleLabel.text             = @"";
    self.topToolView.alpha           = 1;
    self.bottomToolView.alpha        = 1;
    self.isShow                      = NO;
//    self.lockBtn.selected            = self.player.isLockedScreen;
    self.rateTableView.selectedSpeed = 1.0;
//    self.selectFragmentBtn           = nil;
    
    [self cancelSelectedVideoFragment];
}

- (void)showControlView {
//    self.lockBtn.alpha                   = 1;
    self.isShow                          = YES;
    if (self.player.isLockedScreen) {
//        self.topToolView.tkzf_y        = -self.topToolView.tkzf_height;
//        self.bottomToolView.tkzf_y     = self.tkzf_height;
//    } else {
//        self.topToolView.tkzf_y        = 0;
//        self.bottomToolView.tkzf_y     = self.tkzf_height - self.bottomToolView.tkzf_height;
    }
//    self.lockBtn.tkzf_left               = iPhoneX ? 50: 18;
    self.playOrPauseBtn.hidden           = NO;
    self.player.statusBarHidden          = NO;
    if (self.player.isLockedScreen) {
        self.topToolView.alpha           = 0;
//        self.bottomToolView.alpha       = 0;
        self.fastForwardBtn.hidden       = YES;
        self.fastBackwardBtn.hidden      = YES;
        self.rateBtn.hidden              = YES;
    } else {
        self.topToolView.alpha           = 1;
//        self.bottomToolView.alpha      = 1;
        self.fastForwardBtn.hidden       = NO;
        self.fastBackwardBtn.hidden      = NO;
        self.rateBtn.hidden              = NO;
    }
    self.currentTimeLabel.hidden        = NO;
    self.timeSeparatedLabel.hidden        = self.isLandscapeRecordPreview;
    self.totalTimeLabel.hidden        = NO;
    
    [self updateRateBtnTitle:self.player.currentPlayerManager.rate];
    
    if (self.isShowPrompt == NO && self.isLandscapeRecordPreview == NO && self.fragmentModelList.count > 1) {
        self.isShowPrompt = YES;
        [self.layerView showTip:@"蓝色标识部分为语音回答部分，请重点关注！" textColor:@"#ffffff" bgColor:@"#000000B2"];
    }
}

- (void)hideControlView {
    self.isShow                      = NO;
//    self.topToolView.tkzf_y            = -self.topToolView.tkzf_height;
//    self.bottomToolView.tkzf_y         = self.tkzf_height;
//    self.lockBtn.tkzf_left             = iPhoneX ? -82: -47;
    self.player.statusBarHidden      = YES;
    self.playOrPauseBtn.hidden       = YES;
    self.topToolView.alpha           = 0;
//    self.bottomToolView.alpha        = 0;
    self.fastForwardBtn.hidden       = YES;
    self.fastBackwardBtn.hidden      = YES;
    self.rateBtn.hidden              = YES;
//    self.lockBtn.alpha               = 0;
    self.rateTableView.hidden        = YES;
    self.fragmentVideoTableView.hidden    = YES;
    self.currentTimeLabel.hidden   = !self.isLandscapeRecordPreview;
    self.timeSeparatedLabel.hidden   = YES;
    self.totalTimeLabel.hidden   = !self.isLandscapeRecordPreview;
}

- (BOOL)shouldResponseGestureWithPoint:(CGPoint)point withGestureType:(TKZFPlayerGestureType)type touch:(nonnull UITouch *)touch {
    CGRect backRect = [self.topToolView convertRect:self.backBtn.frame toView:self];
    if (CGRectContainsPoint(backRect, point)) {
        return NO;
    }
    CGRect sliderRect = [self.bottomToolView convertRect:self.slider.frame toView:self];
    if (CGRectContainsPoint(sliderRect, point)) {
        return NO;
    }
    CGRect rateRect = [self.bottomToolView convertRect:self.rateBtn.frame toView:self];
    if (CGRectContainsPoint(rateRect, point)) {
        return NO;
    }
    CGRect fastForwardRect = [self.bottomToolView convertRect:self.fastForwardBtn.frame toView:self];
    if (CGRectContainsPoint(fastForwardRect, point)) {
        return NO;
    }
    CGRect fastBackwardRect = [self.bottomToolView convertRect:self.fastBackwardBtn.frame toView:self];
    if (CGRectContainsPoint(fastBackwardRect, point)) {
        return NO;
    }
    if (self.player.isLockedScreen && type != TKZFPlayerGestureTypeSingleTap) { // 锁定屏幕方向后只相应tap手势
        return NO;
    }
    
    CGRect rateTableRect = [self convertRect:self.rateTableView.frame toView:self];
    if (self.rateTableView.hidden == NO && CGRectContainsPoint(rateTableRect, point)) {
        return NO;
    }
    
    CGRect fragmentVideoTableRect = [self convertRect:self.fragmentVideoTableView.frame toView:self];
    if (self.fragmentVideoTableView.hidden == NO && CGRectContainsPoint(fragmentVideoTableRect, point)) {
        return NO;
    }

    return YES;
}

- (void)videoPlayer:(TKZFPlayerController *)videoPlayer presentationSizeChanged:(CGSize)size {
//    self.lockBtn.hidden = self.player.orientationObserver.fullScreenMode == TKZFFullScreenModePortrait;
}

/// 视频view即将旋转
- (void)videoPlayer:(TKZFPlayerController *)videoPlayer orientationWillChange:(TKZFOrientationObserver *)observer {
    if (self.showCustomStatusBar) {
        if (self.hidden) {
            [self.statusBarView destoryTimer];
        } else {
            [self.statusBarView startTimer];
        }
    }
}

- (void)videoPlayer:(TKZFPlayerController *)videoPlayer currentTime:(NSTimeInterval)currentTime totalTime:(NSTimeInterval)totalTime {
    if (!self.slider.isdragging) {
        NSString *currentTimeString = [TKZFUtilities convertTimeSecond:currentTime];
        self.currentTimeLabel.text = currentTimeString;
        NSString *totalTimeString = [TKZFUtilities convertTimeSecond:totalTime];
        self.totalTimeLabel.text = totalTimeString;
        self.slider.value = videoPlayer.progress;
        
        // 不是全部播放，处理分段播放逻辑
        if (self.currentfragmentModel.fragmentType != TKFragmentTypeNone) {
            // 每隔0.5秒检查当前播放时间，切换到对应的片段
            TKFragmentType tempVideoType = [self getVideoTypeWithTime:currentTime];
            TKLogInfo(@"当前播放时间%.0f, tempVideoType = %i", currentTime, (int)tempVideoType);
            
//            if (tempVideoType == TKFragmentTypeNone) {
//                TKLogDebug(@"类型为空，不予处理");
//                return;
//            }
                  
            if (self.currentfragmentModel.fragmentType != tempVideoType) {
                NSTimeInterval newTime = 0;
                // 获取下一个播放时间
                TKVideoFragmentModel *model = nil;
                for (int i = 0; i < self.fragmentModelList.count; i++) {
                    model = self.fragmentModelList[i];
                    
                    TKLogInfo(@"当前播放的时间段%.0f- %.0f,即将播放的时间段%.0f-%.0f，当前播放时间%.0f", self.currentfragmentModel.beginTime, self.currentfragmentModel.endTime, model.beginTime, model.endTime, currentTime  * 1000);
                    
                    // 时间已过，跳过
//                    model.fragmentType != self.currentfragmentModel.fragmentType 不是当前类型或
                    if (currentTime * 1000 >= model.endTime) {
                        if (i != self.fragmentModelList.count - 1) {    // 不是最后一个
                            TKLogInfo(@"时间已过，下一个片段");
                            continue;
                        } else {    // 最后一个
                            TKLogInfo(@"最后也没有找到，暂停播放");
                            [self.player.currentPlayerManager pause];
                            break;
                        }
                    }
                    
                    newTime = model.beginTime / 1000;
                    if (newTime <= self.currentfragmentModel.beginTime / 1000) {
                        TKLogInfo(@"已切换时间，防止重复切换");
                        break;
                    }
                    
                    self.currentfragmentModel.isSelect = NO;
                    self.currentfragmentModel = model;
                    self.currentfragmentModel.isSelect = YES;
                    [self.fragmentVideoTableView updateTableView];
                    
                    TKLogInfo(@"切换播放时间为%.2f", newTime);
                    
                    // 切换到下一个播放时间
                    @tkzf_weakify(self)
                    BOOL isPlaying = self.player.currentPlayerManager.isPlaying;
                    [self.player.currentPlayerManager pause];
                    [self.player seekToTime:newTime completionHandler:^(BOOL finished) {
                        
                        @tkzf_strongify(self)
                //        self.slider.isdragging = NO;
                        if (finished) {
                //            if (self.sliderValueChanged) self.sliderValueChanged(value);
                            if (isPlaying) [self.player.currentPlayerManager play];
                        }
                    }];
                    
                    break;
                }
            }
        }
    }
}

- (void)videoPlayer:(TKZFPlayerController *)videoPlayer bufferTime:(NSTimeInterval)bufferTime {
    self.slider.bufferValue = videoPlayer.bufferProgress;
}

- (void)showTitle:(NSString *)title fullScreenMode:(TKZFFullScreenMode)fullScreenMode {
    self.titleLabel.text = title;
    self.player.orientationObserver.fullScreenMode = fullScreenMode;
//    self.lockBtn.hidden = fullScreenMode == TKZFFullScreenModePortrait;
}

/// 调节播放进度slider和当前时间更新
- (void)sliderValueChanged:(CGFloat)value currentTimeString:(NSString *)timeString {
    self.slider.value = value;
    self.currentTimeLabel.text = timeString;
    self.slider.isdragging = YES;
    [UIView animateWithDuration:0.3 animations:^{
        self.slider.sliderBtn.transform = CGAffineTransformMakeScale(1.2, 1.2);
    }];
}

/// 滑杆结束滑动
- (void)sliderChangeEnded {
    self.slider.isdragging = NO;
    [UIView animateWithDuration:0.3 animations:^{
        self.slider.sliderBtn.transform = CGAffineTransformIdentity;
    }];
}

#pragma mark - setter

- (void)setFullScreenMode:(TKZFFullScreenMode)fullScreenMode {
    _fullScreenMode = fullScreenMode;
    self.player.orientationObserver.fullScreenMode = fullScreenMode;
//    self.lockBtn.hidden = fullScreenMode == TKZFFullScreenModePortrait;
}

- (void)setShowCustomStatusBar:(BOOL)showCustomStatusBar {
    _showCustomStatusBar = showCustomStatusBar;
    self.statusBarView.hidden = !showCustomStatusBar;
}

- (void)setIsLandscapeRecordPreview:(BOOL)isLandscapeRecordPreview {
    _isLandscapeRecordPreview = isLandscapeRecordPreview;
    
    self.backBtn.TKLeft = self.isLandscapeRecordPreview ? 53 : 9;
    [self.backBtn setTitle:(isLandscapeRecordPreview ? @"退出全屏预览" : @"") forState:UIControlStateNormal];
    
    self.selectFragmentBtn.TKRight  = self.topToolView.TKRight - (self.isLandscapeRecordPreview ? 53 : 9);
    
    if (isLandscapeRecordPreview) {
        
        self.currentTimeLabel.TKLeft = 53;
        self.totalTimeLabel.TKRight = self.bottomToolView.tkzf_width - 53;
        self.timeSeparatedLabel.hidden = YES;
    } else {
        self.currentTimeLabel.TKLeft = 9;
        self.timeSeparatedLabel.TKLeft = self.currentTimeLabel.TKRight;
        self.totalTimeLabel.TKLeft = self.timeSeparatedLabel.TKRight;
        self.timeSeparatedLabel.hidden = YES;
    }
}

- (void)setFragmentModelList:(NSArray *)fragmentModelList {
    _fragmentModelList = fragmentModelList;
    
    self.selectFragmentBtn.hidden = fragmentModelList.count <= 1 ||  self.isLandscapeRecordPreview == NO;
}

#pragma mark - getter

- (TKZFPlayerStatusBar *)statusBarView {
    if (!_statusBarView) {
        _statusBarView = [[TKZFPlayerStatusBar alloc] init];
        _statusBarView.hidden = YES;
    }
    return _statusBarView;
}

- (UIView *)topToolView {
    if (!_topToolView) {
        _topToolView = [[UIView alloc] init];
        UIImage *image = [UIImage imageNamed:[NSString stringWithFormat:@"%@.bundle/Resources/TKOpenGeneral/tk_player_top_shadow.png", TK_OPEN_RESOURCE_NAME]];
        _topToolView.layer.contents = (id)image.CGImage;
    }
    return _topToolView;
}

- (UIButton *)backBtn {
    if (!_backBtn) {
        _backBtn = [UIButton buttonWithType:UIButtonTypeCustom];
        [_backBtn setImage:[UIImage imageNamed:[NSString stringWithFormat:@"%@.bundle/Resources/TKOpenGeneral/tk_player_full_back.png", TK_OPEN_RESOURCE_NAME]] forState:UIControlStateNormal];
        [_backBtn setTitle:@"退出全屏预览" forState:UIControlStateNormal];
        _backBtn.titleLabel.font = [UIFont fontWithName:@"PingFang SC" size:18];
    }
    return _backBtn;
}

- (UILabel *)titleLabel {
    if (!_titleLabel) {
        _titleLabel = [[UILabel alloc] init];
        _titleLabel.textColor = [UIColor whiteColor];
        _titleLabel.font = [UIFont systemFontOfSize:15.0];
    }
    return _titleLabel;
}

- (UIButton *)selectFragmentBtn {
    if (!_selectFragmentBtn) {
        _selectFragmentBtn = [UIButton buttonWithType:UIButtonTypeCustom];
        [_selectFragmentBtn setTitle:@"只看回答+质检片段" forState:UIControlStateNormal];
        [_selectFragmentBtn setTitleColor:[TKUIHelper colorWithHexString:@"#FFFFFF"] forState:UIControlStateSelected];
        
        [_selectFragmentBtn setTitle:@"正在看回答+质检片段" forState:UIControlStateSelected];
        [_selectFragmentBtn setTitleColor:[TKUIHelper colorWithHexString:@"#387CFF"] forState:UIControlStateSelected];

        [_selectFragmentBtn setImage:[UIImage imageNamed:[NSString stringWithFormat:@"%@.bundle/Resources/TKOpenGeneral/tk_fragment_select.png", TK_OPEN_RESOURCE_NAME]] forState:UIControlStateSelected];
        _selectFragmentBtn.titleLabel.font = [UIFont fontWithName:@"PingFang SC" size:16];
        [_selectFragmentBtn setBackgroundColor:[UIColor colorWithWhite:1 alpha:0.25]];
        _selectFragmentBtn.layer.cornerRadius = 16;
        
        // 设置图片和文字的偏移量，让图片在右边，文字在左边
        _selectFragmentBtn.imageEdgeInsets = UIEdgeInsetsMake(0, _selectFragmentBtn.titleLabel.intrinsicContentSize.width, 0, -_selectFragmentBtn.titleLabel.intrinsicContentSize.width);
        _selectFragmentBtn.titleEdgeInsets = UIEdgeInsetsMake(0, -_selectFragmentBtn.imageView.intrinsicContentSize.width, 0, _selectFragmentBtn.imageView.intrinsicContentSize.width);
        _selectFragmentBtn.hidden = YES;
    }
    return _selectFragmentBtn;
}

- (UIView *)bottomToolView {
    if (!_bottomToolView) {
        _bottomToolView = [[UIView alloc] init];
        UIImage *image = [UIImage imageNamed:[NSString stringWithFormat:@"%@.bundle/Resources/TKOpenGeneral/tk_player_bottom_shadow.png", TK_OPEN_RESOURCE_NAME]];
        _bottomToolView.layer.contents = (id)image.CGImage;
    }
    return _bottomToolView;
}

- (UIButton *)playOrPauseBtn {
    
    if (!_playOrPauseBtn) {
        _playOrPauseBtn = [UIButton buttonWithType:UIButtonTypeCustom];
        [_playOrPauseBtn setFrameY:(self.TKHeight - 44) / 2.0f];
        [_playOrPauseBtn setFrameX:(self.TKWidth - 44) / 2.0f];
        [_playOrPauseBtn setImage:[UIImage imageNamed:[NSString stringWithFormat:@"%@.bundle/Resources/TKOpenGeneral/tk_one_video_play.png", TK_OPEN_RESOURCE_NAME]] forState:UIControlStateNormal];
        [_playOrPauseBtn setImage:[UIImage imageNamed:[NSString stringWithFormat:@"%@.bundle/Resources/TKOpenGeneral/tk_one_video_stop.png", TK_OPEN_RESOURCE_NAME]] forState:UIControlStateSelected];
    }
    return _playOrPauseBtn;
}

- (UILabel *)currentTimeLabel {
    if (!_currentTimeLabel) {
        _currentTimeLabel = [[UILabel alloc] init];
        _currentTimeLabel.textColor = [UIColor whiteColor];
        _currentTimeLabel.font = [UIFont systemFontOfSize:14.0f];
        _currentTimeLabel.textAlignment = NSTextAlignmentCenter;
    }
    return _currentTimeLabel;
}

- (TKZFSliderView *)slider {
    if (!_slider) {
        _slider = [[TKZFSliderView alloc] init];
        _slider.delegate = self;
        _slider.maximumTrackTintColor = [UIColor colorWithRed:0.5 green:0.5 blue:0.5 alpha:0.8];
        _slider.bufferTrackTintColor  = [UIColor colorWithRed:1 green:1 blue:1 alpha:0.5];
        _slider.minimumTrackTintColor = [UIColor whiteColor];
        [_slider setThumbImage:[UIImage imageNamed:[NSString stringWithFormat:@"%@.bundle/Resources/TKOpenGeneral/tk_player_slider.png", TK_OPEN_RESOURCE_NAME]] forState:UIControlStateNormal];
        _slider.sliderHeight = 2;
        _slider.thumbSize = CGSizeMake(12, 12);
    }
    return _slider;
}

- (UILabel *)totalTimeLabel {
    if (!_totalTimeLabel) {
        _totalTimeLabel = [[UILabel alloc] init];
        _totalTimeLabel.textColor = [UIColor whiteColor];
        _totalTimeLabel.font = [UIFont systemFontOfSize:14.0f];
        _totalTimeLabel.textAlignment = NSTextAlignmentCenter;
    }
    return _totalTimeLabel;
}

//- (UIButton *)lockBtn {
//    if (!_lockBtn) {
//        _lockBtn = [UIButton buttonWithType:UIButtonTypeCustom];
//        [_lockBtn setImage:TKZFPlayer_Image(@"ZFPlayer_unlock-nor") forState:UIControlStateNormal];
//        [_lockBtn setImage:TKZFPlayer_Image(@"ZFPlayer_lock-nor") forState:UIControlStateSelected];
//    }
//    return _lockBtn;
//}

- (UIButton *)rateBtn {
    if (!_rateBtn) {
        _rateBtn = [UIButton buttonWithType:UIButtonTypeCustom];
        [_rateBtn setTitle:@"倍速" forState:UIControlStateNormal];
        [_rateBtn setTitleColor:UIColor.whiteColor forState:UIControlStateNormal];
        _rateBtn.titleLabel.font = [UIFont fontWithName:@"PingFang SC" size:14];
    }
    return _rateBtn;
}


- (UIButton *)fastForwardBtn {
    if (!_fastForwardBtn) {
        _fastForwardBtn = [UIButton buttonWithType:UIButtonTypeCustom];
        [_fastForwardBtn setBackgroundImage:[UIImage imageNamed:[NSString stringWithFormat:@"%@.bundle/Resources/TKOpenGeneral/tk_fast_forward.png", TK_OPEN_RESOURCE_NAME]] forState:UIControlStateNormal];
        [_fastForwardBtn setTitle:@"15" forState:UIControlStateNormal];
        [_fastForwardBtn setTitleColor:UIColor.whiteColor forState:UIControlStateNormal];
        _fastForwardBtn.titleLabel.font = [UIFont fontWithName:@"PingFang SC" size:10];
    }
    return _fastForwardBtn;
}

- (UIButton *)fastBackwardBtn {
    if (!_fastBackwardBtn) {
        _fastBackwardBtn = [UIButton buttonWithType:UIButtonTypeCustom];
        [_fastBackwardBtn setBackgroundImage:[UIImage imageNamed:[NSString stringWithFormat:@"%@.bundle/Resources/TKOpenGeneral/tk_fast_backward.png", TK_OPEN_RESOURCE_NAME]] forState:UIControlStateNormal];
        [_fastBackwardBtn setTitle:@"15" forState:UIControlStateNormal];
        [_fastBackwardBtn setTitleColor:UIColor.whiteColor forState:UIControlStateNormal];
        _fastBackwardBtn.titleLabel.font = [UIFont fontWithName:@"PingFang SC" size:10];
    }
    return _fastBackwardBtn;
}


- (TKLayerView *)layerView{
    if (!_layerView) {
        _layerView = [[TKLayerView alloc] initContentView:self withBtnTextColor:@"#ffffff"];
    }
    return _layerView;
}

- (UILabel *)timeSeparatedLabel {
    if (!_timeSeparatedLabel) {
        _timeSeparatedLabel = [[UILabel alloc] init];
        _timeSeparatedLabel.textColor = [UIColor whiteColor];
        _timeSeparatedLabel.font = [UIFont systemFontOfSize:14.0f];
        _timeSeparatedLabel.textAlignment = NSTextAlignmentCenter;
        _timeSeparatedLabel.text = @"/";
    }
    return _timeSeparatedLabel;
}

@end
