//
//  TKZFPortraitControlView.m
//
//
// 
//


#import "TKZFPortraitControlView.h"
#import "UIView+TKZFFrame.h"
#import "TKZFUtilities.h"
#import "TKZFPlayerConst.h"
#import "TKPlayerToolView.h"
#import "TKSpeedSelectView.h"
#import "TKVideoFragmentModel.h"

@interface TKZFPortraitControlView () <TKZFSliderViewDelegate, TKPlayerToolViewDelegate, TKSpeedSelectViewDelegate>
/// 底部工具栏
@property (nonatomic, strong) UIView *bottomToolView;
/// 顶部工具栏
@property (nonatomic, strong) UIView *topToolView;
/// 标题
@property (nonatomic, strong) UILabel *titleLabel;
/// 播放或暂停按钮
@property (nonatomic, strong) UIButton *playOrPauseBtn;
/// 播放的当前时间 
@property (nonatomic, strong) UILabel *currentTimeLabel;
/// 时间分隔文本"/"
@property (nonatomic, strong) UILabel *timeSeparatedLabel;
/// 滑杆
@property (nonatomic, strong) TKZFSliderView *slider;
/// 视频总时间
@property (nonatomic, strong) UILabel *totalTimeLabel;
/// 全屏按钮
@property (nonatomic, strong) UIButton *fullScreenBtn;
/// 倍速按钮
@property (nonatomic, strong) UIButton *rateBtn;
/// 快进按钮
@property (nonatomic, strong) UIButton *fastForwardBtn;
/// 快退按钮
@property (nonatomic, strong) UIButton *fastBackwardBtn;
/// 倍速选择table
@property (nonatomic, strong) TKSpeedSelectView *rateTableView;


@property (nonatomic, assign) BOOL isShow;


@property (nonatomic, strong) TKPlayerToolView *tkPlayerToolView;//视频暂停重播工具视图
@property (nonatomic, strong) UILabel *playTipLabel;//视频播放按钮底部提示文字
@property (nonatomic, readwrite, strong) UIView *netBadBgView;
@property (nonatomic, readwrite, strong) UILabel *netLabel;
@property (nonatomic, readwrite, strong) UIImageView *netErrorImg;

@property (nonatomic, strong) NSMutableDictionary *requestParam;//请求参数
@property (nonatomic, assign) BOOL isPlay;
@property (nonatomic, readwrite, assign) BOOL isMultipleClickPlayBtn;  // 是否首次点击播放按钮

@end

@implementation TKZFPortraitControlView

- (instancetype)initWithFrame:(CGRect)frame {
    if (self = [super initWithFrame:frame]) {
        // 添加子控件
        [self addSubview:self.topToolView];
        [self addSubview:self.playOrPauseBtn];
        [self addSubview:self.playTipLabel];
        [self addSubview:self.bottomToolView];
        [self.topToolView addSubview:self.titleLabel];
        [self.bottomToolView addSubview:self.currentTimeLabel];
        [self.bottomToolView addSubview:self.timeSeparatedLabel];
        [self.bottomToolView addSubview:self.slider];
        [self.bottomToolView addSubview:self.totalTimeLabel];
        [self.bottomToolView addSubview:self.fullScreenBtn];
        [self.bottomToolView addSubview:self.rateBtn];
        [self.bottomToolView addSubview:self.fastForwardBtn];
        [self.bottomToolView addSubview:self.fastBackwardBtn];
        
        // 设置子控件的响应事件
        [self makeSubViewsAction];
        
        [self resetControlView];
        self.clipsToBounds = YES;
    }
    return self;
}

- (void)layoutSubviews {
    [super layoutSubviews];
    
    CGFloat min_x = 0;
    CGFloat min_y = 0;
    CGFloat min_w = 0;
    CGFloat min_h = 0;
    CGFloat min_view_w = self.bounds.size.width;
    CGFloat min_view_h = self.bounds.size.height;
    CGFloat min_margin = 20;
    
    min_x = 0;
    min_y = 0;
    min_w = min_view_w;
    min_h = 40;
    self.topToolView.frame = CGRectMake(min_x, min_y, min_w, min_h);
    
    min_x = 15;
    min_y = 5;
    min_w = min_view_w - min_x - 15;
    min_h = 30;
    self.titleLabel.frame = CGRectMake(min_x, min_y, min_w, min_h);
    
    min_w = 48;
    min_h = min_w;
    min_x = (self.TKWidth - min_h) * 0.5;
    min_y = (self.TKHeight - min_h) * 0.5;
    self.playOrPauseBtn.frame = CGRectMake(min_x, min_y, min_w, min_h);
    
    min_w = min_view_w;
    min_h = 22;
    min_x = (self.TKWidth - min_view_w) * 0.5;
    min_y = self.playOrPauseBtn.TKBottom;
    self.playTipLabel.frame = CGRectMake(min_x, min_y, min_w, min_h);
    
    min_h = 60;
    min_x = 0;
    min_y = min_view_h - min_h;
    min_w = min_view_w;
    self.bottomToolView.frame = CGRectMake(min_x, min_y, min_w, min_h);
    
    min_x = min_margin;
    min_w = 40;
    min_h = 20;
    min_y = 0;
    self.currentTimeLabel.frame = CGRectMake(min_x, min_y, min_w, min_h);
    
    min_x = self.currentTimeLabel.TKRight;
    min_w = 5;
    min_h = self.currentTimeLabel.TKHeight;
    min_y = self.currentTimeLabel.TKTop;
    self.timeSeparatedLabel.frame = CGRectMake(min_x, min_y, min_w, min_h);
    
    min_w = 40;
    min_h = 20;
    min_x = self.timeSeparatedLabel.TKRight;
    min_y = 0;
    self.totalTimeLabel.frame = CGRectMake(min_x, min_y, min_w, min_h);
    self.totalTimeLabel.tkzf_centerY = self.currentTimeLabel.tkzf_centerY;
    
    min_x = 21;
    min_h = 12;
    min_y = self.bottomToolView.TKHeight - 15 - min_h;
    min_w = self.bottomToolView.TKWidth - 21 * 2;
    self.slider.frame = CGRectMake(min_x, min_y, min_w, min_h);
    
    min_w = 24;
    min_h = min_w;
    min_x = self.bottomToolView.tkzf_width - min_w - min_margin;
    min_y = 0;
    self.fullScreenBtn.frame = CGRectMake(min_x, min_y, min_w, min_h);
    self.fullScreenBtn.tkzf_centerY = self.currentTimeLabel.tkzf_centerY;
    
    min_w = 30;
    min_h = min_margin;
    min_x = self.fullScreenBtn.tkzf_left - min_w - min_margin;
    min_y = 0;
    self.rateBtn.frame = CGRectMake(min_x, min_y, min_w, min_h);
    self.rateBtn.tkzf_centerY = self.fullScreenBtn.tkzf_centerY;
    
    min_w = 24;
    min_h = min_w;
    min_x = self.rateBtn.tkzf_left - min_w - min_margin;
    min_y = 0;
    self.fastForwardBtn.frame = CGRectMake(min_x, min_y, min_w, min_h);
    self.fastForwardBtn.tkzf_centerY = self.fullScreenBtn.tkzf_centerY;
    
    min_w = 24;
    min_h = min_w;
    min_x = self.fastForwardBtn.tkzf_left - min_w - min_margin;
    min_y = 0;
    self.fastBackwardBtn.frame = CGRectMake(min_x, min_y, min_w, min_h);
    self.fastBackwardBtn.tkzf_centerY = self.fullScreenBtn.tkzf_centerY;
    
//    if (!self.isShow) {
//        self.topToolView.tkzf_y = -self.topToolView.tkzf_height;
//        self.bottomToolView.tkzf_y = self.tkzf_height;
//        self.playOrPauseBtn.alpha = 0;
//    } else {
//        self.topToolView.tkzf_y = 0;
//        self.bottomToolView.tkzf_y = self.tkzf_height - self.bottomToolView.tkzf_height;
//        self.playOrPauseBtn.alpha = 1;
//    }
}

- (void)makeSubViewsAction {
    [self.playOrPauseBtn addTarget:self action:@selector(playPauseButtonClickAction:) forControlEvents:UIControlEventTouchUpInside];
    [self.fullScreenBtn addTarget:self action:@selector(fullScreenButtonClickAction:) forControlEvents:UIControlEventTouchUpInside];
    [self.rateBtn addTarget:self action:@selector(rateButtonClickAction:) forControlEvents:UIControlEventTouchUpInside];
    [self.fastForwardBtn addTarget:self action:@selector(fastForwardButtonClickAction:) forControlEvents:UIControlEventTouchUpInside];
    [self.fastBackwardBtn addTarget:self action:@selector(fastBackwardButtonClickAction:) forControlEvents:UIControlEventTouchUpInside];
}

#pragma mark - action

- (void)playPauseButtonClickAction:(UIButton *)sender {
    [self playOrPause];
}

- (void)fullScreenButtonClickAction:(UIButton *)sender {
//    [self.player enterFullScreen:YES animated:YES];
    [self.player enterFullScreen:YES animated:NO];
    
    self.isMultipleClickPlayBtn = YES;
}

- (void)rateButtonClickAction:(UIButton *)sender {
    [self showSpeedSelectView];
}

- (void)fastForwardButtonClickAction:(UIButton *)sender {
    
    NSTimeInterval newTime = self.player.currentTime + 15;
    newTime = newTime > self.player.totalTime ? self.player.totalTime : newTime;
    @tkzf_weakify(self)
    [self.player seekToTime:newTime completionHandler:^(BOOL finished) {
        @tkzf_strongify(self)
//        self.slider.isdragging = NO;
//        if (finished) {
//            if (self.sliderValueChanged) self.sliderValueChanged(value);
//        }
        
        [self.player.currentPlayerManager play];
    }];
}

- (void)fastBackwardButtonClickAction:(UIButton *)sender {
    
    NSTimeInterval newTime = self.player.currentTime - 15;
    newTime = newTime < 0 ? 0 : newTime;
    @tkzf_weakify(self)
    [self.player seekToTime:newTime completionHandler:^(BOOL finished) {
        @tkzf_strongify(self)
//        self.slider.isdragging = NO;
//        if (finished) {
//            if (self.sliderValueChanged) self.sliderValueChanged(value);
//        }
        [self.player.currentPlayerManager play];
    }];
}

/// 根据当前播放状态取反
- (void)playOrPause {
    self.playOrPauseBtn.selected = !self.playOrPauseBtn.isSelected;
    self.playOrPauseBtn.isSelected? [self.player.currentPlayerManager play]: [self.player.currentPlayerManager pause];
    
//    self.playTipLabel.hidden = self.isLandscapeRecordPreview || self.playOrPauseBtn.selected;    // 横屏录制预览一直隐藏，只有竖屏录制预览才展示
    
    // 首次点击，进入全屏状态
    if (self.isMultipleClickPlayBtn == NO && self.playOrPauseBtn.selected) {
        [self fullScreenButtonClickAction:nil];
    }
}

- (void)playBtnSelectedState:(BOOL)selected {
    self.playOrPauseBtn.selected = selected;
    
    self.playTipLabel.hidden = self.isLandscapeRecordPreview || self.playOrPauseBtn.selected;    // 横屏录制预览一直隐藏，只有竖屏录制预览才展示
}

- (void)showSpeedSelectView {
    
    if (_rateTableView == nil) {
        // 获取倍速按钮在父控件坐标下的位置
        CGRect convertedRect = [self.rateBtn convertRect:self.rateBtn.bounds toView:self];
        
        CGFloat width = 130;
        CGFloat height = 116;
        CGFloat x = CGRectGetMinX(convertedRect) + (convertedRect.size.width - width) * 0.5;
        x = (x + width) > self.TKRight ? self.TKRight - 10 - width : x;
        _rateTableView = [[TKSpeedSelectView alloc] initWithFrame:CGRectMake(x, CGRectGetMinY(convertedRect) - height - 10, width, height)];
        _rateTableView.delegate = self;
        _rateTableView.selectedSpeed = self.player.currentPlayerManager.rate <= 0.0 ? 1.0 : self.player.currentPlayerManager.rate;
        [self addSubview:_rateTableView];
    }
    
    _rateTableView.hidden = NO;
}

- (void)updateVideoFragmentProgress {
    
    // 先移除
    [self removeVideoFragmentProgress];

    // 再添加
    for (TKVideoFragmentModel *fragment in self.fragmentModelList) {
        
        if (fragment.fragmentType == TKFragmentTypeNone) continue;
        
//        TKLogDebug(@"self.player.currentPlayerManager.totalTime = %.2f", self.player.currentPlayerManager.totalTime);
        if ( self.player.currentPlayerManager.totalTime == 0) return;
        
        CGFloat startProgress = fragment.beginTime / 1000.0f / self.player.currentPlayerManager.totalTime;
        CGFloat endProgress = fragment.endTime / 1000.0f /  self.player.currentPlayerManager.totalTime;
        
        switch (fragment.fragmentType) {
            case TKFragmentTypeTTSStart:
                break;
            case TKFragmentTypeStartAsr:
            case TKFragmentTypeAsrReslut:
            case TKFragmentTypePause:
                [self.slider setFragmentProgressViewColor:[TKUIHelper colorWithHexString:@"#1061FF"] withStartProgress:startProgress endProgress:endProgress];
                break;
            case TKFragmentTypeFaceDetectErrorStart:
                [self.slider setFragmentProgressViewColor:[TKUIHelper colorWithHexString:@"#FF4848"] withStartProgress:startProgress endProgress:endProgress];
                break;
            default:
                break;
        }
    }
}
    
- (void)removeVideoFragmentProgress {

    [self.slider removeVideoFragmentProgress];
}

#pragma mark - TKZFSliderViewDelegate

- (void)sliderTouchBegan:(float)value {
    self.slider.isdragging = YES;
}

- (void)sliderTouchEnded:(float)value {
    if (self.player.totalTime > 0) {
        self.slider.isdragging = YES;
        if (self.sliderValueChanging) self.sliderValueChanging(value, self.slider.isForward);
        @tkzf_weakify(self)
        [self.player seekToTime:self.player.totalTime*value completionHandler:^(BOOL finished) {
            @tkzf_strongify(self)
            self.slider.isdragging = NO;
            if (finished) {
                if (self.sliderValueChanged) self.sliderValueChanged(value);
            }
        }];
        if (self.seekToPlay) {
            [self.player.currentPlayerManager play];
        }
    } else {
        self.slider.isdragging = NO;
        self.slider.value = 0;
    }
}

- (void)sliderValueChanged:(float)value {
    if (self.player.totalTime == 0) {
        self.slider.value = 0;
        return;
    }
    self.slider.isdragging = YES;
    NSString *currentTimeString = [TKZFUtilities convertTimeSecond:self.player.totalTime*value];
    self.currentTimeLabel.text = currentTimeString;
    if (self.sliderValueChanging) self.sliderValueChanging(value,self.slider.isForward);
}

- (void)sliderTapped:(float)value {
    [self sliderTouchEnded:value];
    NSString *currentTimeString = [TKZFUtilities convertTimeSecond:self.player.totalTime*value];
    self.currentTimeLabel.text = currentTimeString;
}


- (void)updateRateBtnTitle:(CGFloat)speed
{
    if (speed <= 0.0) speed = 1.0;
    
    if (speed == 1.0) {
        [self.rateBtn setTitle:[NSString stringWithFormat:@"倍速"] forState:UIControlStateNormal];
    } else {
        [self.rateBtn setTitle:[NSString stringWithFormat:@"%.1fX", speed] forState:UIControlStateNormal];
    }
    self.rateTableView.selectedSpeed = speed;
}

#pragma mark - TKSpeedSelectViewDelegate
- (void)speedSelectView:(TKSpeedSelectView *)speedSelectView didSelectSpeed:(CGFloat)speed {
    [self updateRateBtnTitle:speed];
    
    self.player.currentPlayerManager.rate = speed;
    _rateTableView.hidden = YES;
}


#pragma mark - public method 

/** 重置ControlView */
- (void)resetControlView {
    self.bottomToolView.alpha        = 1;
    self.slider.value                = 0;
    self.slider.bufferValue          = 0;
    self.currentTimeLabel.text       = @"00:00";
    self.totalTimeLabel.text         = @"00:00";
    self.backgroundColor             = [UIColor clearColor];
    self.playOrPauseBtn.selected     = YES;
    self.playTipLabel.hidden         = YES;
    self.titleLabel.text             = @"";
    self.rateTableView.selectedSpeed = 1.0;
}

- (void)showControlView {
    self.topToolView.alpha           = 1;
//    self.bottomToolView.alpha        = 1;
    self.isShow                      = YES;
//    self.topToolView.tkzf_y            = 0;
//    self.bottomToolView.tkzf_y         = self.tkzf_height - self.bottomToolView.tkzf_height;
//    self.playOrPauseBtn.alpha        = 1;
    self.playOrPauseBtn.hidden       = NO;
    self.player.statusBarHidden      = NO;
    self.playTipLabel.hidden         =      self.isLandscapeRecordPreview || self.playOrPauseBtn.selected;    // 横屏录制预览一直隐藏，只有竖屏录制预览才展示
    
    self.currentTimeLabel.hidden     = NO;
    self.timeSeparatedLabel.hidden   = NO;
    self.totalTimeLabel.hidden       = NO;
    self.rateBtn.hidden              = NO;
    self.fullScreenBtn.hidden        = NO;
    
    if (self.needShowForwardView) self.fastForwardBtn.hidden       = NO;
    if (self.needShowForwardView) self.fastBackwardBtn.hidden      = NO;
    [self updateRateBtnTitle:self.player.currentPlayerManager.rate];
}

- (void)hideControlView {
    self.isShow                      = NO;
//    self.topToolView.tkzf_y            = -self.topToolView.tkzf_height;
//    self.bottomToolView.tkzf_y         = self.tkzf_height;
    self.player.statusBarHidden      = NO;
//    self.playOrPauseBtn.alpha        = 0;
    self.playOrPauseBtn.hidden        = YES;
    self.playTipLabel.hidden         = YES;
//    self.topToolView.alpha           = 0;
//    self.bottomToolView.alpha        = 0;
    self.currentTimeLabel.hidden     = YES;
    self.timeSeparatedLabel.hidden   = YES;
    self.totalTimeLabel.hidden       = YES;
    self.rateBtn.hidden              = YES;
    self.fastForwardBtn.hidden       = YES;
    self.fastBackwardBtn.hidden      = YES;
    self.fullScreenBtn.hidden        = YES;
    self.rateTableView.hidden        = YES;
}

- (BOOL)shouldResponseGestureWithPoint:(CGPoint)point withGestureType:(TKZFPlayerGestureType)type touch:(nonnull UITouch *)touch {
    CGRect sliderRect = [self.bottomToolView convertRect:self.slider.frame toView:self];
    if (CGRectContainsPoint(sliderRect, point)) {
        return NO;
    }
    CGRect fullScreenRect = [self.bottomToolView convertRect:self.fullScreenBtn.frame toView:self];
    if (CGRectContainsPoint(fullScreenRect, point)) {
        return NO;
    }
    CGRect rateRect = [self.bottomToolView convertRect:self.rateBtn.frame toView:self];
    if (CGRectContainsPoint(rateRect, point)) {
        return NO;
    }
    CGRect fastForwardRect = [self.bottomToolView convertRect:self.fastForwardBtn.frame toView:self];
    if (CGRectContainsPoint(fastForwardRect, point)) {
        return NO;
    }
    CGRect fastBackwardRect = [self.bottomToolView convertRect:self.fastBackwardBtn.frame toView:self];
    if (CGRectContainsPoint(fastBackwardRect, point)) {
        return NO;
    }
    
    CGRect rateTableRect = [self convertRect:self.rateTableView.frame toView:self];
    if (CGRectContainsPoint(rateTableRect, point)) {
        return NO;
    }
    return YES;
}

- (void)videoPlayer:(TKZFPlayerController *)videoPlayer currentTime:(NSTimeInterval)currentTime totalTime:(NSTimeInterval)totalTime {
    if (!self.slider.isdragging) {
        NSString *currentTimeString = [TKZFUtilities convertTimeSecond:currentTime];
        self.currentTimeLabel.text = currentTimeString;
        NSString *totalTimeString = [TKZFUtilities convertTimeSecond:totalTime];
        self.totalTimeLabel.text = totalTimeString;
        self.slider.value = videoPlayer.progress;
    }
}

- (void)videoPlayer:(TKZFPlayerController *)videoPlayer bufferTime:(NSTimeInterval)bufferTime {
    self.slider.bufferValue = videoPlayer.bufferProgress;
}

- (void)showTitle:(NSString *)title fullScreenMode:(TKZFFullScreenMode)fullScreenMode {
    self.titleLabel.text = title;
    self.player.orientationObserver.fullScreenMode = fullScreenMode;
}

/// 调节播放进度slider和当前时间更新
- (void)sliderValueChanged:(CGFloat)value currentTimeString:(NSString *)timeString {
    self.isMultipleClickPlayBtn = YES;
    
    self.slider.value = value;
    self.currentTimeLabel.text = timeString;
    self.slider.isdragging = YES;
    [UIView animateWithDuration:0.3 animations:^{
        self.slider.sliderBtn.transform = CGAffineTransformMakeScale(1.2, 1.2);
    }];
}

/// 滑杆结束滑动
- (void)sliderChangeEnded {
    self.slider.isdragging = NO;
    [UIView animateWithDuration:0.3 animations:^{
        self.slider.sliderBtn.transform = CGAffineTransformIdentity;
    }];
}


#pragma mark - setter

- (void)setFullScreenMode:(TKZFFullScreenMode)fullScreenMode {
    _fullScreenMode = fullScreenMode;
    self.player.orientationObserver.fullScreenMode = fullScreenMode;
}

#pragma mark - getter

- (UIView *)topToolView {
    if (!_topToolView) {
        _topToolView = [[UIView alloc] init];
        UIImage *image = TKZFPlayer_Image(@"ZFPlayer_top_shadow");
        _topToolView.layer.contents = (id)image.CGImage;
    }
    return _topToolView;
}

- (UILabel *)titleLabel {
    if (!_titleLabel) {
        _titleLabel = [[UILabel alloc] init];
        _titleLabel.textColor = [UIColor whiteColor];
        _titleLabel.font = [UIFont systemFontOfSize:15.0];
    }
    return _titleLabel;
}

- (UIView *)bottomToolView {
    if (!_bottomToolView) {
        _bottomToolView = [[UIView alloc] init];
        UIImage *image = [UIImage imageNamed:[NSString stringWithFormat:@"%@.bundle/Resources/TKOpenGeneral/tk_video_play_bottom_shadow.png", TK_OPEN_RESOURCE_NAME]];
        _bottomToolView.layer.contents = (id)image.CGImage;
    }
    return _bottomToolView;
}

- (UIButton *)playOrPauseBtn {
    if (!_playOrPauseBtn) {
        _playOrPauseBtn = [UIButton buttonWithType:UIButtonTypeCustom];
        [_playOrPauseBtn setFrameY:(self.TKHeight - 44) / 2.0f];
        [_playOrPauseBtn setFrameX:(self.TKWidth - 44) / 2.0f];
        [_playOrPauseBtn setImage:[UIImage imageNamed:[NSString stringWithFormat:@"%@.bundle/Resources/TKOpenGeneral/tk_one_video_play.png", TK_OPEN_RESOURCE_NAME]] forState:UIControlStateNormal];
        [_playOrPauseBtn setImage:[UIImage imageNamed:[NSString stringWithFormat:@"%@.bundle/Resources/TKOpenGeneral/tk_one_video_stop.png", TK_OPEN_RESOURCE_NAME]] forState:UIControlStateSelected];
    }
    return _playOrPauseBtn;
}

- (UILabel *)currentTimeLabel {
    if (!_currentTimeLabel) {
        _currentTimeLabel = [[UILabel alloc] init];
        _currentTimeLabel.textColor = [UIColor whiteColor];
        _currentTimeLabel.font = [UIFont systemFontOfSize:14.0f];
        _currentTimeLabel.textAlignment = NSTextAlignmentCenter;
    }
    return _currentTimeLabel;
}

- (UILabel *)timeSeparatedLabel {
    if (!_timeSeparatedLabel) {
        _timeSeparatedLabel = [[UILabel alloc] init];
        _timeSeparatedLabel.textColor = [UIColor whiteColor];
        _timeSeparatedLabel.font = [UIFont systemFontOfSize:14.0f];
        _timeSeparatedLabel.textAlignment = NSTextAlignmentCenter;
        _timeSeparatedLabel.text = @"/";
    }
    return _timeSeparatedLabel;
}

- (TKZFSliderView *)slider {
    if (!_slider) {
        _slider = [[TKZFSliderView alloc] init];
        _slider.delegate = self;
//        _slider.maximumTrackTintColor = [UIColor colorWithWhite:1 alpha:0.25];
//        _slider.bufferTrackTintColor  = [TKUIHelper colorWithHexString:@"#1061FF"];
//        _slider.minimumTrackTintColor = [UIColor whiteColor];
        _slider.maximumTrackTintColor = [UIColor colorWithRed:0.5 green:0.5 blue:0.5 alpha:0.8];
        _slider.bufferTrackTintColor  = [UIColor colorWithRed:1 green:1 blue:1 alpha:0.5];
        _slider.minimumTrackTintColor = [UIColor whiteColor];
        [_slider setThumbImage:[UIImage imageNamed:[NSString stringWithFormat:@"%@.bundle/Resources/TKOpenGeneral/tk_player_slider.png", TK_OPEN_RESOURCE_NAME]] forState:UIControlStateNormal];
        _slider.sliderHeight = 2;
        _slider.thumbSize = CGSizeMake(12, 12);
    }
    return _slider;
}

- (UILabel *)totalTimeLabel {
    if (!_totalTimeLabel) { 
        _totalTimeLabel = [[UILabel alloc] init];
        _totalTimeLabel.textColor = [UIColor whiteColor];
        _totalTimeLabel.font = [UIFont systemFontOfSize:14.0f];
        _totalTimeLabel.textAlignment = NSTextAlignmentCenter;
    }
    return _totalTimeLabel;
}

- (UIButton *)rateBtn {
    if (!_rateBtn) {
        _rateBtn = [UIButton buttonWithType:UIButtonTypeCustom];
        [_rateBtn setTitle:@"倍速" forState:UIControlStateNormal];
        [_rateBtn setTitleColor:UIColor.whiteColor forState:UIControlStateNormal];
        _rateBtn.titleLabel.font = [UIFont fontWithName:@"PingFang SC" size:14];
    }
    return _rateBtn;
}

- (UIButton *)fullScreenBtn {
    if (!_fullScreenBtn) {
        _fullScreenBtn = [UIButton buttonWithType:UIButtonTypeCustom];
        [_fullScreenBtn setImage:[UIImage imageNamed:[NSString stringWithFormat:@"%@.bundle/Resources/TKOpenGeneral/tk_one_video_full_screen.png", TK_OPEN_RESOURCE_NAME]] forState:UIControlStateNormal];
    }
    return _fullScreenBtn;
}

- (UIButton *)fastForwardBtn {
    if (!_fastForwardBtn) {
        _fastForwardBtn = [UIButton buttonWithType:UIButtonTypeCustom];
        [_fastForwardBtn setBackgroundImage:[UIImage imageNamed:[NSString stringWithFormat:@"%@.bundle/Resources/TKOpenGeneral/tk_fast_forward.png", TK_OPEN_RESOURCE_NAME]] forState:UIControlStateNormal];
        [_fastForwardBtn setTitle:@"15" forState:UIControlStateNormal];
        [_fastForwardBtn setTitleColor:UIColor.whiteColor forState:UIControlStateNormal];
        _fastForwardBtn.titleLabel.font = [UIFont fontWithName:@"PingFang SC" size:10];
    }
    return _fastForwardBtn;
}

- (UIButton *)fastBackwardBtn {
    if (!_fastBackwardBtn) {
        _fastBackwardBtn = [UIButton buttonWithType:UIButtonTypeCustom];
        [_fastBackwardBtn setBackgroundImage:[UIImage imageNamed:[NSString stringWithFormat:@"%@.bundle/Resources/TKOpenGeneral/tk_fast_backward.png", TK_OPEN_RESOURCE_NAME]] forState:UIControlStateNormal];
        [_fastBackwardBtn setTitle:@"15" forState:UIControlStateNormal];
        [_fastBackwardBtn setTitleColor:UIColor.whiteColor forState:UIControlStateNormal];
        _fastBackwardBtn.titleLabel.font = [UIFont fontWithName:@"PingFang SC" size:10];
    }
    return _fastBackwardBtn;
}

- (void)setNeedShowForwardView:(BOOL)needShowForwardView {
    _needShowForwardView = needShowForwardView;
    
    if (needShowForwardView) {
        self.fastForwardBtn.hidden = !self.isShow;
        self.fastBackwardBtn.hidden = !self.isShow;
    } else {
        self.fastForwardBtn.hidden = YES;
        self.fastBackwardBtn.hidden = YES;
    }
}

- (void)setIsLandscapeRecordPreview:(BOOL)isLandscapeRecordPreview {
    _isLandscapeRecordPreview = isLandscapeRecordPreview;
    
    self.playTipLabel.hidden = isLandscapeRecordPreview || self.playOrPauseBtn.selected;    // 横屏录制预览一直隐藏，只有竖屏录制预览才展示
}

#pragma mark - Selector
/**
 <AUTHOR> 2019年04月26日17:31:53
 @初始化懒加载视频播放按钮底部提示文字
 @return 视频播放按钮底部提示文字
 */
- (UILabel *)playTipLabel{
    if (!_playTipLabel) {
        _playTipLabel=[[UILabel alloc] init];
        _playTipLabel.text = @"点击预览";
        _playTipLabel.font = [UIFont fontWithName:@"PingFang SC" size:16];
        _playTipLabel.textColor = [UIColor colorWithWhite:1 alpha:1];
        _playTipLabel.textAlignment = NSTextAlignmentCenter;

//        float x = (self.TKWidth - self.TKWidth)/2.0f;
//        _playTipLabel.frame = CGRectMake(x, 0, self.TKWidth, 22);
//        [_playTipLabel setFrameY:self.playOrPauseBtn.TKBottom + 17];
//        _playTipLabel.layer.zPosition = MAXFLOAT;//总是保持在最上面避免被播放视频的view挡住
    }
    return _playTipLabel;
}

@end
