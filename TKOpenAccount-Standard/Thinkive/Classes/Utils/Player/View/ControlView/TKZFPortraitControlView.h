//
//  TKZFPortraitControlView.h
//
//
// 
//


#import <UIKit/UIKit.h>
#import "TKZFSliderView.h"
#import "TKZFPlayerController.h"

NS_ASSUME_NONNULL_BEGIN


@protocol TKPlayerControlViewViewDelegate <NSObject>

//重新播放按钮事件
- (void)replayVideoBtnAction;

//暂停播放按钮事件
- (void)pauseVideoBtnAction;

/**
 <AUTHOR> 2019年04月13日14:19:09
 @单向视频结果页播放视频
 */
- (void)playAction;

@end


@interface TKZFPortraitControlView : UIView

@property (nonatomic, readwrite, weak) UIView *contentView;

@property (nonatomic, weak) id<TKPlayerControlViewViewDelegate>delegate;

@property (nonatomic, strong) NSString *secondsString;//录制的视频多少秒

/// 构造方法
/// @param frame frame
/// @param param 参数
- (instancetype)initWithFrame:(CGRect)frame withParam:(NSMutableDictionary *)param;

/**
 @是否展示正在加载视频。YES-展示；NO-隐藏
 */
- (void)showLoadingVideo:(BOOL)isShow;

/**
@Auther Vie 2022年11月10日16:44:19
@param currentTime 当前播放时间
@param longestTime 最长播放时间
*/
- (void)playTime:(float)currentTime longestTime:(float)longestTime;

/**
@Auther Vie 2022年11月10日16:54:16
@暂停播放
@param currentTime 当前播放时间
@param longestTime 最长播放时间
*/
- (void)stopPlay:(float)currentTime longestTime:(float)longestTime;

/**
 <AUTHOR>
 @修改播放状态页面
 */
- (void)changePlayStatus:(BOOL)isPlay;



/// 底部工具栏
@property (nonatomic, strong, readonly) UIView *bottomToolView;

/// 顶部工具栏
@property (nonatomic, strong, readonly) UIView *topToolView;

/// 标题
@property (nonatomic, strong, readonly) UILabel *titleLabel;

/// 播放或暂停按钮
@property (nonatomic, strong, readonly) UIButton *playOrPauseBtn;

/// 播放的当前时间 
@property (nonatomic, strong, readonly) UILabel *currentTimeLabel;

/// 滑杆
@property (nonatomic, strong, readonly) TKZFSliderView *slider;

/// 视频总时间
@property (nonatomic, strong, readonly) UILabel *totalTimeLabel;

/// 全屏按钮
@property (nonatomic, strong, readonly) UIButton *fullScreenBtn;

/// 播放器
@property (nonatomic, weak) TKZFPlayerController *player;

/// slider滑动中
@property (nonatomic, copy, nullable) void(^sliderValueChanging)(CGFloat value,BOOL forward);

/// slider滑动结束
@property (nonatomic, copy, nullable) void(^sliderValueChanged)(CGFloat value);

/// 如果是暂停状态，seek完是否播放，默认YES
@property (nonatomic, assign) BOOL seekToPlay;

/// 全屏模式
@property (nonatomic, assign) TKZFFullScreenMode fullScreenMode;

/// 所有的片段模型
@property (nonatomic, readwrite, strong) NSArray *fragmentModelList;

/// 是否显示快进快退view，默认是NO
@property (nonatomic, assign) BOOL needShowForwardView;

/// 是否横屏录制预览，该属性是考虑到在不需要修改工程横屏设置时，通过旋转方式实现‘横屏’效果时，默认是YES。此时TKZFFullScreenMode是TKZFFullScreenModePortrait
@property (nonatomic, assign) BOOL isLandscapeRecordPreview;

/// 重置控制层
- (void)resetControlView;

/// 显示控制层
- (void)showControlView;

/// 隐藏控制层
- (void)hideControlView;

/// 设置播放时间
- (void)videoPlayer:(TKZFPlayerController *)videoPlayer currentTime:(NSTimeInterval)currentTime totalTime:(NSTimeInterval)totalTime;

/// 设置缓冲时间
- (void)videoPlayer:(TKZFPlayerController *)videoPlayer bufferTime:(NSTimeInterval)bufferTime;

/// 是否响应该手势
- (BOOL)shouldResponseGestureWithPoint:(CGPoint)point withGestureType:(TKZFPlayerGestureType)type touch:(nonnull UITouch *)touch;

/// 标题和全屏模式
- (void)showTitle:(NSString *_Nullable)title fullScreenMode:(TKZFFullScreenMode)fullScreenMode;

/// 根据当前播放状态取反
- (void)playOrPause;

/// 播放按钮状态
- (void)playBtnSelectedState:(BOOL)selected;

/// 调节播放进度slider和当前时间更新
- (void)sliderValueChanged:(CGFloat)value currentTimeString:(NSString *)timeString;

/// 滑杆结束滑动
- (void)sliderChangeEnded;

/// 更新分段播报进度条颜色
- (void)updateVideoFragmentProgress;

@end

NS_ASSUME_NONNULL_END
