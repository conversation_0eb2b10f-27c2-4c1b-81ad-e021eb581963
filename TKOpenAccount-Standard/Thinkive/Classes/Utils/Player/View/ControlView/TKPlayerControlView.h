//
//  TKPlayerControlView.h
//  TKOpenAccount-Standard
//
//  Created by <PERSON> on 2023/2/24.
//  Copyright © 2023 thinkive. All rights reserved.
//

#import <UIKit/UIKit.h>
#import "TKZFPlayerMediaControl.h"
#import "TKZFPortraitControlView.h"
#import "TKZFLandScapeControlView.h"
#import "TKZFSpeedLoadingView.h"
#import "TKZFPlayerMediaControl.h"

NS_ASSUME_NONNULL_BEGIN


@interface TKPlayerControlView : UIView<TKZFPlayerMediaControl>

/// 构造方法
/// @param frame frame
/// @param param 参数
- (instancetype)initWithFrame:(CGRect)frame withParam:(NSMutableDictionary *)param;

/// 竖屏控制层的View
@property (nonatomic, strong, readonly) TKZFPortraitControlView *portraitControlView;

/// 横屏控制层的View
@property (nonatomic, strong, readonly) TKZFLandScapeControlView *landScapeControlView;

/// 加载loading
@property (nonatomic, strong, readonly) TKZFSpeedLoadingView *activity;

/// 快进快退View
@property (nonatomic, strong, readonly) UIView *fastView;

/// 快进快退进度progress
@property (nonatomic, strong, readonly) TKZFSliderView *fastProgressView;

/// 快进快退时间
@property (nonatomic, strong, readonly) UILabel *fastTimeLabel;

/// 快进快退ImageView
@property (nonatomic, strong, readonly) UIImageView *fastImageView;

/// 加载失败按钮
@property (nonatomic, strong, readonly) UIButton *failBtn;

/// 底部播放进度
@property (nonatomic, strong, readonly) TKZFSliderView *bottomPgrogress;

/// 封面图
@property (nonatomic, strong, readonly) UIImageView *coverImageView;

/// 高斯模糊的背景图
@property (nonatomic, strong, readonly) UIImageView *bgImgView;

/// 高斯模糊视图
@property (nonatomic, strong, readonly) UIView *effectView;

///// 小窗口控制层
//@property (nonatomic, strong, readonly) TKZFSmallFloatControlView *floatControlView;

/// 快进视图是否显示动画，默认NO.
@property (nonatomic, assign) BOOL fastViewAnimated;

/// 视频之外区域是否高斯模糊显示，默认YES.
@property (nonatomic, assign) BOOL effectViewShow;

/// 如果是暂停状态，seek完是否播放，默认YES
@property (nonatomic, assign) BOOL seekToPlay;

/// 返回按钮点击回调
@property (nonatomic, copy) void(^backBtnClickCallback)(void);

/// 控制层显示或者隐藏
@property (nonatomic, readonly) BOOL controlViewAppeared;

/// 控制层显示或者隐藏的回调
@property (nonatomic, copy) void(^controlViewAppearedCallback)(BOOL appeared);

/// 控制层自动隐藏的时间，默认2.5秒
@property (nonatomic, assign) NSTimeInterval autoHiddenTimeInterval;

/// 控制层显示、隐藏动画的时长，默认0.25秒
@property (nonatomic, assign) NSTimeInterval autoFadeTimeInterval;

/// 横向滑动控制播放进度时是否显示控制层,默认 YES.
@property (nonatomic, assign) BOOL horizontalPanShowControlView;

/// prepare时候是否显示控制层,默认 NO.
@property (nonatomic, assign) BOOL prepareShowControlView;

/// prepare时候是否显示loading,默认 NO.
@property (nonatomic, assign) BOOL prepareShowLoading;

/// 是否自定义禁止pan手势，默认 NO.
@property (nonatomic, assign) BOOL customDisablePanMovingDirection;

/// 横屏时候是否显示自定义状态栏(iOS13+)，默认 NO.
@property (nonatomic, assign) BOOL showCustomStatusBar;

/// 全屏模式
@property (nonatomic, assign) TKZFFullScreenMode fullScreenMode;

/// 横屏控制view rect。不设置和TKPlayerControlView一样
@property (nonatomic, assign) CGRect landScapeControlViewRect;

/// 是否显示快进快退view，默认是NO
@property (nonatomic, assign) BOOL needShowForwardView;

/// 仅在播放时隐藏controlView，默认是YES
@property (nonatomic, assign) BOOL autoHiddenWhenPlay;

/**
 设置标题、封面、全屏模式

 @param title 视频的标题
 @param coverUrl 视频的封面，占位图默认是灰色的
 @param fullScreenMode 全屏模式
 */
- (void)showTitle:(NSString *)title coverURLString:(NSString *)coverUrl fullScreenMode:(TKZFFullScreenMode)fullScreenMode;

/**
 设置标题、封面、默认占位图、全屏模式

 @param title 视频的标题
 @param coverUrl 视频的封面
 @param placeholder 指定封面的placeholder
 @param fullScreenMode 全屏模式
 */
- (void)showTitle:(NSString *)title coverURLString:(NSString *)coverUrl placeholderImage:(UIImage *)placeholder fullScreenMode:(TKZFFullScreenMode)fullScreenMode;

/**
 设置标题、UIImage封面、全屏模式

 @param title 视频的标题
 @param image 视频的封面UIImage
 @param fullScreenMode 全屏模式
 */
- (void)showTitle:(NSString *)title coverImage:(UIImage *)image fullScreenMode:(TKZFFullScreenMode)fullScreenMode;

//- (void)showFullScreen

/**
 重置控制层
 */
- (void)resetControlView;

@end

NS_ASSUME_NONNULL_END
