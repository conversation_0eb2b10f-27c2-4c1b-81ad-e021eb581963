//
//  TKZFLandScapeControlView.h
//
//
// 
//


#import <UIKit/UIKit.h>
#import "TKZFSliderView.h"
#import "TKZFPlayerController.h"
#import "TKZFPlayerStatusBar.h"

NS_ASSUME_NONNULL_BEGIN

@interface TKZFLandScapeControlView : UIView

/// 顶部工具栏
@property (nonatomic, strong, readonly) UIView *topToolView;

/// 返回按钮
@property (nonatomic, strong, readonly) UIButton *backBtn;

/// 标题
@property (nonatomic, strong, readonly) UILabel *titleLabel;

/// 底部工具栏
@property (nonatomic, strong, readonly) UIView *bottomToolView;

/// 播放或暂停按钮 
@property (nonatomic, strong, readonly) UIButton *playOrPauseBtn;

/// 播放的当前时间
@property (nonatomic, strong, readonly) UILabel *currentTimeLabel;

/// 滑杆
@property (nonatomic, strong, readonly) TKZFSliderView *slider;

/// 视频总时间
@property (nonatomic, strong, readonly) UILabel *totalTimeLabel;

/// 锁定屏幕按钮
@property (nonatomic, strong, readonly) UIButton *lockBtn;

/// 倍速按钮
@property (nonatomic, strong) UIButton *rateBtn;
/// 快进按钮
@property (nonatomic, strong) UIButton *fastForwardBtn;
/// 快退按钮
@property (nonatomic, strong) UIButton *fastBackwardBtn;

@property (nonatomic, strong) TKZFPlayerStatusBar *statusBarView;

/// 横屏时候是否显示自定义状态栏(iOS13+)，默认 NO.
@property (nonatomic, assign) BOOL showCustomStatusBar;

/// 播放器
@property (nonatomic, weak) TKZFPlayerController *player;

/// slider滑动中
@property (nonatomic, copy, nullable) void(^sliderValueChanging)(CGFloat value,BOOL forward);

/// slider滑动结束
@property (nonatomic, copy, nullable) void(^sliderValueChanged)(CGFloat value);

/// 返回按钮点击回调
@property (nonatomic, copy) void(^backBtnClickCallback)(void);

/// 如果是暂停状态，seek完是否播放，默认YES
@property (nonatomic, assign) BOOL seekToPlay;

/// 全屏模式
@property (nonatomic, assign) TKZFFullScreenMode fullScreenMode;

/// 所有的片段模型
@property (nonatomic, readwrite, strong) NSArray *fragmentModelList;

/// 是否显示快进快退view，默认是NO
@property (nonatomic, assign) BOOL needShowForwardView;

/// 是否横屏录制预览，该属性是考虑到在不需要修改工程横屏设置时，通过旋转方式实现‘横屏’效果时，默认是YES。此时TKZFFullScreenMode是TKZFFullScreenModePortrait
@property (nonatomic, assign) BOOL isLandscapeRecordPreview;

/// 重置控制层
- (void)resetControlView;

/// 显示控制层
- (void)showControlView;

/// 隐藏控制层
- (void)hideControlView;

/// 设置播放时间
- (void)videoPlayer:(TKZFPlayerController *)videoPlayer currentTime:(NSTimeInterval)currentTime totalTime:(NSTimeInterval)totalTime;

/// 设置缓冲时间
- (void)videoPlayer:(TKZFPlayerController *)videoPlayer bufferTime:(NSTimeInterval)bufferTime;

/// 是否响应该手势
- (BOOL)shouldResponseGestureWithPoint:(CGPoint)point withGestureType:(TKZFPlayerGestureType)type touch:(nonnull UITouch *)touch;

/// 视频尺寸改变
- (void)videoPlayer:(TKZFPlayerController *)videoPlayer presentationSizeChanged:(CGSize)size;

/// 视频view已经旋转
- (void)videoPlayer:(TKZFPlayerController *)videoPlayer orientationWillChange:(TKZFOrientationObserver *)observer;

/// 标题和全屏模式
- (void)showTitle:(NSString *_Nullable)title fullScreenMode:(TKZFFullScreenMode)fullScreenMode;

/// 根据当前播放状态取反
- (void)playOrPause;

/// 播放按钮状态
- (void)playBtnSelectedState:(BOOL)selected;

/// 调节播放进度slider和当前时间更新
- (void)sliderValueChanged:(CGFloat)value currentTimeString:(NSString *)timeString;

/// 滑杆结束滑动
- (void)sliderChangeEnded;

/// 更新分段播报进度条颜色
- (void)updateVideoFragmentProgress;

@end

NS_ASSUME_NONNULL_END
