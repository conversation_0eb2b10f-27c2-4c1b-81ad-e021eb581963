//
//  TKZFPlayerConst.h
//  TKOpenAccount-Standard
//
//  Created by <PERSON> on 2022/12/7.
//  Copyright © 2022 thinkive. All rights reserved.
//

#ifndef TKZFPlayerConst_h
#define TKZFPlayerConst_h

#import <UIKit/UIKit.h>

typedef NS_ENUM(NSUInteger, TKZFPlayerPlaybackState) {
    TKZFPlayerPlayStateUnknown,
    TKZFPlayerPlayStatePlaying,
    TKZFPlayerPlayStatePaused,
    TKZFPlayerPlayStatePlayFailed,
    TKZFPlayerPlayStatePlayStopped
};

typedef NS_OPTIONS(NSUInteger, TKZFPlayerLoadState) {
    TKZFPlayerLoadStateUnknown        = 0,
    TKZFPlayerLoadStatePrepare        = 1 << 0,
    TKZFPlayerLoadStatePlayable       = 1 << 1,
    TKZFPlayerLoadStatePlaythroughOK  = 1 << 2, // 播放会自动开始
    TKZFPlayerLoadStateStalled        = 1 << 3, // 如果开始播放，将在此状态下自动暂停
};

typedef NS_ENUM(NSInteger, TKZFPlayerScalingMode) {
    TKZFPlayerScalingModeNone,       // 不拉伸
    TKZFPlayerScalingModeAspectFit,  // 等比例拉伸，直至一端拉伸至全屏
    TKZFPlayerScalingModeAspectFill, // 等比例拉伸，直至两端都拉伸至全屏，某一端会被超出屏幕被裁剪掉
    TKZFPlayerScalingModeFill        // 拉伸至全屏
};


// Screen width
#define TKZFPlayerScreenWidth     [[UIScreen mainScreen] bounds].size.width
// Screen height
#define TKZFPlayerScreenHeight    [[UIScreen mainScreen] bounds].size.height


#ifndef weakify
#if DEBUG
#if __has_feature(objc_arc)
#define tkzf_weakify(object) autoreleasepool{} __weak __typeof__(object) weak##_##object = object;
#else
#define tkzf_weakify(object) autoreleasepool{} __block __typeof__(object) block##_##object = object;
#endif
#else
#if __has_feature(objc_arc)
#define tkzf_weakify(object) try{} @finally{} {} __weak __typeof__(object) weak##_##object = object;
#else
#define tkzf_weakify(object) try{} @finally{} {} __block __typeof__(object) block##_##object = object;
#endif
#endif
#endif

#ifndef strongify
#if DEBUG
#if __has_feature(objc_arc)
#define tkzf_strongify(object) autoreleasepool{} __typeof__(object) object = weak##_##object;
#else
#define tkzf_strongify(object) autoreleasepool{} __typeof__(object) object = block##_##object;
#endif
#else
#if __has_feature(objc_arc)
#define tkzf_strongify(object) try{} @finally{} __typeof__(object) object = weak##_##object;
#else
#define tkzf_strongify(object) try{} @finally{} __typeof__(object) object = block##_##object;
#endif
#endif
#endif

#endif /* TKZFPlayerConst_h */
