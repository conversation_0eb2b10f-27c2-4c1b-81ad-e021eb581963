//
//  UIView+TKZFFrame.m
//
//
//
//


#import "UIView+TKZFFrame.h"

@implementation UIView (TKZFFrame)

- (CGFloat)tkzf_x {
    return self.frame.origin.x;
}

- (void)setTkzf_x:(CGFloat)tkzf_x {
    CGRect newFrame   = self.frame;
    newFrame.origin.x = tkzf_x;
    self.frame        = newFrame;
}

- (CGFloat)tkzf_y {
    return self.frame.origin.y;
}

- (void)setTkzf_y:(CGFloat)tkzf_y {
    CGRect newFrame   = self.frame;
    newFrame.origin.y = tkzf_y;
    self.frame        = newFrame;
}

- (CGFloat)tkzf_width {
    return CGRectGetWidth(self.bounds);
}

- (void)setTkzf_width:(CGFloat)tkzf_width {
    CGRect newFrame     = self.frame;
    newFrame.size.width = tkzf_width;
    self.frame          = newFrame;
}

- (CGFloat)tkzf_height {
    return CGRectGetHeight(self.bounds);
}

- (void)setTkzf_height:(CGFloat)tkzf_height {
    CGRect newFrame      = self.frame;
    newFrame.size.height = tkzf_height;
    self.frame           = newFrame;
}

- (CGFloat)tkzf_top {
    return self.frame.origin.y;
}

- (void)setTkzf_top:(CGFloat)tkzf_top {
    CGRect newFrame   = self.frame;
    newFrame.origin.y = tkzf_top;
    self.frame        = newFrame;
}

- (CGFloat)tkzf_bottom {
    return self.frame.origin.y + self.frame.size.height;
}

- (void)setTkzf_bottom:(CGFloat)tkzf_bottom {
    CGRect newFrame   = self.frame;
    newFrame.origin.y = tkzf_bottom - self.frame.size.height;
    self.frame        = newFrame;
}

- (CGFloat)tkzf_left {
    return self.frame.origin.x;
}

- (void)setTkzf_left:(CGFloat)tkzf_left {
    CGRect newFrame   = self.frame;
    newFrame.origin.x = tkzf_left;
    self.frame        = newFrame;
}

- (CGFloat)tkzf_right {
    return self.frame.origin.x + self.frame.size.width;
}

- (void)setTkzf_right:(CGFloat)tkzf_right {
    CGRect newFrame   = self.frame;
    newFrame.origin.x = tkzf_right - self.frame.size.width;
    self.frame        = newFrame;
}

- (CGFloat)tkzf_centerX {
    return self.center.x;
}

- (void)setTkzf_centerX:(CGFloat)tkzf_centerX {
    CGPoint newCenter = self.center;
    newCenter.x       = tkzf_centerX;
    self.center       = newCenter;
}

- (CGFloat)tkzf_centerY {
    return self.center.y;
}

- (void)setTkzf_centerY:(CGFloat)tkzf_centerY {
    CGPoint newCenter = self.center;
    newCenter.y       = tkzf_centerY;
    self.center       = newCenter;
}

- (CGPoint)tkzf_origin {
    return self.frame.origin;
}

- (void)setTkzf_origin:(CGPoint)tkzf_origin {
    CGRect newFrame = self.frame;
    newFrame.origin = tkzf_origin;
    self.frame      = newFrame;
}

- (CGSize)tkzf_size {
    return self.frame.size;
}

- (void)setTkzf_size:(CGSize)tkzf_size {
    CGRect newFrame = self.frame;
    newFrame.size   = tkzf_size;
    self.frame      = newFrame;
}

@end
