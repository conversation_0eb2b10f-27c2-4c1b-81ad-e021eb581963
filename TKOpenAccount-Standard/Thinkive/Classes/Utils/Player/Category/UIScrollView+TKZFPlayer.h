//
//  UIScrollView+TKZFPlayer.h
//  TKZFPlayer
//


#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

/*
 * The scroll direction of scrollView.
 */
typedef NS_ENUM(NSUInteger, TKZFPlayerScrollDirection) {
    TKZFPlayerScrollDirectionNone,
    TKZFPlayerScrollDirectionUp,         // Scroll up
    TKZFPlayerScrollDirectionDown,       // Scroll Down
    TKZFPlayerScrollDirectionLeft,       // Scroll left
    TKZFPlayerScrollDirectionRight       // Scroll right
};

/*
 * The scrollView direction.
 */
typedef NS_ENUM(NSInteger, TKZFPlayerScrollViewDirection) {
    TKZFPlayerScrollViewDirectionVertical,
    TKZFPlayerScrollViewDirectionHorizontal
};

/*
 * The player container type
 */
typedef NS_ENUM(NSInteger, TKZFPlayerContainerType) {
    TKZFPlayerContainerTypeView,
    TKZFPlayerContainerTypeCell
};

typedef NS_ENUM(NSInteger , TKZFPlayerScrollViewScrollPosition) {
    TKZFPlayerScrollViewScrollPositionNone,
    /// Apply to UITableView and UICollectionViewDirection is vertical scrolling.
    TKZFPlayerScrollViewScrollPositionTop,
    TKZFPlayerScrollViewScrollPositionCenteredVertically,
    TKZFPlayerScrollViewScrollPositionBottom,
    
    /// Only apply to UICollectionViewDirection is horizontal scrolling.
    TKZFPlayerScrollViewScrollPositionLeft,
    TKZFPlayerScrollViewScrollPositionCenteredHorizontally,
    TKZFPlayerScrollViewScrollPositionRight
};

@interface UIScrollView (TKZFPlayer)

/// When the TKZFPlayerScrollViewDirection is TKZFPlayerScrollViewDirectionVertical,the property has value.
@property (nonatomic, readonly) CGFloat tkzf_lastOffsetY;

/// When the TKZFPlayerScrollViewDirection is TKZFPlayerScrollViewDirectionHorizontal,the property has value.
@property (nonatomic, readonly) CGFloat tkzf_lastOffsetX;

/// The scrollView scroll direction, default is TKZFPlayerScrollViewDirectionVertical.
@property (nonatomic) TKZFPlayerScrollViewDirection tkzf_scrollViewDirection;

/// The scroll direction of scrollView while scrolling.
/// When the TKZFPlayerScrollViewDirection is TKZFPlayerScrollViewDirectionVertical，this value can only be TKZFPlayerScrollDirectionUp or TKZFPlayerScrollDirectionDown.
/// When the TKZFPlayerScrollViewDirection is TKZFPlayerScrollViewDirectionHorizontal，this value can only be TKZFPlayerScrollDirectionLeft or TKZFPlayerScrollDirectionRight.
@property (nonatomic, readonly) TKZFPlayerScrollDirection tkzf_scrollDirection;

/// Get the cell according to indexPath.
- (UIView *)tkzf_getCellForIndexPath:(NSIndexPath *)indexPath;

/// Get the indexPath for cell.
- (NSIndexPath *)tkzf_getIndexPathForCell:(UIView *)cell;

/**
Scroll to indexPath with position.
 
@param indexPath scroll the  indexPath.
@param scrollPosition  scrollView scroll position.
@param animated animate.
@param completionHandler  Scroll completion callback.
*/
- (void)tkzf_scrollToRowAtIndexPath:(NSIndexPath *)indexPath
                 atScrollPosition:(TKZFPlayerScrollViewScrollPosition)scrollPosition
                         animated:(BOOL)animated
                completionHandler:(void (^ __nullable)(void))completionHandler;

/**
Scroll to indexPath with position.
 
@param indexPath scroll the  indexPath.
@param scrollPosition  scrollView scroll position.
@param duration animate duration.
@param completionHandler  Scroll completion callback.
*/
- (void)tkzf_scrollToRowAtIndexPath:(NSIndexPath *)indexPath
                 atScrollPosition:(TKZFPlayerScrollViewScrollPosition)scrollPosition
                  animateDuration:(NSTimeInterval)duration
                completionHandler:(void (^ __nullable)(void))completionHandler;

///------------------------------------
/// The following method must be implemented in UIScrollViewDelegate.
///------------------------------------

- (void)tkzf_scrollViewDidEndDecelerating;

- (void)tkzf_scrollViewDidEndDraggingWillDecelerate:(BOOL)decelerate;

- (void)tkzf_scrollViewDidScrollToTop;

- (void)tkzf_scrollViewDidScroll;

- (void)tkzf_scrollViewWillBeginDragging;

///------------------------------------
/// end
///------------------------------------


@end

@interface UIScrollView (ZFPlayerCannotCalled)

/// The block invoked When the player appearing.
@property (nonatomic, copy, nullable) void(^tkzf_playerAppearingInScrollView)(NSIndexPath *indexPath, CGFloat playerApperaPercent);

/// The block invoked When the player disappearing.
@property (nonatomic, copy, nullable) void(^tkzf_playerDisappearingInScrollView)(NSIndexPath *indexPath, CGFloat playerDisapperaPercent);

/// The block invoked When the player will appeared.
@property (nonatomic, copy, nullable) void(^tkzf_playerWillAppearInScrollView)(NSIndexPath *indexPath);

/// The block invoked When the player did appeared.
@property (nonatomic, copy, nullable) void(^tkzf_playerDidAppearInScrollView)(NSIndexPath *indexPath);

/// The block invoked When the player will disappear.
@property (nonatomic, copy, nullable) void(^tkzf_playerWillDisappearInScrollView)(NSIndexPath *indexPath);

/// The block invoked When the player did disappeared.
@property (nonatomic, copy, nullable) void(^tkzf_playerDidDisappearInScrollView)(NSIndexPath *indexPath);

/// The block invoked When the player did stop scroll.
@property (nonatomic, copy, nullable) void(^tkzf_scrollViewDidEndScrollingCallback)(NSIndexPath *indexPath);

/// The block invoked When the player did  scroll.
@property (nonatomic, copy, nullable) void(^tkzf_scrollViewDidScrollCallback)(NSIndexPath *indexPath);

/// The block invoked When the player should play.
@property (nonatomic, copy, nullable) void(^tkzf_playerShouldPlayInScrollView)(NSIndexPath *indexPath);

/// The current player scroll slides off the screen percent.
/// the property used when the `stopWhileNotVisible` is YES, stop the current playing player.
/// the property used when the `stopWhileNotVisible` is NO, the current playing player add to small container view.
/// 0.0~1.0, defalut is 0.5.
/// 0.0 is the player will disappear.
/// 1.0 is the player did disappear.
@property (nonatomic) CGFloat tkzf_playerDisapperaPercent;

/// The current player scroll to the screen percent to play the video.
/// 0.0~1.0, defalut is 0.0.
/// 0.0 is the player will appear.
/// 1.0 is the player did appear.
@property (nonatomic) CGFloat tkzf_playerApperaPercent;

/// The current player controller is disappear, not dealloc
@property (nonatomic) BOOL tkzf_viewControllerDisappear;

/// Has stopped playing
@property (nonatomic, assign) BOOL tkzf_stopPlay;

/// The currently playing cell stop playing when the cell has out off the screen，defalut is YES.
@property (nonatomic, assign) BOOL tkzf_stopWhileNotVisible;

/// The indexPath is playing.
@property (nonatomic, nullable) NSIndexPath *tkzf_playingIndexPath;

/// The indexPath should be play while scrolling.
@property (nonatomic, nullable) NSIndexPath *tkzf_shouldPlayIndexPath;

/// WWANA networks play automatically,default NO.
@property (nonatomic, getter=tkzf_isWWANAutoPlay) BOOL tkzf_WWANAutoPlay;

/// The player should auto player,default is YES.
@property (nonatomic) BOOL tkzf_shouldAutoPlay;

/// The view tag that the player display in scrollView.
@property (nonatomic) NSInteger tkzf_containerViewTag;

/// The video contrainerView in normal model.
@property (nonatomic, strong) UIView *tkzf_containerView;

/// The video contrainerView type.
@property (nonatomic, assign) TKZFPlayerContainerType tkzf_containerType;

/// Filter the cell that should be played when the scroll is stopped (to play when the scroll is stopped).
- (void)tkzf_filterShouldPlayCellWhileScrolled:(void (^ __nullable)(NSIndexPath *indexPath))handler;

/// Filter the cell that should be played while scrolling (you can use this to filter the highlighted cell).
- (void)tkzf_filterShouldPlayCellWhileScrolling:(void (^ __nullable)(NSIndexPath *indexPath))handler;

@end

@interface UIScrollView (ZFPlayerDeprecated)

/// The block invoked When the player did stop scroll.
@property (nonatomic, copy, nullable) void(^tkzf_scrollViewDidStopScrollCallback)(NSIndexPath *indexPath) __attribute__((deprecated("use `TKZFPlayerController.tkzf_scrollViewDidEndScrollingCallback` instead.")));

/// The block invoked When the player should play.
@property (nonatomic, copy, nullable) void(^tkzf_shouldPlayIndexPathCallback)(NSIndexPath *indexPath) __attribute__((deprecated("use `TKZFPlayerController.tkzf_playerShouldPlayInScrollView` instead.")));


/// Scroll to indexPath position  `TKZFPlayerScrollViewScrollPositionTop` with animations.
- (void)tkzf_scrollToRowAtIndexPath:(NSIndexPath *)indexPath
                completionHandler:(void (^ __nullable)(void))completionHandler __attribute__((deprecated("use `tkzf_scrollToRowAtIndexPath:atScrollPosition:animated:completionHandler:` instead.")));

/// Scroll to indexPath position  `TKZFPlayerScrollViewScrollPositionTop` with animations.
- (void)tkzf_scrollToRowAtIndexPath:(NSIndexPath *)indexPath
                         animated:(BOOL)animated
                completionHandler:(void (^ __nullable)(void))completionHandler __attribute__((deprecated("use `tkzf_scrollToRowAtIndexPath:atScrollPosition:animated:completionHandler:` instead.")));

/// Scroll to indexPath position  `TKZFPlayerScrollViewScrollPositionTop` with animations.
- (void)tkzf_scrollToRowAtIndexPath:(NSIndexPath *)indexPath
              animateWithDuration:(NSTimeInterval)duration
                completionHandler:(void (^ __nullable)(void))completionHandler __attribute__((deprecated("use `tkzf_scrollToRowAtIndexPath:atScrollPosition:animateDuration:completionHandler:` instead.")));

@end

NS_ASSUME_NONNULL_END
