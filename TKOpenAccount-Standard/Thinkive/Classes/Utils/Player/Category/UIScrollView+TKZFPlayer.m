//
//  UIScrollView+TKZFPlayer.m
//
//
// 
//


#import "UIScrollView+TKZFPlayer.h"
#import <objc/runtime.h>
#import "TKZFReachabilityManager.h"
#import "TKZFPlayerConst.h"

#pragma clang diagnostic push
#pragma clang diagnostic ignored"-Wdeprecated-declarations"

@interface UIScrollView ()

@property (nonatomic) CGFloat tkzf_lastOffsetY;
@property (nonatomic) CGFloat tkzf_lastOffsetX;
@property (nonatomic) TKZFPlayerScrollDirection tkzf_scrollDirection;

@end

@implementation UIScrollView (TKZFPlayer)

#pragma mark - public method

- (UIView *)tkzf_getCellForIndexPath:(NSIndexPath *)indexPath {
    if ([self _isTableView]) {
        UITableView *tableView = (UITableView *)self;
        UITableViewCell *cell = [tableView cellForRowAtIndexPath:indexPath];
        return cell;
    } else if ([self _isCollectionView]) {
        UICollectionView *collectionView = (UICollectionView *)self;
        UICollectionViewCell *cell = [collectionView cellForItemAtIndexPath:indexPath];
        return cell;
    }
    return nil;
}

- (NSIndexPath *)tkzf_getIndexPathForCell:(UIView *)cell {
    if ([self _isTableView]) {
        UITableView *tableView = (UITableView *)self;
        NSIndexPath *indexPath = [tableView indexPathForCell:(UITableViewCell *)cell];
        return indexPath;
    } else if ([self _isCollectionView]) {
        UICollectionView *collectionView = (UICollectionView *)self;
        NSIndexPath *indexPath = [collectionView indexPathForCell:(UICollectionViewCell *)cell];
        return indexPath;
    }
    return nil;
}

/**
Scroll to indexPath with position.
 
@param indexPath scroll the  indexPath.
@param scrollPosition  scrollView scroll position.
@param animated animate.
@param completionHandler  Scroll completion callback.
*/
- (void)tkzf_scrollToRowAtIndexPath:(NSIndexPath *)indexPath
                 atScrollPosition:(TKZFPlayerScrollViewScrollPosition)scrollPosition
                         animated:(BOOL)animated
                completionHandler:(void (^ __nullable)(void))completionHandler {
    [self tkzf_scrollToRowAtIndexPath:indexPath atScrollPosition:scrollPosition animateDuration:animated ? 0.4 : 0.0 completionHandler:completionHandler];
}

- (void)tkzf_scrollToRowAtIndexPath:(NSIndexPath *)indexPath
                 atScrollPosition:(TKZFPlayerScrollViewScrollPosition)scrollPosition
                  animateDuration:(NSTimeInterval)duration
                completionHandler:(void (^ __nullable)(void))completionHandler {
    BOOL animated = duration > 0.0;
    if ([self _isTableView]) {
        UITableView *tableView = (UITableView *)self;
        UITableViewScrollPosition tableScrollPosition = UITableViewScrollPositionNone;
        if (scrollPosition <= TKZFPlayerScrollViewScrollPositionBottom) {
            tableScrollPosition = (UITableViewScrollPosition)scrollPosition;
        }
        [tableView scrollToRowAtIndexPath:indexPath atScrollPosition:tableScrollPosition animated:animated];
    } else if ([self _isCollectionView]) {
        UICollectionView *collectionView = (UICollectionView *)self;
        if (self.tkzf_scrollViewDirection == TKZFPlayerScrollViewDirectionVertical) {
            UICollectionViewScrollPosition collectionScrollPosition = UICollectionViewScrollPositionNone;
            switch (scrollPosition) {
                case TKZFPlayerScrollViewScrollPositionNone:
                    collectionScrollPosition = UICollectionViewScrollPositionNone;
                    break;
                case TKZFPlayerScrollViewScrollPositionTop:
                    collectionScrollPosition = UICollectionViewScrollPositionTop;
                    break;
                case TKZFPlayerScrollViewScrollPositionCenteredVertically:
                    collectionScrollPosition = UICollectionViewScrollPositionCenteredVertically;
                    break;
                case TKZFPlayerScrollViewScrollPositionBottom:
                    collectionScrollPosition = UICollectionViewScrollPositionBottom;
                    break;
                default:
                    break;
            }
            [collectionView scrollToItemAtIndexPath:indexPath atScrollPosition:collectionScrollPosition animated:animated];
        } else if (self.tkzf_scrollViewDirection == TKZFPlayerScrollViewDirectionHorizontal) {
            UICollectionViewScrollPosition collectionScrollPosition = UICollectionViewScrollPositionNone;
            switch (scrollPosition) {
                case TKZFPlayerScrollViewScrollPositionNone:
                    collectionScrollPosition = UICollectionViewScrollPositionNone;
                    break;
                case TKZFPlayerScrollViewScrollPositionLeft:
                    collectionScrollPosition = UICollectionViewScrollPositionLeft;
                    break;
                case TKZFPlayerScrollViewScrollPositionCenteredHorizontally:
                    collectionScrollPosition = UICollectionViewScrollPositionCenteredHorizontally;
                    break;
                case TKZFPlayerScrollViewScrollPositionRight:
                    collectionScrollPosition = UICollectionViewScrollPositionRight;
                    break;
                default:
                    break;
            }
            [collectionView scrollToItemAtIndexPath:indexPath atScrollPosition:collectionScrollPosition animated:animated];
        }
    }
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(duration * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        if (completionHandler) completionHandler();
    });
}

- (void)tkzf_scrollViewDidEndDecelerating {
    BOOL scrollToScrollStop = !self.tracking && !self.dragging && !self.decelerating;
    if (scrollToScrollStop) {
        [self _scrollViewDidStopScroll];
    }
}

- (void)tkzf_scrollViewDidEndDraggingWillDecelerate:(BOOL)decelerate {
    if (!decelerate) {
        BOOL dragToDragStop = self.tracking && !self.dragging && !self.decelerating;
        if (dragToDragStop) {
            [self _scrollViewDidStopScroll];
        }
    }
}

- (void)tkzf_scrollViewDidScrollToTop {
    [self _scrollViewDidStopScroll];
}

- (void)tkzf_scrollViewDidScroll {
    if (self.tkzf_scrollViewDirection == TKZFPlayerScrollViewDirectionVertical) {
        [self _findCorrectCellWhenScrollViewDirectionVertical:nil];
        [self _scrollViewScrollingDirectionVertical];
    } else {
        [self _findCorrectCellWhenScrollViewDirectionHorizontal:nil];
        [self _scrollViewScrollingDirectionHorizontal];
    }
}

- (void)tkzf_scrollViewWillBeginDragging {
    [self _scrollViewBeginDragging];
}

#pragma mark - private method

- (void)_scrollViewDidStopScroll {
    self.tkzf_scrollDirection = TKZFPlayerScrollDirectionNone;
    @tkzf_weakify(self)
    [self tkzf_filterShouldPlayCellWhileScrolled:^(NSIndexPath * _Nonnull indexPath) {
        @tkzf_strongify(self)
        if (self.tkzf_scrollViewDidStopScrollCallback) self.tkzf_scrollViewDidStopScrollCallback(indexPath);
        if (self.tkzf_scrollViewDidEndScrollingCallback) self.tkzf_scrollViewDidEndScrollingCallback(indexPath);
    }];
}

- (void)_scrollViewBeginDragging {
    if (self.tkzf_scrollViewDirection == TKZFPlayerScrollViewDirectionVertical) {
        self.tkzf_lastOffsetY = self.contentOffset.y;
    } else {
        self.tkzf_lastOffsetX = self.contentOffset.x;
    }
}

/**
  The percentage of scrolling processed in vertical scrolling.
 */
- (void)_scrollViewScrollingDirectionVertical {
    CGFloat offsetY = self.contentOffset.y;
    self.tkzf_scrollDirection = (offsetY - self.tkzf_lastOffsetY > 0) ? TKZFPlayerScrollDirectionUp : TKZFPlayerScrollDirectionDown;
    self.tkzf_lastOffsetY = offsetY;
    if (self.tkzf_stopPlay) return;
    
    UIView *playerView;
    if (self.tkzf_containerType == TKZFPlayerContainerTypeCell) {
        // Avoid being paused the first time you play it.
        if (self.contentOffset.y < 0) return;
        if (!self.tkzf_playingIndexPath) return;
        
        UIView *cell = [self tkzf_getCellForIndexPath:self.tkzf_playingIndexPath];
        if (!cell) {
            if (self.tkzf_playerDidDisappearInScrollView) self.tkzf_playerDidDisappearInScrollView(self.tkzf_playingIndexPath);
            return;
        }
        playerView = [cell viewWithTag:self.tkzf_containerViewTag];
    } else if (self.tkzf_containerType == TKZFPlayerContainerTypeView) {
        if (!self.tkzf_containerView) return;
        playerView = self.tkzf_containerView;
    }
    
    CGRect rect1 = [playerView convertRect:playerView.frame toView:self];
    CGRect rect = [self convertRect:rect1 toView:self.superview];
    /// playerView top to scrollView top space.
    CGFloat topSpacing = CGRectGetMinY(rect) - CGRectGetMinY(self.frame) - CGRectGetMinY(playerView.frame);
    /// playerView bottom to scrollView bottom space.
    CGFloat bottomSpacing = CGRectGetMaxY(self.frame) - CGRectGetMaxY(rect) + CGRectGetMinY(playerView.frame);
    /// The height of the content area.
    CGFloat contentInsetHeight = CGRectGetMaxY(self.frame) - CGRectGetMinY(self.frame);
    
    CGFloat playerDisapperaPercent = 0;
    CGFloat playerApperaPercent = 0;
    
    if (self.tkzf_scrollDirection == TKZFPlayerScrollDirectionUp) { /// Scroll up
        /// Player is disappearing.
        if (topSpacing <= 0 && CGRectGetHeight(rect) != 0) {
            playerDisapperaPercent = -topSpacing/CGRectGetHeight(rect);
            if (playerDisapperaPercent > 1.0) playerDisapperaPercent = 1.0;
            if (self.tkzf_playerDisappearingInScrollView) self.tkzf_playerDisappearingInScrollView(self.tkzf_playingIndexPath, playerDisapperaPercent);
        }
        
        /// Top area
        if (topSpacing <= 0 && topSpacing > -CGRectGetHeight(rect)/2) {
            /// When the player will disappear.
            if (self.tkzf_playerWillDisappearInScrollView) self.tkzf_playerWillDisappearInScrollView(self.tkzf_playingIndexPath);
        } else if (topSpacing <= -CGRectGetHeight(rect)) {
            /// When the player did disappeared.
            if (self.tkzf_playerDidDisappearInScrollView) self.tkzf_playerDidDisappearInScrollView(self.tkzf_playingIndexPath);
        } else if (topSpacing > 0 && topSpacing <= contentInsetHeight) {
            /// Player is appearing.
            if (CGRectGetHeight(rect) != 0) {
                playerApperaPercent = -(topSpacing-contentInsetHeight)/CGRectGetHeight(rect);
                if (playerApperaPercent > 1.0) playerApperaPercent = 1.0;
                if (self.tkzf_playerAppearingInScrollView) self.tkzf_playerAppearingInScrollView(self.tkzf_playingIndexPath, playerApperaPercent);
            }
            /// In visable area
            if (topSpacing <= contentInsetHeight && topSpacing > contentInsetHeight-CGRectGetHeight(rect)/2) {
                /// When the player will appear.
                if (self.tkzf_playerWillAppearInScrollView) self.tkzf_playerWillAppearInScrollView(self.tkzf_playingIndexPath);
            } else {
                /// When the player did appeared.
                if (self.tkzf_playerDidAppearInScrollView) self.tkzf_playerDidAppearInScrollView(self.tkzf_playingIndexPath);
            }
        }
        
    } else if (self.tkzf_scrollDirection == TKZFPlayerScrollDirectionDown) { /// Scroll Down
        /// Player is disappearing.
        if (bottomSpacing <= 0 && CGRectGetHeight(rect) != 0) {
            playerDisapperaPercent = -bottomSpacing/CGRectGetHeight(rect);
            if (playerDisapperaPercent > 1.0) playerDisapperaPercent = 1.0;
            if (self.tkzf_playerDisappearingInScrollView) self.tkzf_playerDisappearingInScrollView(self.tkzf_playingIndexPath, playerDisapperaPercent);
        }
        
        /// Bottom area
        if (bottomSpacing <= 0 && bottomSpacing > -CGRectGetHeight(rect)/2) {
            /// When the player will disappear.
            if (self.tkzf_playerWillDisappearInScrollView) self.tkzf_playerWillDisappearInScrollView(self.tkzf_playingIndexPath);
        } else if (bottomSpacing <= -CGRectGetHeight(rect)) {
            /// When the player did disappeared.
            if (self.tkzf_playerDidDisappearInScrollView) self.tkzf_playerDidDisappearInScrollView(self.tkzf_playingIndexPath);
        } else if (bottomSpacing > 0 && bottomSpacing <= contentInsetHeight) {
            /// Player is appearing.
            if (CGRectGetHeight(rect) != 0) {
                playerApperaPercent = -(bottomSpacing-contentInsetHeight)/CGRectGetHeight(rect);
                if (playerApperaPercent > 1.0) playerApperaPercent = 1.0;
                if (self.tkzf_playerAppearingInScrollView) self.tkzf_playerAppearingInScrollView(self.tkzf_playingIndexPath, playerApperaPercent);
            }
            /// In visable area
            if (bottomSpacing <= contentInsetHeight && bottomSpacing > contentInsetHeight-CGRectGetHeight(rect)/2) {
                /// When the player will appear.
                if (self.tkzf_playerWillAppearInScrollView) self.tkzf_playerWillAppearInScrollView(self.tkzf_playingIndexPath);
            } else {
                /// When the player did appeared.
                if (self.tkzf_playerDidAppearInScrollView) self.tkzf_playerDidAppearInScrollView(self.tkzf_playingIndexPath);
            }
        }
    }
}

/**
 The percentage of scrolling processed in horizontal scrolling.
 */
- (void)_scrollViewScrollingDirectionHorizontal {
    CGFloat offsetX = self.contentOffset.x;
    self.tkzf_scrollDirection = (offsetX - self.tkzf_lastOffsetX > 0) ? TKZFPlayerScrollDirectionLeft : TKZFPlayerScrollDirectionRight;
    self.tkzf_lastOffsetX = offsetX;
    if (self.tkzf_stopPlay) return;
    
    UIView *playerView;
    if (self.tkzf_containerType == TKZFPlayerContainerTypeCell) {
        // Avoid being paused the first time you play it.
        if (self.contentOffset.x < 0) return;
        if (!self.tkzf_playingIndexPath) return;
        
        UIView *cell = [self tkzf_getCellForIndexPath:self.tkzf_playingIndexPath];
        if (!cell) {
            if (self.tkzf_playerDidDisappearInScrollView) self.tkzf_playerDidDisappearInScrollView(self.tkzf_playingIndexPath);
            return;
        }
       playerView = [cell viewWithTag:self.tkzf_containerViewTag];
    } else if (self.tkzf_containerType == TKZFPlayerContainerTypeView) {
        if (!self.tkzf_containerView) return;
        playerView = self.tkzf_containerView;
    }
    
    CGRect rect1 = [playerView convertRect:playerView.frame toView:self];
    CGRect rect = [self convertRect:rect1 toView:self.superview];
    /// playerView left to scrollView left space.
    CGFloat leftSpacing = CGRectGetMinX(rect) - CGRectGetMinX(self.frame) - CGRectGetMinX(playerView.frame);
    /// playerView bottom to scrollView right space.
    CGFloat rightSpacing = CGRectGetMaxX(self.frame) - CGRectGetMaxX(rect) + CGRectGetMinX(playerView.frame);
    /// The height of the content area.
    CGFloat contentInsetWidth = CGRectGetMaxX(self.frame) - CGRectGetMinX(self.frame);
    
    CGFloat playerDisapperaPercent = 0;
    CGFloat playerApperaPercent = 0;
    
    if (self.tkzf_scrollDirection == TKZFPlayerScrollDirectionLeft) { /// Scroll left
        /// Player is disappearing.
        if (leftSpacing <= 0 && CGRectGetWidth(rect) != 0) {
            playerDisapperaPercent = -leftSpacing/CGRectGetWidth(rect);
            if (playerDisapperaPercent > 1.0) playerDisapperaPercent = 1.0;
            if (self.tkzf_playerDisappearingInScrollView) self.tkzf_playerDisappearingInScrollView(self.tkzf_playingIndexPath, playerDisapperaPercent);
        }
        
        /// Top area
        if (leftSpacing <= 0 && leftSpacing > -CGRectGetWidth(rect)/2) {
            /// When the player will disappear.
            if (self.tkzf_playerWillDisappearInScrollView) self.tkzf_playerWillDisappearInScrollView(self.tkzf_playingIndexPath);
        } else if (leftSpacing <= -CGRectGetWidth(rect)) {
            /// When the player did disappeared.
            if (self.tkzf_playerDidDisappearInScrollView) self.tkzf_playerDidDisappearInScrollView(self.tkzf_playingIndexPath);
        } else if (leftSpacing > 0 && leftSpacing <= contentInsetWidth) {
            /// Player is appearing.
            if (CGRectGetWidth(rect) != 0) {
                playerApperaPercent = -(leftSpacing-contentInsetWidth)/CGRectGetWidth(rect);
                if (playerApperaPercent > 1.0) playerApperaPercent = 1.0;
                if (self.tkzf_playerAppearingInScrollView) self.tkzf_playerAppearingInScrollView(self.tkzf_playingIndexPath, playerApperaPercent);
            }
            /// In visable area
            if (leftSpacing <= contentInsetWidth && leftSpacing > contentInsetWidth-CGRectGetWidth(rect)/2) {
                /// When the player will appear.
                if (self.tkzf_playerWillAppearInScrollView) self.tkzf_playerWillAppearInScrollView(self.tkzf_playingIndexPath);
            } else {
                /// When the player did appeared.
                if (self.tkzf_playerDidAppearInScrollView) self.tkzf_playerDidAppearInScrollView(self.tkzf_playingIndexPath);
            }
        }
        
    } else if (self.tkzf_scrollDirection == TKZFPlayerScrollDirectionRight) { /// Scroll right
        /// Player is disappearing.
        if (rightSpacing <= 0 && CGRectGetWidth(rect) != 0) {
            playerDisapperaPercent = -rightSpacing/CGRectGetWidth(rect);
            if (playerDisapperaPercent > 1.0) playerDisapperaPercent = 1.0;
            if (self.tkzf_playerDisappearingInScrollView) self.tkzf_playerDisappearingInScrollView(self.tkzf_playingIndexPath, playerDisapperaPercent);
        }
        
        /// Bottom area
        if (rightSpacing <= 0 && rightSpacing > -CGRectGetWidth(rect)/2) {
            /// When the player will disappear.
            if (self.tkzf_playerWillDisappearInScrollView) self.tkzf_playerWillDisappearInScrollView(self.tkzf_playingIndexPath);
        } else if (rightSpacing <= -CGRectGetWidth(rect)) {
            /// When the player did disappeared.
            if (self.tkzf_playerDidDisappearInScrollView) self.tkzf_playerDidDisappearInScrollView(self.tkzf_playingIndexPath);
        } else if (rightSpacing > 0 && rightSpacing <= contentInsetWidth) {
            /// Player is appearing.
            if (CGRectGetWidth(rect) != 0) {
                playerApperaPercent = -(rightSpacing-contentInsetWidth)/CGRectGetWidth(rect);
                if (playerApperaPercent > 1.0) playerApperaPercent = 1.0;
                if (self.tkzf_playerAppearingInScrollView) self.tkzf_playerAppearingInScrollView(self.tkzf_playingIndexPath, playerApperaPercent);
            }
            /// In visable area
            if (rightSpacing <= contentInsetWidth && rightSpacing > contentInsetWidth-CGRectGetWidth(rect)/2) {
                /// When the player will appear.
                if (self.tkzf_playerWillAppearInScrollView) self.tkzf_playerWillAppearInScrollView(self.tkzf_playingIndexPath);
            } else {
                /// When the player did appeared.
                if (self.tkzf_playerDidAppearInScrollView) self.tkzf_playerDidAppearInScrollView(self.tkzf_playingIndexPath);
            }
        }
    }
}

/**
 Find the playing cell while the scrollDirection is vertical.
 */
- (void)_findCorrectCellWhenScrollViewDirectionVertical:(void (^ __nullable)(NSIndexPath *indexPath))handler {
    if (!self.tkzf_shouldAutoPlay) return;
    if (self.tkzf_containerType == TKZFPlayerContainerTypeView) return;

    if (!self.tkzf_stopWhileNotVisible) {
        /// If you have a cell that is playing, stop the traversal.
        if (self.tkzf_playingIndexPath) {
            NSIndexPath *finalIndexPath = self.tkzf_playingIndexPath;
            if (self.tkzf_scrollViewDidScrollCallback) self.tkzf_scrollViewDidScrollCallback(finalIndexPath);
            if (handler) handler(finalIndexPath);
            self.tkzf_shouldPlayIndexPath = finalIndexPath;
            return;
        }
    }
    NSArray *visiableCells = nil;
    NSIndexPath *indexPath = nil;
    BOOL isLast = self.contentOffset.y + self.frame.size.height >= self.contentSize.height;
    if ([self _isTableView]) {
        UITableView *tableView = (UITableView *)self;
        visiableCells = [tableView visibleCells];
        // First visible cell indexPath
        indexPath = tableView.indexPathsForVisibleRows.firstObject;
        if ((self.contentOffset.y <= 0 || isLast) && (!self.tkzf_playingIndexPath || [indexPath compare:self.tkzf_playingIndexPath] == NSOrderedSame)) {
            UITableViewCell *cell = [tableView cellForRowAtIndexPath:indexPath];
            UIView *playerView = [cell viewWithTag:self.tkzf_containerViewTag];
            if (playerView && !playerView.hidden && playerView.alpha > 0.01) {
                if (self.tkzf_scrollViewDidScrollCallback) self.tkzf_scrollViewDidScrollCallback(indexPath);
                if (handler) handler(indexPath);
                self.tkzf_shouldPlayIndexPath = indexPath;
                return;
            }
        }
    } else if ([self _isCollectionView]) {
        UICollectionView *collectionView = (UICollectionView *)self;
        visiableCells = [collectionView visibleCells];
        NSArray *sortedIndexPaths = [collectionView.indexPathsForVisibleItems sortedArrayUsingComparator:^NSComparisonResult(id obj1, id obj2) {
            return [obj1 compare:obj2];
        }];
        
        visiableCells = [visiableCells sortedArrayUsingComparator:^NSComparisonResult(id obj1, id obj2) {
            NSIndexPath *path1 = (NSIndexPath *)[collectionView indexPathForCell:obj1];
            NSIndexPath *path2 = (NSIndexPath *)[collectionView indexPathForCell:obj2];
            return [path1 compare:path2];
        }];
        
        // First visible cell indexPath
        indexPath = isLast ? sortedIndexPaths.lastObject : sortedIndexPaths.firstObject;
        if ((self.contentOffset.y <= 0 || isLast) && (!self.tkzf_playingIndexPath || [indexPath compare:self.tkzf_playingIndexPath] == NSOrderedSame)) {
            UICollectionViewCell *cell = [collectionView cellForItemAtIndexPath:indexPath];
            UIView *playerView = [cell viewWithTag:self.tkzf_containerViewTag];
            if (playerView && !playerView.hidden && playerView.alpha > 0.01) {
                if (self.tkzf_scrollViewDidScrollCallback) self.tkzf_scrollViewDidScrollCallback(indexPath);
                if (handler) handler(indexPath);
                self.tkzf_shouldPlayIndexPath = indexPath;
                return;
            }
        }
    }
    
    NSArray *cells = nil;
    if (self.tkzf_scrollDirection == TKZFPlayerScrollDirectionUp) {
        cells = visiableCells;
    } else {
        cells = [visiableCells reverseObjectEnumerator].allObjects;
    }
    
    /// Mid line.
    CGFloat scrollViewMidY = CGRectGetHeight(self.frame)/2;
    /// The final playing indexPath.
    __block NSIndexPath *finalIndexPath = nil;
    /// The final distance from the center line.
    __block CGFloat finalSpace = 0;
    @tkzf_weakify(self)
    [cells enumerateObjectsUsingBlock:^(UIView *cell, NSUInteger idx, BOOL * _Nonnull stop) {
        @tkzf_strongify(self)
        UIView *playerView = [cell viewWithTag:self.tkzf_containerViewTag];
        if (!playerView || playerView.hidden || playerView.alpha <= 0.01) return;
        CGRect rect1 = [playerView convertRect:playerView.frame toView:self];
        CGRect rect = [self convertRect:rect1 toView:self.superview];
        /// playerView top to scrollView top space.
        CGFloat topSpacing = CGRectGetMinY(rect) - CGRectGetMinY(self.frame) - CGRectGetMinY(playerView.frame);
        /// playerView bottom to scrollView bottom space.
        CGFloat bottomSpacing = CGRectGetMaxY(self.frame) - CGRectGetMaxY(rect) + CGRectGetMinY(playerView.frame);
        CGFloat centerSpacing = ABS(scrollViewMidY - CGRectGetMidY(rect));
        NSIndexPath *indexPath = [self tkzf_getIndexPathForCell:cell];
        
        /// Play when the video playback section is visible.
        if ((topSpacing >= -(1 - self.tkzf_playerApperaPercent) * CGRectGetHeight(rect)) && (bottomSpacing >= -(1 - self.tkzf_playerApperaPercent) * CGRectGetHeight(rect))) {
            if (!finalIndexPath || centerSpacing < finalSpace) {
                finalIndexPath = indexPath;
                finalSpace = centerSpacing;
            }
        }
    }];
    /// if find the playing indexPath.
    if (finalIndexPath) {
        if (self.tkzf_scrollViewDidScrollCallback) self.tkzf_scrollViewDidScrollCallback(indexPath);
        if (handler) handler(finalIndexPath);
    }
    self.tkzf_shouldPlayIndexPath = finalIndexPath;
}

/**
 Find the playing cell while the scrollDirection is horizontal.
 */
- (void)_findCorrectCellWhenScrollViewDirectionHorizontal:(void (^ __nullable)(NSIndexPath *indexPath))handler {
    if (!self.tkzf_shouldAutoPlay) return;
    if (self.tkzf_containerType == TKZFPlayerContainerTypeView) return;
    if (!self.tkzf_stopWhileNotVisible) {
        /// If you have a cell that is playing, stop the traversal.
        if (self.tkzf_playingIndexPath) {
            NSIndexPath *finalIndexPath = self.tkzf_playingIndexPath;
            if (self.tkzf_scrollViewDidScrollCallback) self.tkzf_scrollViewDidScrollCallback(finalIndexPath);
            if (handler) handler(finalIndexPath);
            self.tkzf_shouldPlayIndexPath = finalIndexPath;
            return;
        }
    }
    
    NSArray *visiableCells = nil;
    NSIndexPath *indexPath = nil;
    BOOL isLast = self.contentOffset.x + self.frame.size.width >= self.contentSize.width;
    if ([self _isTableView]) {
        UITableView *tableView = (UITableView *)self;
        visiableCells = [tableView visibleCells];
        // First visible cell indexPath
        indexPath = tableView.indexPathsForVisibleRows.firstObject;
        if ((self.contentOffset.x <= 0 || isLast) && (!self.tkzf_playingIndexPath || [indexPath compare:self.tkzf_playingIndexPath] == NSOrderedSame)) {
            UITableViewCell *cell = [tableView cellForRowAtIndexPath:indexPath];
            UIView *playerView = [cell viewWithTag:self.tkzf_containerViewTag];
            if (playerView && !playerView.hidden && playerView.alpha > 0.01) {
                if (self.tkzf_scrollViewDidScrollCallback) self.tkzf_scrollViewDidScrollCallback(indexPath);
                if (handler) handler(indexPath);
                self.tkzf_shouldPlayIndexPath = indexPath;
                return;
            }
        }
    } else if ([self _isCollectionView]) {
        UICollectionView *collectionView = (UICollectionView *)self;
        visiableCells = [collectionView visibleCells];
        NSArray *sortedIndexPaths = [collectionView.indexPathsForVisibleItems sortedArrayUsingComparator:^NSComparisonResult(id obj1, id obj2) {
            return [obj1 compare:obj2];
        }];
        
        visiableCells = [visiableCells sortedArrayUsingComparator:^NSComparisonResult(id obj1, id obj2) {
            NSIndexPath *path1 = (NSIndexPath *)[collectionView indexPathForCell:obj1];
            NSIndexPath *path2 = (NSIndexPath *)[collectionView indexPathForCell:obj2];
            return [path1 compare:path2];
        }];
        
        // First visible cell indexPath
        indexPath = isLast ? sortedIndexPaths.lastObject : sortedIndexPaths.firstObject;
        if ((self.contentOffset.x <= 0 || isLast) && (!self.tkzf_playingIndexPath || [indexPath compare:self.tkzf_playingIndexPath] == NSOrderedSame)) {
            UICollectionViewCell *cell = [collectionView cellForItemAtIndexPath:indexPath];
            UIView *playerView = [cell viewWithTag:self.tkzf_containerViewTag];
            if (playerView && !playerView.hidden && playerView.alpha > 0.01) {
                if (self.tkzf_scrollViewDidScrollCallback) self.tkzf_scrollViewDidScrollCallback(indexPath);
                if (handler) handler(indexPath);
                self.tkzf_shouldPlayIndexPath = indexPath;
                return;
            }
        }
    }
    
    NSArray *cells = nil;
    if (self.tkzf_scrollDirection == TKZFPlayerScrollDirectionUp) {
        cells = visiableCells;
    } else {
        cells = [visiableCells reverseObjectEnumerator].allObjects;
    }
    
    /// Mid line.
    CGFloat scrollViewMidX = CGRectGetWidth(self.frame)/2;
    /// The final playing indexPath.
    __block NSIndexPath *finalIndexPath = nil;
    /// The final distance from the center line.
    __block CGFloat finalSpace = 0;
    @tkzf_weakify(self)
    [cells enumerateObjectsUsingBlock:^(UIView *cell, NSUInteger idx, BOOL * _Nonnull stop) {
        @tkzf_strongify(self)
        UIView *playerView = [cell viewWithTag:self.tkzf_containerViewTag];
        if (!playerView || playerView.hidden || playerView.alpha <= 0.01) return;
        CGRect rect1 = [playerView convertRect:playerView.frame toView:self];
        CGRect rect = [self convertRect:rect1 toView:self.superview];
        /// playerView left to scrollView top space.
        CGFloat leftSpacing = CGRectGetMinX(rect) - CGRectGetMinX(self.frame) - CGRectGetMinX(playerView.frame);
        /// playerView right to scrollView top space.
        CGFloat rightSpacing = CGRectGetMaxX(self.frame) - CGRectGetMaxX(rect) + CGRectGetMinX(playerView.frame);
        CGFloat centerSpacing = ABS(scrollViewMidX - CGRectGetMidX(rect));
        NSIndexPath *indexPath = [self tkzf_getIndexPathForCell:cell];
        
        /// Play when the video playback section is visible.
        if ((leftSpacing >= -(1 - self.tkzf_playerApperaPercent) * CGRectGetWidth(rect)) && (rightSpacing >= -(1 - self.tkzf_playerApperaPercent) * CGRectGetWidth(rect))) {
            if (!finalIndexPath || centerSpacing < finalSpace) {
                finalIndexPath = indexPath;
                finalSpace = centerSpacing;
            }
        }
    }];
    /// if find the playing indexPath.
    if (finalIndexPath) {
        if (self.tkzf_scrollViewDidScrollCallback) self.tkzf_scrollViewDidScrollCallback(indexPath);
        if (handler) handler(finalIndexPath);
        self.tkzf_shouldPlayIndexPath = finalIndexPath;
    }
}

- (BOOL)_isTableView {
    return [self isKindOfClass:[UITableView class]];
}

- (BOOL)_isCollectionView {
    return [self isKindOfClass:[UICollectionView class]];
}

#pragma mark - getter

- (TKZFPlayerScrollDirection)tkzf_scrollDirection {
    return [objc_getAssociatedObject(self, _cmd) integerValue];
}

- (TKZFPlayerScrollViewDirection)tkzf_scrollViewDirection {
    return [objc_getAssociatedObject(self, _cmd) integerValue];
}

- (CGFloat)tkzf_lastOffsetY {
    return [objc_getAssociatedObject(self, _cmd) floatValue];
}

- (CGFloat)tkzf_lastOffsetX {
    return [objc_getAssociatedObject(self, _cmd) floatValue];
}

#pragma mark - setter

- (void)setTkzf_scrollDirection:(TKZFPlayerScrollDirection)tkzf_scrollDirection {
    objc_setAssociatedObject(self, @selector(tkzf_scrollDirection), @(tkzf_scrollDirection), OBJC_ASSOCIATION_RETAIN_NONATOMIC);
}

- (void)setTkzf_scrollViewDirection:(TKZFPlayerScrollViewDirection)tkzf_scrollViewDirection {
    objc_setAssociatedObject(self, @selector(tkzf_scrollViewDirection), @(tkzf_scrollViewDirection), OBJC_ASSOCIATION_RETAIN_NONATOMIC);
}

- (void)setTkzf_lastOffsetY:(CGFloat)tkzf_lastOffsetY {
    objc_setAssociatedObject(self, @selector(tkzf_lastOffsetY), @(tkzf_lastOffsetY), OBJC_ASSOCIATION_RETAIN_NONATOMIC);
}

- (void)setTkzf_lastOffsetX:(CGFloat)tkzf_lastOffsetX {
    objc_setAssociatedObject(self, @selector(tkzf_lastOffsetX), @(tkzf_lastOffsetX), OBJC_ASSOCIATION_RETAIN_NONATOMIC);
}

@end

@implementation UIScrollView (ZFPlayerCannotCalled)

- (void)tkzf_filterShouldPlayCellWhileScrolling:(void (^ __nullable)(NSIndexPath *indexPath))handler {
    if (self.tkzf_scrollViewDirection == TKZFPlayerScrollViewDirectionVertical) {
        [self _findCorrectCellWhenScrollViewDirectionVertical:handler];
    } else {
        [self _findCorrectCellWhenScrollViewDirectionHorizontal:handler];
    }
}

- (void)tkzf_filterShouldPlayCellWhileScrolled:(void (^ __nullable)(NSIndexPath *indexPath))handler {
    if (!self.tkzf_shouldAutoPlay) return;
    @tkzf_weakify(self)
    [self tkzf_filterShouldPlayCellWhileScrolling:^(NSIndexPath *indexPath) {
        @tkzf_strongify(self)
        /// 如果当前控制器已经消失，直接return
        if (self.tkzf_viewControllerDisappear) return;
        if ([TKZFReachabilityManager sharedManager].isReachableViaWWAN && !self.tkzf_WWANAutoPlay) {
            /// 移动网络
            self.tkzf_shouldPlayIndexPath = indexPath;
            return;
        }
        if (handler) handler(indexPath);
        self.tkzf_playingIndexPath = indexPath;
    }];
}

#pragma mark - getter

- (void (^)(NSIndexPath * _Nonnull, CGFloat))tkzf_playerDisappearingInScrollView {
    return objc_getAssociatedObject(self, _cmd);
}

- (void (^)(NSIndexPath * _Nonnull, CGFloat))tkzf_playerAppearingInScrollView {
    return objc_getAssociatedObject(self, _cmd);
}

- (void (^)(NSIndexPath * _Nonnull))tkzf_playerDidAppearInScrollView {
    return objc_getAssociatedObject(self, _cmd);
}

- (void (^)(NSIndexPath * _Nonnull))tkzf_playerWillDisappearInScrollView {
    return objc_getAssociatedObject(self, _cmd);
}

- (void (^)(NSIndexPath * _Nonnull))tkzf_playerWillAppearInScrollView {
    return objc_getAssociatedObject(self, _cmd);
}

- (void (^)(NSIndexPath * _Nonnull))tkzf_playerDidDisappearInScrollView {
    return objc_getAssociatedObject(self, _cmd);
}

- (void (^)(NSIndexPath * _Nonnull))tkzf_scrollViewDidEndScrollingCallback {
    return objc_getAssociatedObject(self, _cmd);
}

- (void (^)(NSIndexPath * _Nonnull))tkzf_scrollViewDidScrollCallback {
    return objc_getAssociatedObject(self, _cmd);
}

- (void (^)(NSIndexPath * _Nonnull))tkzf_playerShouldPlayInScrollView {
    return objc_getAssociatedObject(self, _cmd);
}

- (CGFloat)tkzf_playerApperaPercent {
    return [objc_getAssociatedObject(self, _cmd) floatValue];
}

- (CGFloat)tkzf_playerDisapperaPercent {
    return [objc_getAssociatedObject(self, _cmd) floatValue];
}

- (BOOL)tkzf_viewControllerDisappear {
    return [objc_getAssociatedObject(self, _cmd) boolValue];
}

- (BOOL)tkzf_stopPlay {
    NSNumber *number = objc_getAssociatedObject(self, _cmd);
    if (number) return number.boolValue;
    self.tkzf_stopPlay = YES;
    return YES;
}

- (BOOL)tkzf_stopWhileNotVisible {
    return [objc_getAssociatedObject(self, _cmd) boolValue];
}

- (NSIndexPath *)tkzf_playingIndexPath {
    return objc_getAssociatedObject(self, _cmd);
}

- (NSIndexPath *)tkzf_shouldPlayIndexPath {
    return objc_getAssociatedObject(self, _cmd);
}

- (NSInteger)tkzf_containerViewTag {
    return [objc_getAssociatedObject(self, _cmd) integerValue];
}

- (BOOL)tkzf_isWWANAutoPlay {
    return [objc_getAssociatedObject(self, _cmd) boolValue];
}

- (BOOL)tkzf_shouldAutoPlay {
    NSNumber *number = objc_getAssociatedObject(self, _cmd);
    if (number) return number.boolValue;
    self.tkzf_shouldAutoPlay = YES;
    return YES;
}

- (TKZFPlayerContainerType)tkzf_containerType {
    return [objc_getAssociatedObject(self, _cmd) integerValue];
}

- (UIView *)tkzf_containerView {
    return objc_getAssociatedObject(self, _cmd);
}

#pragma mark - setter

- (void)setTkzf_playerDisappearingInScrollView:(void (^)(NSIndexPath * _Nonnull, CGFloat))tkzf_playerDisappearingInScrollView {
    objc_setAssociatedObject(self, @selector(tkzf_playerDisappearingInScrollView), tkzf_playerDisappearingInScrollView, OBJC_ASSOCIATION_COPY_NONATOMIC);
}

- (void)setTkzf_playerAppearingInScrollView:(void (^)(NSIndexPath * _Nonnull, CGFloat))tkzf_playerAppearingInScrollView {
    objc_setAssociatedObject(self, @selector(tkzf_playerAppearingInScrollView), tkzf_playerAppearingInScrollView, OBJC_ASSOCIATION_COPY_NONATOMIC);
}

- (void)setTkzf_playerDidAppearInScrollView:(void (^)(NSIndexPath * _Nonnull))tkzf_playerDidAppearInScrollView {
    objc_setAssociatedObject(self, @selector(tkzf_playerDidAppearInScrollView), tkzf_playerDidAppearInScrollView, OBJC_ASSOCIATION_COPY_NONATOMIC);
}

- (void)setTkzf_playerWillDisappearInScrollView:(void (^)(NSIndexPath * _Nonnull))tkzf_playerWillDisappearInScrollView {
    objc_setAssociatedObject(self, @selector(tkzf_playerWillDisappearInScrollView), tkzf_playerWillDisappearInScrollView, OBJC_ASSOCIATION_COPY_NONATOMIC);
}

- (void)setTkzf_playerWillAppearInScrollView:(void (^)(NSIndexPath * _Nonnull))tkzf_playerWillAppearInScrollView {
    objc_setAssociatedObject(self, @selector(tkzf_playerWillAppearInScrollView), tkzf_playerWillAppearInScrollView, OBJC_ASSOCIATION_COPY_NONATOMIC);
}

- (void)setTkzf_playerDidDisappearInScrollView:(void (^)(NSIndexPath * _Nonnull))tkzf_playerDidDisappearInScrollView {
    objc_setAssociatedObject(self, @selector(tkzf_playerDidDisappearInScrollView), tkzf_playerDidDisappearInScrollView, OBJC_ASSOCIATION_COPY_NONATOMIC);
}

- (void)setTkzf_scrollViewDidEndScrollingCallback:(void (^)(NSIndexPath * _Nonnull))tkzf_scrollViewDidEndScrollingCallback {
    objc_setAssociatedObject(self, @selector(tkzf_scrollViewDidEndScrollingCallback), tkzf_scrollViewDidEndScrollingCallback, OBJC_ASSOCIATION_COPY_NONATOMIC);
}

- (void)setTkzf_scrollViewDidScrollCallback:(void (^)(NSIndexPath * _Nonnull))tkzf_scrollViewDidScrollCallback {
    objc_setAssociatedObject(self, @selector(tkzf_scrollViewDidScrollCallback), tkzf_scrollViewDidScrollCallback, OBJC_ASSOCIATION_COPY_NONATOMIC);
}

- (void)setTkzf_playerShouldPlayInScrollView:(void (^)(NSIndexPath * _Nonnull))tkzf_playerShouldPlayInScrollView {
    objc_setAssociatedObject(self, @selector(tkzf_playerShouldPlayInScrollView), tkzf_playerShouldPlayInScrollView, OBJC_ASSOCIATION_COPY_NONATOMIC);
}

- (void)setTkzf_playerApperaPercent:(CGFloat)tkzf_playerApperaPercent {
    objc_setAssociatedObject(self, @selector(tkzf_playerApperaPercent), @(tkzf_playerApperaPercent), OBJC_ASSOCIATION_COPY_NONATOMIC);
}

- (void)setTkzf_playerDisapperaPercent:(CGFloat)tkzf_playerDisapperaPercent {
    objc_setAssociatedObject(self, @selector(tkzf_playerDisapperaPercent), @(tkzf_playerDisapperaPercent), OBJC_ASSOCIATION_COPY_NONATOMIC);
}

- (void)setTkzf_viewControllerDisappear:(BOOL)tkzf_viewControllerDisappear {
    objc_setAssociatedObject(self, @selector(tkzf_viewControllerDisappear), @(tkzf_viewControllerDisappear), OBJC_ASSOCIATION_COPY_NONATOMIC);
}

- (void)setTkzf_stopPlay:(BOOL)tkzf_stopPlay {
    objc_setAssociatedObject(self, @selector(tkzf_stopPlay), @(tkzf_stopPlay), OBJC_ASSOCIATION_RETAIN_NONATOMIC);
}

- (void)setTkzf_stopWhileNotVisible:(BOOL)tkzf_stopWhileNotVisible {
    objc_setAssociatedObject(self, @selector(tkzf_stopWhileNotVisible), @(tkzf_stopWhileNotVisible), OBJC_ASSOCIATION_RETAIN_NONATOMIC);
}

- (void)setTkzf_playingIndexPath:(NSIndexPath *)tkzf_playingIndexPath {
    objc_setAssociatedObject(self, @selector(tkzf_playingIndexPath), tkzf_playingIndexPath, OBJC_ASSOCIATION_RETAIN_NONATOMIC);
    if (tkzf_playingIndexPath && [tkzf_playingIndexPath compare:self.tkzf_shouldPlayIndexPath] != NSOrderedSame) {
        self.tkzf_shouldPlayIndexPath = tkzf_playingIndexPath;
    }
}

- (void)setTkzf_shouldPlayIndexPath:(NSIndexPath *)tkzf_shouldPlayIndexPath {
    if (self.tkzf_playerShouldPlayInScrollView) self.tkzf_playerShouldPlayInScrollView(tkzf_shouldPlayIndexPath);
    if (self.tkzf_shouldPlayIndexPathCallback) self.tkzf_shouldPlayIndexPathCallback(tkzf_shouldPlayIndexPath);
    objc_setAssociatedObject(self, @selector(tkzf_shouldPlayIndexPath), tkzf_shouldPlayIndexPath, OBJC_ASSOCIATION_RETAIN_NONATOMIC);
}

- (void)setTkzf_containerViewTag:(NSInteger)tkzf_containerViewTag {
    objc_setAssociatedObject(self, @selector(tkzf_containerViewTag), @(tkzf_containerViewTag), OBJC_ASSOCIATION_RETAIN_NONATOMIC);
}

- (void)setTkzf_containerType:(TKZFPlayerContainerType)tkzf_containerType {
    objc_setAssociatedObject(self, @selector(tkzf_containerType), @(tkzf_containerType), OBJC_ASSOCIATION_RETAIN_NONATOMIC);
}

- (void)setTkzf_containerView:(UIView *)tkzf_containerView {
    objc_setAssociatedObject(self, @selector(tkzf_containerView), tkzf_containerView, OBJC_ASSOCIATION_RETAIN_NONATOMIC);
}

- (void)setTkzf_shouldAutoPlay:(BOOL)tkzf_shouldAutoPlay {
    objc_setAssociatedObject(self, @selector(tkzf_shouldAutoPlay), @(tkzf_shouldAutoPlay), OBJC_ASSOCIATION_RETAIN_NONATOMIC);
}

- (void)setTkzf_WWANAutoPlay:(BOOL)tkzf_WWANAutoPlay {
    objc_setAssociatedObject(self, @selector(tkzf_isWWANAutoPlay), @(tkzf_WWANAutoPlay), OBJC_ASSOCIATION_RETAIN_NONATOMIC);
}

@end


@implementation UIScrollView (ZFPlayerDeprecated)

#pragma mark - getter

- (void (^)(NSIndexPath * _Nonnull))tkzf_scrollViewDidStopScrollCallback {
    return objc_getAssociatedObject(self, _cmd);
}

- (void (^)(NSIndexPath * _Nonnull))tkzf_shouldPlayIndexPathCallback {
    return objc_getAssociatedObject(self, _cmd);
}

#pragma mark - setter

- (void)setTkzf_scrollViewDidStopScrollCallback:(void (^)(NSIndexPath * _Nonnull))tkzf_scrollViewDidStopScrollCallback {
    objc_setAssociatedObject(self, @selector(tkzf_scrollViewDidStopScrollCallback), tkzf_scrollViewDidStopScrollCallback, OBJC_ASSOCIATION_COPY_NONATOMIC);
}

- (void)setTkzf_shouldPlayIndexPathCallback:(void (^)(NSIndexPath * _Nonnull))tkzf_shouldPlayIndexPathCallback {
    objc_setAssociatedObject(self, @selector(tkzf_shouldPlayIndexPathCallback), tkzf_shouldPlayIndexPathCallback, OBJC_ASSOCIATION_COPY_NONATOMIC);
}

#pragma mark - method

- (void)tkzf_scrollToRowAtIndexPath:(NSIndexPath *)indexPath completionHandler:(void (^ __nullable)(void))completionHandler {
    [self tkzf_scrollToRowAtIndexPath:indexPath animated:YES completionHandler:completionHandler];
}

- (void)tkzf_scrollToRowAtIndexPath:(NSIndexPath *)indexPath animated:(BOOL)animated completionHandler:(void (^ __nullable)(void))completionHandler {
    [self tkzf_scrollToRowAtIndexPath:indexPath animateWithDuration:animated ? 0.4 : 0.0 completionHandler:completionHandler];
}

/// Scroll to indexPath with animations duration.
- (void)tkzf_scrollToRowAtIndexPath:(NSIndexPath *)indexPath animateWithDuration:(NSTimeInterval)duration completionHandler:(void (^ __nullable)(void))completionHandler {
    [self tkzf_scrollToRowAtIndexPath:indexPath atScrollPosition:TKZFPlayerScrollViewScrollPositionTop animateDuration:duration completionHandler:completionHandler];
}

@end

#pragma clang diagnostic pop
