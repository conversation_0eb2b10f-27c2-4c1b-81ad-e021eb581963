//
//  ZFOrentationObserver.h
//
//
// 
//


#import <UIKit/UIKit.h>
#import "TKZFPlayerView.h"

@class TKZFPortraitViewController;

NS_ASSUME_NONNULL_BEGIN

/// Full screen mode
typedef NS_ENUM(NSUInteger, TKZFFullScreenMode) {
    TKZFFullScreenModeAutomatic,  // Determine full screen mode automatically
    TKZFFullScreenModeLandscape,  // Landscape full screen mode
    TKZFFullScreenModePortrait    // Portrait full screen Model
};

/// Portrait full screen mode.
typedef NS_ENUM(NSUInteger, TKZFPortraitFullScreenMode) {
    TKZFPortraitFullScreenModeScaleToFill,    // Full fill
    TKZFPortraitFullScreenModeScaleAspectFit  // contents scaled to fit with fixed aspect. remainder is transparent
};

/// Player view mode
typedef NS_ENUM(NSUInteger, TKZFRotateType) {
    TKZFRotateTypeNormal,         // Normal
    TKZFRotateTypeCell            // Cell
};

/**
 Rotation of support direction
 */
typedef NS_OPTIONS(NSUInteger, TKZFInterfaceOrientationMask) {
    TKZFInterfaceOrientationMaskUnknow = 0,
    TKZFInterfaceOrientationMaskPortrait = (1 << 0),
    TKZFInterfaceOrientationMaskLandscapeLeft = (1 << 1),
    TKZFInterfaceOrientationMaskLandscapeRight = (1 << 2),
    TKZFInterfaceOrientationMaskPortraitUpsideDown = (1 << 3),
    TKZFInterfaceOrientationMaskLandscape = (TKZFInterfaceOrientationMaskLandscapeLeft | TKZFInterfaceOrientationMaskLandscapeRight),
    TKZFInterfaceOrientationMaskAll = (TKZFInterfaceOrientationMaskPortrait | TKZFInterfaceOrientationMaskLandscape | TKZFInterfaceOrientationMaskPortraitUpsideDown),
    TKZFInterfaceOrientationMaskAllButUpsideDown = (TKZFInterfaceOrientationMaskPortrait | TKZFInterfaceOrientationMaskLandscape),
};

/// This enumeration lists some of the gesture types that the player has by default.
typedef NS_OPTIONS(NSUInteger, TKZFDisablePortraitGestureTypes) {
    TKZFDisablePortraitGestureTypesNone         = 0,
    TKZFDisablePortraitGestureTypesTap          = 1 << 0,
    TKZFDisablePortraitGestureTypesPan          = 1 << 1,
    TKZFDisablePortraitGestureTypesAll          = (TKZFDisablePortraitGestureTypesTap | TKZFDisablePortraitGestureTypesPan)
};

@protocol ZFPortraitOrientationDelegate <NSObject>

- (void)tkzf_orientationWillChange:(BOOL)isFullScreen;

- (void)tkzf_orientationDidChanged:(BOOL)isFullScreen;

- (void)tkzf_interationState:(BOOL)isDragging;

@end

@interface TKZFOrientationObserver : NSObject

/// update the rotateView and containerView.
- (void)updateRotateView:(TKZFPlayerView *)rotateView
           containerView:(UIView *)containerView;

/// Container view of a full screen state player.
@property (nonatomic, strong, readonly, nullable) UIView *fullScreenContainerView;

/// Container view of a small screen state player.
@property (nonatomic, weak) UIView *containerView;

/// The block invoked When player will rotate.
@property (nonatomic, copy, nullable) void(^orientationWillChange)(TKZFOrientationObserver *observer, BOOL isFullScreen);

/// The block invoked when player rotated.
@property (nonatomic, copy, nullable) void(^orientationDidChanged)(TKZFOrientationObserver *observer, BOOL isFullScreen);

/// Full screen mode, the default landscape into full screen
@property (nonatomic) TKZFFullScreenMode fullScreenMode;

@property (nonatomic, assign) TKZFPortraitFullScreenMode portraitFullScreenMode;

/// rotate duration, default is 0.30
@property (nonatomic) NSTimeInterval duration;

/// If the full screen.
@property (nonatomic, readonly, getter=isFullScreen) BOOL fullScreen;

/// Lock screen orientation
@property (nonatomic, getter=isLockedScreen) BOOL lockedScreen;

/// The fullscreen statusbar hidden.
@property (nonatomic, assign) BOOL fullScreenStatusBarHidden;

/// default is  UIStatusBarStyleLightContent.
@property (nonatomic, assign) UIStatusBarStyle fullScreenStatusBarStyle;

/// defalut is UIStatusBarAnimationSlide.
@property (nonatomic, assign) UIStatusBarAnimation fullScreenStatusBarAnimation;

@property (nonatomic, assign) CGSize presentationSize;

/// default is TKZFDisablePortraitGestureTypesAll.
@property (nonatomic, assign) TKZFDisablePortraitGestureTypes disablePortraitGestureTypes;

/// The current orientation of the player.
/// Default is UIInterfaceOrientationPortrait.
@property (nonatomic, readonly) UIInterfaceOrientation currentOrientation;

/// Whether allow the video orientation rotate.
/// default is YES.
@property (nonatomic, assign) BOOL allowOrientationRotation;

/// The support Interface Orientation,default is TKZFInterfaceOrientationMaskAllButUpsideDown
@property (nonatomic, assign) TKZFInterfaceOrientationMask supportInterfaceOrientation;

/// 竖屏页面控制器
@property (nonatomic, strong) TKZFPortraitViewController *portraitViewController;

/// Add the device orientation observer.
- (void)addDeviceOrientationObserver;

/// Remove the device orientation observer.
- (void)removeDeviceOrientationObserver;

/// Enter the fullScreen while the TKZFFullScreenMode is TKZFFullScreenModeLandscape.
- (void)rotateToOrientation:(UIInterfaceOrientation)orientation animated:(BOOL)animated;

/// Enter the fullScreen while the TKZFFullScreenMode is TKZFFullScreenModeLandscape.
- (void)rotateToOrientation:(UIInterfaceOrientation)orientation animated:(BOOL)animated completion:(void(^ __nullable)(void))completion;

/// Enter the fullScreen while the TKZFFullScreenMode is TKZFFullScreenModePortrait.
- (void)enterPortraitFullScreen:(BOOL)fullScreen animated:(BOOL)animated;

/// Enter the fullScreen while the TKZFFullScreenMode is TKZFFullScreenModePortrait.
- (void)enterPortraitFullScreen:(BOOL)fullScreen animated:(BOOL)animated completion:(void(^ __nullable)(void))completion;

/// FullScreen mode is determined by TKZFFullScreenMode.
- (void)enterFullScreen:(BOOL)fullScreen animated:(BOOL)animated;

/// FullScreen mode is determined by TKZFFullScreenMode.
- (void)enterFullScreen:(BOOL)fullScreen animated:(BOOL)animated completion:(void (^ _Nullable)(void))completion;

@end

NS_ASSUME_NONNULL_END


