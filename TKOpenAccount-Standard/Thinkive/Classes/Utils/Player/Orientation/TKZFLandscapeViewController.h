//
//  ZFFullscreenViewController.h
//
//
// 
//


#import <UIKit/UIKit.h>
@class TKZFLandscapeViewController;

NS_ASSUME_NONNULL_BEGIN

@protocol TKZFLandscapeViewControllerDelegate <NSObject>
@optional
- (BOOL)ls_shouldAutorotate;
- (void)rotationFullscreenViewController:(TKZFLandscapeViewController *)viewController viewWillTransitionToSize:(CGSize)size withTransitionCoordinator:(id<UIViewControllerTransitionCoordinator>)coordinator;

@end

@interface TKZFLandscapeViewController : UIViewController

@property (nonatomic, weak, nullable) id<TKZFLandscapeViewControllerDelegate> delegate;

@property (nonatomic, assign) BOOL disableAnimations;

@property (nonatomic, assign) BOOL statusBarHidden;
/// default is  UIStatusBarStyleLightContent.
@property (nonatomic, assign) UIStatusBarStyle statusBarStyle;
/// defalut is UIStatusBarAnimationSlide.
@property (nonatomic, assign) UIStatusBarAnimation statusBarAnimation;

@end

NS_ASSUME_NONNULL_END
