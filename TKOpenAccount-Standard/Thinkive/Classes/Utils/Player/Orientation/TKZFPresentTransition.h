//
//  TKZFPresentTransition.h
//
//
// 
//


#import <UIKit/UIKit.h>
#import "TKZFOrientationObserver.h"

typedef NS_ENUM(NSUInteger, TKZFPresentTransitionType) {
    TKZFPresentTransitionTypePresent,
    TKZFPresentTransitionTypeDismiss,
};

@interface TKZFPresentTransition : NSObject<UIViewControllerAnimatedTransitioning>

@property (nonatomic, weak) id<ZFPortraitOrientationDelegate> delagate;

@property (nonatomic, assign) CGRect contentFullScreenRect;

@property (nonatomic, assign, getter=isFullScreen) BOOL fullScreen;

@property (nonatomic, assign) BOOL interation;

@property (nonatomic, assign) NSTimeInterval duration;

- (void)transitionWithTransitionType:(TKZFPresentTransitionType)type
                         contentView:(UIView *)contentView
                       containerView:(UIView *)containerView;

@end
