//
//  TKZFPersentInteractiveTransition.h
//
//
// 
//

//

#import <UIKit/UIKit.h>
#import "TKZFOrientationObserver.h"

@interface TKZFPersentInteractiveTransition : UIPercentDrivenInteractiveTransition

@property (nonatomic, weak) id<ZFPortraitOrientationDelegate> delagate;

@property (nonatomic, assign) BOOL interation;

/// default is TKZFDisablePortraitGestureTypesNone.
@property (nonatomic, assign) TKZFDisablePortraitGestureTypes disablePortraitGestureTypes;

@property (nonatomic, assign) BOOL fullScreenAnimation;

@property (nonatomic, assign) CGRect contentFullScreenRect;

@property (nonatomic, weak) UIViewController *viewController;

- (void)updateContentView:(UIView *)contenView
            containerView:(UIView *)containerView;

@end
