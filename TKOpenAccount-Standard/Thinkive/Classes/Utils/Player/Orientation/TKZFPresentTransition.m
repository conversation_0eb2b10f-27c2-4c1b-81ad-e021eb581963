//
//  TKZFPresentTransition.m
//
//
// 
//


#import "TKZFPresentTransition.h"
#import "TKZFPlayerConst.h"

@interface TKZFPresentTransition ()

@property (nonatomic, strong) UIView *contentView;
@property (nonatomic, assign) TKZFPresentTransitionType type;
@property (nonatomic, strong) UIView *containerView;
@property (nonatomic, assign, getter=isTransiting) BOOL transiting;

@end

@implementation TKZFPresentTransition

- (void)transitionWithTransitionType:(TKZFPresentTransitionType)type
                         contentView:(UIView *)contentView
                       containerView:(UIView *)containerView {
    
    self.type = type;
    self.contentView = contentView;
    self.containerView = containerView;
}

- (NSTimeInterval)transitionDuration:(id<UIViewControllerContextTransitioning>)transitionContext {
    return self.duration == 0 ? 0.25f : self.duration;
}

- (void)animateTransition:(id<UIViewControllerContextTransitioning>)transitionContext {
    switch (self.type) {
        case TKZFPresentTransitionTypePresent: {
            [self presentAnimation:transitionContext];
        }
            break;
        case TKZFPresentTransitionTypeDismiss: {
            [self dismissAnimation:transitionContext];
        }
            break;
    }
}

- (void)presentAnimation:(id<UIViewControllerContextTransitioning>)transitionContext {
    UIViewController *toVC = [transitionContext viewControllerForKey:UITransitionContextToViewControllerKey];
    UIViewController *fromVC = [transitionContext viewControllerForKey:UITransitionContextFromViewControllerKey];
    if ([fromVC isKindOfClass:[UINavigationController class]]) {
        UINavigationController *nav = (UINavigationController *)fromVC;
        fromVC = nav.viewControllers.lastObject;
    } else if ([fromVC isKindOfClass:[UITabBarController class]]) {
        UITabBarController *tabBar = (UITabBarController *)fromVC;
        if ([tabBar.selectedViewController isKindOfClass:[UINavigationController class]]) {
            UINavigationController *nav = (UINavigationController *)tabBar.selectedViewController;
            fromVC = nav.viewControllers.lastObject;
        } else {
            fromVC = tabBar.selectedViewController;
        }
    }
    UIView *containerView = [transitionContext containerView];
    [containerView addSubview:toVC.view];
    [containerView addSubview:self.contentView];
    CGRect originRect = [self.containerView convertRect:self.contentView.frame toView:toVC.view];
    self.contentView.frame = originRect;

    UIColor *tempColor = toVC.view.backgroundColor;
    toVC.view.backgroundColor = [tempColor colorWithAlphaComponent:0];
    toVC.view.alpha = 1;
    [self.delagate tkzf_orientationWillChange:YES];
    
//    containerView.frame = CGRectMake(0, 0, UISCREEN_HEIGHT, UISCREEN_WIDTH);
//    CGFloat angle = (90.0f * M_PI) / 180.0f; // 旋转90度，顺时针方向
//    CGAffineTransform transform = CGAffineTransformMakeRotation(angle);
//    containerView.transform = transform;
    
    CGRect toRect = self.contentFullScreenRect;
    self.transiting = YES;
    [UIView animateWithDuration:[self transitionDuration:transitionContext] animations:^{
        self.contentView.frame = toRect;
        [self.contentView layoutIfNeeded];
        toVC.view.backgroundColor = [tempColor colorWithAlphaComponent:1.f];
    } completion:^(BOOL finished) {
        self.transiting = NO;
        [toVC.view addSubview:self.contentView];
        [transitionContext completeTransition:YES];
        [self.delagate tkzf_orientationDidChanged:YES];
        if (!CGRectEqualToRect(toRect, self.contentFullScreenRect)) {
            self.contentView.frame = self.contentFullScreenRect;
            [self.contentView layoutIfNeeded];
        }
    }];
}

- (void)dismissAnimation:(id<UIViewControllerContextTransitioning>)transitionContext {
    UIView *containerView = [transitionContext containerView];
    UIViewController *toVC = [transitionContext viewControllerForKey:UITransitionContextToViewControllerKey];
    if ([toVC isKindOfClass:[UINavigationController class]]) {
        UINavigationController *nav = (UINavigationController *)toVC;
        toVC = nav.viewControllers.lastObject;
    } else if ([toVC isKindOfClass:[UITabBarController class]]) {
        UITabBarController *tabBar = (UITabBarController *)toVC;
        if ([tabBar.selectedViewController isKindOfClass:[UINavigationController class]]) {
            UINavigationController *nav = (UINavigationController *)tabBar.selectedViewController;
            toVC = nav.viewControllers.lastObject;
        } else {
            toVC = tabBar.selectedViewController;
        }
    }
    
    UIViewController *fromVC = [transitionContext viewControllerForKey:UITransitionContextFromViewControllerKey];
    fromVC.view.frame = containerView.bounds;
    [containerView addSubview:fromVC.view];
    [containerView addSubview:self.contentView];
    
    CGRect originRect = [fromVC.view convertRect:self.contentView.frame toView:toVC.view];
    self.contentView.frame = originRect;
    CGRect toRect = [self.containerView convertRect:self.containerView.bounds toView:toVC.view];
    [fromVC.view convertRect:self.contentView.bounds toView:self.containerView.window];
    [self.delagate tkzf_orientationWillChange:NO];
    self.transiting = YES;
    [UIView animateWithDuration:[self transitionDuration:transitionContext] animations:^{
        fromVC.view.alpha = 0;
        self.contentView.frame = toRect;
        [self.contentView layoutIfNeeded];
    } completion:^(BOOL finished) {
        [self.containerView addSubview:self.contentView];
        self.contentView.frame = self.containerView.bounds;
        [transitionContext completeTransition:YES];
        [self.delagate tkzf_orientationDidChanged:NO];
        self.transiting = NO;
    }];
}

- (void)setContentFullScreenRect:(CGRect)contentFullScreenRect {
    _contentFullScreenRect = contentFullScreenRect;
    if (!self.transiting && self.isFullScreen && !self.interation) {
        self.contentView.frame = contentFullScreenRect;
    }
}

@end
