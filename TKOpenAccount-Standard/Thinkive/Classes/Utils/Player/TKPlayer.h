//
//  TKPlayer.h
//  TKOpenAccount-Standard
//
//  Created by <PERSON> on 2021/8/30.
//  Copyright © 2021 thinkive. All rights reserved.
//

#import "TKZFPlayerConst.h"
#import "TKZFOrientationObserver.h"
#import "TKZFPlayerMediaPlayback.h"
#import <AVFoundation/AVFoundation.h>

NS_ASSUME_NONNULL_BEGIN

@class TKPlayer;
@class TKZFPlayerView;


@protocol TKPlayerDelegate <NSObject>

/// 播放状态更新
- (void)tkPlayerPlay:(TKPlayer *)player didUpdateStatus:(AVPlayerStatus)stauts;

/// 播放状态更新
- (void)tkPlayerPlay:(TKPlayer *)player didUpdatePlayTime:(NSTimeInterval)time;

/// 播放结束
- (void)tkPlayerPlayDidEnd:(TKPlayer *)player;

/// 云端加载失败--->本地加载失败--->返回失败回调
- (void)tkPlayerLoadResourceFail:(TKPlayer *)player url:(NSURL *)url;

/// 开始加载资源回调
- (void)tkPlayerPlay:(TKPlayer *)player didStartLoadUrl:(NSURL *)url;

@end

@interface TKPlayer : NSObject<TKZFPlayerMediaPlayback>

/// 根据url生成的asset。也有可能是本地加载的视频资源
@property (nonatomic, strong, readonly) AVURLAsset *asset;

/// 加载的item
@property (nonatomic, strong, readonly) AVPlayerItem *playerItem;

/// 真实播放器
@property (nonatomic, strong) AVPlayer *realPlayer;

/// 播放时间刷新间隔，默认1s
@property (nonatomic, assign) NSTimeInterval timeRefreshInterval;

/// 视频请求头
@property (nonatomic, strong) NSDictionary *requestHeader;

// 视频播放器图层
@property (nonatomic, strong) AVPlayerLayer *avPlayerLayer;

/// 代理
@property (nonatomic, weak)   id<TKPlayerDelegate>delegate;

/// 容器
@property (nonatomic, strong) UIView *contentView;

/// 是否使用扬声器播放，默认为YES
@property (nonatomic, readwrite, assign) BOOL speakerPlayback;

/// 是否倒置图层，默认为NO
@property (nonatomic, readwrite, assign) BOOL isInvertLayer;

/// 下载的文件全路径
@property (nonatomic, readwrite, copy) NSString *downLoadFileFullPath;

/// 加载云端资源超时时间，默认是3s
@property (nonatomic, readwrite, assign) NSTimeInterval loadRemoteVideoMaxTime;

/// 禁用云端加载失败时下载文件的功能，默认NO
@property (nonatomic, readwrite, assign) NSTimeInterval disableDownload;

// 是否正在下载文件到本地
@property (nonatomic, readwrite, assign) BOOL isDownloadingFile;


/// 类构造方法
/// @param urlStr 播放的视频地址
+ (instancetype)playerWithURL:(nullable NSURL *)url contentView:(nullable UIView *)contentView;
+ (instancetype)playerWithURL:(nullable NSURL *)url;


@end


NS_ASSUME_NONNULL_END
