//
//  TKZFPlayerMediaControl.h
//
//
// 
//


#import <Foundation/Foundation.h>
#import "TKZFPlayerMediaPlayback.h"
#import "TKZFOrientationObserver.h"
#import "TKZFPlayerGestureControl.h"
#import "TKZFReachabilityManager.h"
@class TKZFPlayerController;

NS_ASSUME_NONNULL_BEGIN

@protocol TKZFPlayerMediaControl <NSObject>

@required
/// Current playerController
@property (nonatomic, weak) TKZFPlayerController *player;

@optional

#pragma mark - Playback state

/// When the player prepare to play the video.
- (void)videoPlayer:(TKZFPlayerController *)videoPlayer prepareToPlay:(NSURL *)assetURL;

/// When th player playback state changed.
- (void)videoPlayer:(TKZFPlayerController *)videoPlayer playStateChanged:(TKZFPlayerPlaybackState)state;

/// When th player loading state changed.
- (void)videoPlayer:(TKZFPlayerController *)videoPlayer loadStateChanged:(TKZFPlayerLoadState)state;

#pragma mark - progress

/**
 When the playback changed.
 
 @param videoPlayer the player.
 @param currentTime the current play time.
 @param totalTime the video total time.
 */
- (void)videoPlayer:(TKZFPlayerController *)videoPlayer
        currentTime:(NSTimeInterval)currentTime
          totalTime:(NSTimeInterval)totalTime;

/**
 When buffer progress changed.
 */
- (void)videoPlayer:(TKZFPlayerController *)videoPlayer
         bufferTime:(NSTimeInterval)bufferTime;

/**
 When you are dragging to change the video progress.
 */
- (void)videoPlayer:(TKZFPlayerController *)videoPlayer
       draggingTime:(NSTimeInterval)seekTime
          totalTime:(NSTimeInterval)totalTime;

/**
 When play end.
 */
- (void)videoPlayerPlayEnd:(TKZFPlayerController *)videoPlayer;

/**
 When play failed.
 */
- (void)videoPlayerPlayFailed:(TKZFPlayerController *)videoPlayer error:(id)error;

#pragma mark - lock screen

/**
 When set `videoPlayer.lockedScreen`.
 */
- (void)lockedVideoPlayer:(TKZFPlayerController *)videoPlayer lockedScreen:(BOOL)locked;

#pragma mark - Screen rotation

/**
 When the fullScreen maode will changed.
 */
- (void)videoPlayer:(TKZFPlayerController *)videoPlayer orientationWillChange:(TKZFOrientationObserver *)observer;

/**
 When the fullScreen maode did changed.
 */
- (void)videoPlayer:(TKZFPlayerController *)videoPlayer orientationDidChanged:(TKZFOrientationObserver *)observer;

#pragma mark - The network changed

/**
 When the network changed
 */
- (void)videoPlayer:(TKZFPlayerController *)videoPlayer reachabilityChanged:(TKZFReachabilityStatus)status;

#pragma mark - The video size changed

/**
 When the video size changed
 */
- (void)videoPlayer:(TKZFPlayerController *)videoPlayer presentationSizeChanged:(CGSize)size;

#pragma mark - Gesture

/**
 When the gesture condition
 */
- (BOOL)gestureTriggerCondition:(TKZFPlayerGestureControl *)gestureControl
                    gestureType:(TKZFPlayerGestureType)gestureType
              gestureRecognizer:(UIGestureRecognizer *)gestureRecognizer
                          touch:(UITouch *)touch;

/**
 When the gesture single tapped
 */
- (void)gestureSingleTapped:(TKZFPlayerGestureControl *)gestureControl;

/**
 When the gesture double tapped
 */
- (void)gestureDoubleTapped:(TKZFPlayerGestureControl *)gestureControl;

/**
 When the gesture begin panGesture
 */
- (void)gestureBeganPan:(TKZFPlayerGestureControl *)gestureControl
           panDirection:(TKZFPanDirection)direction
            panLocation:(TKZFPanLocation)location;

/**
 When the gesture paning
 */
- (void)gestureChangedPan:(TKZFPlayerGestureControl *)gestureControl
             panDirection:(TKZFPanDirection)direction
              panLocation:(TKZFPanLocation)location
             withVelocity:(CGPoint)velocity;

/**
 When the end panGesture
 */
- (void)gestureEndedPan:(TKZFPlayerGestureControl *)gestureControl
           panDirection:(TKZFPanDirection)direction
            panLocation:(TKZFPanLocation)location;

/**
 When the pinchGesture changed
 */
- (void)gesturePinched:(TKZFPlayerGestureControl *)gestureControl
                 scale:(float)scale;

/**
 When the gesture longPress changed
 */
- (void)longPressed:(TKZFPlayerGestureControl *)gestureControl state:(TKZFLongPressGestureRecognizerState)state;

#pragma mark - scrollview

/**
 When the player will appear in scrollView.
 */
- (void)playerWillAppearInScrollView:(TKZFPlayerController *)videoPlayer;

/**
 When the player did appear in scrollView.
 */
- (void)playerDidAppearInScrollView:(TKZFPlayerController *)videoPlayer;

/**
 When the player will disappear in scrollView.
 */
- (void)playerWillDisappearInScrollView:(TKZFPlayerController *)videoPlayer;

/**
 When the player did disappear in scrollView.
 */
- (void)playerDidDisappearInScrollView:(TKZFPlayerController *)videoPlayer;

/**
 When the player appearing in scrollView.
 */
- (void)playerAppearingInScrollView:(TKZFPlayerController *)videoPlayer playerApperaPercent:(CGFloat)playerApperaPercent;

/**
 When the player disappearing in scrollView.
 */
- (void)playerDisappearingInScrollView:(TKZFPlayerController *)videoPlayer playerDisapperaPercent:(CGFloat)playerDisapperaPercent;

/**
 When the small float view show.
 */
- (void)videoPlayer:(TKZFPlayerController *)videoPlayer floatViewShow:(BOOL)show;

@end

NS_ASSUME_NONNULL_END

