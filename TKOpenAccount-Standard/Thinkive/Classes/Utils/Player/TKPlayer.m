//
//  TKPlayer.m
//  TKOpenAccount-Standard
//
//  Created by <PERSON> on 2021/8/30.
//  Copyright © 2021 thinkive. All rights reserved.
//

#import "TKPlayer.h"
#import "TKZFKVOController.h"
#import "TKZFPlayerView.h"
#import "TKSVGImageView.h"


static NSString *const kStatus                   = @"status";
static NSString *const kLoadedTimeRanges         = @"loadedTimeRanges";
static NSString *const kPlaybackBufferEmpty      = @"playbackBufferEmpty";
static NSString *const kPlaybackLikelyToKeepUp   = @"playbackLikelyToKeepUp";
static NSString *const kPresentationSize         = @"presentationSize";

@interface TKPlayerPresentView : UIView

@property (nonatomic, strong) AVPlayer *player;
/// default is AVLayerVideoGravityResizeAspect.
@property (nonatomic, strong) AVLayerVideoGravity videoGravity;

@end

@implementation TKPlayerPresentView

+ (Class)layerClass {
    return [AVPlayerLayer class];
}

- (AVPlayerLayer *)avLayer {
    return (AVPlayerLayer *)self.layer;
}

- (void)setPlayer:(AVPlayer *)player {
    if (player == _player) return;
    self.avLayer.player = player;
}

- (void)setVideoGravity:(AVLayerVideoGravity)videoGravity {
    if (videoGravity == self.videoGravity) return;
    [self avLayer].videoGravity = videoGravity;
}

- (AVLayerVideoGravity)videoGravity {
    return [self avLayer].videoGravity;
}

@end



@interface TKPlayer()
{
    dispatch_queue_t _videoDownloadQueue;
    id _timeObserver;
    id _itemEndObserver;
    TKZFKVOController *_playerItemKVO;
}

@property (nonatomic, readwrite, strong) TKSVGImageView *loadingImageView;   // 加载视频过程中的动画
//@property (nonatomic, readwrite, strong) UIImageView *loadingImageView;   // 加载视频过程中的动画
@property (nonatomic, readwrite, strong) UILabel *loadingLabel;   // 加载视频过程中的展示文本

//@property (nonatomic, readwrite, strong) UIActivityIndicatorView *activityIndicatorView; // 加载视频过程中的动画
@property (nonatomic, assign) BOOL isBuffering;
//@property (nonatomic, readwrite, strong) UIImageView *previewImageView; // 播放图层显示的视频第一帧
@property (nonatomic, assign) BOOL isPlaying;//是否正在预览视频
@property (nonatomic, readwrite, strong) id timeObserver;  // 音频播放监听者

@property (nonatomic, readwrite, assign) BOOL isAlreadyDownloadedFile;    // 是否已下载过视频。disableDownload为1时，此值不可用
@property (nonatomic, readwrite, strong) NSURL *realAssetURL;    // 播放器真实加载的地址
@property (nonatomic, readwrite, assign) BOOL isFirstPlayDownloadFile;    // 是否首次播放已下载的视频
@property (nonatomic, readwrite, assign) BOOL isDownloadFileAgain;    // 是否再次下载的视频

@property (nonatomic, readwrite, assign) BOOL isShowLoading;    // 是否正在展示加载动画

@property (nonatomic, readwrite, strong) NSTimer *loadVideoTimeoutTimer; // 开始录制超时定时器，防止不做录制占用时间过长
@property (nonatomic, readwrite, assign) BOOL isObserverVideoItem;  // 是否给video item添加了observer

@property (nonatomic, readwrite, strong) TKDownloadSessionManager *downloadSessionManager; // 下载管理者
@property (nonatomic, readwrite, strong) TKDownloadModel *currentDownloadModel;  // 当前正在下载文件模型

@property (nonatomic, strong) AVAssetImageGenerator *imageGenerator;   // 预览图片加载器

@property (nonatomic, assign) BOOL isReadyToPlay;   // 是否准备播放
@property (nonatomic, assign) BOOL pauseWhenBuffer;   // 由于缓冲暂停播放


@end

@implementation TKPlayer

@synthesize view                           = _view;
@synthesize currentTime                    = _currentTime;
@synthesize totalTime                      = _totalTime;
@synthesize playerPlayTimeChanged          = _playerPlayTimeChanged;
@synthesize playerBufferTimeChanged        = _playerBufferTimeChanged;
@synthesize playerDidToEnd                 = _playerDidToEnd;
@synthesize bufferTime                     = _bufferTime;
@synthesize playState                      = _playState;
@synthesize loadState                      = _loadState;
@synthesize assetURL                       = _assetURL;
@synthesize playerPrepareToPlay            = _playerPrepareToPlay;
@synthesize playerReadyToPlay              = _playerReadyToPlay;
@synthesize playerPlayStateChanged         = _playerPlayStateChanged;
@synthesize playerLoadStateChanged         = _playerLoadStateChanged;
@synthesize seekTime                       = _seekTime;
@synthesize muted                          = _muted;
@synthesize volume                         = _volume;
@synthesize presentationSize               = _presentationSize;
@synthesize isPlaying                      = _isPlaying;
@synthesize rate                           = _rate;
@synthesize isPreparedToPlay               = _isPreparedToPlay;
@synthesize shouldAutoPlay                 = _shouldAutoPlay;
@synthesize scalingMode                    = _scalingMode;
@synthesize playerPlayFailed               = _playerPlayFailed;
@synthesize presentationSizeChanged        = _presentationSizeChanged;


#pragma mark - Init && Dealloc
- (void)dealloc
{
    if (self.realPlayer) [self stop];
    
//    TKLogDebug(@"TKPlayer dealloc");
    
    if (self.isAlreadyDownloadedFile) {
        // 移除下载的本地音频文件
        [self deleteLocalVideoFile:self.downLoadFileFullPath];
    }
    self.isAlreadyDownloadedFile = NO;
}

+ (instancetype)playerWithURL:(NSURL *)url;
{
    return [self playerWithURL:url contentView:nil];;
}

+ (instancetype)playerWithURL:(NSURL *)url contentView:(UIView *)contentView {
    
    TKPlayer *player = [self new];
    if ([TKStringHelper isNotEmpty:url.absoluteString]) player.assetURL = url;   // 先设置url，是防止setUrl里面重复设置item
    if (contentView != nil) player.contentView = contentView;
    
    return player;
}

- (instancetype)init {
    self = [super init];
    if (self) {
        _scalingMode = TKZFPlayerScalingModeAspectFit;
        _shouldAutoPlay = YES;
        
        _volume = 1;
        _videoDownloadQueue = dispatch_queue_create("com.thinkive.TKPlayer.videoDownloadQueue", DISPATCH_QUEUE_SERIAL);
        _loadRemoteVideoMaxTime = 3;
        
        TKLogInfo(@"思迪播放器日志(%@):初始化播放器", self);
    }
    return self;
}

#pragma mark - Noti
- (void)itemObserving {
    
    TKLogInfo(@"思迪播放器日志(%@):移除observer item", self);
    [_playerItemKVO safelyRemoveAllObservers];
    
    TKLogInfo(@"思迪播放器日志(%@):observer item", self);
    _playerItemKVO = [[TKZFKVOController alloc] initWithTarget:_playerItem];
    [_playerItemKVO safelyAddObserver:self
                           forKeyPath:kStatus
                              options:NSKeyValueObservingOptionNew
                              context:nil];
    [_playerItemKVO safelyAddObserver:self
                           forKeyPath:kPlaybackBufferEmpty
                              options:NSKeyValueObservingOptionNew
                              context:nil];
    [_playerItemKVO safelyAddObserver:self
                           forKeyPath:kPlaybackLikelyToKeepUp
                              options:NSKeyValueObservingOptionNew
                              context:nil];
    [_playerItemKVO safelyAddObserver:self
                           forKeyPath:kLoadedTimeRanges
                              options:NSKeyValueObservingOptionNew
                              context:nil];
    [_playerItemKVO safelyAddObserver:self
                           forKeyPath:kPresentationSize
                              options:NSKeyValueObservingOptionNew
                              context:nil];
    
    TKLogInfo(@"思迪播放器日志(%@):添加音频时间监听者，监听音频播放时间", self);
    CMTime interval = CMTimeMakeWithSeconds(self.timeRefreshInterval > 0 ? self.timeRefreshInterval : 1, NSEC_PER_SEC);
    @tkzf_weakify(self)
    _timeObserver = [self.realPlayer addPeriodicTimeObserverForInterval:interval queue:dispatch_get_main_queue() usingBlock:^(CMTime time) {
        @tkzf_strongify(self)
        if (!self) return;
        NSArray *loadedRanges = self.playerItem.seekableTimeRanges;
        if (self.isPlaying && self.loadState == TKZFPlayerLoadStateStalled) self.realPlayer.rate = self.rate;
        if (loadedRanges.count > 0) {
            if (self.playerPlayTimeChanged) self.playerPlayTimeChanged(self, self.currentTime, self.totalTime);
            
            // 回调当前播放的时间
            if (self.delegate && [self.delegate respondsToSelector:@selector(tkPlayerPlay:didUpdatePlayTime:)]) {
                [self.delegate tkPlayerPlay:self didUpdatePlayTime:self.currentTime];
            }
        }
    }];
    
    _itemEndObserver = [[NSNotificationCenter defaultCenter] addObserverForName:AVPlayerItemDidPlayToEndTimeNotification object:self.playerItem queue:[NSOperationQueue mainQueue] usingBlock:^(NSNotification * _Nonnull note) {
        @tkzf_strongify(self)
        if (!self) return;
        self.playState = TKZFPlayerPlayStatePlayStopped;
        if (self.playerDidToEnd) self.playerDidToEnd(self);

        [self playVideoEnd];
    }];
}


#pragma mark - KVO
- (void)observeValueForKeyPath:(NSString *)keyPath ofObject:(id)object change:(NSDictionary<NSString *,id> *)change context:(void *)context {

    if (object == _realPlayer.currentItem && [keyPath isEqualToString:kStatus]) {
        [self handlePlayerStatusChange:change];
    } else if ([keyPath isEqualToString:kPlaybackBufferEmpty]) {
        // When the buffer is empty
        if (self.playerItem.playbackBufferEmpty) {
            self.loadState = TKZFPlayerLoadStateStalled;
            [self bufferingSomeSecond];
        }
        TKLogInfo(@"思迪播放器日志(%@):kPlaybackBufferEmpty self.playerItem.playbackBufferEmpty = %i", self, self.playerItem.playbackBufferEmpty);
        
    } else if ([keyPath isEqualToString:kPlaybackLikelyToKeepUp]) {
        // When the buffer is good
        if (self.playerItem.playbackLikelyToKeepUp) {
            self.loadState = TKZFPlayerLoadStatePlayable;
            if (self.isPlaying) [self.realPlayer play];
        }
        TKLogInfo(@"思迪播放器日志(%@):kPlaybackLikelyToKeepUp self.playerItem.playbackLikelyToKeepUp = %i", self, self.playerItem.playbackLikelyToKeepUp);
    } else if ([keyPath isEqualToString:kLoadedTimeRanges]) {
        NSTimeInterval bufferTime = [self availableDuration];
        self->_bufferTime = bufferTime;
        if (self.playerBufferTimeChanged) self.playerBufferTimeChanged(self, bufferTime);

        CGFloat bufferProgress = 0.0f;
        if (self.totalTime > 0) {
            bufferProgress = self.bufferTime / self.totalTime;
        };

        TKLogInfo(@"思迪播放器日志(%@):kLoadedTimeRanges bufferProgress = %.2f", self, bufferProgress);
    } else if ([keyPath isEqualToString:kPresentationSize]) {
        self.presentationSize = self.playerItem.presentationSize;
        TKLogInfo(@"思迪播放器日志(%@):kPresentationSize, presentationSize = %@", self, NSStringFromCGSize(self.presentationSize));
    } else {
        TKLogInfo(@"思迪播放器日志(%@):super observeValueForKeyPath", self);
        [super observeValueForKeyPath:keyPath ofObject:object change:change context:context];
    }
}

- (void)handlePlayerStatusChange:(NSDictionary<NSString *,id> *)change
{
    AVPlayerStatus status = [[change objectForKey:NSKeyValueChangeNewKey] integerValue];
    
    if (self.realPlayer.currentItem.status == AVPlayerItemStatusReadyToPlay) {
        if (!self.isReadyToPlay) {
            self.isReadyToPlay = YES;
            self.loadState = TKZFPlayerLoadStatePlaythroughOK;
            if (self.playerReadyToPlay) self.playerReadyToPlay(self, self.assetURL);
        }
        
        TKLogInfo(@"思迪播放器日志(%@):音频准备就绪", self);
        
        [self showLoadingVideo:NO];
        [self stopLoadVideoimeoutTimer];
        if (self.seekTime) {
            if (self.shouldAutoPlay) [self pause];
            @tkzf_weakify(self)
            [self seekToTime:self.seekTime completionHandler:^(BOOL finished) {
                @tkzf_strongify(self)
                if (finished) {
                    if (self.shouldAutoPlay) [self play];
                }
            }];
            self.seekTime = 0;
        } else {
            TKLogInfo(@"思迪播放器日志(%@):shouldAutoPlay = %i, isPlay = %i", self, self.shouldAutoPlay, self.isPlaying);
            if (self.shouldAutoPlay && self.isPlaying) [self play];
        }
        self.realPlayer.muted = self.muted;
        NSArray *loadedRanges = self.playerItem.seekableTimeRanges;
        if (loadedRanges.count > 0) {
            if (self.playerPlayTimeChanged) self.playerPlayTimeChanged(self, self.currentTime, self.totalTime);
        }
        
    } else if (self.realPlayer.currentItem.status == AVPlayerItemStatusFailed) {
        
        NSError *error = self.realPlayer.currentItem.error;
        if (self.playerPlayFailed) self.playerPlayFailed(self, error);
        
        TKLogInfo(@"思迪播放器日志(%@):音频准备失败，失败原因=%@, 音频地址(%@)", self, error.description, self.realAssetURL.absoluteString);
        
        // 偶现前面的可以播放，一段时间后报加载失败，先屏蔽后面持续观察
//        [self pause];
        
        [self stopLoadVideoimeoutTimer];

        // reallUrl会动态变化，可能先加载云端的，再变成本地的
        if ([self isLoadRemoteUrl] && self.disableDownload == NO && self.isDownloadFileAgain == NO) {

            self.isDownloadFileAgain = YES;
            
//            if ([_url.absoluteString isEqualToString:@"https://www.dfzq.com.cn/upload/20201111/20201111160506803164"]) {
//                    _url = [NSURL URLWithString:@"https://www.dfzq.com.cn/upload/20201111/202011111605068031648.mp4"];
//            }

//            if ([_assetURL.absoluteString isEqualToString:@"https://sample-videos.com/video123/mp4/480/big_buck_bunny_480p_2mb"]) {
//                    _assetURL = [NSURL URLWithString:@"https://sample-videos.com/video123/mp4/480/big_buck_bunny_480p_2mb.mp4"];
//            }

            // 继续展示Loading。增加这个逻辑是防止一种情况，先回调成功，后面又回调失败，触发本地下载
            [self showLoadingVideo:YES];

            TKLogInfo(@"思迪播放器日志(%@):云端加载音频失败，下载音频", self);
            // 加载原始的url地址，加载失败下载到本地
            [self downloadOneWayVideo:self.assetURL.absoluteString downloadFilePath:self.downLoadFileFullPath];
        } else {
            
            self.playState = TKZFPlayerPlayStatePlayFailed;
            self->_isPlaying = NO;
            
            // 回调
            TKLogInfo(@"思迪播放器日志(%@):云端和本地加载音频失败，回调前台", self);
            [self execLoadResourceFail];
        }
    }
    
    // 回调
    if (self.delegate && [self.delegate respondsToSelector:@selector(tkPlayerPlay:didUpdateStatus:)]) {
        [self.delegate tkPlayerPlay:self didUpdateStatus:status];
    }
}

#pragma mark - public method

- (void)play {
    if (!_isPreparedToPlay) {
        TKLogInfo(@"思迪播放器日志(%@):want to play but can't play", self);
        [self prepareToPlay];
    } else {
        if (self.isDownloadingFile) {
            TKLogInfo(@"思迪播放器日志(%@):want to play but is downloading file", self);
            return;
        }
        
        TKLogInfo(@"思迪播放器日志(%@):play", self);
        [self.realPlayer play];
        self.realPlayer.rate = self.rate;
        self->_isPlaying = YES;
        self.playState = TKZFPlayerPlayStatePlaying;
    }
}

- (void)pause {
    TKLogInfo(@"思迪播放器日志(%@):pause", self);
    
    [self.realPlayer pause];
    self->_isPlaying = NO;
    self.playState = TKZFPlayerPlayStatePaused;
    [_playerItem cancelPendingSeeks];
    [_asset cancelLoading];
    
    _pauseWhenBuffer = NO;
}

- (void)bufferingToPause {
    [self.realPlayer pause];
    self->_isPlaying = NO;
    self.playState = TKZFPlayerPlayStatePaused;
    [_playerItem cancelPendingSeeks];
    [_asset cancelLoading];
    
    _pauseWhenBuffer = YES;
    TKLogInfo(@"思迪播放器日志(%@):pause when buffer", self);
}

/// 停止
- (void)stop {
    TKLogInfo(@"思迪播放器日志(%@):stop", self);
    
    [_playerItemKVO safelyRemoveAllObservers];
    self.loadState = TKZFPlayerLoadStateUnknown;
    self.playState = TKZFPlayerPlayStatePlayStopped;
    if (self.realPlayer.rate != 0) [self.realPlayer pause];
    [_playerItem cancelPendingSeeks];
    [_asset cancelLoading];
    
    [self.realPlayer removeTimeObserver:_timeObserver];
    [self.realPlayer replaceCurrentItemWithPlayerItem:nil];
    self.presentationSize = CGSizeZero;
    
//    [self.view removeFromSuperview];
//    _view = nil;
    
    _timeObserver = nil;
    
    [[NSNotificationCenter defaultCenter] removeObserver:_itemEndObserver name:AVPlayerItemDidPlayToEndTimeNotification object:self.playerItem];
    _itemEndObserver = nil;
    
    // 停止定时器
    [self stopLoadVideoimeoutTimer];
    
    // 停止下载
    if (self.currentDownloadModel) {
        [self.downloadSessionManager cancleWithDownloadModel:self.currentDownloadModel];
        self.currentDownloadModel = nil;
    }
    
    _isPlaying = NO;
    _realPlayer = nil;
    _assetURL = nil;
    _realAssetURL = nil;
    _playerItem = nil;
    _isPreparedToPlay = NO;
    self->_currentTime = 0;
    self->_totalTime = 0;
    self->_bufferTime = 0;
    self.isReadyToPlay = NO;
    
}


- (void)reloadPlayer {
    TKLogInfo(@"思迪播放器日志(%@):重置音频源，再重新设置(%@)", self, self.assetURL.absoluteString);
    
    if (self.isLoadRemoteUrl) {
        // 先移除已有的本地音频文件
        [self deleteLocalVideoFile:self.downLoadFileFullPath];
        
        self.isDownloadFileAgain = NO;
    }
    
    self.seekTime = self.currentTime;
    [self prepareToPlay];
}

- (BOOL)canPlay
{
//    return self.realPlayer.currentItem.status == AVPlayerStatusReadyToPlay;
    return self.loadState == TKZFPlayerLoadStatePlaythroughOK;
}


- (void)replay {
    @tkzf_weakify(self)
    [self seekToTime:0 completionHandler:^(BOOL finished) {
        @tkzf_strongify(self)
        if (finished) {
            [self play];
        }
    }];
}

- (void)seekToTime:(NSTimeInterval)time completionHandler:(void (^ __nullable)(BOOL finished))completionHandle {
    
    if (self.totalTime > 0) {
        [_realPlayer.currentItem cancelPendingSeeks];
        int32_t timeScale = _realPlayer.currentItem.asset.duration.timescale;
        CMTime seekTime = CMTimeMakeWithSeconds(time, timeScale);
        [_realPlayer seekToTime:seekTime toleranceBefore:kCMTimeZero toleranceAfter:kCMTimeZero completionHandler:completionHandle];
    } else {
        self.seekTime = time;
    }
}

// 同步获取视频帧
- (UIImage *)thumbnailImageAtCurrentTime {
    CMTime expectedTime = self.playerItem.currentTime;
    CGImageRef cgImage = NULL;
    
    self.imageGenerator.requestedTimeToleranceBefore = kCMTimeZero;
    self.imageGenerator.requestedTimeToleranceAfter = kCMTimeZero;
    cgImage = [self.imageGenerator copyCGImageAtTime:expectedTime actualTime:NULL error:NULL];

    if (!cgImage) {
        self.imageGenerator.requestedTimeToleranceBefore = kCMTimePositiveInfinity;
        self.imageGenerator.requestedTimeToleranceAfter = kCMTimePositiveInfinity;
        cgImage = [self.imageGenerator copyCGImageAtTime:expectedTime actualTime:NULL error:NULL];
    }
    
    UIImage *image = [UIImage imageWithCGImage:cgImage];
    CGImageRelease(cgImage);  // CGImageRef won't be released by ARC
    return image;
}

// 异步获取视频帧
- (void)thumbnailImageAtCurrentTime:(void(^)(UIImage *))handler {
    CMTime expectedTime = self.playerItem.currentTime;
    [self.imageGenerator generateCGImagesAsynchronouslyForTimes:@[[NSValue valueWithCMTime:expectedTime]] completionHandler:^(CMTime requestedTime, CGImageRef  _Nullable image, CMTime actualTime, AVAssetImageGeneratorResult result, NSError * _Nullable error) {
        
        if (handler && image != NULL && CFGetTypeID(image) == CGImageGetTypeID()) {
            
            UIImage *finalImage = [UIImage imageWithCGImage:image];
            // 回调
            dispatch_async(dispatch_get_main_queue(), ^{
                handler(finalImage);
            });
        }
    }];
}

#pragma mark - private method
/// Calculate buffer progress
- (NSTimeInterval)availableDuration {
    NSArray *timeRangeArray = _playerItem.loadedTimeRanges;
    CMTime currentTime = [_realPlayer currentTime];
    BOOL foundRange = NO;
    CMTimeRange aTimeRange = {0};
    if (timeRangeArray.count) {
        aTimeRange = [[timeRangeArray objectAtIndex:0] CMTimeRangeValue];
        if (CMTimeRangeContainsTime(aTimeRange, currentTime)) {
            foundRange = YES;
        }
    }
    
    if (foundRange) {
        CMTime maxTime = CMTimeRangeGetEnd(aTimeRange);
        NSTimeInterval playableDuration = CMTimeGetSeconds(maxTime);
        if (playableDuration > 0) {
            return playableDuration;
        }
    }
    return 0;
}

- (void)initializePlayer {
    TKLogInfo(@"思迪播放器日志(%@):initializePlayer", self);
    
    _asset = [AVURLAsset URLAssetWithURL:self.realAssetURL options:self.requestHeader];
    _playerItem = [AVPlayerItem playerItemWithAsset:_asset];
    
    if (_playerItem == nil) {
        TKLogInfo(@"思迪播放器日志(%@)：待设置的realAssetURL不为空, 但生成的item为空,回调外层", self);

        [self execLoadResourceFail];
        
        return;
    }
    
    // 重置来切换资源
    //        [_realPlayer replaceCurrentItemWithPlayerItem:_playerItem]; // 苹果底层用信号量来切换音频，阻塞线程，会造成卡顿
    _realPlayer = [AVPlayer playerWithPlayerItem:_playerItem];
    _imageGenerator = [AVAssetImageGenerator assetImageGeneratorWithAsset:_asset];
//    _imageGenerator = [AVAssetImageGenerator assetImageGeneratorWithAsset:_asset];
    
    [self enableAudioTracks:YES inPlayerItem:_playerItem];

    self.view.presentationSize = CGSizeZero;
    self.view.scalingMode = TKZFPlayerScalingModeNone;
    TKPlayerPresentView *presentView = [[TKPlayerPresentView alloc] init];
    presentView.player = _realPlayer;
    self.view.playerView = presentView;
    [self transformAvplayerLayer:self.isInvertLayer];
    
    // 同步获取封面图
//    self.view.coverImageView.image = [self thumbnailImageAtCurrentTime];
    __weak typeof(self) weakSelf = self;
    [self thumbnailImageAtCurrentTime:^(UIImage * _Nonnull image) {
        weakSelf.view.coverImageView.image = image;
    }];

    if (@available(iOS 9.0, *)) {
        _playerItem.canUseNetworkResourcesForLiveStreamingWhilePaused = NO;
    }
    if (@available(iOS 10.0, *)) {
        _playerItem.preferredForwardBufferDuration = 5;
        /// 关闭AVPlayer默认的缓冲延迟播放策略，提高首屏播放速度
        _realPlayer.automaticallyWaitsToMinimizeStalling = NO;
    }
    [self itemObserving];
    
    [self createLoadVideoTimeoutTimer:self.loadRemoteVideoMaxTime]; // 添加超时定时器
    
    [self showLoadingVideo:YES];
    
    if (self.delegate && [self.delegate respondsToSelector:@selector(tkPlayerPlay:didStartLoadUrl:)]) {
        [self.delegate tkPlayerPlay:self didStartLoadUrl:self.assetURL];
    }
    TKLogInfo(@"思迪播放器日志(%@)：开始加载资源,回调外层", self);
}

/// 播放速度切换方法
- (void)enableAudioTracks:(BOOL)enable inPlayerItem:(AVPlayerItem*)playerItem {
    for (AVPlayerItemTrack *track in playerItem.tracks){
        if ([track.assetTrack.mediaType isEqual:AVMediaTypeVideo]) {
            track.enabled = enable;
        }
    }
}

/**
 *  缓冲较差时候回调这里
 */
- (void)bufferingSomeSecond {
    // playbackBufferEmpty会反复进入，因此在bufferingOneSecond延时播放执行完之前再调用bufferingSomeSecond都忽略
    if (self.isBuffering || self.playState == TKZFPlayerPlayStatePlayStopped) return;
    /// 没有网络
    Network_Type networkType = [TKNetHelper getNetworkType];
    if (networkType == Network_No) return;
    self.isBuffering = YES;
    
    TKLogInfo(@"思迪播放器日志(%@)：缓冲中...", self);
    
    // 需要先暂停一小会之后再播放，否则网络状况不好的时候时间在走，声音播放不出来
    [self bufferingToPause];
    
    [self showLoadingVideo:YES];
    
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(3.0 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        [self showLoadingVideo:NO];
        
        // 如果此时用户已经暂停了，则不再需要开启播放了
        if (!self.pauseWhenBuffer && self.loadState == TKZFPlayerLoadStateStalled) {
            TKLogInfo(@"思迪播放器日志(%@)：缓冲3s后，用户已经暂停了，则不再需要播放", self);
            self.isBuffering = NO;
            return;
        }
        [self play];
        // 如果执行了play还是没有播放则说明还没有缓存好，则再次缓存一段时间
        self.isBuffering = NO;
//        if (!self.playerItem.isPlaybackLikelyToKeepUp) [self bufferingSomeSecond];
    });
}

- (void)prepareToPlay
{
    TKLogInfo(@"思迪播放器日志(%@):prepareToPlay", self);
    
    if (!_assetURL) {
        TKLogInfo(@"思迪播放器日志(%@):prepareToPlay _assetURL 为空", self);
        return;
    }
//    _realAssetURL = _assetURL;
    if (!_realAssetURL) _realAssetURL = _assetURL;
    _isPreparedToPlay = YES;
    
    [self initializePlayer];
    if (self.shouldAutoPlay) {
        [self play];
    }
    
    self.loadState = TKZFPlayerLoadStatePrepare;
    if (self.playerPrepareToPlay) self.playerPrepareToPlay(self, self.assetURL);
}

- (void)transformAvplayerLayer:(BOOL)isInvertLayer
{
    //竖屏摄像头player播放是倒立的要调整
    CGAffineTransform transform = CGAffineTransformIdentity;
    //顺时针旋转180度
    if (isInvertLayer) transform = CGAffineTransformMakeRotation(M_PI / 180.0 * 180.0);
    self.avPlayerLayer.affineTransform = transform;
}


- (void)playLocalVideo:(NSString *)localFilePath
{
    if ([TKStringHelper isEmpty:localFilePath]) {
        TKLogInfo(@"思迪播放器日志(%@)：播放本地视频文件为空",  self);
        
        [self showLoadingVideo:NO];
        
        if (self.delegate && [self.delegate respondsToSelector:@selector(tkPlayerLoadResourceFail:url:)]) {
            [self.delegate tkPlayerLoadResourceFail:self url:(self.assetURL ? self.assetURL : nil)];  // 传入远程url则返回远程url，否则是本地文件播放，返回本地文件
        }
        
        return;
    }
    
    self.realAssetURL = [NSURL fileURLWithPath:localFilePath];
    
    TKLogInfo(@"思迪播放器日志(%@):播放本地文件(%@)", self, self.realAssetURL.absoluteString);
    TKLogInfo(@"思迪播放器日志(%@):自动播放 = %i", self, self.shouldAutoPlay);
    
    if (self.isPreparedToPlay) {
        
        if (self.shouldAutoPlay) [self play];
    } else {
        // 等待加载播放器，再调用播放
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            if (self.shouldAutoPlay) [self play];
        });
    }
}

- (void)downloadOneWayVideo:(NSString *)remoteUrlStr downloadFilePath:(NSString *)downloadFilePath
{
    if (self.isDownloadingFile == YES) return;  // 正在下载，后续的请求全部抛掉
    
    self.isDownloadingFile = YES;
    
    TKLogInfo(@"思迪播放器日志(%@):视频下载检测", self);
    
    // 下载播放
    // 异步处理
    __weak typeof(self) weakSelf = self;
    dispatch_async(self->_videoDownloadQueue, ^{

        NSString *filePath = downloadFilePath;  // 本地文件
        NSFileManager *fileManager = [NSFileManager defaultManager];

        BOOL fileExists = [fileManager fileExistsAtPath:filePath];
        if (!fileExists) {

            TKLogInfo(@"思迪播放器日志(%@):视频文件本地不存在---(%@)，开始下载---(%@)", weakSelf, filePath, remoteUrlStr);
            
            NSURL *url = [NSURL URLWithString:remoteUrlStr];
            
            weakSelf.currentDownloadModel = [weakSelf.downloadSessionManager startDownloadURLString:url.absoluteString toDestinationPath:filePath progress:^(TKDownloadProgress *progress) {

                TKLogInfo(@"思迪播放器日志(%@):正在下载文件.......%.2f", weakSelf, progress.progress);
            } state:^(TKDownloadState state, NSString *filePath, NSError *error) {
                
                if (state == TKDownloadStateFailed){
                    
                    weakSelf.isDownloadingFile = NO;
                    weakSelf.currentDownloadModel = nil;
                    weakSelf.isAlreadyDownloadedFile = YES;
                    weakSelf.playState = TKZFPlayerPlayStatePlayFailed;
                    weakSelf.isPlaying = NO;
                    [weakSelf showLoadingVideo:NO];

                    TKLogInfo(@"思迪播放器日志(%@):视频文件下载失败", weakSelf);
                    dispatch_async(dispatch_get_main_queue(), ^{
                        
                        if (weakSelf.delegate && [weakSelf.delegate respondsToSelector:@selector(tkPlayerLoadResourceFail:url:)]) {
                            [weakSelf.delegate tkPlayerLoadResourceFail:weakSelf url:weakSelf.assetURL];
                        }
                    });
                    
                } else if(state == TKDownloadStateCompleted){

                    weakSelf.isDownloadingFile = NO;
                    weakSelf.currentDownloadModel = nil;
                    weakSelf.isAlreadyDownloadedFile = YES;
                    weakSelf.isFirstPlayDownloadFile = YES;
                    TKLogInfo(@"思迪播放器日志(%@):视频文件下载成功,文件地址(%@)", weakSelf, filePath);

                    dispatch_async(dispatch_get_main_queue(), ^{
                        
                        // 本地播放
                        [weakSelf playLocalVideo:filePath];
                    });
                }
            }];
            
        } else {
            TKLogInfo(@"思迪播放器日志(%@):视频文件已存在,停止下载，播放本地文件(%@)", weakSelf, filePath);

            weakSelf.isFirstPlayDownloadFile = YES;
            weakSelf.isDownloadingFile = NO;
            weakSelf.isAlreadyDownloadedFile = YES;
            weakSelf.currentDownloadModel = nil;
            
            dispatch_async(dispatch_get_main_queue(), ^{
                
                // 本地播放
                [weakSelf playLocalVideo:filePath];
            });
        }
    });
}

- (void)deleteLocalVideoFile:(NSString *)localFilePath
{
    if ([TKStringHelper isEmpty:localFilePath]) return;
    
    NSFileManager *fileManager = [NSFileManager defaultManager];
    BOOL fileExists = [fileManager fileExistsAtPath:localFilePath];
    
    if (fileExists == NO) {
        TKLogInfo(@"思迪播放器日志(%@):本地文件不存在,删除本地文件流程中断, (%@)", self, localFilePath);
        return;
    }

    // 删除原有的视频
    NSError *error = nil;
    [fileManager removeItemAtPath:localFilePath error:&error];
    if (error) {
        TKLogInfo(@"思迪播放器日志(%@):删除本地文件出错%@", self, error.description);
    } else {
        fileExists = NO;
        TKLogInfo(@"思迪播放器日志(%@):本地文件删除成功(%@)", self, localFilePath);
    }
}


/**
 @是否展示正在加载视频。YES-展示；NO-隐藏
 */
- (void)showLoadingVideo:(BOOL)isShow {
    
    self.isShowLoading = isShow;
    
    // 回调
    dispatch_async(dispatch_get_main_queue(), ^{
        
//        if (isShow) {
//
//            self.activityIndicatorView.hidden = NO;
//            [self.activityIndicatorView startAnimating];
//            [self.contentView bringSubviewToFront:self.activityIndicatorView];
//        } else {
//            self.activityIndicatorView.hidden = YES;
//            [self.activityIndicatorView stopAnimating];
//        }
        if (isShow) {
            
            self.loadingImageView.hidden = NO;
            [self.contentView bringSubviewToFront:self.loadingImageView];
            [self startLoadingAnimation];
            
            self.loadingLabel.hidden = NO;
            [self.contentView bringSubviewToFront:self.loadingLabel];
        } else {
            
            self.loadingImageView.hidden = YES;
            [self stopLoadingAnimation];
            self.loadingLabel.hidden = YES;
        }
    });
    
}

- (void)startLoadingAnimation
{
    if (![_loadingImageView.layer animationForKey:@"loadingImageViewAnnimation"]) {
        CABasicAnimation *rotationAnimation = [CABasicAnimation animationWithKeyPath:@"transform.rotation.z"];
        rotationAnimation.fromValue = [NSNumber numberWithFloat:0];
        rotationAnimation.toValue = [NSNumber numberWithFloat:2.0*M_PI];
        rotationAnimation.repeatCount = MAXFLOAT;
        rotationAnimation.duration = 2;
        rotationAnimation.timingFunction = [CAMediaTimingFunction functionWithName:kCAMediaTimingFunctionLinear];
        rotationAnimation.removedOnCompletion = NO;
        [_loadingImageView.layer addAnimation:rotationAnimation forKey:@"loadingImageViewAnnimation"];
    }
}

- (void)stopLoadingAnimation
{
    if (![_loadingImageView.layer animationForKey:@"loadingImageViewAnnimation"]) {
        [_loadingImageView.layer removeAnimationForKey:@"loadingImageViewAnnimation"];
    }
}

- (void)createLoadVideoTimeoutTimer:(NSTimeInterval)timeoutInterval
{
    // 本地资源加载不设置超时机制
    if ([self isLoadRemoteUrl] && self.disableDownload == NO) {
        [self stopLoadVideoimeoutTimer];
        
        TKLogInfo(@"思迪播放器日志(%@)：添加视频加载定时器", self);
        
        // 设置开始录制超时定时器
        self.loadVideoTimeoutTimer = [NSTimer timerWithTimeInterval:timeoutInterval target:self selector:@selector(loadVideoTimeOutBreakLink:) userInfo:nil repeats:NO];
        [[NSRunLoop mainRunLoop] addTimer:self.loadVideoTimeoutTimer forMode:NSRunLoopCommonModes];
    }
}

- (void)stopLoadVideoimeoutTimer
{
    if (self.loadVideoTimeoutTimer) {
        TKLogInfo(@"思迪播放器日志(%@)：销毁视频加载定时器", self);
        
        [self.loadVideoTimeoutTimer invalidate];
        self.loadVideoTimeoutTimer = nil;
    }
}

- (void)loadVideoTimeOutBreakLink:(NSTimer *)timer
{
    [self pause];
    
    TKLogInfo(@"思迪播放器日志(%@):资源加载超时，先移除observer item", self);
    [_playerItemKVO safelyRemoveAllObservers];
    
    [self stopLoadVideoimeoutTimer];

    TKLogInfo(@"思迪播放器日志(%@)：加载云端视频超时，转本地下载", self);
    // 开始下载文件
    [self downloadOneWayVideo:self.assetURL.absoluteString downloadFilePath:self.downLoadFileFullPath];
    
//    if ([self isLoadRemoteUrl] && self.disableDownload == NO) {
//
//        // 开始下载文件
//        [self downloadOneWayVideo:self.assetURL.absoluteString downloadFilePath:self.downLoadFileFullPath];
//    } else {
//        // 不下载文件，直接回调失败
//        TKLogInfo(@"思迪播放器日志(%@)：云端和本地加载音频失败，回调前台", self);
//        [self execLoadResourceFail];
//    }
}

- (void)execLoadResourceFail
{
    dispatch_async(dispatch_get_main_queue(), ^{
        
        [self showLoadingVideo:NO];
        
        if (self.delegate && [self.delegate respondsToSelector:@selector(tkPlayerLoadResourceFail:url:)]) {
            [self.delegate tkPlayerLoadResourceFail:self url:self.assetURL];
        }
    });
}

- (void)playVideoEnd
{
    TKLogInfo(@"思迪播放器日志(%@):音频播放结束，回调前台", self);

//    // 暂停播放
//    [self pause];
    self.isPlaying = NO;

//    // 重置进度
//    [self.playerItem seekToTime:kCMTimeZero];

    // 回调
    if (self.delegate && [self.delegate respondsToSelector:@selector(tkPlayerPlayDidEnd:)]) {
        [self.delegate tkPlayerPlayDidEnd:self];
    }
}

- (BOOL)isLoadRemoteUrl
{
    return [self.realAssetURL.scheme isEqualToString:@"https"] ||
    [self.realAssetURL.scheme isEqualToString:@"http"];
}

#pragma mark - Getter
- (void)setAssetURL:(NSURL *)url {
    
//    if ([url.absoluteString isEqualToString:@"https://www.dfzq.com.cn/upload/20201111/20201111160506803164.mp4"]) {
//        url = [NSURL URLWithString:@"https://www.dfzq.com.cn/upload/20201111/20201111160506803164"];
//    }
    
//    if ([url.absoluteString isEqualToString:@"https://sample-videos.com/video123/mp4/480/big_buck_bunny_480p_2mb.mp4"]) {
//        url = [NSURL URLWithString:@"https://sample-videos.com/video123/mp4/480/big_buck_bunny_480p_2mb"];
//    }
    
    if (self.isLoadRemoteUrl) {
        // 先移除已有的本地音频文件
        [self deleteLocalVideoFile:self.downLoadFileFullPath];
        
        self.isDownloadFileAgain = NO;
    }
    
    // _assetURL只是起到记录的作用。重复设置同一个url的拦截放在setrealAssetURL
    _assetURL = url;
    
    // 非空判断
    if (url == nil || [TKStringHelper isEmpty:url.absoluteString]) {
        
        if ([TKStringHelper isNotEmpty:_realAssetURL.absoluteString]) {
            TKLogInfo(@"思迪播放器日志(%@)：上一次url设置不为空，重置上次的所有处理",  self);
            
            if (self.realPlayer) [self stop];
        }
        
        TKLogInfo(@"思迪播放器日志(%@)：传入的url为空,回调外层",  self);
        
        [self showLoadingVideo:NO];
        
        if (self.delegate && [self.delegate respondsToSelector:@selector(tkPlayerLoadResourceFail:url:)]) {
            [self.delegate tkPlayerLoadResourceFail:self url:url];
        }
        
        return;
    };
    
    self.realAssetURL = url;   // 使用点语法加载资源

}

- (void)setRealAssetURL:(NSURL *)realAssetURL {
    
    if (![_realAssetURL.absoluteString isEqualToString:realAssetURL.absoluteString]) {
        
        // 会清空所有的数据，因此需要先缓存url
        NSURL *url = _assetURL;
        if (self.realPlayer) [self stop];
        self.isFirstPlayDownloadFile = NO;
        
        TKLogInfo(@"思迪播放器日志(%@)：设置新的realAssetURL (%@)",  self, realAssetURL.absoluteString);
        _assetURL = url;
        _realAssetURL = realAssetURL;
        
        [self prepareToPlay];
    } else {
        TKLogInfo(@"思迪播放器日志(%@)：再次设置url地址一样，请用reload方法重新加载,本次操作被过滤",  self);
    }
}

- (void)setPlayState:(TKZFPlayerPlaybackState)playState {
    _playState = playState;
    if (self.playerPlayStateChanged) self.playerPlayStateChanged(self, playState);
}

- (void)setLoadState:(TKZFPlayerLoadState)loadState {
    _loadState = loadState;
    if (self.playerLoadStateChanged) self.playerLoadStateChanged(self, loadState);
}

- (void)setRate:(float)rate {
    _rate = rate;
    if (self.realPlayer && fabsf(_realPlayer.rate) > 0.00001f) {
        self.realPlayer.rate = rate;
    }
}

- (void)setMuted:(BOOL)muted {
    _muted = muted;
    self.realPlayer.muted = muted;
}

- (void)setScalingMode:(TKZFPlayerScalingMode)scalingMode {
    _scalingMode = scalingMode;
    TKPlayerPresentView *presentView = (TKPlayerPresentView *)self.view.playerView;
    self.view.scalingMode = scalingMode;
    switch (scalingMode) {
        case TKZFPlayerScalingModeNone:
            presentView.videoGravity = AVLayerVideoGravityResizeAspect;
            break;
        case TKZFPlayerScalingModeAspectFit:
            presentView.videoGravity = AVLayerVideoGravityResizeAspect;
            break;
        case TKZFPlayerScalingModeAspectFill:
            presentView.videoGravity = AVLayerVideoGravityResizeAspectFill;
            break;
        case TKZFPlayerScalingModeFill:
            presentView.videoGravity = AVLayerVideoGravityResize;
            break;
        default:
            break;
    }
}

- (void)setVolume:(float)volume {
    _volume = MIN(MAX(0, volume), 1);
    self.realPlayer.volume = _volume;
}

- (void)setPresentationSize:(CGSize)presentationSize {
    
    _presentationSize = presentationSize;
    self.view.presentationSize = presentationSize;
    if (self.presentationSizeChanged) {
        self.presentationSizeChanged(self, self.presentationSize);
    }
}

- (void)setSpeakerPlayback:(BOOL)speakerPlayback {
    if (_speakerPlayback != speakerPlayback) {
        _speakerPlayback = speakerPlayback;
        
        // 设置麦克风播放
        AVAudioSessionCategory category = AVAudioSession.sharedInstance.category;
        AVAudioSessionCategoryOptions option = AVAudioSession.sharedInstance.categoryOptions;
//        [[AVAudioSession sharedInstance] setActive:YES error:nil];
        
        if (_speakerPlayback == YES) {
            
            option = option | AVAudioSessionCategoryOptionDefaultToSpeaker;
        } else {
            option = option ^ AVAudioSessionCategoryOptionDefaultToSpeaker;
        }
        
        [[AVAudioSession sharedInstance] setCategory:category withOptions:option error:nil];
    }
}

- (void)setContentView:(UIView *)contentView {
    _contentView = contentView;
        
    // 设置为黑色
    contentView.backgroundColor = UIColor.blackColor;
    self.view.frame = contentView.bounds;
    [contentView addSubview:self.view];
    
    [_loadingImageView removeFromSuperview];
    _loadingImageView = nil;
    
    [_loadingLabel removeFromSuperview];
    _loadingLabel = nil;
    
    [self showLoadingVideo:self.isShowLoading];
}

- (void)setIsInvertLayer:(BOOL)isInvertLayer {
    if (_isInvertLayer != isInvertLayer) {
        _isInvertLayer = isInvertLayer;
        
        [self transformAvplayerLayer:isInvertLayer];
    }
}

- (TKSVGImageView *)loadingImageView {
    if (_loadingImageView == nil) {
        
        CGFloat width = 38;
        //        _loadingImageView = [[UIImageView alloc] initWithFrame:CGRectMake(self.contentView.TKWidth * 0.5 -  width * 0.5, self.contentView.TKHeight * 0.5 - width * 0.5, width, width)];
        //        _loadingImageView.image = [UIImage imageNamed:[NSString stringWithFormat:@"%@.bundle/Resources/TKOpenGeneral/tk_video_loading.png",TK_OPEN_RESOURCE_NAME]];
        
        _loadingImageView = [[TKSVGImageView alloc] initWithFrame:CGRectMake(self.contentView.TKWidth * 0.5 -  width * 0.5, self.contentView.TKHeight * 0.5 - width * 0.5, width, width)];
        
        TKSVGImage *image = [TKSVGImage imageNamed:[NSString stringWithFormat:@"%@.bundle/Resources/TKOpenGeneral/tk_video_loading.svg",TK_OPEN_RESOURCE_NAME]];
        image = [image imageWithRenderingMode:UIImageRenderingModeAlwaysTemplate];
        
        _loadingImageView.image = image;
        [_loadingImageView setTintColor:[UIColor whiteColor]];
        
        [self.contentView addSubview:_loadingImageView];
        
    }
    
    return _loadingImageView;
}

- (UILabel *)loadingLabel {
    if (_loadingLabel == nil) {
        
        _loadingLabel = [[UILabel alloc] initWithFrame:CGRectMake(0, self.loadingImageView.TKBottom + 12, self.contentView.TKWidth, 20)];
        _loadingLabel.text = @"加载中...";
        _loadingLabel.font = [UIFont fontWithName:@"PingFang SC" size:14];
        _loadingLabel.textAlignment = NSTextAlignmentCenter;
        _loadingLabel.textColor = [UIColor whiteColor];
        [self.contentView addSubview:_loadingLabel];
        
        //        self.contentView.backgroundColor = UIColor.redColor;
    }
    return _loadingLabel;
}


#pragma mark - Getter
- (TKZFPlayerView *)view {
    if (!_view) {
        TKZFPlayerView *view = [[TKZFPlayerView alloc] init];
        _view = view;
    }
    return _view;
}

- (AVPlayerLayer *)avPlayerLayer {
    TKPlayerPresentView *view = (TKPlayerPresentView *)self.view.playerView;
    return [view avLayer];
}

- (float)rate {
    return _rate == 0.0 ? 1.0 : _rate;
}

- (NSTimeInterval)totalTime {
    NSTimeInterval sec = CMTimeGetSeconds(self.realPlayer.currentItem.duration);
    
    if (isnan(sec)) {
        return 0;
    }
    return sec;
}

- (NSTimeInterval)currentTime {
    NSTimeInterval sec = CMTimeGetSeconds(self.realPlayer.currentItem.currentTime);
    if (isnan(sec) || sec < 0) {
        return 0;
    }
    return sec;
}


- (TKDownloadSessionManager *)downloadSessionManager {
    if (_downloadSessionManager == nil) {
        _downloadSessionManager = [TKDownloadSessionManager manager];
    }
    
    return _downloadSessionManager;
}


@end

