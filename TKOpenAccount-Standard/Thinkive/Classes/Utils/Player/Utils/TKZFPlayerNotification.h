//
//  TKZFPlayerNotification.h
//
//
// 
//


#import <UIKit/UIKit.h>
#import <AVFoundation/AVFoundation.h>

NS_ASSUME_NONNULL_BEGIN

typedef NS_ENUM(NSUInteger, TKZFPlayerBackgroundState) {
    TKZFPlayerBackgroundStateForeground,  // Enter the foreground from the background.
    TKZFPlayerBackgroundStateBackground,  // From the foreground to the background.
};

@interface TKZFPlayerNotification : NSObject

@property (nonatomic, readonly) TKZFPlayerBackgroundState backgroundState;

@property (nonatomic, copy, nullable) void(^willResignActive)(TKZFPlayerNotification *registrar);

@property (nonatomic, copy, nullable) void(^didBecomeActive)(TKZFPlayerNotification *registrar);

@property (nonatomic, copy, nullable) void(^newDeviceAvailable)(TKZFPlayerNotification *registrar);

@property (nonatomic, copy, nullable) void(^oldDeviceUnavailable)(TKZFPlayerNotification *registrar);

@property (nonatomic, copy, nullable) void(^categoryChange)(TKZFPlayerNotification *registrar);

@property (nonatomic, copy, nullable) void(^volumeChanged)(float volume);

@property (nonatomic, copy, nullable) void(^audioInterruptionCallback)(AVAudioSessionInterruptionType interruptionType);

- (void)addNotification;

- (void)removeNotification;

@end

NS_ASSUME_NONNULL_END
