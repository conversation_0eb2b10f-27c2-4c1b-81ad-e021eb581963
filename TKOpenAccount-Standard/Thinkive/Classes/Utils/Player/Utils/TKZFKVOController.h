//
//  TKZFKVOController.h
//  TKOpenAccount-Standard
//
//  Created by <PERSON> on 2022/12/6.
//  Copyright © 2022 thinkive. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface TKZFKVOController : NSObject

/// 添加KVO的目标
/// - Parameter target: 目标
- (instancetype)initWithTarget:(NSObject *)target;

/// 添加KVO observer
/// - Parameters:
///   - observer: observer
///   - keyPath: keyPath
///   - options: options
///   - context: context
- (void)safelyAddObserver:(NSObject *)observer
               forKeyPath:(NSString *)keyPath
                  options:(NSKeyValueObservingOptions)options
                  context:(void * _Nullable)context;

/// 移除KVO observer
/// - Parameters:
///   - observer: observer
///   - keyPath: keyPath
- (void)safelyRemoveObserver:(NSObject *)observer
                  forKeyPath:(NSString *)keyPath;

/// 移除所有KVO observer
- (void)safelyRemoveAllObservers;

@end

NS_ASSUME_NONNULL_END
