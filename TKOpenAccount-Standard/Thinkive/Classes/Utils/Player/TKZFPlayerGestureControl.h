//
//  TKZFPlayerGestureControl.h
//
//
// 
//


#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

typedef NS_ENUM(NSUInteger, TKZFPlayerGestureType) {
    TKZFPlayerGestureTypeUnknown,
    TKZFPlayerGestureTypeSingleTap,
    TKZFPlayerGestureTypeDoubleTap,
    TKZFPlayerGestureTypePan,
    TKZFPlayerGestureTypePinch
};

typedef NS_ENUM(NSUInteger, TKZFPanDirection) {
    TKZFPanDirectionUnknown,
    TKZFPanDirectionV,
    TKZFPanDirectionH,
};

typedef NS_ENUM(NSUInteger, TKZFPanLocation) {
    TKZFPanLocationUnknown,
    TKZFPanLocationLeft,
    TKZFPanLocationRight,
};

typedef NS_ENUM(NSUInteger, TKZFPanMovingDirection) {
    TKZFPanMovingDirectionUnkown,
    TKZFPanMovingDirectionTop,
    TKZFPanMovingDirectionLeft,
    TKZFPanMovingDirectionBottom,
    TKZFPanMovingDirectionRight,
};

/// This enumeration lists some of the gesture types that the player has by default.
typedef NS_OPTIONS(NSUInteger, TKZFPlayerDisableGestureTypes) {
    TKZFPlayerDisableGestureTypesNone         = 0,
    TKZFPlayerDisableGestureTypesSingleTap    = 1 << 0,
    TKZFPlayerDisableGestureTypesDoubleTap    = 1 << 1,
    TKZFPlayerDisableGestureTypesPan          = 1 << 2,
    TKZFPlayerDisableGestureTypesPinch        = 1 << 3,
    TKZFPlayerDisableGestureTypesLongPress    = 1 << 4,
    TKZFPlayerDisableGestureTypesAll          = (TKZFPlayerDisableGestureTypesSingleTap | TKZFPlayerDisableGestureTypesDoubleTap | TKZFPlayerDisableGestureTypesPan | TKZFPlayerDisableGestureTypesPinch | TKZFPlayerDisableGestureTypesLongPress)
};

/// This enumeration lists some of the pan gesture moving direction that the player not support.
typedef NS_OPTIONS(NSUInteger, TKZFPlayerDisablePanMovingDirection) {
    TKZFPlayerDisablePanMovingDirectionNone         = 0,       /// Not disable pan moving direction.
    TKZFPlayerDisablePanMovingDirectionVertical     = 1 << 0,  /// Disable pan moving vertical direction.
    TKZFPlayerDisablePanMovingDirectionHorizontal   = 1 << 1,  /// Disable pan moving horizontal direction.
    TKZFPlayerDisablePanMovingDirectionAll          = (TKZFPlayerDisablePanMovingDirectionVertical | TKZFPlayerDisablePanMovingDirectionHorizontal)  /// Disable pan moving all direction.
};

/// Long press gesture state
typedef NS_ENUM(NSUInteger, TKZFLongPressGestureRecognizerState) {
    TKZFLongPressGestureRecognizerStateBegan,
    TKZFLongPressGestureRecognizerStateChanged,
    TKZFLongPressGestureRecognizerStateEnded
};

@interface TKZFPlayerGestureControl : NSObject

/// Gesture condition callback.
@property (nonatomic, copy, nullable) BOOL(^triggerCondition)(TKZFPlayerGestureControl *control, TKZFPlayerGestureType type, UIGestureRecognizer *gesture, UITouch *touch);

/// Single tap gesture callback.
@property (nonatomic, copy, nullable) void(^singleTapped)(TKZFPlayerGestureControl *control);

/// Double tap gesture callback.
@property (nonatomic, copy, nullable) void(^doubleTapped)(TKZFPlayerGestureControl *control);

/// Begin pan gesture callback.
@property (nonatomic, copy, nullable) void(^beganPan)(TKZFPlayerGestureControl *control, TKZFPanDirection direction, TKZFPanLocation location);

/// Pan gesture changing callback.
@property (nonatomic, copy, nullable) void(^changedPan)(TKZFPlayerGestureControl *control, TKZFPanDirection direction, TKZFPanLocation location, CGPoint velocity);

/// End the Pan gesture callback.
@property (nonatomic, copy, nullable) void(^endedPan)(TKZFPlayerGestureControl *control, TKZFPanDirection direction, TKZFPanLocation location);

/// Pinch gesture callback.
@property (nonatomic, copy, nullable) void(^pinched)(TKZFPlayerGestureControl *control, float scale);

/// longpress tap gesture callback.
@property (nonatomic, copy, nullable) void(^longPressed)(TKZFPlayerGestureControl *control, TKZFLongPressGestureRecognizerState state);

/// The single tap gesture.
@property (nonatomic, strong, readonly) UITapGestureRecognizer *singleTap;

/// The double tap gesture.
@property (nonatomic, strong, readonly) UITapGestureRecognizer *doubleTap;

/// The pan tap gesture.
@property (nonatomic, strong, readonly) UIPanGestureRecognizer *panGR;

/// The pinch gesture.
@property (nonatomic, strong, readonly) UIPinchGestureRecognizer *pinchGR;

/// The long press gesture.
@property (nonatomic, strong, readonly) UILongPressGestureRecognizer *longPressGR;

/// The pan gesture direction.
@property (nonatomic, readonly) TKZFPanDirection panDirection;

/// The pan location.
@property (nonatomic, readonly) TKZFPanLocation panLocation;

/// The moving drection.
@property (nonatomic, readonly) TKZFPanMovingDirection panMovingDirection;

/// The gesture types that the player not support.
@property (nonatomic) TKZFPlayerDisableGestureTypes disableTypes;

/// The pan gesture moving direction that the player not support.
@property (nonatomic) TKZFPlayerDisablePanMovingDirection disablePanMovingDirection;

/**
 Add  all gestures(singleTap、doubleTap、panGR、pinchGR) to the view.
 */
- (void)addGestureToView:(UIView *)view;

/**
 Remove all gestures(singleTap、doubleTap、panGR、pinchGR) form the view.
 */
- (void)removeGestureToView:(UIView *)view;

@end

NS_ASSUME_NONNULL_END
