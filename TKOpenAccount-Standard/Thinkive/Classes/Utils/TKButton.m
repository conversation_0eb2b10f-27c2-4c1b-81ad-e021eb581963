//
//  TKButton.m
//  TKApp
//
//  Created by 叶璐 on 15/7/7.
//  Copyright (c) 2015年 liubao. All rights reserved.
//

#import "TKButton.h"

@implementation TKButton

- (instancetype)init
{
    self = [super init];
    if (self) {
        // 设置共性属性
        [self setCommonProperty];
    }
    return self;
}

- (void)setCommonProperty
{
    self.layer.masksToBounds = YES;
    self.layer.cornerRadius = 40;
    self.backgroundColor = [UIColor colorWithRed:51/255.0 green:72/255.0 blue:142/255.0 alpha:1];
    self.titleLabel.font=[UIFont systemFontOfSize:18.0];
    [self setContentHorizontalAlignment:UIControlContentHorizontalAlignmentCenter];
    [self setContentVerticalAlignment:UIControlContentVerticalAlignmentCenter];
}

- (void)setBackgroundImage:(NSString *)normalImageName andSeletedImage:(NSString *)seletedImage
{
    UIImage *image = [UIImage imageNamed:normalImageName];
    [self setBackgroundImage:image forState:UIControlStateNormal];
    
    image = [UIImage imageNamed:seletedImage];
    [self setBackgroundImage:image forState:UIControlStateHighlighted];
    [self setBackgroundImage:image forState:UIControlStateSelected];
    self.frame = CGRectMake(self.frame.size.width/2-40, 5, 80, 80);
}

@end
