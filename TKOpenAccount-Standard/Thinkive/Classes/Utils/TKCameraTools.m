//
//  TKCameraTools.m
//  TKApp
//
//  Created by 叶璐 on 15/7/7.
//  Copyright (c) 2015年 liubao. All rights reserved.
//

#import "TKCameraTools.h"
#import <ImageIO/CGImageProperties.h>


static CGFloat DegreesToRadians(CGFloat degrees) {return degrees * M_PI / 180;};

@implementation TKCameraTools
{
    AVCaptureVideoPreviewLayer *_previewlayer;
    AVCaptureDevice *_videoDevice;  // 视频输入
    AVCaptureStillImageOutput *_stillCameraOutput;
    CIDetector *_faceDetector;  // 人脸识别类
    UIView *_previewView;
    UIImage *_rectangle;  // 人脸标识的方框
    AVCaptureSession *_session;
    AVCaptureVideoDataOutput *_videoDataoutput;
    AVCaptureAudioDataOutput *_audioOutput;
    UIImage *_faceImage;  // 人脸照片
    TKCameraToolsImageBlock _imageCallBackBlock;
    NSTimeInterval _faceDetectInterval;   // 人脸检测间隔时间
    
    AVAssetWriter *_videoWriter;
    AVAssetWriterInput *_audioWriterInput;
    AVAssetWriterInput *_videoWriterInput;
    BOOL _strtRecording, isStartDetectFace, isFace;   // 开始录像
    
    NSTimer *dfTimer;
}

-(void)setupCameraInView:(UIView *)previewView andFrame:(CGRect)frame
{
    if (IOS7_OR_LATER) {
        AVAuthorizationStatus status =  [AVCaptureDevice authorizationStatusForMediaType:AVMediaTypeVideo];
        switch (status) {
            case AVAuthorizationStatusNotDetermined:
            {
                // 许可对话没有出现，发起授权许可
                [AVCaptureDevice requestAccessForMediaType:AVMediaTypeVideo completionHandler:^(BOOL granted) {
                    if (granted) {
                        // go on
                    }
                    else
                    {
                        //用户拒绝
                        TKLogInfo(@"need to do --------用户拒绝");
                    }
                }];
                break;
            }
            case AVAuthorizationStatusAuthorized:
            {
                // 继续
                break;
            }
            case AVAuthorizationStatusRestricted:
            {
                // 用户拒绝授权，或者无法访问相机
                TKLogInfo(@"need to do --------用户拒绝授权，或者无法访问相机");
                break;
            }
            default:
                break;
        }
    }
    
    _previewView = previewView;
    
    _session = [self setupCaptureSession];
    _previewlayer = [AVCaptureVideoPreviewLayer layerWithSession: _session];
    _previewlayer.videoGravity = AVLayerVideoGravityResizeAspectFill;
    _previewlayer.backgroundColor = [UIColor blackColor].CGColor;
    _previewlayer.frame = frame;
    _previewlayer.connection.videoOrientation = AVCaptureVideoOrientationPortrait;
    [_previewView.layer addSublayer:_previewlayer];
}

- (void)teardownCamera
{
    [_previewlayer removeFromSuperlayer];
    _previewlayer = nil;
}

#pragma mark - 初始化视频相关
- (AVCaptureSession *)setupCaptureSession
{
    NSError *error = nil;
    
    // AVCaptureSession 管理输入与输出之间的数据流，以及在出现问题时生成运行时错误。
    AVCaptureSession *session = [[AVCaptureSession alloc] init];
    // 自动调整摄像机配置分辨率
    if ([[UIDevice currentDevice] userInterfaceIdiom] == UIUserInterfaceIdiomPhone){
        [session setSessionPreset:AVCaptureSessionPreset352x288];
    } else {
        [session setSessionPreset:AVCaptureSessionPresetPhoto];
    }
    
    //  添加视频输入
    _videoDevice = [self getCamera:AVCaptureDevicePositionFront];
    AVCaptureDeviceInput *videoInput = [AVCaptureDeviceInput deviceInputWithDevice:_videoDevice error:&error];
    if (error)
    {
        TKLogInfo(@"Failed to get video input");
        _videoDevice = nil;
        return nil;
    }
    if ([session canAddInput:videoInput]) {
        [session addInput:videoInput];
    }
    
    // 添加音频输入
    AVCaptureDevice *audioDevice = [[AVCaptureDevice devicesWithMediaType:AVMediaTypeAudio] firstObject];
    AVCaptureDeviceInput *audioInput = [AVCaptureDeviceInput deviceInputWithDevice:audioDevice error:&error];
    if (error)
    {
        TKLogInfo(@"Failed to get video input");
        _videoDevice = nil;
        return nil;
    }
    if ([session canAddInput:audioInput]) {
        [session addInput:audioInput];
    }
    
    // 捕捉静态图片
    _stillCameraOutput = [[AVCaptureStillImageOutput alloc]init];
    NSDictionary *myOutputSettings = [[NSDictionary alloc] initWithObjectsAndKeys:AVVideoCodecJPEG,AVVideoCodecKey,nil];
    [_stillCameraOutput setOutputSettings:myOutputSettings];
    if ([session canAddOutput:_stillCameraOutput]) {
        [session addOutput:_stillCameraOutput];
    }

    //AVCaptureOutput 是一个抽象类，描述 capture session 的结果,获取数据流
    _videoDataoutput = [[AVCaptureVideoDataOutput alloc] init];
    NSDictionary *rgbOutputSettings = [NSDictionary dictionaryWithObject:
            [NSNumber numberWithInt:kCVPixelFormatType_32BGRA] forKey:(id)kCVPixelBufferPixelFormatTypeKey];

    [_videoDataoutput setVideoSettings:rgbOutputSettings];
    [_videoDataoutput setAlwaysDiscardsLateVideoFrames:YES];
    dispatch_queue_t videoDataOutputQueue = dispatch_queue_create("VideoDataOutputQueue", DISPATCH_QUEUE_SERIAL);
    [_videoDataoutput setSampleBufferDelegate:self queue:videoDataOutputQueue];
    if ([session canAddOutput:_videoDataoutput])
    {
        [session addOutput:_videoDataoutput];
    }

    [[_videoDataoutput connectionWithMediaType:AVMediaTypeVideo] setEnabled:YES];

    // 添加音频输出
    _audioOutput = [[AVCaptureAudioDataOutput alloc] init];
    [_audioOutput setSampleBufferDelegate:self queue:videoDataOutputQueue];
    if ([session canAddOutput:_audioOutput]) {
        [session addOutput:_audioOutput];
    }
    
    // 启动相机
    dispatch_async(dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_BACKGROUND, 0), ^{
        [session startRunning];
    });
    
    // 等待摄像头启动
    sleep(1);
    
    return session;
}

- (void)initVideoAudioWriter:(NSString *)path
{
    CGSize size = _previewView.frame.size;
    
    NSError *error = nil;
    
    if (!_videoWriter) {
        _videoWriter = [[AVAssetWriter alloc] initWithURL:[NSURL fileURLWithPath:path] fileType:AVFileTypeQuickTimeMovie error:&error];
        NSParameterAssert(_videoWriter);
    }
    
    if(error)
    {
        TKLogInfo(@"error = %@", [error localizedDescription]);
    }
    
    // 添加视频输入
    NSDictionary *videoCompressionProps = [NSDictionary dictionaryWithObjectsAndKeys:[NSNumber numberWithDouble:128.0*1024.0],AVVideoAverageBitRateKey,  nil ];
    
    NSDictionary *videoSettings = [NSDictionary dictionaryWithObjectsAndKeys:AVVideoCodecH264, AVVideoCodecKey,[NSNumber numberWithInt:size.height], AVVideoWidthKey,[NSNumber numberWithInt:size.width],AVVideoHeightKey,videoCompressionProps, AVVideoCompressionPropertiesKey, nil];
    
    _videoWriterInput = [AVAssetWriterInput assetWriterInputWithMediaType:AVMediaTypeVideo outputSettings:videoSettings];
    
    _videoWriterInput.expectsMediaDataInRealTime = YES;// 是否裁剪视频输入
    
    NSParameterAssert(_videoWriterInput);
    
    // 反转镜像
    BOOL isMirrored = NO;
    
    if ([_previewlayer respondsToSelector:@selector(connection)])
    {
        isMirrored = _previewlayer.connection.isVideoMirrored;
    }
    _videoWriterInput.transform
    = [self transformFromVideoBufferOrientationToOrientation:
       AVCaptureVideoOrientationPortrait withAutoMirroring:isMirrored];
    
    
    // 添加音频输入
    AudioChannelLayout acl;
    
    bzero( &acl, sizeof(acl));
    
    acl.mChannelLayoutTag = kAudioChannelLayoutTag_Mono;
    
    NSDictionary* audioOutputSettings = nil;
    
    audioOutputSettings = [ NSDictionary dictionaryWithObjectsAndKeys:
                           [ NSNumber numberWithInt: kAudioFormatMPEG4AAC ], AVFormatIDKey,
                           [ NSNumber numberWithInt:64000], AVEncoderBitRateKey,
                           [ NSNumber numberWithFloat: 44100.0 ], AVSampleRateKey,
                           [ NSNumber numberWithInt: 1 ], AVNumberOfChannelsKey,
                           [ NSData dataWithBytes: &acl length: sizeof( acl ) ], AVChannelLayoutKey,
                           nil ];
    
    _audioWriterInput = [AVAssetWriterInput assetWriterInputWithMediaType: AVMediaTypeAudio
                                                           outputSettings: audioOutputSettings];
    _audioWriterInput.expectsMediaDataInRealTime = YES;
    
    // add input
    if ([_videoWriter canAddInput:_videoWriterInput])
    {
        [_videoWriter addInput:_videoWriterInput];
        TKLogInfo(@"I can add videoWriterInput");
    }
    else
    {
        TKLogInfo(@"i can't add this input");
    }
    
    if ([_videoWriter canAddInput:_audioWriterInput])
    {
        [_videoWriter addInput:_audioWriterInput];
        TKLogInfo(@"I can add audioWriterInput");
    }
    else
    {
        TKLogInfo(@"i can't add this input");
    }
}

- (void)closeVideoWriter
{
    TKLogInfo(@"+++++++停止writer");
    
    @try{
        if(_videoWriter.status == AVAssetWriterStatusWriting)
        {
            [_videoWriterInput markAsFinished];
            [_videoWriter finishWritingWithCompletionHandler:^{
                TKLogInfo(@"finishWritingWithCompletionHandler++++++");
                _videoWriter = nil;
            }];
        }
        else
        {
            TKLogInfo(@"--------_videoWriter.status:%d", _videoWriter.status);
        }
       
    }
    @catch(NSException *exception) {
        TKLogInfo(@"exception:%@", exception);
    }
    @finally {
        
    }
  
}

// Auto mirroring: Front camera is mirrored; back camera isn't
- (CGAffineTransform)transformFromVideoBufferOrientationToOrientation:(AVCaptureVideoOrientation)orientation withAutoMirroring:(BOOL)mirror
{
    CGAffineTransform transform = CGAffineTransformIdentity;
    
    transform = CGAffineTransformMakeRotation(M_PI*3/2);
    
    if (_videoDevice.position == AVCaptureDevicePositionFront )
    {
        if ( mirror ) {
            transform = CGAffineTransformScale(transform, -1, 1);
        }
        else {
            if ( orientation ==AVCaptureVideoOrientationPortrait ) {
                transform = CGAffineTransformRotate( transform, M_PI );
            }
        }
    }
    
//    transform = CGAffineTransformScale(transform, 1, 1);
    return transform;
}

// 缓冲转照片
- (UIImage *)imageFromSampleBuffer:(CMSampleBufferRef)sampleBuffer
{
    // Get a CMSampleBuffer's Core Video image buffer for the media data
    CVImageBufferRef imageBuffer = CMSampleBufferGetImageBuffer(sampleBuffer);
    // Lock the base address of the pixel buffer
    CVPixelBufferLockBaseAddress(imageBuffer, 0);
    
    // Get the number of bytes per row for the pixel buffer
    void *baseAddress = CVPixelBufferGetBaseAddress(imageBuffer);
    
    // Get the number of bytes per row for the pixel buffer
    size_t bytesPerRow = CVPixelBufferGetBytesPerRow(imageBuffer);
    // Get the pixel buffer width and height
    size_t width = CVPixelBufferGetWidth(imageBuffer);
    size_t height = CVPixelBufferGetHeight(imageBuffer);
    
    // Create a device-dependent RGB color space
    CGColorSpaceRef colorSpace = CGColorSpaceCreateDeviceRGB();
    
    // Create a bitmap graphics context with the sample buffer data
    CGContextRef context = CGBitmapContextCreate(baseAddress, width, height, 8,
                                                 bytesPerRow, colorSpace, kCGBitmapByteOrder32Little | kCGImageAlphaPremultipliedFirst);
    // Create a Quartz image from the pixel data in the bitmap graphics context
    CGImageRef quartzImage = CGBitmapContextCreateImage(context);
    // Unlock the pixel buffer
    CVPixelBufferUnlockBaseAddress(imageBuffer,0);
    
    // Free up the context and color space
    CGContextRelease(context);
    CGColorSpaceRelease(colorSpace);
    
    // Create an image object from the Quartz image
    //UIImage *image = [UIImage imageWithCGImage:quartzImage];
    UIImage *image = [UIImage imageWithCGImage:quartzImage scale:1.0f orientation:UIImageOrientationLeftMirrored];
    
    // Release the Quartz image
    CGImageRelease(quartzImage);
    
    return image;
}

// 反转照片
- (UIImage *)fixOrientation:(UIImage*)image
{
    // No-op if the orientation is already correct
    if (image.imageOrientation == UIImageOrientationUp) return image;
    
    // We need to calculate the proper transformation to make the image upright.
    // We do it in 2 steps: Rotate if Left/Right/Down, and then flip if Mirrored.
    CGAffineTransform transform = CGAffineTransformIdentity;
    
    switch (image.imageOrientation)
    {
        case UIImageOrientationDown:
        case UIImageOrientationDownMirrored:
            transform = CGAffineTransformTranslate(transform, image.size.width, image.size.height);
            transform = CGAffineTransformRotate(transform, M_PI);
            break;
            
        case UIImageOrientationLeft:
        case UIImageOrientationLeftMirrored:
            transform = CGAffineTransformTranslate(transform, image.size.width, 0);
            transform = CGAffineTransformRotate(transform, M_PI_2);
            break;
            
        case UIImageOrientationRight:
        case UIImageOrientationRightMirrored:
            transform = CGAffineTransformTranslate(transform, 0, image.size.height);
            transform = CGAffineTransformRotate(transform, -M_PI_2);
            break;
        default:
            break;
    }
    
    switch (image.imageOrientation)
    {
        case UIImageOrientationUpMirrored:
        case UIImageOrientationDownMirrored:
            transform = CGAffineTransformTranslate(transform, image.size.width, 0);
            transform = CGAffineTransformScale(transform, -1, 1);
            break;
            
        case UIImageOrientationLeftMirrored:
        case UIImageOrientationRightMirrored:
            transform = CGAffineTransformTranslate(transform, image.size.height, 0);
            transform = CGAffineTransformScale(transform, -1, 1);
            break;
        default:
            break;
    }
    
    
    CGImageRef imgRef = image.CGImage;
    
    // Now we draw the underlying CGImage into a new context, applying the transform
    // calculated above.
    CGContextRef ctx = CGBitmapContextCreate(NULL, image.size.width, image.size.height,
                                             CGImageGetBitsPerComponent(imgRef), 0,
                                             CGImageGetColorSpace(imgRef),
                                             CGImageGetBitmapInfo(imgRef));
    CGContextConcatCTM(ctx, transform);
    switch (image.imageOrientation)
    {
        case UIImageOrientationLeft:
        case UIImageOrientationLeftMirrored:
        case UIImageOrientationRight:
        case UIImageOrientationRightMirrored:
            // Grr...
            CGContextDrawImage(ctx, CGRectMake(0,0,image.size.height,image.size.width), imgRef);
            break;
            
        default:
            CGContextDrawImage(ctx, CGRectMake(0,0,image.size.width,image.size.height), imgRef);
            break;
    }
    
    // And now we just create a new UIImage from the drawing context
    CGImageRef cgimg = CGBitmapContextCreateImage(ctx);
    
    UIImage * img = [[UIImage alloc] initWithCGImage:cgimg];
    
    CGContextRelease(ctx);
    CGImageRelease(cgimg);
    return img;
}

#pragma mark - implement AVCaptureVideoDataOutputSampleBufferDelegate
- (void)captureOutput:(AVCaptureOutput *)captureOutput didOutputSampleBuffer:(CMSampleBufferRef)sampleBuffer fromConnection:(AVCaptureConnection *)connection
{
    CMTime lastSampleTime = CMSampleBufferGetPresentationTimeStamp(sampleBuffer);
    
    if ([captureOutput isKindOfClass:[AVCaptureVideoDataOutput class]] &&  isStartDetectFace) {//进行人脸检测
        
        static NSDate * lasetdate;
        
        NSDate* date = [[NSDate alloc]initWithTimeIntervalSinceNow:lastSampleTime.timescale ];
        
        NSTimeInterval timeNow = [date timeIntervalSinceNow];
        
        NSTimeInterval timeOffset = timeNow - [lasetdate timeIntervalSinceNow];
        
        if ( timeOffset > _faceDetectInterval) {
            
            lasetdate = date;
            
            if ([connection isActive]) {
                
                [self DoFaceDetect:sampleBuffer];
            }
        }

    }

    if (_strtRecording)
    {
        if(_videoWriter.status == AVAssetWriterStatusUnknown)
        {
            [_videoWriter startWriting];
            [_videoWriter startSessionAtSourceTime:lastSampleTime];
        }
        
        if(_videoWriter.status > AVAssetWriterStatusWriting)
        {
            if (_videoWriter.status == AVAssetWriterStatusCompleted)
            {
                TKLogInfo(@"write videoData finish");
            }else if(_videoWriter.status == AVAssetWriterStatusFailed )
            {
                TKLogInfo(@"write videoData Error: %@", _videoWriter.error);
            }
            
            [_videoWriter endSessionAtSourceTime:lastSampleTime];
            return;
        }
    
        
        // 写入视频
        if (captureOutput == _videoDataoutput)
        {
            if ([_videoWriterInput isReadyForMoreMediaData])
            {
                if(![_videoWriterInput appendSampleBuffer:sampleBuffer])
                {
                    TKLogInfo(@"Unable to write to video input");
                }

            }
        }
        // 写入音频
        else if (captureOutput == _audioOutput)
        {
            if ([_audioWriterInput isReadyForMoreMediaData])
            {
                if( ![_audioWriterInput appendSampleBuffer:sampleBuffer] )
                {
                    TKLogInfo(@"Unable to write to audio input");
                }

            }
        }
    }

}

- (void)DoFaceDetect:(CMSampleBufferRef)sampleBuffer
{
    if (_faceDetector) {
        
        CVPixelBufferRef pixelBuffer = CMSampleBufferGetImageBuffer(sampleBuffer);
        
        CFDictionaryRef attachments = CMCopyDictionaryOfAttachments(kCFAllocatorDefault, sampleBuffer, kCMAttachmentMode_ShouldPropagate);
        
        CIImage *ciImage = [[CIImage alloc] initWithCVPixelBuffer:pixelBuffer
                                                          options:(__bridge NSDictionary *)attachments];
        if (attachments) {
            CFRelease(attachments);
        }
        
        // make sure your device orientation is not locked.
        UIDeviceOrientation curDeviceOrientation = [[UIDevice currentDevice] orientation];
        
        NSDictionary *imageOptions = [NSDictionary dictionaryWithObject:[self exifOrientation:curDeviceOrientation] forKey:CIDetectorImageOrientation];
        
        NSArray *features = [_faceDetector featuresInImage:ciImage options:imageOptions];
        
        //            if ([_delegate respondsToSelector:@selector(currentFaceNumber:)]) {
        //                [_delegate currentFaceNumber:features.count];
        //            }
        
        //            // 绘制人脸位置
        //            CMFormatDescriptionRef fdesc = CMSampleBufferGetFormatDescription(sampleBuffer);
        //            CGRect cleanAperture = CMVideoFormatDescriptionGetCleanAperture(fdesc, false /*originIsTopLeft == false*/);
        //
        //            dispatch_async(dispatch_get_main_queue(), ^(void) {
        //                [self drawFaces:features
        //                    forVideoBox:cleanAperture
        //                    orientation:curDeviceOrientation];
        //            });
        
        TKLogInfo(@"%ld", features.count);
        
        if (features.count == 1) {
            
            _faceImage = [self imageFromSampleBuffer:sampleBuffer];
            
            _faceImage = [self fixOrientation:_faceImage];
            
            isFace = YES;
        
        }

    }
}

#pragma mark - 画出人脸位置
- (void)drawFaces:(NSArray *)features
      forVideoBox:(CGRect)clearAperture
      orientation:(UIDeviceOrientation)orientation
{
    NSArray *sublayers = [NSArray arrayWithArray:[_previewlayer sublayers]];
    NSInteger sublayersCount = [sublayers count], currentSublayer = 0;
    NSInteger featuresCount = [features count], currentFeature = 0;
    
    [CATransaction begin];
    [CATransaction setValue:(id)kCFBooleanTrue forKey:kCATransactionDisableActions];
    
    // hide all the face layers
    for ( CALayer *layer in sublayers ) {
        if ( [[layer name] isEqualToString:@"FaceLayer"] )
            [layer setHidden:YES];
    }
    
    if ( featuresCount == 0 ) {
        [CATransaction commit];
        return; // early bail.
    }
    
    CGSize parentFrameSize = [_previewView frame].size;
    NSString *gravity = [_previewlayer videoGravity];
    BOOL isMirrored = NO;
    if ([_previewlayer respondsToSelector:@selector(connection)])
    {
        isMirrored = _previewlayer.connection.isVideoMirrored;
    }
    
    CGRect previewBox = [self videoPreviewBoxForGravity:gravity
                                              frameSize:parentFrameSize
                                           apertureSize:clearAperture.size];
    
    for ( CIFaceFeature *ff in features ) {
        // find the correct position for the square layer within the previewLayer
        // the feature box originates in the bottom left of the video frame.
        // (Bottom right if mirroring is turned on)
        CGRect faceRect = [ff bounds];
        
        // flip preview width and height
        CGFloat temp = faceRect.size.width;
        faceRect.size.width = faceRect.size.height;
        faceRect.size.height = temp;
        temp = faceRect.origin.x;
        faceRect.origin.x = faceRect.origin.y;
        faceRect.origin.y = temp;
        // scale coordinates so they fit in the preview box, which may be scaled
        CGFloat widthScaleBy = previewBox.size.width / clearAperture.size.height;
        CGFloat heightScaleBy = previewBox.size.height / clearAperture.size.width;
        faceRect.size.width *= widthScaleBy;
        faceRect.size.height *= heightScaleBy;
        faceRect.origin.x *= widthScaleBy;
        faceRect.origin.y *= heightScaleBy;
        
        if ( isMirrored )
            faceRect = CGRectOffset(faceRect, previewBox.origin.x + previewBox.size.width - faceRect.size.width - (faceRect.origin.x * 2), previewBox.origin.y);
        else
            faceRect = CGRectOffset(faceRect, previewBox.origin.x, previewBox.origin.y);
        
        CALayer *featureLayer = nil;
        
        // re-use an existing layer if possible
        while ( !featureLayer && (currentSublayer < sublayersCount) ) {
            CALayer *currentLayer = [sublayers objectAtIndex:currentSublayer++];
            if ( [[currentLayer name] isEqualToString:@"FaceLayer"] ) {
                featureLayer = currentLayer;
                [currentLayer setHidden:NO];
            }
        }
        
        // create a new one if necessary
        if ( !featureLayer ) {
            featureLayer = [[CALayer alloc]init];
            featureLayer.contents = (id)_rectangle.CGImage;
            [featureLayer setName:@"FaceLayer"];
            [_previewlayer addSublayer:featureLayer];
            featureLayer = nil;
        }
        [featureLayer setFrame:faceRect];
        
        switch (orientation) {
            case UIDeviceOrientationPortrait:
                [featureLayer setAffineTransform:CGAffineTransformMakeRotation(DegreesToRadians(0.0))];
                break;
            case UIDeviceOrientationPortraitUpsideDown:
                [featureLayer setAffineTransform:CGAffineTransformMakeRotation(DegreesToRadians(180.))];
                break;
            case UIDeviceOrientationLandscapeLeft:
                [featureLayer setAffineTransform:CGAffineTransformMakeRotation(DegreesToRadians(90.))];
                break;
            case UIDeviceOrientationLandscapeRight:
                [featureLayer setAffineTransform:CGAffineTransformMakeRotation(DegreesToRadians(-90.))];
                break;
            case UIDeviceOrientationFaceUp:
            case UIDeviceOrientationFaceDown:
            default:
                break; // leave the layer in its last known orientation
        }
        currentFeature++;
    }
    
    [CATransaction commit];
}

- (CGRect)videoPreviewBoxForGravity:(NSString *)gravity
                          frameSize:(CGSize)frameSize
                       apertureSize:(CGSize)apertureSize
{
    CGFloat apertureRatio = apertureSize.height / apertureSize.width;
    CGFloat viewRatio = frameSize.width / frameSize.height;
    
    CGSize size = CGSizeZero;
    if ([gravity isEqualToString:AVLayerVideoGravityResizeAspectFill]) {
        if (viewRatio > apertureRatio) {
            size.width = frameSize.width;
            size.height = apertureSize.width * (frameSize.width / apertureSize.height);
        } else {
            size.width = apertureSize.height * (frameSize.height / apertureSize.width);
            size.height = frameSize.height;
        }
    } else if ([gravity isEqualToString:AVLayerVideoGravityResizeAspect]) {
        if (viewRatio > apertureRatio) {
            size.width = apertureSize.height * (frameSize.height / apertureSize.width);
            size.height = frameSize.height;
        } else {
            size.width = frameSize.width;
            size.height = apertureSize.width * (frameSize.width / apertureSize.height);
        }
    } else if ([gravity isEqualToString:AVLayerVideoGravityResize]) {
        size.width = frameSize.width;
        size.height = frameSize.height;
    }
    
    CGRect videoBox;
    videoBox.size = size;
    if (size.width < frameSize.width)
        videoBox.origin.x = (frameSize.width - size.width) / 2;
    else
        videoBox.origin.x = (size.width - frameSize.width) / 2;
    
    if ( size.height < frameSize.height )
        videoBox.origin.y = (frameSize.height - size.height) / 2;
    else
        videoBox.origin.y = (size.height - frameSize.height) / 2;
    
    return videoBox;
}

// 调整图片方向
- (NSNumber *) exifOrientation: (UIDeviceOrientation) orientation
{
    int exifOrientation;
    
    enum {
        PHOTOS_EXIF_0ROW_TOP_0COL_LEFT			= 1, //   1  =  0th row is at the top, and 0th column is on the left (THE DEFAULT).
        PHOTOS_EXIF_0ROW_TOP_0COL_RIGHT			= 2, //   2  =  0th row is at the top, and 0th column is on the right.
        PHOTOS_EXIF_0ROW_BOTTOM_0COL_RIGHT      = 3, //   3  =  0th row is at the bottom, and 0th column is on the right.
        PHOTOS_EXIF_0ROW_BOTTOM_0COL_LEFT       = 4, //   4  =  0th row is at the bottom, and 0th column is on the left.
        PHOTOS_EXIF_0ROW_LEFT_0COL_TOP          = 5, //   5  =  0th row is on the left, and 0th column is the top.
        PHOTOS_EXIF_0ROW_RIGHT_0COL_TOP         = 6, //   6  =  0th row is on the right, and 0th column is the top.
        PHOTOS_EXIF_0ROW_RIGHT_0COL_BOTTOM      = 7, //   7  =  0th row is on the right, and 0th column is the bottom.
        PHOTOS_EXIF_0ROW_LEFT_0COL_BOTTOM       = 8  //   8  =  0th row is on the left, and 0th column is the bottom.
    };
    
    switch (orientation) {
        case UIDeviceOrientationPortraitUpsideDown:  // Device oriented vertically, home button on the top
            exifOrientation = PHOTOS_EXIF_0ROW_LEFT_0COL_BOTTOM;
            break;
        case UIDeviceOrientationLandscapeLeft:       // Device oriented horizontally, home button on the right
            if (_videoDevice.position == AVCaptureDevicePositionFront)
                exifOrientation = PHOTOS_EXIF_0ROW_BOTTOM_0COL_RIGHT;
            else
                exifOrientation = PHOTOS_EXIF_0ROW_TOP_0COL_LEFT;
            break;
        case UIDeviceOrientationLandscapeRight:      // Device oriented horizontally, home button on the left
            if (_videoDevice.position == AVCaptureDevicePositionFront) {
                exifOrientation = PHOTOS_EXIF_0ROW_TOP_0COL_LEFT;
            }
            else
                exifOrientation = PHOTOS_EXIF_0ROW_BOTTOM_0COL_RIGHT;
            break;
        case UIDeviceOrientationPortrait:            // Device oriented vertically, home button on the bottom
        default:
            exifOrientation = PHOTOS_EXIF_0ROW_RIGHT_0COL_TOP;
            break;
    }
    return [NSNumber numberWithInt:exifOrientation];
}

-(void)startFaceDetectInDuration:(NSTimeInterval)interval
{
    if (interval <= 0) {
        
        _faceDetectInterval = 0.2;
    }
    else
    {
        _faceDetectInterval = interval;
    }
    
    _rectangle = [UIImage imageNamed:[NSString stringWithFormat:@"%@.bundle/TKOpenPlugin60006/rectangle.png",TK_OPEN_RESOURCE_NAME]];
    
    CIContext *cContext = [CIContext contextWithOptions:nil];
    
    isStartDetectFace = YES;
    
    dfTimer = [NSTimer scheduledTimerWithTimeInterval:1.5f target:self selector:@selector(handleDFTimer:) userInfo:nil repeats:YES];
    
    NSDictionary *detectorOptions = [[NSDictionary alloc] initWithObjectsAndKeys:CIDetectorAccuracyHigh, CIDetectorAccuracy, nil];
    
    _faceDetector = [CIDetector detectorOfType:CIDetectorTypeFace context:cContext options:detectorOptions];
}

-(void)stopFaceDetect
{
    if (_faceDetector) {
        _faceDetector = nil;
    }
}

- (void)handleDFTimer:(NSTimer*)timer
{
    if (isFace) {
        
        isFace = NO;
        
    }else{//未找到人脸
        
        if ([_delegate respondsToSelector:@selector(currentFaceNumber:)]) {
            [_delegate currentFaceNumber:0];
        }

    
    }

}

- (void)getFaceImage:(TKCameraToolsImageBlock)imageCallBack
{
    if (_faceImage) {
        _faceImage = nil;
    }
    
    if (imageCallBack) {
        _imageCallBackBlock = imageCallBack;
    }
    else
    {
        TKLogInfo(@"error! block==nil");
    }
}

- (void)takePhotos:(TKCameraToolsImageBlock)image
{
    AVCaptureConnection *myVideoConnection = nil;
    for (AVCaptureConnection *connection in _stillCameraOutput.connections) {
        for (AVCaptureInputPort *port in [connection inputPorts]) {
            if ([[port mediaType] isEqual:AVMediaTypeVideo]) {
                myVideoConnection = connection;
                break;
            }
        }
    }
    
    //撷取影像（包含拍照音效）
    [_stillCameraOutput captureStillImageAsynchronouslyFromConnection:myVideoConnection completionHandler:^(CMSampleBufferRef imageDataSampleBuffer, NSError *error) {
        
        //完成撷取时的处理程序(Block)
        if (imageDataSampleBuffer) {
            NSData *imageData = [AVCaptureStillImageOutput jpegStillImageNSDataRepresentation:imageDataSampleBuffer];
            
            //取得的静态影像
            UIImage *myImage = [[UIImage alloc] initWithData:imageData];
            image(myImage);
        }
    }];
}

- (void)setFocus
{
    NSError *error = nil;
    // 设置自动对焦  设置对焦模式必须锁定摄像头
    if (_videoDevice.isFocusPointOfInterestSupported && [_videoDevice isFocusModeSupported:AVCaptureFocusModeContinuousAutoFocus]) {
        if ([_videoDevice lockForConfiguration:&error]) {// 必须上锁
            _videoDevice.focusMode = AVCaptureFocusModeContinuousAutoFocus;
            [_videoDevice unlockForConfiguration];// 必须解锁
        }
        else
        {
            
        }
    }
}

//AVCaptureDevice 是关于相机硬件的接口。它被用于控制硬件特性，诸如镜头的位置、曝光、闪光灯等。
- (AVCaptureDevice *)getCamera:(AVCaptureDevicePosition) position
{
    //获取前置摄像头设备
    NSArray *cameras = [AVCaptureDevice devicesWithMediaType:AVMediaTypeVideo];
    for (AVCaptureDevice *device in cameras)
    {
        if (device.position == AVCaptureDevicePositionFront)
            return device;
    }
    
    return [AVCaptureDevice defaultDeviceWithMediaType:AVMediaTypeVideo];
}

- (void)startRecordInFilePath:(NSString *)path
{
    // 删除录像文件
    NSFileManager *fm = [[NSFileManager alloc] init];
    
    if ([fm fileExistsAtPath:path])
    {
//        // 停止录像
//        [self stopRecord];
        
        NSError *error;
        if ([fm removeItemAtPath:path error:&error]) {
            TKLogInfo(@"deleteRecord success");
            unlink([path UTF8String]);
        }
    }
    
    if ([TKStringHelper isEmpty:path]) {
        return;
    }
    
    if (!_session.isRunning) {
        dispatch_async(dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_BACKGROUND, 0), ^{
            [_session startRunning];
        });
        
        // 等待摄像头启动
        sleep(1);
    }
    
    [self startFaceDetectInDuration:0];
    
    [self initVideoAudioWriter:path];
    
    _strtRecording = YES;
}

- (void)reRecord
{

    [self closeVideoWriter];
    
    _strtRecording = NO;
}

- (void)stopRecord
{
    [self closeVideoWriter];
    
    _strtRecording = NO;
    
    isStartDetectFace = NO;
    
    isFace = NO;
    
    if (_session.isRunning) {
        dispatch_async(dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_BACKGROUND, 0), ^{
            [_session stopRunning];
        });
        
    }
    
    if (dfTimer) {
        
        [dfTimer invalidate];
        
        dfTimer = nil;
    }

}

/**
 *  <AUTHOR> 2015-04-24 00:12:37
 *
 *  获取前后置摄像头的连接（已经建立好的连接）
 *
 *  @param mediaType 前置/后置摄像头
 *
 *
 *  @return
 *       AVCaptureConnection：前置后置摄像头连接
 */
- (AVCaptureConnection *)connectionWithMediaType:(NSString *)mediaType fromConnections:(NSArray *)connections
{
    for ( AVCaptureConnection *connection in connections ) {
        for ( AVCaptureInputPort *port in [connection inputPorts] ) {
            if ( [[port mediaType] isEqual:mediaType] ) {
                return connection;
            }
        }
    }
    return nil;
}

- (UIImage*)fetchFacePhoto{
    
    return _faceImage;
}

- (void)dealloc
{
    if (_videoWriter) {
        _videoWriter = nil;
    }
}
@end
