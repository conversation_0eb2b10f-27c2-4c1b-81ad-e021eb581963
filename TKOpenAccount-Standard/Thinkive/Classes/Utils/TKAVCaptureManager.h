//
//  TKAVCaptureManager.h
//  TKOpenAccount-Standard
//
//  Created by <PERSON><PERSON> on 2017/7/7.
//  Copyright © 2017年 thinkive. All rights reserved.
//

#import <Foundation/Foundation.h>

@protocol TKRecordDelegate<NSObject>

- (void)isStartRecording:(float)rTime;

- (void)isFinishRecording:(float)rTime;

@end

typedef NS_ENUM(NSInteger, TK_VIDEO_BUSINESS_TYPE) {

    TK_VIDEO_PREVIEW = 0, //预览
    TK_VIDEO_TAKE_PHOTO = 1, //拍照
    TK_VIDEO_RECORD_VIDEO = 2 //录制

};

typedef void(^TKHandleResultBlock)(id hResult);

/**
 *
 * @class TKAVCaptureManager
 *
 * @brief 相机操作管理类（拍照、录制视频、预览）
 *
 */
@interface TKAVCaptureManager : NSObject

@property (nonatomic, assign) id <TKRecordDelegate> rDelegate;

- (instancetype)initWithPreviewView:(UIView *)previewView withCameraPosition:(AVCaptureDevicePosition)cPosition withCamraOrientation:(AVCaptureVideoOrientation)cOrientation handleBusinessType:(TK_VIDEO_BUSINESS_TYPE)bType;

/**
 *
 *@method takePicture：
 *
 *@brief 拍摄照片
 *
 *@param handerBlock（拍摄结果回调）
 */
- (void)takePicture:(TKHandleResultBlock)handerBlock;

/**
 *
 *@method reTakePicture
 *
 *@brief 重拍照片
 *
 */
- (void)reTakePicture;

/**
 *
 *@method cancelTakePicture
 *
 *@brief 取消拍摄
 *
 */
- (void)cancelTakePicture;

/**
 *
 *@method startRecording
 *
 *@brief 开始录制视频
 *
 */
- (void)startRecording;

/**
 *
 *@method stopRecording
 *
 *@brief 停止录制视频
 *
 */
- (void)stopRecording;

/**
 *
 *@method resetRecording
 *
 *@brief 重置录制
 *
 */
- (void)resetRecording;

- (void)setVideoFileName:(NSString *)vfName;

- (float)getRecordTime;

- (BOOL)isRecordingVideo;
@end
