//
//  TKSmartTwoVideoManager.m
//  TKOpenAccount-Standard
//
//  Created by 夏博文 on 2023/1/28.
//  Copyright © 2023 thinkive. All rights reserved.
//

#import "TKSmartTwoVideoManager.h"
#import "TKDirectVideoModel.h"
#import "TKOpenAccountService.h"
#import "TKChatService.h"
#import "TKChatServiceDelegate.h"
@interface TKSmartTwoVideoManager()<TKSmartTwoVideoManagerDelegate,TKChatServiceDelegate>
{

}

@property (nonatomic, strong) id<TKSmartTwoVideoManagerProtocol> smartTwoVideoManager;
@property (nonatomic, strong) TKOpenAccountService *mService;
@property(nonatomic, assign) BOOL  isLinePositionLog;//是否记录了第一次排队位置,默认no
@property (nonatomic, assign) int linePosition;//记录排队位置，被插队了也显示原来靠前的位置
@property (nonatomic, assign) BOOL isNeedTransfiniteNumber;//是否需要超限人数提示逻辑；需要的话也只提示一次
@property (nonatomic, assign) BOOL isNeed3Queue;//是否需要3.0排队
@property (nonatomic, strong) TKChatService *service;//3.0排队用的service
@end
@implementation TKSmartTwoVideoManager
@synthesize delegate=_delegate;
@synthesize requestParams=_requestParams;
@synthesize contentView=_contentView;
@synthesize remoteContentView=_remoteContentView;

#pragma mark - Init

/// 构造方法
/// @param configParam 初始化参数字典
- (instancetype)initWithConfig:(NSMutableDictionary *)requestParams {
    if (self = [self init]) {
        self.mService=[[TKOpenAccountService alloc] init];
        self.requestParams = requestParams;
        _linePosition = 0;
        self.isLinePositionLog=NO;
        if ([TKStringHelper isEmpty:self.requestParams[@"transfiniteNumber"]]) {
            self.isNeedTransfiniteNumber=NO;
        }else{
            self.isNeedTransfiniteNumber=YES;
        }
        [TKDirectVideoModel shareInstance].isCancelLineingUp=NO;
    }
    
    return  self;
}

/**
 *
 * @method startLineingUp
 *
 * @brief 开始4.0视频排队接口
 *
 */
- (void)startLineingUp{
    //不是3.0排队就去走4.0排队
    if ([self.requestParams[@"version"] isEqualToString:@"3.0"]) {
        [self startOldLineingUp];
    }else{
        [self startNewLineingUp];
    }
}

/**
 *
 * @method stopLineingUp
 *
 * @brief 结束4.0视频排队接口
 *
 */
- (void)stopLineingUp{
    //不是3.0停止排队就去走4.0停止排队
    if ([self.requestParams[@"version"] isEqualToString:@"3.0"]) {
        [self stopOldLineingUp];
    }else{
        [self stopNewLineingUp];
    }
}


//是否改变无坐席过程中小字提示语，message是排队bus返回的提示语
-(NSString *)queueStaffOfflineSubMsg:(NSString *)message{
    NSString *subMsgString=@"";

    //排队返回的message
    if([TKStringHelper isNotEmpty:message]){
        subMsgString=message;
    }
    
    //提示语h5入参调整位置提示语优先级最高
    //针对h5参数类型容错处理
    NSString *queueStaffOfflineSubMsg;
    if(self.requestParams[@"videoTipMsg"]&&[self.requestParams[@"videoTipMsg"] isKindOfClass:[NSDictionary class]]){
        queueStaffOfflineSubMsg=self.requestParams[@"videoTipMsg"][@"queueStaffOfflineSubMsg"];
    }else{
        queueStaffOfflineSubMsg=[TKDataHelper jsonToDictionary:self.requestParams[@"videoTipMsg"]][@"queueStaffOfflineSubMsg"];
    }
    
    if([TKStringHelper isNotEmpty:queueStaffOfflineSubMsg]){
        subMsgString=queueStaffOfflineSubMsg;
    }else{
        subMsgString=@"若长时间无法成功接通，您可以联系客服反馈";
    }
    return subMsgString;
}


//是否改变排队过程中小字提示语，message是排队bus返回的提示语
-(NSString *)queueLocationChangeString:(NSString *)message location:(int)location{
    NSString *queueLocationTip=@"";

    //排队返回的message
    if([TKStringHelper isNotEmpty:message]){
        queueLocationTip=message;
    }
    
    //提示语h5入参调整位置提示语优先级最高
    if([TKStringHelper isNotEmpty:self.requestParams[@"queueLocationMsg"]]){
        if ([self.requestParams[@"queueLocationMsg"] rangeOfString:@"{queue_location}"].location != NSNotFound) {
            queueLocationTip=[self.requestParams[@"queueLocationMsg"] stringByReplacingOccurrencesOfString:@"{queue_location}" withString:[NSString stringWithFormat:@"%d",location]];
        }
    }
    

    //提示语h5入参调整位置提示语优先级最高
    //针对h5参数类型容错处理
    NSString *queueLocationSubMsg;
    if(self.requestParams[@"videoTipMsg"]&&[self.requestParams[@"videoTipMsg"] isKindOfClass:[NSDictionary class]]){
        queueLocationSubMsg=self.requestParams[@"videoTipMsg"][@"queueLocationSubMsg"];
    }else{
        queueLocationSubMsg=[TKDataHelper jsonToDictionary:self.requestParams[@"videoTipMsg"]][@"queueLocationSubMsg"];
    }
    
    if([TKStringHelper isNotEmpty:queueLocationSubMsg]){
        if ([queueLocationSubMsg rangeOfString:@"{queue_location}"].location != NSNotFound) {
            queueLocationTip=[queueLocationSubMsg stringByReplacingOccurrencesOfString:@"{queue_location}" withString:[NSString stringWithFormat:@"%d",location]];
        }
    }
    return queueLocationTip;
}

//是否改变坐席确认接入过程中小字提示语，message是排队bus返回的提示语
-(NSString *)queueWaitAgreeSubMsg:(NSString *)message{
    NSString *subMsgString=@"";

    //排队返回的message
    if([TKStringHelper isNotEmpty:message]){
        subMsgString=message;
    }
    
    
    //提示语h5入参调整位置提示语优先级最高
    //针对h5参数类型容错处理
    NSString *queueWaitAgreeSubMsg;
    if(self.requestParams[@"videoTipMsg"]&&[self.requestParams[@"videoTipMsg"] isKindOfClass:[NSDictionary class]]){
        queueWaitAgreeSubMsg=self.requestParams[@"videoTipMsg"][@"queueWaitAgreeSubMsg"];
    }else{
        queueWaitAgreeSubMsg=[TKDataHelper jsonToDictionary:self.requestParams[@"videoTipMsg"]][@"queueWaitAgreeSubMsg"];
    }
    
    if([TKStringHelper isNotEmpty:queueWaitAgreeSubMsg]){
        subMsgString=queueWaitAgreeSubMsg;
    }
    return subMsgString;
}

//是否改变连接视频服务器过程中小字提示语，message是排队bus返回的提示语
-(NSString *)queueConnectingSubMsg:(NSString *)message{
    NSString *subMsgString=@"";

    //排队返回的message
    if([TKStringHelper isNotEmpty:message]){
        subMsgString=message;
    }
    
    //提示语h5入参调整位置提示语优先级最高
    //针对h5参数类型容错处理
    NSString *queueConnectingSubMsg;
    if(self.requestParams[@"videoTipMsg"]&&[self.requestParams[@"videoTipMsg"] isKindOfClass:[NSDictionary class]]){
        queueConnectingSubMsg=self.requestParams[@"videoTipMsg"][@"queueConnectingSubMsg"];
    }else{
        queueConnectingSubMsg=[TKDataHelper jsonToDictionary:self.requestParams[@"videoTipMsg"]][@"queueConnectingSubMsg"];
    }
    
    if([TKStringHelper isNotEmpty:queueConnectingSubMsg]){
        subMsgString=queueConnectingSubMsg;
    }
    return subMsgString;
}


#pragma mark 4.0排队相关接口
/**
 *
 * @method startNewLineingUp
 *
 * @brief 开始4.0视频排队接口
 *
 */
- (void)startNewLineingUp{
    
    if (self.requestParams && ![TKDirectVideoModel shareInstance].isCancelLineingUp) {
        
        self.requestParams[@"funcNo"]=@"501301";
        
        if (!self.requestParams[@"origin"]) {
            self.requestParams[@"origin"]=@"2";
        }
        
        if (!self.requestParams[@"level"]) {
            self.requestParams[@"level"]=@"0";
        }
        
        [self.mService handleNetworkWithURL:self.requestParams[@"url"] param:self.requestParams callBackFunc:^(ResultVo *resultVo) {
            
            if (resultVo.errorNo == 0) {

                [self handleEnqueueResultSuccess:resultVo];
            }else{//网络异常
                [self handleEnqueueResultError:resultVo];
            }
        }];
        
    }else{
        
        TKLogInfo(@"数据异常");
    }
    
}

- (void)handleEnqueueResultSuccess:(ResultVo *)resultVo {
    NSArray *arr = (NSArray*)resultVo.results;
    if (arr.count <= 0) {
        [self performSelector:@selector(startLineingUp) withObject:nil afterDelay:1.0];
        return;
    }
    
    NSDictionary *resReslut = [arr objectAtIndex:0];
    if (!resReslut) {
        
        [self performSelector:@selector(startLineingUp) withObject:nil afterDelay:1.0];
        return;
    }
    
    if ([[resReslut objectForKey:@"staff_exist"] isEqualToString:@"true"]) {//有坐席
        [TKDirectVideoModel shareInstance].isQueueStaffExist=YES;//有坐席
        if ([[resReslut objectForKey:@"queue_location"] integerValue] == 0){//排队位置 可以开始见证
            
            if ([resReslut objectForKey:@"server_roomNo"]){//开始见证
                
                [self handleEnqueueQueueHeadResult:resReslut];
                
            }else{//未获取到见证地址
                
                [self startLineingUp];
            }
        }else{//处于排队中
            
            [self handleEnqueueNotQueueHeadResult:resReslut];
        }
    }else{//没有坐席
        [TKDirectVideoModel shareInstance].isQueueStaffExist=NO;//没有坐席
        TKLogInfo(@"查询坐席...");
        if (self.delegate && [self.delegate respondsToSelector:@selector(changeSmartTwoVideoTipText:queueLocation:currentStatus:)]) {
            [TKDirectVideoModel shareInstance].queueSubMsg=[self queueStaffOfflineSubMsg:resReslut[@"message"]];
            
            //提示语h5入参调整位置提示语优先级最高
            //针对h5参数类型容错处理
            NSString *queueStaffOfflineMsg;
            if(self.requestParams[@"videoTipMsg"]&&[self.requestParams[@"videoTipMsg"] isKindOfClass:[NSDictionary class]]){
                queueStaffOfflineMsg=self.requestParams[@"videoTipMsg"][@"queueStaffOfflineMsg"];
            }else{
                queueStaffOfflineMsg=[TKDataHelper jsonToDictionary:self.requestParams[@"videoTipMsg"]][@"queueStaffOfflineMsg"];
            }
            
            if([TKStringHelper isNotEmpty:queueStaffOfflineMsg]){
                [TKDirectVideoModel shareInstance].queueBigMsg=queueStaffOfflineMsg;
            }else{
                [TKDirectVideoModel shareInstance].queueBigMsg=[NSString stringWithFormat:@"无%@在线，请稍候...",[TKDirectVideoModel shareInstance].serviceTipString];
            }

            [self.delegate changeSmartTwoVideoTipText:[TKDirectVideoModel shareInstance].queueBigMsg queueLocation:0 currentStatus:TKOpenQueueStatusNOService];
        }
        
        [self performSelector:@selector(startLineingUp) withObject:nil afterDelay:1.0];
    }
}

- (void)handleEnqueueNotQueueHeadResult:(NSDictionary *)resReslut
{
    TKLogInfo(@"排队中...");
    
    if ([[resReslut objectForKey:@"queue_location"] integerValue] < 0) {

        if (self.delegate && [self.delegate respondsToSelector:@selector(changeSmartTwoVideoTipText:queueLocation:currentStatus:)]) {
            [TKDirectVideoModel shareInstance].queueSubMsg=[self queueWaitAgreeSubMsg:resReslut[@"message"]];
            
            [TKDirectVideoModel shareInstance].queueBigMsg=[NSString stringWithFormat:@"等待%@确认，请稍候...",[TKDirectVideoModel shareInstance].serviceTipString];
            if ([TKStringHelper isNotEmpty:self.requestParams[@"queueWaitAgreeMsg"]]) {
                [TKDirectVideoModel shareInstance].queueBigMsg=self.requestParams[@"queueWaitAgreeMsg"];
            }
            
  
            //提示语h5入参调整位置提示语优先级最高
            //针对h5参数类型容错处理
            NSString *queueWaitAgreeMsg;
            if(self.requestParams[@"videoTipMsg"]&&[self.requestParams[@"videoTipMsg"] isKindOfClass:[NSDictionary class]]){
                queueWaitAgreeMsg=self.requestParams[@"videoTipMsg"][@"queueWaitAgreeMsg"];
            }else{
                queueWaitAgreeMsg=[TKDataHelper jsonToDictionary:self.requestParams[@"videoTipMsg"]][@"queueWaitAgreeMsg"];
            }
            
            if([TKStringHelper isNotEmpty:queueWaitAgreeMsg]){
                [TKDirectVideoModel shareInstance].queueBigMsg=queueWaitAgreeMsg;
            }
            
            [self.delegate changeSmartTwoVideoTipText:[TKDirectVideoModel shareInstance].queueBigMsg queueLocation:[[resReslut objectForKey:@"queue_location"] intValue] currentStatus:TKOpenQueueStatusGetService];
        }
    }else{
        
        int currentPosition = [[resReslut objectForKey:@"queue_location"] intValue];

        if (self.linePosition == 0 || self.linePosition > currentPosition||[self.requestParams[@"isShowTruePosition"] intValue]==1) {
            //默认被插队，或位置不变，不修改位置提示语
            self.linePosition = currentPosition;
        }
        
        if(!self.isLinePositionLog) {
            //记录第一次排队位置日志
            self.isLinePositionLog=YES;
            NSString *logString=[NSString stringWithFormat:@"TKMSG1002:%d",self.linePosition];

            if (self.delegate && [self.delegate respondsToSelector:@selector(uploadSmartTwoVideoLog:)]) {
                [self.delegate uploadSmartTwoVideoLog:logString];
            }
        }
        
        //有tip_type修改提示语，如果被插队或位置不变话术就用原来的(东亚前海的需求)
        if ([resReslut objectForKey:@"tip_type"]) {
            if ([TKDirectVideoModel shareInstance].tipType==0||[TKDirectVideoModel shareInstance].tipType>[[resReslut objectForKey:@"tip_type"] intValue]) {
                [TKDirectVideoModel shareInstance].tipType=[[resReslut objectForKey:@"tip_type"] intValue];
            }
        }
        
        //排队人数超限提示逻辑
        if (self.isNeedTransfiniteNumber) {
            if (self.linePosition>=[self.requestParams[@"transfiniteNumber"] intValue]) {
                if (![TKDirectVideoModel shareInstance].isShowAlert) {
                    [TKDirectVideoModel shareInstance].isShowAlert = YES;
                    [TKDirectVideoModel shareInstance].witnessResult=@"-11";
                    self.isNeedTransfiniteNumber=false;
                    
                    if (self.delegate && [self.delegate respondsToSelector:@selector(alertSmartTwoVideoTip:describe:cancelBtnTitle:takeBtnTitle:)]) {
                        [self.delegate alertSmartTwoVideoTip:@"排队人数过多！" describe:@"排队人数多，等待时间过长，建议您切换为自助见证，无需排队" cancelBtnTitle:@"继续等待" takeBtnTitle:@"切换为自助见证"];
                    }
                    
                    //新设计稿点继续排队后才显示底部的继续见证按钮
//                    if (self.delegate && [self.delegate respondsToSelector:@selector(showCancelBtn)]) {
//                        [self.delegate showCancelBtn];
//                    }
                 }
            }
        }

        if (self.delegate && [self.delegate respondsToSelector:@selector(changeSmartTwoVideoTipText:queueLocation:currentStatus:)]) {
            [TKDirectVideoModel shareInstance].queueSubMsg=[self queueLocationChangeString:resReslut[@"message"] location:self.linePosition];
            
            //提示语h5入参调整位置提示语优先级最高
            //针对h5参数类型容错处理
            NSString *queueLocationMsg;
            if(self.requestParams[@"videoTipMsg"]&&[self.requestParams[@"videoTipMsg"] isKindOfClass:[NSDictionary class]]){
                queueLocationMsg=self.requestParams[@"videoTipMsg"][@"queueLocationMsg"];
            }else{
                queueLocationMsg=[TKDataHelper jsonToDictionary:self.requestParams[@"videoTipMsg"]][@"queueLocationMsg"];
            }
            
            if([TKStringHelper isNotEmpty:queueLocationMsg]){
                [TKDirectVideoModel shareInstance].queueBigMsg=queueLocationMsg;
            }else{
                [TKDirectVideoModel shareInstance].queueBigMsg=@"正在排队中...";
            }
            [self.delegate changeSmartTwoVideoTipText:[TKDirectVideoModel shareInstance].queueBigMsg queueLocation:self.linePosition currentStatus:TKOpenQueueStatusLocation];
        }
    }
    
    [self performSelector:@selector(startLineingUp) withObject:nil afterDelay:1.0];
}

- (void)handleEnqueueQueueHeadResult:(NSDictionary *)resReslut
{
    if (self.delegate && [self.delegate respondsToSelector:@selector(changeSmartTwoVideoTipText:queueLocation:currentStatus:)]) {
        [TKDirectVideoModel shareInstance].queueSubMsg=[self queueConnectingSubMsg:resReslut[@"message"]];
        
        //提示语h5入参调整位置提示语优先级最高
        //针对h5参数类型容错处理
        NSString *queueConnectingMsg;
        if(self.requestParams[@"videoTipMsg"]&&[self.requestParams[@"videoTipMsg"] isKindOfClass:[NSDictionary class]]){
            queueConnectingMsg=self.requestParams[@"videoTipMsg"][@"queueConnectingMsg"];
        }else{
            queueConnectingMsg=[TKDataHelper jsonToDictionary:self.requestParams[@"videoTipMsg"]][@"queueConnectingMsg"];
        }
        
        if([TKStringHelper isNotEmpty:queueConnectingMsg]){
            [TKDirectVideoModel shareInstance].queueBigMsg=queueConnectingMsg;
        }else{
            [TKDirectVideoModel shareInstance].queueBigMsg=@"正在连接视频服务器，请稍候...";
        }
        [self.delegate changeSmartTwoVideoTipText:[TKDirectVideoModel shareInstance].queueBigMsg queueLocation:0 currentStatus:TKOpenQueueStatusConcentVideo];
    }
    
    NSArray *arr;
    NSString *tempIp;
    NSString *tempPort;
    NSString *tempRoom;
    //如果有server_info特殊情况的视频服务器处理
    if ([resReslut objectForKey:@"server_info"]) {
        [TKDirectVideoModel shareInstance].videoServerInfo=[[NSMutableDictionary alloc] init];
        if ([resReslut[@"server_info"] isKindOfClass:[NSDictionary class]]) {
            [TKDirectVideoModel shareInstance].videoServerInfo=(NSMutableDictionary *)resReslut[@"server_info"];
        }else{
            [TKDirectVideoModel shareInstance].videoServerInfo=(NSMutableDictionary *)[TKDataHelper jsonToDictionary:resReslut[@"server_info"]];
        }
        
        //serverIp存在就直接用，（anychat，tchat）
        if ([TKStringHelper isNotEmpty:[TKDirectVideoModel shareInstance].videoServerInfo[@"serverIp"]]) {
            tempIp=[TKDirectVideoModel shareInstance].videoServerInfo[@"serverIp"];
            tempPort=[TKDirectVideoModel shareInstance].videoServerInfo[@"tcpPort"];
        }
        
        //即构的ip端口单独处理
        if ([TKStringHelper isNotEmpty:[TKDirectVideoModel shareInstance].videoServerInfo[@"dispatchServer"]]) {
            NSString *videoPath=[TKDirectVideoModel shareInstance].videoServerInfo[@"dispatchServer"];
            if([videoPath hasPrefix:@"https://"]){
                videoPath=[videoPath stringByReplacingOccurrencesOfString:@"https://" withString:@""];
            }else if([videoPath hasPrefix:@"http://"]){
                videoPath=[videoPath stringByReplacingOccurrencesOfString:@"http://" withString:@""];
            }
            NSArray *portRoomArray=[videoPath componentsSeparatedByString:@":"];
            tempIp=portRoomArray[0];
            tempPort=portRoomArray[1];
        }
        
        tempRoom=resReslut[@"room_no"];
        arr=[[NSArray alloc] initWithObjects:tempIp,tempPort,tempRoom, nil];
        
    }else{
        [TKDirectVideoModel shareInstance].videoServerInfo=nil;
        //针对ipv6和ipv4不同情况处理
        if ([[resReslut objectForKey:@"server_roomNo"] rangeOfString:@"]"].location == NSNotFound) {
            //ipv4地址
            arr=[[resReslut objectForKey:@"server_roomNo"] componentsSeparatedByString:@":"];
        }else{
            //是ipv6的地址有[]情况
            NSArray *tempArray=[[resReslut objectForKey:@"server_roomNo"] componentsSeparatedByString:@"]"];
            NSString *tempIp;
            if ([@"tchat" isEqualToString:self.requestParams[@"videoType"]] || (self.requestParams[@"videoType"] && [self.requestParams[@"videoType"] integerValue] == 0)) {
                //tchat使用ipv6需要[]
                tempIp=[NSString stringWithFormat:@"%@]",tempArray[0]];
            }else{
                //anychat使用ipv6不需要[]
                tempIp=[tempArray[0] stringByReplacingOccurrencesOfString:@"["withString:@""];
            }
            
            NSArray *portRoomArray=[tempArray[1] componentsSeparatedByString:@":"];
            NSString *tempPort=portRoomArray[1];
            NSString *tempRoom=portRoomArray[2];
            arr=[[NSArray alloc] initWithObjects:tempIp,tempPort,tempRoom, nil];
        }
    }
    
    

    
    
    if (arr.count >= 3) {
        
        NSString *acRoomId = arr[2];
        
        TKLogInfo(@"房间号:%@",acRoomId);
        //外面的staff_tips是坐席登录的时候设置给排队的，agree_param里面的staffTips 是接入客户那一刻设置给排队的，为了满足统一见证，在接入不同业务时，给客户显示不同坐席信息类型
        if ([resReslut objectForKey:@"staff_tips"]) {
            //坐席自定义给前端,显示提示语
            [TKDirectVideoModel shareInstance].staffTips=resReslut[@"staff_tips"];
        }
        
        if ([resReslut objectForKey:@"agree_param"]) {
            NSDictionary *agreeParam=[TKDataHelper jsonToDictionary:resReslut[@"agree_param"]];
            if ([agreeParam objectForKey:@"staffTips"]) {
                //坐席自定义给前端,显示提示语
                [TKDirectVideoModel shareInstance].staffTips=agreeParam[@"staffTips"];
            }
        }
        
        
        
        if ([resReslut objectForKey:@"ext_param"]) {
            //存在需要返回给h5的排队扩展参数
            if ([resReslut[@"ext_param"] isKindOfClass:[NSDictionary class]]) {
                [TKDirectVideoModel shareInstance].extParam=[TKDataHelper dictionaryToJson:resReslut[@"ext_param"]];
            }else{
                [TKDirectVideoModel shareInstance].extParam=resReslut[@"ext_param"];
            }
        }
        
        if (acRoomId && ![acRoomId isEqualToString:@""]) {
            
            // 埋点-双向-排队-成功
            NSMutableDictionary *eventDic = [NSMutableDictionary dictionaryWithDictionary:self.requestParams];
            eventDic[@"errorNo"] = @"0";
            eventDic[@"event_err"] = @"";
            [TKDirectVideoModel shareInstance].isEnqueueSuccess = YES;
            [TKStatisticEventHelper sendEvent:TKPrivateEventVideoWitness subEventName:TKPrivateSubEventVideoWitnessEnqueue progress:TKPrivateEventProgressEnd result:TKPrivateEventResultSuccess orientation:TKPrivateVideoOrientationPortrait oneWayVideoType:TKPrivateOneWayVideoTypeNone prepareVideoProgress:TKPrivatePrepareVideoProgressNone eventDic:eventDic];
            
            [TKDirectVideoModel shareInstance].witRoomId = acRoomId;
            //h5传了视频服务器ip端口的话就用h5传的，h5没穿再用排队bus的
            NSString *videoServerIP=self.requestParams[@"videoServerIP"]?self.requestParams[@"videoServerIP"]:arr[0];
            NSString *videoServerPort=self.requestParams[@"videoServerPort"]?self.requestParams[@"videoServerPort"]:arr[1];
            
            NSString *logString=[NSString stringWithFormat:@"TKMSG1003:%@|%@:%@",acRoomId,videoServerIP,videoServerPort];
            if (self.delegate && [self.delegate respondsToSelector:@selector(uploadSmartTwoVideoLog:)]) {
                [self.delegate uploadSmartTwoVideoLog:logString];
            }

            if (self.delegate && [self.delegate respondsToSelector:@selector(tkStartSmartTwoVideo:withPort:)]) {
                [self.delegate tkStartSmartTwoVideo:videoServerIP withPort:[videoServerPort intValue]];
            }
            
        }else{
            
            TKLogInfo(@"未获取正常的房间号");
            
            [self performSelector:@selector(startLineingUp) withObject:nil afterDelay:1.0];
            
        }
    }
}

- (void)handleEnqueueResultError:(ResultVo *)resultVo
{
    TKLogInfo(@"501301_%ld-%@", (long)resultVo.errorNo, resultVo.errorInfo);
    
    
    if (self.delegate && [self.delegate respondsToSelector:@selector(changeSmartTwoVideoTipText:queueLocation:currentStatus:)]) {
        [self.delegate changeSmartTwoVideoTipText:@"网络环境较差，请等待或更换网络..." queueLocation:0 currentStatus:TKOpenQueueStatusStart];
    }
    
    if (resultVo.errorNo == -1001) {
        
        [self performSelector:@selector(startLineingUp) withObject:nil afterDelay:1.0];
        
    }else{
        
        if (![TKDirectVideoModel shareInstance].isShowAlert) {            
            [TKDirectVideoModel shareInstance].isShowAlert = YES;
            
            if (resultVo.errorNo == -100000||resultVo.errorNo == -100001||resultVo.errorNo == -100002||resultVo.errorNo == -100003||resultVo.errorNo == -100004||resultVo.errorNo == -100005||resultVo.errorNo == -100006) {
                [TKDirectVideoModel shareInstance].witnessResult = @"-9";//网络异常
            }else{
                [TKDirectVideoModel shareInstance].witnessResult = @"-2";//排队异常
            }
            [TKDirectVideoModel shareInstance].witnessInfo=resultVo.errorInfo;
            
            if (self.delegate && [self.delegate respondsToSelector:@selector(alertSmartTwoVideoTip:describe:cancelBtnTitle:takeBtnTitle:)]) {
                [self.delegate alertSmartTwoVideoTip:@"视频录制提示" describe:@"您的网络异常，请重新排队！" cancelBtnTitle:nil takeBtnTitle:@"确定"];
            }
        }
        
    }
}


/**
 *
 * @method stopNewLineingUp
 *
 * @brief 结束4.0视频排队接口
 *
 */
- (void)stopNewLineingUp{
    
    if (self.requestParams) {
        
        
        NSString *logString=[NSString stringWithFormat:@"TKMSG1021:用户主动取消排队"];
        if (self.delegate && [self.delegate respondsToSelector:@selector(uploadSmartTwoVideoLog:)]) {
            [self.delegate uploadSmartTwoVideoLog:logString];
        }
        
        self.requestParams[@"funcNo"]=@"501302";
        
        if (!self.requestParams[@"origin"]) {
            self.requestParams[@"origin"]=@"2";
        }
        
        if (!self.requestParams[@"level"]) {
            self.requestParams[@"level"]=@"0";
        }
        
        self.requestParams[@"abnormal_exit"]=[NSNumber numberWithBool:[TKDirectVideoModel shareInstance].aExit];
        
        [self.mService handleNetworkWithURL:self.requestParams[@"url"] param:self.requestParams callBackFunc:^(ResultVo *resultVo) {
            
            dispatch_async(dispatch_get_main_queue(), ^{
                
                if (resultVo.errorNo == 0) {
                    
                    NSArray *arr = (NSArray*)resultVo.results;
                    
                    if (arr.count > 0) {
                        
                        NSDictionary *resReslut = [arr objectAtIndex:0];
                        
                        if (resReslut && [[resReslut objectForKey:@"flag"] isEqualToString:@"true"]) {
                            
                            TKLogInfo(@"排队取消成功");
                        }
                    }
                    
                }else{//网络异常
                    
                    TKLogInfo(@"501302_%@", resultVo.errorInfo);
                    
                }
            });
            
        }];
        
    }else{
        
        TKLogInfo(@"数据异常");
    }
    
}

#pragma mark 3.0排队相关接口
/**
 *
 * @method startOldLineingUp
 *
 * @brief 开始3.0视频排队接口
 *
 */
- (void)startOldLineingUp{
    NSString *hostUrl=self.requestParams[@"url"];
    self.isNeed3Queue=YES;//是否需要3.0排队
    self.requestParams[@"url"]=hostUrl;

    self.service = [[TKChatService alloc] initWithDelegate:self host:self.requestParams[@"url"] withParams:self.requestParams];
    [self.service requestForBranchStateByBranchNo:self.requestParams[@"orgId"]];
}

/**
 *
 * @method stopOldLineingUp
 *
 * @brief 结束3.0视频排队接口
 *
 */
- (void)stopOldLineingUp{
    self.isNeed3Queue=NO;
    [self.service requestEndOrder:self.requestParams[@"userId"] orgId:self.requestParams[@"orgId"]];
    [self.service requestEndVideoByUserId:self.requestParams[@"userId"]];
}

////////轮询///////

//检查是否轮到自己
-(void)checkTurn{
    //检查是否到自己
    if (self.isNeed3Queue) {
            [self.service requestForIsMyTurnByUserId:self.requestParams[@"userId"]];
    }
    
}
//查询当前座席状态
-(void)checkBranchState{
    
    [self.service requestForBranchStateByBranchNo:self.requestParams[@"orgId"]];
    
}
////////轮询end////////

//等待结束,轮到自己见证
-(void)endWaitAndRequestVideo{
    //请求地址
    [self.service requestForVideoServerByUserId:self.requestParams[@"userId"]];
}

#pragma mark TKChatServiceDelegate
//接口数据异常代理
- (void)handleRequestException:(NSString *)errorNo errorDescription:(NSString *)eMsg{
    if (![TKDirectVideoModel shareInstance].isShowAlert) {
        
        [TKDirectVideoModel shareInstance].isShowAlert = YES;
        
        if (self.delegate && [self.delegate respondsToSelector:@selector(alertSmartTwoVideoTip:describe:cancelBtnTitle:takeBtnTitle:)]) {
            [self.delegate alertSmartTwoVideoTip:@"视频录制提示" describe:[TKStringHelper isEmpty:eMsg]? @"数据异常":eMsg cancelBtnTitle:nil takeBtnTitle:@"确定"];
        }
    }
}

/**
 *  排队前查询一下座席状态,一直轮询到有座席，再开始排队流程
 *
 *  @param loc 如果返回0，则提示没有座席，不进入排队流程
 *  @param e   错误信息
 */
-(void)chatServiceRequestBranchStateBack:(int)loc error:(NSString *)e{
    if(loc==0){
        

        if (self.delegate && [self.delegate respondsToSelector:@selector(changeSmartTwoVideoTipText:queueLocation:currentStatus:)]) {
            [self.delegate changeSmartTwoVideoTipText:[NSString stringWithFormat:@"无%@在线",[TKDirectVideoModel shareInstance].serviceTipString]  queueLocation:0 currentStatus:TKOpenQueueStatusNOService];
        }
        self.isNeed3Queue = NO;
        [NSTimer scheduledTimerWithTimeInterval:3 target:self selector:@selector(checkBranchState) userInfo:nil repeats:NO];
    }else{
        self.isNeed3Queue = YES;
        //开始申请排队
        [self.service requestForToOrder:self.requestParams[@"userId"] nickName:self.requestParams[@"userName"] orgId:self.requestParams[@"orgId"] level:self.requestParams[@"level"]?self.requestParams[@"level"]:@"0" origin:@"ios"];
    }
}


//是否到自己见证
-(void)chatServiceIsMyTurn:(BOOL)isMe error:(NSString*)e staff_tips:(NSString *)staff_tips{
    if (isMe) {
        //轮到自己
        self.isNeed3Queue = NO;
//        [self changeTipText:@"正在接入中，请稍候... " queueLocation:0 currentStatus:TKOpenQueueStatusGetService];
        if (self.delegate && [self.delegate respondsToSelector:@selector(changeSmartTwoVideoTipText:queueLocation:currentStatus:)]) {
            NSString *tip=[NSString stringWithFormat:@"等待%@确认，请稍候...",[TKDirectVideoModel shareInstance].serviceTipString];
            if ([TKStringHelper isNotEmpty:self.requestParams[@"queueWaitAgreeMsg"]]) {
                tip=self.requestParams[@"queueWaitAgreeMsg"];
            }
            [self.delegate changeSmartTwoVideoTipText:tip  queueLocation:0 currentStatus:TKOpenQueueStatusGetService];
        }
        [self endWaitAndRequestVideo];
        
    }else{
        [self.service requestForOrgOrderCountByUserId:self.requestParams[@"userId"] orgId:self.requestParams[@"orgId"]];
        [NSTimer scheduledTimerWithTimeInterval:3 target:self selector:@selector(checkTurn) userInfo:nil repeats:NO];
        
    }
}


//网点排队人数
-(void)chatServiceOrgOrderCount:(int)pcount error:(NSString *)e{

    if (pcount == -1)
    {
        if (![TKDirectVideoModel shareInstance].isShowAlert) {
            
            [TKDirectVideoModel shareInstance].isShowAlert = YES;
            
            if (self.delegate && [self.delegate respondsToSelector:@selector(alertSmartTwoVideoTip:describe:cancelBtnTitle:takeBtnTitle:)]) {
                [self.delegate alertSmartTwoVideoTip:@"视频录制提示" describe:@"您的网络异常，请重新排队！" cancelBtnTitle:nil takeBtnTitle:@"确定"];
            }
        }
    }
    else{
        
        if (self.delegate && [self.delegate respondsToSelector:@selector(changeSmartTwoVideoTipText:queueLocation:currentStatus:)]) {
            [TKDirectVideoModel shareInstance].queueSubMsg=[self queueLocationChangeString:nil location:self.linePosition];
            [self.delegate changeSmartTwoVideoTipText:@"正在排队中…"  queueLocation:pcount currentStatus:TKOpenQueueStatusLocation];
        }
    }
}
//请求排队
-(void)chatServiceRequestToOrderBack:(int)loc error:(NSString *)e{
    
    [self checkTurn];

}

//获得见证资源
-(void)chatServiceGetVideoIP:(NSString *)ip port:(int)port roomId:(NSString *)_roomId extParam:(NSString *)eParam error:(NSString *)e{
    
//    [self changeTipText:@"正在连接视频服务器，请稍候..." queueLocation:0 currentStatus:TKOpenQueueStatusConcentVideo];
    if (self.delegate && [self.delegate respondsToSelector:@selector(changeSmartTwoVideoTipText:queueLocation:currentStatus:)]) {
        [self.delegate changeSmartTwoVideoTipText:@"正在连接视频服务器，请稍候..." queueLocation:0 currentStatus:TKOpenQueueStatusConcentVideo];
    }
   
//    self.witRoomId = _roomId;
    [TKDirectVideoModel shareInstance].witRoomId = _roomId;
//    [self tkStartNewVideoWitness:ip withPort:port];
    if (self.delegate && [self.delegate respondsToSelector:@selector(tkStartSmartTwoVideo:withPort:)]) {
        [self.delegate tkStartSmartTwoVideo:ip withPort:port];
    }
}

#pragma mark 视频开启关闭相关
/***
 启动视频见证
 */
- (void)startSmartTwoVideo:(NSString*)sUrl withPort:(int)sPort{
    [TKDirectVideoModel shareInstance].tkCountDown=20;
    [self.smartTwoVideoManager startSmartTwoVideo:sUrl withPort:sPort];
}

/**
 *
 * @method tkStopTChatWitness
 *
 * @brief 结束视频见证
 *
 */
-(void)stopSmartTwoVideo{
    [self.smartTwoVideoManager stopSmartTwoVideo];
}

/**
 *
 * @method tkSwitchCameraNewTChatWitness
 *
 * @brief 切换摄像头
 *
 */
-(void)switchCameraSmartTwoVideo:(BOOL)isFrontCamera{
    [self.smartTwoVideoManager switchCameraSmartTwoVideo:[TKDirectVideoModel shareInstance].isFrontCamera];
}

/**
 *
 * @method sendMsgToVideoServer
 *
 * @brief 发送消息给坐席
 *
 */
-(void)sendMsgToVideoServer:(NSString *)msg{
    [self.smartTwoVideoManager sendMsgToVideoServer:msg];
}

#pragma mark - Setter
-(void)setContentView:(UIView *)contentView{
    _contentView=contentView;
    self.smartTwoVideoManager.contentView=contentView;
}
-(void)setRemoteContentView:(UIView *)remoteContentView{
    _remoteContentView=remoteContentView;
    self.smartTwoVideoManager.remoteContentView=remoteContentView;
}

#pragma mark lazyloading

- (id<TKSmartTwoVideoManagerProtocol>)smartTwoVideoManager {
    if (!_smartTwoVideoManager) {
        
        NSString *className = nil;
        // 获取真实的工具类
        if ( [_requestParams[@"videoType"] integerValue] == 0) {
            TKLogInfo(@"---启动TChat视频---");
            className=@"TKTChatSmartTwoVideoManager";
        }else if ([_requestParams[@"videoType"] integerValue] == 5) {
            TKLogInfo(@"---启动TChatRtc视频---");
            className=@"TKTChatRtcSmartTwoVideoManager";
        }else if ([_requestParams[@"videoType"] integerValue] == 4) {
            TKLogInfo(@"---启动Zego视频---");
            className=@"TKZegoSmartTwoVideoManager";
        }else{
            TKLogInfo(@"---启动AnyChat视频---");
            className=@"TKAnyChatSmartTwoVideoManager";
        }
        
        Class speechRecognizeManagerClazz = NSClassFromString(className);
        
        NSAssert(speechRecognizeManagerClazz != nil, @"视频实现工具类不存在");
        
        // 初始化并设置代理
        _smartTwoVideoManager = [[speechRecognizeManagerClazz alloc] initWithConfig:self.requestParams];
        _smartTwoVideoManager.delegate = self.delegate;
    }
    
    return (id<TKSmartTwoVideoManagerProtocol>)_smartTwoVideoManager;
}


@end
