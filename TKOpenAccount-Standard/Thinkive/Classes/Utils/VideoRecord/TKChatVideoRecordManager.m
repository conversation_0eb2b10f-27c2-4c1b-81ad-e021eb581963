//
//  TKChatVideoRecordManager.m
//  TKOpenAccount-Standard
//
//  Created by <PERSON> on 2021/4/17.
//  Copyright © 2021 thinkive. All rights reserved.
//

#import "TKChatVideoRecordManager.h"
#import "TKOpenAccountService.h"

#import <TChat/TChatDefine.h>
#import <TChat/TChatErrorCode.h>
#import <TChat/TChatCore.h>

#import <AVFoundation/AVAsset.h>
#import <AVFoundation/AVAssetImageGenerator.h>
#import <AVFoundation/AVTime.h>

#import "TKChatErrorConverter.h"

@interface TKChatVideoRecordManager()<TKCCNotifyMessageDelegate, TKCCTextMsgDelegate, TKCCTransDataDelegate, TKCCVideoDataDelegate, TKCCAudioDataDelegate, TKCCSnapShotDelegate>
{
    BOOL isTCStartingVideo;
    int tcSeatId ,tcUserId;
    
    dispatch_queue_t _tchatReleaseQueue;
    dispatch_queue_t _tchatReconnectQueue;
}

@property (nonatomic, readwrite, assign) BOOL isFirstIntTChat;
@property (nonatomic, readwrite, copy) NSString *authKey;   // 视频密码
@property (nonatomic, readwrite, copy) NSString *loginId;   // 登录id
@property (nonatomic, readwrite, copy) NSString *sercurityNo;   // 视频授权号
@property (nonatomic, readwrite, copy) NSString *signalServer;  // 信令服务地址

@property (nonatomic, readwrite, copy) NSString *centerServerHost;    // TChat服务器主机
@property (nonatomic, readwrite, copy) NSString *centerServerPort;  // TChat服务器端口

// 视频录制路径
@property (nonatomic, readwrite, copy) NSString *originalVideoPath; // 从服务器获取的初始路径
@property (nonatomic, readwrite, copy) NSString *finalVideoPath; // 最终录制的视频录制

@property (nonatomic, readwrite, assign) BOOL isDisconnecting; // 正在断开链接

@property (nonatomic, readwrite, strong) TKOpenAccountService *openAccountService; // 请求类

@property (nonatomic, readwrite, assign) int connectTime; // 连接时长
@property (nonatomic, readwrite, assign) BOOL isSeatAudioDataReady; // 坐席音频是否准备就绪
@property (nonatomic, readwrite, strong) NSTimer *timeoutTimer; // 超时定时器
@property (nonatomic, readwrite, strong) NSTimer *TTSTimeoutTimer; // 超时定时器
@property (nonatomic, readwrite, strong) NSTimer *reconnectRetryTimer; // 重连重试定时器
@property (nonatomic, readwrite, strong) NSTimer *reconnectTotalTimeOutTimer; // 重连总超时定时器
@property (nonatomic, readwrite, strong) NSTimer *reconnectIntervalTimer; // 重连间隔定时器
@property (nonatomic, readwrite, assign) BOOL isReceiveStopTTSMsg; // 是否收到语音播报结束回调
@property (nonatomic, readwrite, assign) BOOL isFirstTTS; // 是否第一次TTS
@property (nonatomic, readwrite, assign) float rate; // 播放速率
@property (nonatomic, readwrite, assign) BOOL isFirstReleaseTChat; // 是否第一次释放tchat

@property (nonatomic, readwrite, strong) NSDate *lastEnterBackgroundDate; // 最近进入后台的时间

@property (nonatomic, readwrite, strong) NSTimer *takePicturesTimer; // 超时定时器
//@property (nonatomic, readwrite, strong) NSFileHandle *handle; // 音频写入工具

@property (nonatomic, readwrite, assign) int currentReconnectNumber;    /// 当前重连次数

@property (nonatomic, readwrite, strong) NSArray *synthesisArray;   // 是否正在合成多个语音
@property (nonatomic, readwrite, assign) int currentIndex;
@property (nonatomic, readwrite, assign) BOOL localDeviceIsOpen;    // 本地麦克风和摄像头是否打开
@property (nonatomic, readwrite, assign) BOOL isForceStopReconnect; // 是否强制断开重连

@end

@implementation TKChatVideoRecordManager
@synthesize delegate = _delegate;
@synthesize configParam = _configParam;
@synthesize contentView = _contentView;
@synthesize remoteContentView = _remoteContentView;
@synthesize isLandscape = _isLandscape;
@synthesize isFrontCamera = _isFrontCamera;
@synthesize disableMicrophone = _disableMicrophone;
@synthesize requestID = _requestID;
@synthesize contentViewY = _contentViewY;

#pragma mark - Init && Dealloc
//- (void)dealloc {
////    TKLogDebug(@"TKChatVideoRecordManager dealloc");
//
//
//}

- (instancetype)initWithConfig:(NSDictionary *)configParam {
    if (self = [super init]) {
        self.configParam = configParam;
        self.isFirstIntTChat = YES;
        self.isFirstTTS = YES;
        self.rate = [configParam getFloatWithKey:@"rate"] == 0 ? 1 : [configParam getFloatWithKey:@"rate"];
        _tchatReleaseQueue = dispatch_queue_create("com.thinkive.TKChatVideoRecordManager.tchatReleaseQueue", DISPATCH_QUEUE_SERIAL);
        _tchatReconnectQueue = dispatch_queue_create("com.thinkive.TKChatVideoRecordManager.tchatReconnectQueue", DISPATCH_QUEUE_SERIAL);
        self.disableFetchPicture = NO;
        self.isFrontCamera = YES;
    }
    
    return  self;
}

#pragma mark - Public Selector
// 请求视频房间号
- (void)bootDevcie:(BOOL)isFirst {
    
    if ([self canReconnect] == NO) {    // 不需要重连的场景才回调。重连的场景都在重连方法中回调了
        TKLogInfo(@"思迪服务器录制日志：首次重连时更新UI");
        
        dispatch_async(dispatch_get_main_queue(), ^{
            if (self.delegate && [self.delegate respondsToSelector:@selector(connectServerRoomDidStart)]) {
                [self.delegate connectServerRoomDidStart];
            }
        });
    }
    
    __weak typeof(self) weakSelf = self;
    
    NSString *url = [self.configParam getStringWithKey:@"url"];
    url = [TKStringHelper isEmpty:url] ? [self.configParam getStringWithKey:@"videoServerUrl"] : url ;
    [self requestServerRoomWithUrlStr:url enableVh:self.enableVh callBack:^(BOOL success, NSInteger errorNo, NSString * _Nonnull errorMsg) {

        if (success) {

            if (isFirst) [weakSelf initTChatWitness];

            [weakSelf connectServer];
        } else {

            errorMsg = [NSString stringWithFormat:@"%@(%d)", errorMsg, (int)errorNo];
            [weakSelf handleBootDeviceFail:errorMsg];
        }
    }];
}

/// 断开视频服务器并销毁释放资源
- (void)stopDevice:(BOOL)needRelease {
    
    [self disconnectServer:needRelease];
}

// 请求视频房间号
- (void)requestServerRoomWithUrlStr:(NSString *)urlStr callBack:(void((^)(BOOL success, NSInteger errorNo, NSString *errorMsg)))callback {
    
    [self requestServerRoomWithUrlStr:urlStr enableVh:NO callBack:callback];
}

// 请求视频房间号
- (void)requestServerRoomWithUrlStr:(NSString *)urlStr enableVh:(BOOL)enableVh callBack:(void((^)(BOOL success, NSInteger errorNo, NSString *errorMsg)))callback {
    
    if (self.isDisconnecting == NO) {
        TKLogInfo(@"思迪服务器录制日志：正在申请房间号");
        [self sendRequestToRequestServerRoomWithUrlStr:urlStr enableVh:enableVh callBack:callback];
    } else {
        // 正在断开服务器，不断轮询，直到可以重新连接
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            TKLogInfo(@"思迪服务器录制日志：正在断开服务器，不断轮询，直到可以重新连接");
            [self requestServerRoomWithUrlStr:urlStr enableVh:enableVh callBack:callback];
        });
    }
}

// 发送网络请求获取房间信息
- (void)sendRequestToRequestServerRoomWithUrlStr:(NSString *)urlStr enableVh:(BOOL)enableVh callBack:(void((^)(BOOL success, NSInteger errorNo, NSString *errorMsg)))callback
{
    if (self.isForceStopReconnect) {
        TKLogInfo(@"思迪服务器录制日志：重连已超时，强制断开，抛弃处理结果");
        return;
    }
    
    NSString *url = urlStr;
    NSMutableDictionary *param = [TKOpenAccountService filterRequestParam:self.configParam];
    param[@"funcNo"] = @"********";
    param[@"enable_vh"] = enableVh ? @"1" : @"0";    // 是否申请虚拟人房间号
    if ([TKStringHelper isNotEmpty:[self.configParam getStringWithKey:@"videoAdditionParams"]]) {
        
        param[@"addition"] = [self.configParam getStringWithKey:@"videoAdditionParams"];
    }
    
//    [self showLoading];
    
    __weak typeof(self) weakSelf = self;
    [self.openAccountService requestServerRoomWithURL:url param:param callBackFunc:^(ResultVo *resultVo) {
            
        NSString *msg = [TKStringHelper isNotEmpty:resultVo.errorInfo] ? resultVo.errorInfo : @"网络异常，请重试";
//            [weakSelf showLoading];
        
        // 埋点-单向_请求房间_结果
        [self sendRequestRoomEvent:resultVo enableVh:enableVh];
        
        if (resultVo.errorNo == 0) {

            NSArray *arr = (NSArray*)resultVo.results;

            if (arr.count > 0) {

                NSDictionary *resReslut = [arr objectAtIndex:0];
                
                // bus服务版本
                if ([resReslut isKindOfClass:NSDictionary.class] && resReslut.allKeys.count > 0) {
                    
                    weakSelf.authKey = resReslut[@"auth_key"];
                    weakSelf.loginId = resReslut[@"login_id"];
                    weakSelf.sercurityNo = resReslut[@"sercurity_no"];
                    weakSelf.signalServer = resReslut[@"signal_server"];
                    weakSelf.originalVideoPath = resReslut[@"video_path"];
                    weakSelf.centerServerHost = resReslut[@"center_server_host"];
                    weakSelf.centerServerPort = resReslut[@"center_server_port"];
                    
                    // 获取h5传入的中心服务器地址
                    NSString *targetCenterServer = [weakSelf.configParam getStringWithKey:@"centerServer"];
                    if ([TKStringHelper isNotEmpty:targetCenterServer]) {
                        NSArray *tempArr = [TKStringHelper string:targetCenterServer splitWith:@":"];
                        if (tempArr.count == 2) {
                            weakSelf.centerServerHost = tempArr.firstObject;
                            weakSelf.centerServerPort = tempArr.lastObject;
                        }
                    }
                    
                    TKLogInfo(@"思迪服务器录制日志：录制文件地址%@, 中心服务器地址%@, 房间号%@", weakSelf.originalVideoPath, weakSelf.centerServerHost, weakSelf.sercurityNo);
                    
                    if (callback != nil) {
                        callback(YES, resultVo.errorNo, nil);
                    }
                    
                    return;
                }
            }
        }
        
        
        if (callback != nil && (resultVo.errorNo != 920 &&  // 用户未登陆
                               resultVo.errorNo != 922 &&  // 无效的Token
                               resultVo.errorNo != 924 &&  // 用户未登陆
                                resultVo.errorNo != -999 &&  // 无效的Token
                                resultVo.errorNo != -10009)) {
            TKLogInfo(@"思迪服务器录制日志：申请房间号失败，回调结果");
            callback(NO, resultVo.errorNo, msg);
        }
        
        TKLogInfo(@"思迪服务器录制日志：正在申请房间号结果信息：%@", msg);
    }];
}

/// 根据设备id获取视频房间token，获取成功后运行TChat
/// - Parameter isFirstInitTChat: 是否首次运行TChat
- (void)sendRequestToGetServerRoomTokenByDeviceID:(BOOL)isFirstInitTChat {
    
    TKLogInfo(@"思迪服务器录制日志：正在通过设备ID申请Token，是否首次运行TChat：%i", self.isFirstIntTChat);
    
    NSString *urlStr = [self.configParam getStringWithKey:@"tokenServerUrl"];
    NSMutableDictionary *param = [TKOpenAccountService filterRequestParam:self.configParam];
    param[@"funcNo"] = @"********";
    param[@"deviceId"] = [TKDeviceHelper getDeviceUUID];
    
    //    [self showLoading];
    
    __weak typeof(self) weakSelf = self;
    [self.openAccountService requestServerRoomWithURL:urlStr param:param callBackFunc:^(ResultVo *resultVo) {
        
        NSString *msg = [TKStringHelper isNotEmpty:resultVo.errorInfo] ? resultVo.errorInfo : @"网络异常，请重试";
        //            [weakSelf showLoading];
        
        if (resultVo.errorNo == 0) {
            
            NSArray *arr = (NSArray*)resultVo.results;
            
            if (arr.count > 0) {
                
                NSDictionary *resReslut = [arr objectAtIndex:0];
                
                // bus服务版本
                if ([resReslut isKindOfClass:NSDictionary.class] && resReslut.allKeys.count > 0) {
                    
                    NSString *jwtToken = [resReslut getStringWithKey:@"token"];
    
                    // 更新Token
                    NSMutableDictionary *requestHeaders = (NSMutableDictionary *)[weakSelf.configParam getObjectWithKey:@"requestHeaders"].mutableCopy;
                    requestHeaders = requestHeaders ?: [NSMutableDictionary dictionary];
                    requestHeaders[@"tk-jwt-authorization"] = jwtToken;
                    NSMutableDictionary *requestParam = [NSMutableDictionary dictionaryWithDictionary:weakSelf.configParam];
                    requestParam[@"requestHeaders"] = requestHeaders;
                    weakSelf.configParam = requestParam;
    
                    TKLogInfo(@"思迪服务器录制日志：通过设备ID申请Token成功，运行TChat");
                    // 运行TChat
                    [weakSelf bootDevcie:weakSelf.isFirstIntTChat];
                    return;
                }
            }
        }
        
        TKLogInfo(@"思迪服务器录制日志：通过设备ID申请Token失败");
        // 异常重连
        if ([weakSelf canReconnect]) {
            
            dispatch_async(dispatch_get_main_queue(), ^{
                
                dispatch_async(self->_tchatReconnectQueue, ^{
                    
                    if (weakSelf.isForceStopReconnect) {
                        TKLogInfo(@"思迪服务器录制日志：重连已超时，强制断开，抛弃处理结果");
                        return;
                    }
                    
                    weakSelf.reconnecting = YES;
                    weakSelf.connectReady = NO;
                    TKLogInfo(@"思迪服务器录制日志：TChat已断开");
                    
                    // 创建重连超时定时器
                    if (weakSelf.reconnectTotalTimeOutTimer == nil) [weakSelf createReconnectTotalTimeOutTimer];
                    
                    // 重连
                    weakSelf.currentReconnectNumber++;
                    TKLogInfo(@"思迪服务器录制日志：准备开始第%i次重连，重新申请Token", weakSelf.currentReconnectNumber);
                    // 增加重连间隔，防止网络闪退导致重连次数快速消耗
                    [weakSelf addReconnectionInterval:^(NSTimer *timer) {
                        TKLogInfo(@"思迪服务器录制日志：正式开始第%i次重连,重新申请Token", weakSelf.currentReconnectNumber);
                        [weakSelf sendRequestToGetServerRoomTokenByDeviceID:weakSelf.isFirstIntTChat];
                    }];
                });
            });
            
        } else {
            TKLogInfo(@"思迪服务器录制日志：无须重连，回调外层错误：获取token失败");
            [weakSelf handleBootDeviceFail:@"获取token失败"];
        }
    }];
    
    return;
    
//    __weak typeof(self) weakSelf = self;
//    
//    //1：获取签名加密商户Id，服务器颁发
//    NSString *signAppId = [self.configParam getStringWithKey:@"appId"];
//    
//    //2：签名密钥Key，服务器颁发
//    NSString *signKey = [self.configParam getStringWithKey:@"secret"];
//    
//    //3：根据签名密钥Key生成加密密钥key
//    NSString *encryKey = [[TKSM3Helper sm3Hash:signKey]substringWithRange:NSMakeRange(0, 16)];
//    
//    //5：加密原始业务请求参数
//    NSDictionary *dic = @{@"deviceId" : [TKDeviceHelper getDeviceUUID]};
//    NSString *encryParam = [TKSM4Helper stringWithSM4EncryptString:[TKDataHelper dictionaryToJson:dic] withKey:encryKey];
//    
//    //6: 构建最终发送的签名加密请求包
//    NSMutableDictionary *signReqParam = [NSMutableDictionary dictionary];
//    //请求包体
//    [signReqParam setObject:encryParam forKey:@"data"];
//    //请求流水号
//    [signReqParam setObject: [TKUUIDHelper uuid] forKey:@"request_id"];
////    //开启加密
////    [signReqParam setObject:[NSNumber numberWithBool:true] forKey:@"encrypted"];
//    
//    //7:生成签名串
//    NSString *signature = [TKSM3Helper sm3Hash:[NSString stringWithFormat:@"secret=%@data=%@request_id=%@", signKey, [signReqParam objectForKey:@"data"], [signReqParam objectForKey:@"request_id"]]];
//    
//    //开启加密
//    [signReqParam setObject:signature forKey:@"tk-trans-signature"];
//    
//    //8:生成请求头
//    NSMutableDictionary *headerFieldDic = [NSMutableDictionary dictionary];
//    [headerFieldDic setObject:signAppId forKey:@"tk-trans-merchant-key"];
////    [headerFieldDic setObject:signature forKey:@"tk-trans-signature"];
//    [headerFieldDic setObject:@"128ecd9f12fffc626d20b7079c84ad6d" forKey:@"sa-app-key"];
//    [headerFieldDic setObject:@"application/x-www-form-urlencoded" forKey:@"Content-Type"];
//    [headerFieldDic setObject:@"csc108.com" forKey:@"Referer"];
//    
////    NSString *urlStr = @"https://opt-dev.thinkive.com:15149/kh-stkkh-server/auth/token";
//    NSString *urlStr = [self.configParam getStringWithKey:@"tokenServerUrl"];
//    
//    [TKURLRequestHelper getRequest:urlStr isEncodeUrl:YES paramDic:signReqParam headerFiledDic:headerFieldDic timeout:8.0f completionHandler:^(NSDictionary *result) {
//        
//        NSString *lpMsgBuf=  [[NSString alloc] initWithData:result[@"result"] encoding:NSUTF8StringEncoding];
//        NSDictionary *dic = [TKDataHelper jsonToDictionary:lpMsgBuf];
//        
//        NSDictionary *UnifiedVideoBean = (NSDictionary *)[dic getObjectWithKey:@"UnifiedVideoBean"];
//        if ([UnifiedVideoBean isKindOfClass:NSDictionary.class] && UnifiedVideoBean.allKeys.count > 0) {
//            NSString *resultCode = [UnifiedVideoBean getStringWithKey:@"resultCode"];
////            NSString *messageInfo = [UnifiedVideoBean getStringWithKey:@"messageInfo"];
//            
//            if ([resultCode isEqualToString:@"200"]) {
//                NSString *jwtToken = [UnifiedVideoBean getStringWithKey:@"jwtToken"];
//                
//                // 更新Token
//                NSMutableDictionary *requestHeaders = (NSMutableDictionary *)[weakSelf.configParam getObjectWithKey:@"requestHeaders"].mutableCopy;
//                requestHeaders = requestHeaders ?: [NSMutableDictionary dictionary];
//                requestHeaders[@"tk-jwt-authorization"] = jwtToken;
//                NSMutableDictionary *requestParam = [NSMutableDictionary dictionaryWithDictionary:weakSelf.configParam];
//                requestParam[@"requestHeaders"] = requestHeaders;
//                weakSelf.configParam = requestParam;
//                
//                TKLogInfo(@"思迪服务器录制日志：通过设备ID申请Token成功，运行TChat");
//                // 运行TChat
//                [weakSelf bootDevcie:weakSelf.isFirstIntTChat];
//                return;
//            }
//        }
//        
//        TKLogInfo(@"思迪服务器录制日志：通过设备ID申请Token失败");
//        // 异常重连
//        if ([weakSelf canReconnect]) {
//            
//            dispatch_async(dispatch_get_main_queue(), ^{
//                
//                dispatch_async(self->_tchatReconnectQueue, ^{
//                    
//                    if (weakSelf.isForceStopReconnect) {
//                        TKLogInfo(@"思迪服务器录制日志：重连已超时，强制断开，抛弃处理结果");
//                        return;
//                    }
//                    
//                    weakSelf.reconnecting = YES;
//                    weakSelf.connectReady = NO;
//                    
//                    // 创建重连超时定时器
//                    if (weakSelf.reconnectTotalTimeOutTimer == nil) [weakSelf createReconnectTotalTimeOutTimer];
//                    
//                    // 重连
//                    weakSelf.currentReconnectNumber++;
//                    TKLogInfo(@"思迪服务器录制日志：准备开始第%i次重连，重新申请Token", weakSelf.currentReconnectNumber);
//                    // 增加重连间隔，防止网络闪退导致重连次数快速消耗
//                    [weakSelf addReconnectionInterval:^(NSTimer *timer) {
//                        TKLogInfo(@"思迪服务器录制日志：正式开始第%i次重连,重新申请Token", weakSelf.currentReconnectNumber);
//                        [weakSelf sendRequestToGetServerRoomTokenByDeviceID:weakSelf.isFirstIntTChat];
//                    }];
//                });
//            });
//            
//        } else {
//            TKLogInfo(@"思迪服务器录制日志：无须重连，回调外层错误：获取token失败");
//            [weakSelf handleBootDeviceFail:@"获取token失败"];
//        }
//    }];
}

// 发送网络请求获取录制视频地址
- (void)sendRequestToRequestVideoPathWithUrlStr:(NSString *)urlStr callBack:(void((^)(BOOL success, NSInteger errorNo, NSString *errorMsg)))callback
{
    NSString *url = urlStr;
    NSMutableDictionary *param = [TKOpenAccountService filterRequestParam:self.configParam];
    param[@"funcNo"] = @"********";
    
    //    [self showLoading];
    
    __weak typeof(self) weakSelf = self;
    [self.openAccountService requestServerRoomWithURL:url param:param callBackFunc:^(ResultVo *resultVo) {
        
        NSString *msg = [TKStringHelper isNotEmpty:resultVo.errorInfo] ? resultVo.errorInfo : @"网络异常，请重试";
        //            [weakSelf showLoading];
        
        if (resultVo.errorNo == 0) {
            
            NSArray *arr = (NSArray*)resultVo.results;
            
            if (arr.count > 0) {
                
                NSDictionary *resReslut = [arr objectAtIndex:0];
                
                // bus服务版本
                if ([resReslut isKindOfClass:NSDictionary.class] && resReslut.allKeys.count > 0) {
                    
                    weakSelf.originalVideoPath = [resReslut getStringWithKey:@"video_path"];
                    TKLogInfo(@"思迪服务器录制日志：更新录制文件地址%@", weakSelf.originalVideoPath);
                    
                    if (callback != nil) {
                        callback(YES, resultVo.errorNo, nil);
                    }
                    
                    return;
                }
            }
        }
        
        TKLogInfo(@"思迪服务器录制日志：更新录制文件地址结果信息：%@", msg);
        if (callback != nil && (resultVo.errorNo != 920 &&  // 用户未登陆
                                resultVo.errorNo != 922 &&  // 无效的Token
                                resultVo.errorNo != 924 &&  // 用户未登陆
                                resultVo.errorNo != -999 &&  // 无效的Token
                                resultVo.errorNo != -10009)) {
            TKLogInfo(@"思迪服务器录制日志：更新录制文件地址失败，回调外层");
            callback(NO, resultVo.errorNo, msg);
        }
    }];
}

- (void)sendRequestRoomEvent:(ResultVo *)resultVo enableVh:(BOOL)enableVh {
//    // 埋点-单向_请求房间_成功
//    if (resultVo.errorNo == 0) {
//        NSMutableDictionary *eventDic = [NSMutableDictionary dictionaryWithDictionary:self.configParam];
//        eventDic[@"errorNo"] = [NSString stringWithFormat:@"%i", 0];
//        eventDic[@"event_err"] = resultVo.errorInfo;
//        eventDic[@"liveDetectType"] = [NSString stringWithFormat:@"%i", TKPrivateLiveDetectTypeServer];    // 活体类型
//        TKPrivateEventResult result = TKPrivateEventResultNone;
//        result = resultVo.errorNo == 0 ? TKPrivateEventResultSuccess : result;
//        result = resultVo.errorNo != 0 ? TKPrivateEventResultFail : result;
//        if (self.isLiveDetect) {    // 服务端活体
//            
//            [TKStatisticEventHelper sendEvent:TKPrivateEventLiveDetect
//                                 subEventName:TKPrivateSubEventLiveDetectRequestRoom
//                                     progress:TKPrivateEventProgressNone
//                                       result:result
//                                  orientation:self.isLandscape ? TKPrivateVideoOrientationLandscape : TKPrivateVideoOrientationPortrait
//                              oneWayVideoType:TKPrivateOneWayVideoTypeNone
//                         prepareVideoProgress:TKPrivatePrepareVideoProgressNone
//                                     eventDic:eventDic];
//        } else {    // 服务端录制 | 数字人录制
//            [TKStatisticEventHelper sendEvent:TKPrivateEventOneWayVideo
//                                 subEventName:TKPrivateSubEventOneWayVideoRequestRoom
//                                     progress:TKPrivateEventProgressNone
//                                       result:result
//                                  orientation:self.isLandscape ? TKPrivateVideoOrientationLandscape : TKPrivateVideoOrientationPortrait
//                              oneWayVideoType:enableVh ? TKPrivateOneWayVideoTypeTChatDigitalMan : TKPrivateOneWayVideoTypeTChatSmart
//                         prepareVideoProgress:TKPrivatePrepareVideoProgressNone
//                                     eventDic:eventDic];
//        }
//    }
}

// 初始化TChat并连接服务器
- (void)initTChatWitness
{
    TKLogInfo(@"思迪服务器录制日志：正在初始化TChat");
    
    int initFlag = -1;
    
    @try {
        initFlag = [TChatCore InitSDK];
        TKLogInfo(@"思迪服务器录制日志：初始化TChat结果 %i", initFlag);
    } @catch (NSException *exception) {
        TKLogInfo(@"思迪服务器录制日志：初始化TChat失败，%@", exception);
    } @finally {
        TKLogInfo(@"思迪服务器录制日志：初始化TChat指令已执行");
    }
    
//    [self showLoading];
    
    //直接返回失败的时候就不走回调了
    if (initFlag!=0) {
        TKLogInfo(@"思迪服务器录制日志：初始化TChat失败");
        
//        NSString *errorMsg = [NSString stringWithFormat:@"%@(%d)", @"初始化视频出错", (int)initFlag];
        NSString *errorMsg = [NSString stringWithFormat:@"%@(%@)", @"初始化视频出错", [TKChatErrorConverter converTChatErrorCode:initFlag]];
        [self handleBootDeviceFail:errorMsg];
        return;
    }
    
    [TChatCore shareTChatCore].notifyMsgDelegate = self;
    [TChatCore shareTChatCore].transDataDelegate = self;
    [TChatCore shareTChatCore].textMsgDelegate = self;
//    [TChatCore shareTChatCore].videodataDelegate = self;
//    [TChatCore shareTChatCore].audiodataDelegate = self;
    [TChatCore shareTChatCore].snapshotDelegate = self;
    [TChatCore SetServerAuthPass: [TKStringHelper isNotEmpty:self.configParam[@"serverAuthPass"]] ? self.configParam[@"serverAuthPass"] : @"123456"];
    
    [TChatCore SetSDKOptionInt:TKCC_SO_OUTPUT_AUDIO_DATA :1]; // 设置音频输出设备
    [TChatCore SetSDKOptionInt:TKCC_SO_OUTPUT_VIDEO_DATA :1]; // 设置视频输出设备
//    [TChatCore SetSDKOptionInt:TKCC_SO_OUTPUT_PEER_AUDIO_DATA :1]; // 获取对端音频数据
    
    [TChatCore SetSDKOptionInt:TKCC_SO_LOCAL_VIDEO_FPS : 30]; // 设置视频编码帧率
    [TChatCore SetSDKOptionInt:TKCC_SO_LOCAL_AUDIO_AEC_LEVEL : -1]; // 关闭单向视频中的回声消除，降低CPU
    [TChatCore SetSDKOptionInt:TKCC_SO_RECONNECT : (self.canReconnect ? 1 : 0)]; // 关闭重连
    [TChatCore SetSDKOptionInt:TKCC_SO_VIDEO_RC_MODES : 0]; ///< 视频码率控制模式（整形；默认为1；0：质量优先；1：码率优先）
    [TChatCore SetSDKOptionInt:TKCC_SO_RECONNECT_TIMEOUT : self.maximumReconnectionsDuration]; ///< TChat新增连接中、重连成功事件
    if (self.isLandscape == YES) [TChatCore RotateUserVideo:-1 :90];
    
    CGFloat videoWidth = 480;
    CGFloat videoHeight = 640;
    [TChatCore SetSDKOptionInt:TKCC_SO_LOCAL_VIDEO_WIDTH : [TKStringHelper isNotEmpty:self.configParam[@"videoWidth"]] ? [self.configParam getIntWithKey:@"videoWidth"] : videoWidth]; // 设置视频输出设备
    [TChatCore SetSDKOptionInt:TKCC_SO_LOCAL_VIDEO_HEIGHT : [TKStringHelper isNotEmpty:self.configParam[@"videoHeight"]] ? [self.configParam getIntWithKey:@ "videoHeight"] :videoHeight]; // 设置视频输出设备


    //是否开启监控参数 isEnableMonCollection  0 ：禁用，1:开启，默认0禁用
    if ([self.configParam getIntWithKey:@"isEnableMonCollection"]==1) {
        [TChatCore SetSDKOptionInt:TKCC_SO_MON_COLLECTION :1]; //开启监控上报
    }
    // 获取h5传入的中心服务器地址
    NSString *targetMediaServer = [self.configParam getStringWithKey:@"mediaServer"];
    if ([TKStringHelper isNotEmpty:targetMediaServer]) {
        [TChatCore SetMediaServer:targetMediaServer];
    }

    isTCStartingVideo = NO;
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(becomeTChatActive:) name:UIApplicationDidBecomeActiveNotification object:nil];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(enterBackground) name:UIApplicationDidEnterBackgroundNotification object:nil];
}


/// 连接服务器
- (void)connectServer {
    
    // 开始连接到正式录制，媒体还没就绪.需要等OnUserAudioDataReady回调。长时间没有收到回调，断开录制
    // 开启定时器检查回调情况，没有回调关闭服务
    [self createTimeoutTimer];
    
    TKLogInfo(@"思迪服务器录制日志：开始连接服务器(%@:%i)，添加超时定时器", self.centerServerHost, self.centerServerPort.intValue);
    
    [TChatCore Connect:self.centerServerHost :self.centerServerPort.intValue];
}

/// 断开视频服务器并销毁释放资源
- (void)disconnectServer:(BOOL)needRelease {
    
    self.isDisconnecting = YES;
    
    self.mute = YES;
    [self invalidateTakePicturesTimer]; // 先关闭拍照
    [TChatCore UserVideoControl:-1 :NO];
    [TChatCore UserAudioControl:-1 :NO];
    [TChatCore StopUserVideo:-1];
    [TChatCore UserVideoControl:tcSeatId :NO];
    [TChatCore UserAudioControl:tcSeatId :NO];
    if (self.remoteContentView) [TChatCore StopUserVideo:tcSeatId];

    // 异步处理
    dispatch_async(_tchatReleaseQueue, ^{
        
        if (self.sercurityNo) {
            
            TKLogInfo(@"思迪服务器录制日志：断开TChat服务器");
            
            // 重置
//            [TChatCore EnableSpeaker:NO];
            @try {
                [TChatCore LeaveRoom];
                [TChatCore Logout];
                TKLogInfo(@"思迪服务器录制日志：离开房间或者登出成功");
            } @catch (NSException *exception) {
                TKLogInfo(@"思迪服务器录制日志：离开房间或者登出失败 %@", exception);
            } @finally {
                TKLogInfo(@"思迪服务器录制日志：离开房间和登出指令已执行");
            }
            
            @try {
    //            self.contentView = nil;
    //            self.remoteContentView = nil;
                self.centerServerHost = nil;
                self.centerServerPort = nil;
                self.sercurityNo = nil;
                self.isFirstIntTChat = YES;
                self.isFirstTTS = YES;
                self->isTCStartingVideo = NO;
                self.isSeatAudioDataReady = NO;
                self->tcSeatId = 0;
                self->tcUserId = 0;
                self.requestID = 0;
                TKLogInfo(@"思迪服务器录制日志：重置属性完毕");
            } @catch (NSException *exception) {
                TKLogInfo(@"思迪服务器录制日志：重置属性失败 %@", exception);
            } @finally {
                TKLogInfo(@"思迪服务器录制日志：重置属性完毕已执行");
            }
            
            @try {
                [self invalidateTimeOutTimer];
                [self invalidateTTSTimeOutTimer];
                [[NSNotificationCenter defaultCenter] removeObserver:self];
                TKLogInfo(@"思迪服务器录制日志：断开TChat服务器完毕");
            } @catch (NSException *exception) {
                TKLogInfo(@"思迪服务器录制日志：关闭超时定时器或者TTS超时定时器或者移除监听失败 %@", exception);
            } @finally {
                TKLogInfo(@"思迪服务器录制日志：断开TChat服务器指令已执行");
            }
        }
        
        if (needRelease && self.isFirstReleaseTChat == NO) {
            
            // 销毁重连超时定时器
            [self invalidateReconnectTotalTimeOutTimer];
            
            self.isFirstReleaseTChat = YES;
            
            @try {
                [TChatCore Release];   // 释放资源
                TKLogInfo(@"思迪服务器录制日志：释放TChat完毕");
            } @catch (NSException *exception) {
                TKLogInfo(@"思迪服务器录制日志：释放TChat失败 %@", exception);
            } @finally {
                TKLogInfo(@"思迪服务器录制日志：释放TChat指令已执行");
            }
        }
        
        self.isDisconnecting = NO;
        self.connectReady = NO; // 标记断开
        TKLogInfo(@"思迪服务器录制日志：TChat已断开");
    });
}

- (NSString *)getFullVideoPath {
    
    NSMutableString *tempUrl = [self.configParam[@"url"] mutableCopy];
    tempUrl = [TKStringHelper isEmpty:tempUrl] ? [self.configParam getStringWithKey:@"videoServerUrl"].mutableCopy : tempUrl ;
    if ([TKStringHelper isNotEmpty:[self.configParam getStringWithKey:@"videoPreviewUrl"]]) {
        tempUrl = [[self.configParam getStringWithKey:@"videoPreviewUrl"] mutableCopy];
        // H5传入
        if ([tempUrl containsString:@"?"]) {
            [tempUrl appendFormat:@"&function=play&video_path=%@", _finalVideoPath];
        } else {
            [tempUrl appendFormat:@"?function=play&video_path=%@", _finalVideoPath];
        }
        
    } else { // SDK处理
        NSRange range = [tempUrl rangeOfString:@"servlet"];
        if (range.location != NSNotFound) {
            tempUrl = [[tempUrl substringToIndex:range.location + range.length] mutableCopy];
        }
        
        tempUrl = [NSMutableString stringWithFormat:@"%@/VideoPlaytAction?function=play&video_path=%@", tempUrl, _finalVideoPath];
    }
    
    NSDictionary *dic = self.configParam[@"requestHeaders"];
    if ([dic isKindOfClass:NSDictionary.class]) {
        if ([dic isKindOfClass:NSString.class]) dic = [TKDataHelper jsonToDictionary:(NSString *)dic];
        
        [dic enumerateKeysAndObjectsUsingBlock:^(NSString *key, id obj, BOOL * _Nonnull stop) {
            
            [tempUrl appendString:[NSString stringWithFormat:@"&%@=%@", key, obj]];
        }];
    }
    
    return [TKStringHelper encodeURL:tempUrl];
}

/// 开始录制视频
- (void)startRecord {
    
//    NSLog(@"请求开始录制  start_record");
    
    NSDictionary *dic = @{
        @"type" : @"start_record",
        @"orientation" : self.isLandscape ? @(1) : @(0),  // 0为竖屏  1为横屏
        @"path" : [TKStringHelper isNotEmpty:_originalVideoPath] ? _originalVideoPath : @""};
    [self sendTransBufferDataWith:dic];
}

/// 停止录制视频
- (void)stopRecord {
    
//    NSLog(@"请求停止录制  stop_record");
    
    NSDictionary *dic = @{
        @"type" : @"stop_record"};
    [self sendTransBufferDataWith:dic];
}

/// 语音播报
/// @param text 播报的内容
- (void)syntheticAndPlay:(NSString *)text tipSpeed:(nonnull NSString *)tipSpeed {
    
    TKLogInfo(@"语音播报内容%@", text);
    
//    NSLog(@"请求播报  start_tts");
    if ([TKStringHelper isEmpty:text]) {
        [self ttsStopCallBack];
        return;
    }
    
    NSDictionary *dic = @{
        @"type" : @"start_tts",
        @"text" : [TKStringHelper isNotEmpty:text] ? [TKBase64Helper stringWithEncodeBase64String:text] : @"",
        @"role": @(0), // 播报角色；0为女声，1位男声
        @"request_id": @(++self.requestID)}; // request_id 请求ID；同开始播放本地视频（虚拟视频）接口
    [self sendTransBufferDataWith:dic];
    
    [self createTTSTimeOutTimer:text.length * 0.3 / self.rate + 7];
}

// 播报多段语音
- (void)syntheticAndPlayContents:(NSArray *)texts tipSpeed:(nonnull NSString *)tipSpeed {
    TKLogDebug(@"思迪语音合成调试:准备播报%i段语音", (int)texts.count);
    self.currentIndex = 0;
    self.synthesisArray = texts;
    
    TKLogDebug(@"思迪语音合成调试:语音播放开始, text = %@", texts[self.currentIndex]);
    [self syntheticAndPlay:texts[self.currentIndex] tipSpeed:tipSpeed];
}

/// 停止语音播报
- (void)stopSyntheticAndPlay {
    
//    NSLog(@"请求停止播报  stop_tts");
    
    NSDictionary *dic = @{
        @"type" : @"stop_tts"};
    [self sendTransBufferDataWith:dic];
}

/// 虚拟人语音播报
/// @param text 播报的内容或播报的文件
/// @param fileSource 文件来源，1:服务器视频文件，2:视频流
- (void)virtualSyntheticAndPlay:(NSString *)text fileName:(NSString *)fileName fileSource:(NSString *)fileSource {
    [self invalidateTTSTimeOutTimer];
    
    if ([fileSource isEqualToString:@"0"]) {
        [self syntheticAndPlay:text tipSpeed:@""];
    } else if ([fileSource isEqualToString:@"1"]) {
        [self virtualPlayVideo:fileName];
    } else if ([fileSource isEqualToString:@"2"]) {
        [self virtualSyntheticAndPlay:text];
    } else {
        // 直接回调播放完毕
        [self ttsStopCallBack];
    }
}

/// 停止虚拟人语音播报
- (void)stopVirtualSyntheticAndPlay {
    [self stopSyntheticAndPlay];    // 停止普通tts合成播报
    [self stopVirtualSynthetic];    // 停止虚拟人合成播报
    [self stopVirtualPlayVideo];    // 停止虚拟人播放云端固定音频
}

/// 虚拟人语音合成
/// @param text 播报的内容
- (void)virtualSyntheticAndPlay:(NSString *)text {
    TKLogInfo(@"语音播报内容%@", text);
    
    //    NSLog(@"请求虚拟人播报  start_vh");
    if ([TKStringHelper isEmpty:text]) {
        [self ttsStopCallBack];
        return;
    }
    
    NSDictionary *dic = @{
        @"type" : @"start_vh",
        @"text" : [TKStringHelper isNotEmpty:text] ? [TKBase64Helper stringWithEncodeBase64String:text] : @"",
        @"request_id": @(++self.requestID)// request_id 请求ID；同开始播放本地视频（虚拟视频）接口
        
    };
    [self sendTransBufferDataWith:dic];
    
    [self createTTSTimeOutTimer:text.length * 0.3 / self.rate + 7];
}

/// 停止虚拟人语音合成
- (void)stopVirtualSynthetic {
    
//    NSLog(@"请求停止虚拟人播报  stop_vh");
    
    NSDictionary *dic = @{
        @"type" : @"stop_vh"};
    [self sendTransBufferDataWith:dic];
}

/// 虚拟人播报静态文件
/// @param text 播报的内容
- (void)virtualPlayVideo:(NSString *)fileName {
//    NSLog(@"请求播放虚拟人音频  start_play_video");
    
    NSDictionary *dic = @{
        @"type" : @"start_play_video",
        @"name" : [NSString stringWithFormat:@"%@", fileName],
        @"request_id": @(++self.requestID)// request_id 请求ID；同开始播放本地视频（虚拟视频）接口;
    };
    [self sendTransBufferDataWith:dic];
}

/// 停止虚拟人播报静态文件
- (void)stopVirtualPlayVideo {
//    NSLog(@"请求停止播放虚拟人音频  stop_play_video");
    
    NSDictionary *dic = @{
        @"type" : @"stop_play_video"};
    [self sendTransBufferDataWith:dic];
}

/// 开始语音识别
- (void)startRecognize {
    
//    NSLog(@"请求开始识别  start_asr");
    
    NSDictionary *dic = @{
        @"type" : @"start_asr",
        @"request_id": @(++self.requestID)// request_id 请求ID；同开始播放本地视频（虚拟视频）接口;
    };
    [self sendTransBufferDataWith:dic];
}

/// 停止语音识别
- (void)stopRecognize {
//    NSLog(@"请求停止识别  stop_asr");
    
    NSDictionary *dic = @{
        @"type" : @"stop_asr"};
    [self sendTransBufferDataWith:dic];
}

/// 播放音频
/// @param flag 播放音频索引。和后端保持一致
- (void)playVideo:(NSString *)flag {
//    NSLog(@"请求播放音频  playVideo");
    
    NSDictionary *dic = @{
        @"type" : @"start_play",
        @"flag" : [NSString stringWithFormat:@"%@", flag],
        @"request_id": @(++self.requestID)// request_id 请求ID；同开始播放本地视频（虚拟视频）接口;
    };
    [self sendTransBufferDataWith:dic];
}

/// 停止播放音频
- (void)stopPlayVideo {
//    NSLog(@"请求停止播放音频  stopPlayVideo");
    
    NSDictionary *dic = @{
        @"type" : @"stop_play"};
    [self sendTransBufferDataWith:dic];
}

/// 设置静音
- (void)enableMute:(BOOL)mute {
//    NSLog(@"请求停止播放音频  stopPlayVideo");
    
    NSDictionary *dic = @{
        @"type" : @"enable_mute",
        @"mute" : @(mute),  // 静音标志：0表示收音，1表示静音
        @"request_id": @(++self.requestID)
    };
    [self sendTransBufferDataWith:dic];
    
    // 以下指令会自动实现静音、收音功能
    // 静音：播报文本、播报音频、播放视频
    // 收音：开始录制、开始识别、外部驱动（上层主动设置）
}

// 获取视频第一帧
- (UIImage*)getVideoPreViewImage:(NSURL *)path
{
    TKLogInfo(@"思迪播放器日志: 获取视频第一帧图片， path = %@", path);
    
    AVURLAsset *asset = [[AVURLAsset alloc] initWithURL:path options:nil];
    AVAssetImageGenerator *assetGen = [[AVAssetImageGenerator alloc] initWithAsset:asset];
    
    assetGen.appliesPreferredTrackTransform = YES;
    CMTime time = CMTimeMakeWithSeconds(0.0, 600);
    NSError *error = nil;
    CMTime actualTime;
    CGImageRef image = [assetGen copyCGImageAtTime:time actualTime:&actualTime error:&error];
    UIImage *videoImage = [[UIImage alloc] initWithCGImage:image];
    CGImageRelease(image);
    
    return videoImage;
}

// 获取本地视频第一帧
-(UIImage *)getLocalVideoPreViewImage:(NSString *)filePath
{
    TKLogInfo(@"思迪播放器日志: 获取本地视频第一帧图片， path = %@", filePath);
    
    NSURL *sourceURL = [NSURL fileURLWithPath:filePath];
    AVAsset *asset = [AVAsset assetWithURL:sourceURL];
    AVAssetImageGenerator *imageGenerator = [[AVAssetImageGenerator alloc]initWithAsset:asset];
    imageGenerator.appliesPreferredTrackTransform = YES;
    CMTime time = CMTimeMake(0, 1);
    NSError *error;
    CGImageRef imageRef = [imageGenerator copyCGImageAtTime:time actualTime:NULL error:&error];
    UIImage *thumbnail = [UIImage imageWithCGImage:imageRef];
    CGImageRelease(imageRef);  // CGImageRef won't be released by ARC
    return thumbnail;
}

/// 开始抓取图片
- (void)startFetchPicture {
    [self invalidateTakePicturesTimer];
    [self createAndRunTakePicturesTimer];
}

/// 停止抓取图片
- (void)stopFetchPicture {
    [self invalidateTakePicturesTimer];
}


//切换摄像头
-(void)tkSwitchVideoCamera:(BOOL)isFrontCamera{
    if (isFrontCamera) {
        // 改成后置摄像头
        NSString *camName = [TChatCore GetDeviceName:TKCC_DT_VIDEOCAPTURE :0];
        [TChatCore SelectDevice:TKCC_DT_VIDEOCAPTURE :camName];
        [TChatCore ShowUserVideo:-1 :self.contentView :NO];    // 前置摄像头镜像，后置不用镜像
        TKLogInfo(@"切换摄像头为:%@",camName);
        if (self.isLandscape == YES) [TChatCore RotateUserVideo:-1 :270];
    }else{
        // 改成前置置摄像头
        NSString *camName = [TChatCore GetDeviceName:TKCC_DT_VIDEOCAPTURE :1];
        [TChatCore SelectDevice:TKCC_DT_VIDEOCAPTURE :camName];
        [TChatCore ShowUserVideo:-1 :self.contentView :YES];    // 前置摄像头镜像，后置不用镜像
        TKLogInfo(@"切换摄像头为:%@",camName);
        if (self.isLandscape == YES) [TChatCore RotateUserVideo:-1 :90];
    }
}

/// 重连服务
- (void)reconnectServer {
    
    if (self.reconnecting) {
        TKLogInfo(@"思迪服务器录制日志：正在重连，抛弃外层的重连指令");
        return;
    }
    
    self.isForceStopReconnect = NO;
    self.currentReconnectNumber = 0;
    [self reconnect];
}

- (void)openLocalDevice:(BOOL)isOpen {
//    if (self.delayOpenLoaclDevice == YES) {
    self.localDeviceIsOpen = isOpen;
    
    TKLogInfo(@"思迪服务器录制日志：本地麦克风、摄像头即将%@", isOpen ? @"打开" : @"关闭");
    
    if (isOpen) {
        
        NSString *videoDeviceName = [TChatCore GetDeviceName:TKCC_DT_VIDEOCAPTURE :1];
        [TChatCore SelectDevice:TKCC_DT_VIDEOCAPTURE :videoDeviceName];
        [TChatCore UserVideoControl:-1 :NO];
        [TChatCore UserVideoControl:-1 :YES];
        [TChatCore ShowUserVideo:-1 :self.contentView :self.isFrontCamera];
        
        [TChatCore UserAudioControl:-1 :NO];
        [TChatCore UserAudioControl:-1 :YES];
        [TChatCore SetSDKOptionInt:TKCC_SO_MUTE_SELF_AUDIO :0]; // 记录录音数据，往服务器发送
        
        // 创建拍照定时器,获取在框检测图片
        [self startFetchPicture];
    } else {
        if (self.reconnecting) {
            TKLogInfo(@"思迪服务器录制日志：正在重连，抛弃外层的关闭本地设备指令");
            return;
        }
        
//        [TChatCore UserSpeakerControl:NO];
        [TChatCore UserAudioControl:-1 :NO];
        [TChatCore SetSDKOptionInt:TKCC_SO_MUTE_SELF_AUDIO :1]; // 清空录音数据，不往服务器发送
        
        [TChatCore StopUserVideo:-1];
        [TChatCore UserVideoControl:-1 : NO];
        
        // 停止抓取图片
        [self stopFetchPicture];
    }
//    }
}

#pragma mark - Private Selector
- (void)sendTextMessage:(NSString *)message
{
    [TChatCore SendTextMessage:tcSeatId :NO :message];
}

- (void)sendTransBufferDataWith:(NSDictionary *)dic
{
    if (self.connectReady == NO) {
        TKLogInfo(@"思迪服务器录制日志：TChat未就绪不发送任何指令");
        return;  // 重连过程中不发送任何指令
    }
    
    NSString *cmd = [NSString stringWithFormat:@"h5cmd@%@", [TKDataHelper objectToJson:dic]];
    [TChatCore TransBuffer:tcSeatId :[cmd dataUsingEncoding:NSUTF8StringEncoding]];
}

/**
 *  <AUTHOR> 2019年09月10日14:03:52
 *  App从后台返回前台
 *  @param notif
 */
- (void)becomeTChatActive:(NSNotification *)notif{
    
//    NSLog(@"从后台回到前台时间%.2f", -[self.lastEnterBackgroundDate timeIntervalSinceNow]);
    // 退到后台时长超过1分钟后，关闭视频
    if (-[self.lastEnterBackgroundDate timeIntervalSinceNow] > 60) {
        dispatch_async(dispatch_get_main_queue(), ^{

            if (self.delegate && [self.delegate respondsToSelector:@selector(TChatBecomeActive)]) {
                [self.delegate TChatBecomeActive];
            }
        });
        return;
    }
    
    // 已经初始化TChat完毕才需要处理。初始化完成之前，收到回调不需要处理
    if (self.isFirstIntTChat == NO && self.delayOpenLoaclDevice == NO) {
        TKLogInfo(@"思迪服务器录制日志：回到前台，重新渲染本地画面");
        
        [TChatCore UserVideoControl: -1 : NO];
        [TChatCore UserVideoControl: -1 : YES];
        
        [TChatCore ShowUserVideo:-1 :self.contentView :self.isFrontCamera];
    }
}

- (void)enterBackground
{
    self.lastEnterBackgroundDate = [NSDate new];
}

// 启动TChat视频
- (void)openRemoteDevice {
    // 控制虚拟坐席的音视频
    [TChatCore UserVideoControl:tcSeatId :YES];
    [TChatCore UserAudioControl:tcSeatId :YES];
}

- (void)ttsOutTimeBreakLink:(NSTimer *)timer
{
    if (self.isReceiveStopTTSMsg == NO) {
        
        NSDictionary *userInfo = timer.userInfo;
        NSTimeInterval time = (NSTimeInterval)[userInfo[@"time"] doubleValue];
        TKLogInfo(@"思迪服务器录制日志：请求播报%.2fs没收到播报结束回调，断开视频", time);
        
        dispatch_async(dispatch_get_main_queue(), ^{
            
            [self invalidateTTSTimeOutTimer];
            
            [self recordErrorCallBack:@"视频录制异常退出(语音播报超时)"];
        });
    } else {
        TKLogInfo(@"思迪服务器录制日志：tts超时了，但是已收到StopTTSMsg");
    }
}


- (void)seatAudioReadyOutTimeBreakLink:(NSTimer *)timer
{
    if (self.isSeatAudioDataReady == NO) {
        TKLogInfo(@"思迪服务器录制日志：进入房间后10s没收到就绪回调，断开视频");

        NSString *errorMsg = @"初始化视频出错";
        [self handleBootDeviceFail:[NSString stringWithFormat:@"%@(超时)", errorMsg]];
    } else {
        TKLogInfo(@"思迪服务器录制日志：就绪回调超时了，但是已收到isSeatAudioDataReady");
    }
}

- (void)handleBootDeviceFail:(NSString *)errorMsg
{
    self.reconnecting = NO;
    
    if ([self canReconnect] == NO || self.isForceStopReconnect == YES) {
        dispatch_async(dispatch_get_main_queue(), ^{

    //            [self hideLoading];
            
            TKLogInfo(@"思迪服务器录制日志：运行TChat失败，回调外层：%@", errorMsg);
            
            [self invalidateTimeOutTimer];
            // 销毁重连超时定时器
            [self invalidateReconnectTotalTimeOutTimer];

            if (self.delegate && [self.delegate respondsToSelector:@selector(connectServerRoomDidComplete:errorMsg:)]) {
                [self.delegate connectServerRoomDidComplete:NO errorMsg:errorMsg];
            }

        });
    } else {
        
        [self reconnect];
    }
}

- (void)takePictures
{
    [TChatCore Snapshot:-1 :TKCC_SNAPSHOT_FLAGS_DATA :0 :nil];
//    [TChatCore Snapshot:tcSeatId :TKCC_SNAPSHOT_FLAGS_DATA :0 :@""];
    
//    NSLog(@"takePictures result = %i", result);
}

- (void)invalidateTakePicturesTimer
{
    if (self.takePicturesTimer) {
        TKLogInfo(@"思迪服务器录制日志：销毁拍照定时器");
        [self.takePicturesTimer invalidate];
        self.takePicturesTimer = nil;
    }
}

- (void)invalidateTimeOutTimer
{
    if (self.timeoutTimer) {
        TKLogInfo(@"思迪服务器录制日志：销毁启动超时定时器");
        [self.timeoutTimer invalidate];
        self.timeoutTimer = nil;
    }
}

- (void)invalidateTTSTimeOutTimer
{
    if (self.TTSTimeoutTimer) {
        TKLogInfo(@"思迪服务器录制日志：销毁TTS超时定时器");
        [self.TTSTimeoutTimer invalidate];
        self.TTSTimeoutTimer = nil;
    }
}

- (void)createAndRunTakePicturesTimer
{
    if (self.disableFetchPicture == NO) {
        
        TKLogInfo(@"思迪服务器录制日志：创建拍照定时器");
        
        self.takePicturesTimer = [NSTimer timerWithTimeInterval:0.2 target:self selector:@selector(takePictures) userInfo:nil repeats:YES];
        [[NSRunLoop mainRunLoop] addTimer:self.takePicturesTimer forMode:NSRunLoopCommonModes];
    }
}

- (void)createTimeoutTimer
{
    [self invalidateTimeOutTimer];
    
    TKLogInfo(@"思迪服务器录制日志：创建启动超时定时器");
    self.timeoutTimer = [NSTimer timerWithTimeInterval:10 target:self selector:@selector(seatAudioReadyOutTimeBreakLink:) userInfo:nil repeats:NO];
    [[NSRunLoop mainRunLoop] addTimer:self.timeoutTimer forMode:NSRunLoopCommonModes];
}

- (void)createTTSTimeOutTimer:(NSTimeInterval)time
{
    // 第一次tts做超时处理
//    if (self.isFirstTTS) {
//        self.isFirstTTS = NO;
    
        [self invalidateTTSTimeOutTimer];
    
        self.isReceiveStopTTSMsg = NO;
        
        // 从tts播报开始记录，没有收到结束回调就断开视频
        self.TTSTimeoutTimer = [NSTimer timerWithTimeInterval:time target:self selector:@selector(ttsOutTimeBreakLink:) userInfo:@{@"time" : @(time)} repeats:NO];
        [[NSRunLoop mainRunLoop] addTimer:self.TTSTimeoutTimer forMode:NSRunLoopCommonModes];
        
//        TKLogInfo(@"思迪服务器录制日志：首次TTS播报，增加超时定时器");
        TKLogInfo(@"思迪服务器录制日志：TTS播报，增加超时定时器");
//    }
}

- (void)invalidateReconnectRetryTimer
{
    if (self.reconnectRetryTimer) {
        TKLogInfo(@"思迪服务器录制日志：销毁重连检查定时器");
        
        [self.reconnectRetryTimer invalidate];
        self.reconnectRetryTimer = nil;
    }
}

- (void)createReconnectRetryTimer
{
    [self invalidateReconnectRetryTimer];
    
    TKLogInfo(@"思迪服务器录制日志：创建重连检查定时器");
    self.reconnectRetryTimer = [NSTimer timerWithTimeInterval:2 target:self selector:@selector(reconnect) userInfo:nil repeats:YES];
    [[NSRunLoop mainRunLoop] addTimer:self.reconnectRetryTimer forMode:NSRunLoopCommonModes];
}

- (void)invalidateReconnectTotalTimeOutTimer
{
    if (self.reconnectTotalTimeOutTimer) {
        TKLogInfo(@"思迪服务器录制日志：销毁重连总时长超时定时器");
        
        [self.reconnectTotalTimeOutTimer invalidate];
        self.reconnectTotalTimeOutTimer = nil;
    }
}

- (void)createReconnectTotalTimeOutTimer
{
    [self invalidateReconnectTotalTimeOutTimer];
    
    self.isForceStopReconnect = NO;
    
    if (self.maximumReconnectionsDuration <=0 ) return;
    
    NSTimeInterval time = self.maximumReconnectionsDuration;
    
    TKLogInfo(@"思迪服务器录制日志：创建重连总时长超时定时器，总时长%f", time);
    self.reconnectTotalTimeOutTimer = [NSTimer timerWithTimeInterval:time target:self selector:@selector(reconnectTotalTimeOut:) userInfo:@{@"time" : @(time)} repeats:YES];
    [[NSRunLoop mainRunLoop] addTimer:self.reconnectTotalTimeOutTimer forMode:NSRunLoopCommonModes];
}

- (void)reconnectTotalTimeOut:(NSTimer *)timer
{
    NSDictionary *userInfo = timer.userInfo;
    NSTimeInterval time = (NSTimeInterval)[userInfo[@"time"] doubleValue];
    TKLogInfo(@"思迪服务器录制日志：%.2fs没有重连成功，断开视频", time);
    
    self.isForceStopReconnect = YES;
    [self invalidateReconnectionIntervalTimer];
    [self.openAccountService cancelAllRequest];
    
    dispatch_async(dispatch_get_main_queue(), ^{
        
        [self invalidateReconnectTotalTimeOutTimer];
        
        [self handleBootDeviceFail:@"初始化视频出错(重连超时)"];
    });
}

- (void)reconnect
{
    __weak typeof(self) weakSelf = self;
//    dispatch_async(dispatch_get_main_queue(), ^{
//        
//        BOOL isHidden = !self.contentView.window && !self.remoteContentView.window;
        dispatch_async(self->_tchatReconnectQueue, ^{
            
            if (weakSelf.isForceStopReconnect) {
                TKLogInfo(@"思迪服务器录制日志：重连已超时，强制断开，抛弃处理结果");
                return;
            }
            
            // 正在重连不予处理
            if (weakSelf.reconnecting) {
                TKLogInfo(@"思迪服务器录制日志：正在重连不予处理");
                return;
            }
            
//            // UI都不在显示时，不予重连
//            if (isHidden) {
//                TKLogInfo(@"思迪服务器录制日志：UI都不在显示时，暂缓重连，创建定时器刷新页面");
//                [self createReconnectRetryTimer];
//                return;
//            }
//            
//            // 页面已显示，销毁定时器，开始重连
//            [self invalidateReconnectRetryTimer];
            
            weakSelf.reconnecting = YES;
            weakSelf.connectReady = NO;
            TKLogInfo(@"思迪服务器录制日志：TChat已断开");
            
            weakSelf.currentReconnectNumber++;  // 重连次数加1
            TKLogInfo(@"思迪服务器录制日志：准备开始第%i次重连", self.currentReconnectNumber);
            
            // 创建重连超时定时器
            if (weakSelf.reconnectTotalTimeOutTimer == nil) [weakSelf createReconnectTotalTimeOutTimer];
            
            if (weakSelf.currentReconnectNumber == 1) {
                TKLogInfo(@"思迪服务器录制日志：首次重连时更新UI");
                
                dispatch_async(dispatch_get_main_queue(), ^{
                    if (weakSelf.delegate && [weakSelf.delegate respondsToSelector:@selector(connectServerRoomDidStart)]) {
                        [weakSelf.delegate connectServerRoomDidStart];
                    }
                });
            }
            
            // 增加重连间隔，防止网络闪退导致重连次数快速消耗
            [weakSelf addReconnectionInterval:^(NSTimer *timer) {
                TKLogInfo(@"思迪服务器录制日志：正式开始第%i次重连", weakSelf.currentReconnectNumber);
                [weakSelf disconnectServer:NO]; // 先断开
                [weakSelf bootDevcie:NO]; // 重连
            }];
        });
//    });
}

- (void)addReconnectionInterval:(void (NS_SWIFT_SENDABLE ^)(NSTimer *timer))block
{
    int reconnectionInterval = ceilf(pow(2, self.currentReconnectNumber - 1) * 0.5);
    reconnectionInterval = reconnectionInterval > 4 ? 4 : reconnectionInterval;
    TKLogInfo(@"思迪服务器录制日志：增加重连间隔时间%i", reconnectionInterval);
    
    [self createReconnectionIntervalTimer:reconnectionInterval block:block];
}

- (BOOL)canReconnect
{
    return self.maximumReconnections - self.currentReconnectNumber > 0;
}

- (void)invalidateReconnectionIntervalTimer
{
    if (self.reconnectIntervalTimer) {
        TKLogInfo(@"思迪服务器录制日志：销毁重连间隔定时器");
        
        [self.reconnectIntervalTimer invalidate];
        self.reconnectIntervalTimer = nil;
    }
}

- (void)createReconnectionIntervalTimer:(NSTimeInterval)time block:(void (NS_SWIFT_SENDABLE ^)(NSTimer *timer))block
{
    [self invalidateReconnectionIntervalTimer];
    
    TKLogInfo(@"思迪服务器录制日志：创建重连间隔定时器");
    self.reconnectIntervalTimer = [NSTimer timerWithTimeInterval:time repeats:NO block:^(NSTimer * _Nonnull timer) {
        TKLogInfo(@"思迪服务器录制日志：增加的重连间隔时间已到，执行block");
        if (block) block(timer);
    }];
    [[NSRunLoop mainRunLoop] addTimer:self.reconnectIntervalTimer forMode:NSRunLoopCommonModes];
}

#pragma mark - TKCCNotifyMessageDelegate
// 连接服务器消息
- (void)OnConnect: (BOOL)success : (int)errorCode
{
    TKLogInfo(@"思迪服务器录制日志：连接服务器%@ errorcode:%d", success == YES ? @"成功" : @"失败" ,errorCode);
     
    if (self.isForceStopReconnect) {
        TKLogInfo(@"思迪服务器录制日志：重连已超时，强制断开，抛弃处理结果");
        return;
    }
    
    if(errorCode == 0){//连接视频服务器成功
        
        NSString *loginId = [NSString stringWithFormat:@"%@", self.loginId];
        [TChatCore Login:loginId : @""];
        
    }else{
        
        NSString *errorMsg = @"初始化视频出错";
//        [self handleBootDeviceFail:[NSString stringWithFormat:@"%@(%d)", errorMsg, errorCode]];
        [self handleBootDeviceFail:[NSString stringWithFormat:@"%@(%@)", errorMsg, [TKChatErrorConverter converTChatErrorCode:errorCode]]];
    }
}

// 用户登陆消息
- (void)OnLogin: (int)userId : (int)errorCode
{
    TKLogInfo(@"思迪服务器录制日志：userId: %i 登录%@ errorcode:%d", userId, errorCode == 0 ? @"成功" : @"失败" ,errorCode);
    
    if (self.isForceStopReconnect) {
        TKLogInfo(@"思迪服务器录制日志：重连已超时，强制断开，抛弃处理结果");
        return;
    }
    
    if(errorCode == 0){//登录成功
        
        tcUserId = userId;
        [TChatCore EnterRoom:[self.sercurityNo intValue] : self.authKey];
//        [TChatCore EnterRoom:40000 : @"25116bc3ff4910a96548def19b9c60e7"];
    }else{
        
        NSString *errorMsg = [NSString stringWithFormat:@"初始化视频出错(%@)", [TKChatErrorConverter converTChatErrorCode:errorCode]];
        [self handleBootDeviceFail:errorMsg];
    }
}
// 用户进入房间消息
- (void) OnEnterRoom: (int)roomId : (int)errorCode
{
    TKLogInfo(@"思迪服务器录制日志：进入房间(%i)%@ errorcode:%d", roomId, errorCode == 0 ? @"成功" : @"失败" ,errorCode);
    
    if (self.isForceStopReconnect) {
        TKLogInfo(@"思迪服务器录制日志：重连已超时，强制断开，抛弃处理结果");
        return;
    }
    
    if (errorCode == 0) {//进入房间成功
                 
        // 音频设备
        if (self.disableMicrophone) {
            [TChatCore SetSDKOptionInt:TKCC_SO_ENABLE_NETEQ:0]; // 关闭neteq
            [TChatCore SelectDevice:TKCC_DT_AUDIOCAPTURE :@""]; // 初始化虚拟音频设备，不录制声音
        }
        else {
//            [TChatCore SetSDKOptionInt:TKCC_SO_ENABLE_NETEQ:1]; // 关闭neteq
            [TChatCore SetSDKOptionInt:TKCC_SO_ENABLE_NETEQ:0]; // 关闭neteq
        }
        
        // 马上开启设备
        if (self.delayOpenLoaclDevice == NO) {
            // 视频设备
            NSString *videoDeviceName = [TChatCore GetDeviceName:TKCC_DT_VIDEOCAPTURE :1];
            [TChatCore SelectDevice:TKCC_DT_VIDEOCAPTURE :videoDeviceName];
            [TChatCore UserVideoControl:-1 : YES];
            
            [TChatCore UserAudioControl:-1 :YES];
        } else {
            // 该SDK仅在数字人TChat SDK中支持
//            [TChatCore UserSpeakerControl:YES];
        }
//            self.isControlAudio = YES; // 标记控制了音频设备
        
        [TChatCore EnableSpeaker:YES];
        
    }else{
        
        NSString *errorMsg = @"初始化视频出错";
        errorMsg = [NSString stringWithFormat:@"%@(%@)", errorMsg, [TKChatErrorConverter converTChatErrorCode:errorCode]];
        [self handleBootDeviceFail:errorMsg];
    }
}

//// 初始化通道
- (void) OnInitChannel: (int)errorCode
{
    TKLogInfo(@"思迪服务器录制日志：TChat初始化完成，错误码%i", errorCode);
    
    if (self.isForceStopReconnect) {
        TKLogInfo(@"思迪服务器录制日志：重连已超时，强制断开，抛弃处理结果");
        return;
    }
    
    // 可能同时返回两端都是500错误。需要加锁。否则同时disconnect会闪退
    @synchronized (self) {
        if (self.isFirstIntTChat == YES) {

            self.isFirstIntTChat = NO;
            
            if (errorCode != 0) {
                NSString *errorMsg = @"初始化视频出错";
                errorMsg = [NSString stringWithFormat:@"%@(%@)", errorMsg, [TKChatErrorConverter converTChatErrorCode:errorCode]];
                [self handleBootDeviceFail:errorMsg];
            }
        }
    }
}

// 视频数据就绪
- (void) OnUserVideoDataReady: (int)userId : (int)dataInfo{
    
    int ret = dataInfo & 0x0000FFFF;
    TKLogInfo(@"思迪服务器录制日志：视频数据 userId = %i, dataInfo = %i, ret = %i", userId, dataInfo, ret);
    
    //自己视频画面调整占用屏幕大小，以便展示出底层的白色背景
    float  width = (dataInfo & 0xFFFF0000) >> 16;
    float  height   = (dataInfo & 0x0000FFFF);
    TKLogInfo(@"userId = %i，视频调整前宽度：%f|视频调整前高度：%f", userId, width,height);
    
    if (self.isForceStopReconnect) {
        TKLogInfo(@"思迪服务器录制日志：重连已超时，强制断开，抛弃处理结果");
        return;
    }
    
    dispatch_async(dispatch_get_main_queue(), ^{
        if (userId == -1) {
            
            CGPoint center = self.contentView.center;

            CGSize videoSize = [self computeViewSizeWithPresentSize:CGSizeMake(width, height) viewOriginSize:CGSizeMake(self.contentView.TKWidth, self.contentView.TKHeight) scalingMode:self.scalingMode];
            
            TKLogInfo(@"userId = %i，视频调整后宽度：%f|视频调整后高度：%f", userId, videoSize.width, videoSize.height);
            
            [self.contentView setFrameWidth:videoSize.width];
            [self.contentView setFrameHeight:videoSize.height];
            self.contentView.center = center;
            
            if(self.contentViewY!=0){
                self.contentView.TKTop=self.contentViewY;
            }
            
            // 马上渲染
            if (self.delayOpenLoaclDevice == NO) {
                
                [TChatCore ShowUserVideo:-1 :self.contentView :self.isFrontCamera];
                
                // 创建拍照定时器,获取在框检测图片
                [self startFetchPicture];
            }
            
            if (self.delegate && [self.delegate respondsToSelector:@selector(contentViewVideoDataReady)]) {
                [self.delegate contentViewVideoDataReady];
            }
    
        } else if (userId == self->tcSeatId) {

            CGSize videoSize = [self computeViewSizeWithPresentSize:CGSizeMake(width, height) viewOriginSize:CGSizeMake(self.remoteContentView.TKWidth, self.remoteContentView.TKHeight) scalingMode:self.remoteScalingMode];
            TKLogInfo(@"userId = %i，视频调整后宽度：%f|视频调整后高度：%f", userId, videoSize.width, videoSize.height);
            
            [self.remoteContentView setFrameWidth:videoSize.width];
            [self.remoteContentView setFrameHeight:videoSize.height];
    //        TKLogInfo(@"视频调整后宽度：%f|视频调整后高度：%f",videoWidth,videoHeight);
            self.remoteContentView.TKLeft = (UIApplication.sharedApplication.keyWindow.TKWidth - videoSize.width) * 0.5;
            
            // 设置位置
            if (videoSize.height >= UISCREEN_HEIGHT * 0.9) {    // 虚拟人框足够高时居中展示，不够高时浮于底部
                // 居中
                self.remoteContentView.center = UIApplication.sharedApplication.keyWindow.center;
            } else {
                // 依附在底部
                self.remoteContentView.TKBottom = UIApplication.sharedApplication.keyWindow.TKBottom - IPHONEX_BUTTOM_HEIGHT;
            }
            
            // TChat重连时防止画面不会更新，需要先关闭对端视频再打开
            [TChatCore UserVideoControl:self->tcSeatId : NO];
            [TChatCore UserVideoControl:self->tcSeatId : YES];
            [TChatCore ShowUserVideo:self->tcSeatId :self.remoteContentView :NO];
            
            // 处理TChat重连逻辑
//            if (self.reconnecting) {
//                self.reconnecting = NO;
//                self.connectReady = YES; // 标记连接
//                TKLogInfo(@"思迪服务器录制日志：TChat重连完毕");
//                
//                dispatch_async(dispatch_get_main_queue(), ^{
//                    
//                    if (self.delegate && [self.delegate respondsToSelector:@selector(connectServerRoomDidComplete:errorMsg:)]) {
//                        [self.delegate connectServerRoomDidComplete:YES errorMsg:@""];
//                    }
//                });
//            }
        }
    });
}

- (CGSize)computeViewSizeWithPresentSize:(CGSize)presentSize viewOriginSize:(CGSize)viewOriginSize scalingMode:(TKChatVideoRecordManagerScalingMode)scalingMode
{
    float videoWidth;
    float videoHeight;
    
    float aspectRatio = viewOriginSize.height / viewOriginSize.width;//高除以宽的比例
    float ratioRequirements = presentSize.height / presentSize.width; //高除以宽的要求比例
    
    switch (scalingMode) {
        case TKChatVideoRecordManagerScalingModeAsceptFill:
            //全屏等比拉伸
            if (aspectRatio > ratioRequirements) {
                videoHeight = viewOriginSize.height;
                videoWidth = videoHeight / ratioRequirements;
            } else {
                videoWidth = viewOriginSize.width;
                videoHeight = videoWidth * ratioRequirements;
            }
            break;
        case TKChatVideoRecordManagerScalingModeFill:
            //全屏等比拉伸
            videoWidth = viewOriginSize.width;
            videoHeight = viewOriginSize.height;
            break;
        default:
            //全屏等比拉伸
            if (aspectRatio < ratioRequirements) {
                videoHeight = viewOriginSize.height;
                videoWidth = videoHeight / ratioRequirements;
            } else {
                videoWidth = viewOriginSize.width;
                videoHeight = videoWidth * ratioRequirements;
            }
            break;
    }
    
    
    return CGSizeMake(videoWidth, videoHeight);
}

/*
userid:            用户编号，为-1时，表示本地视频
filepath:            快照标志为0，表示文件路径；否则，表示文件Base64数据
errorcode:        错误代码
flags:            快照标志
param:            用户自定义参数（整型）
userstr:            用户自定义参数（字符串）
 */
- (void) OnSnapShotCallBack: (int)taskId : (NSString*)filePath :
    (int)errorCode : (int)flags : (int)param : (NSString*)userStr {
    
    if (taskId == -1 && errorCode == 0 && flags == TKCC_SNAPSHOT_FLAGS_DATA) {
        @autoreleasepool
        {
            UIImage *image = [UIImage imageWithData:[TKBase64Helper dataWithDecodeBase64String:filePath]];
    //        NSLog(@"image = %@", image);

            dispatch_async(dispatch_get_main_queue(), ^{

                if (self.delegate && [self.delegate respondsToSelector:@selector(OnVideoDataCallBack:)]) {
                    [self.delegate OnVideoDataCallBack:image];
                }
            });
        }
    }
}

//// 音频数据就绪
- (void) OnUserAudioDataReady: (int)userId : (int)dataInfo {
    
    int ret = dataInfo & 0x0000FFFF;
    TKLogInfo(@"思迪服务器录制日志：音频数据 userId = %i, dataInfo = %i, ret = %i", userId, dataInfo, ret);
    
    if (self.isForceStopReconnect) {
        TKLogInfo(@"思迪服务器录制日志：重连已超时，强制断开，抛弃处理结果");
        return;
    }

//    NSString *errorMsg = @"网络异常,请稍侯重试";
    if (userId == tcSeatId) { // 虚拟坐席音频准备就绪，开始录制
        
//        [self hideLoading];
        self.mute = NO; // 允许对端设备外放声音
        
        // 销毁定时器
        [self invalidateTimeOutTimer];
        TKLogInfo(@"思迪服务器录制日志：对端音频就绪，删除启动超时定时器");

        // 标记就绪
        self.isSeatAudioDataReady = YES;
        if (self.reconnecting == YES) {
            
            // 重置重连标记
            self.reconnecting = NO;
            self.currentReconnectNumber = 0;
            TKLogInfo(@"思迪服务器录制日志：重连完毕");
            // 销毁重连超时定时器
            [self invalidateReconnectTotalTimeOutTimer];
        }
        // 标记已连接
        self.connectReady = YES;
        TKLogInfo(@"思迪服务器录制日志：TChat已就绪");
        
        dispatch_async(dispatch_get_main_queue(), ^{
            
            if (self.delegate && [self.delegate respondsToSelector:@selector(connectServerRoomDidComplete:errorMsg:)]) {
                [self.delegate connectServerRoomDidComplete:YES errorMsg:@""];
            }
        });
    }
}

// 离开房间
- (void) OnLeaveRoom: (int)roomId {
    TKLogInfo(@"离开房间, 房间号:%d" ,roomId);
    
}


// 房间在线用户消息
- (void) OnRoomOnlineUser: (int)userNum : (int)roomId
{
    TKLogInfo(@"房间人数:%d,房间号:%d",userNum,roomId);
    
    if (self.isForceStopReconnect) {
        TKLogInfo(@"思迪服务器录制日志：重连已超时，强制断开，抛弃处理结果");
        return;
    }
    
    if (userNum > 2) {
        
        NSString *errorMsg = @"初始化视频出错(房间人数超限)";
        [self handleBootDeviceFail:errorMsg];
        return;
    }
    
    if (userNum >= 2) {
        
        NSMutableArray *onlineUser = [TChatCore GetRoomOnlineUser];//获取房间中在线用户（坐席）
        
        if (onlineUser && onlineUser.count > 0){
            
            for (int i = 0; i< onlineUser.count; i++) {
                
                if ([[onlineUser objectAtIndex:i] intValue] != tcUserId) {

                    tcSeatId = [[onlineUser objectAtIndex:i] intValue];

                    // 控制虚拟坐席音视频设备
                    if (!isTCStartingVideo) {

                        isTCStartingVideo = YES;

                        dispatch_async(dispatch_get_main_queue(), ^{

                            [self openRemoteDevice];
                        });

                    }
                    
                    break;
                }
            }            
        }
        
    }
}

// 网络质量
- (void) OnNetQuality: (int)local : (int)qos {
    [self connectServerStautsCallBack:qos];
}

////有新用户（坐席）进入房间消息
//- (void) OnUserEnterRoom: (int)userId
//{
//    NSString *l = [NSString stringWithFormat:@"坐席[%@]进入房间",[TChatCore GetUserStateString:userId :TKCC_USERSTATE_NICKNAME]];
//
//    TKLogInfo(@"%@", l);
//
//    if (!isTCStartingVideo) {
//
//        tcSeatId = userId;
//
//        isTCStartingVideo = YES;
//
//        dispatch_async(dispatch_get_main_queue(), ^{
//
//            [self openRemoteDevice];
//        });
//    }
//}

//-(void)OnLeaveRoom:(int)roomId
//{
//    TKLogInfo(@"退出房间回调:roomId-%d",roomId);
//}

-(void)OnUserAudioCtl:(int)userId :(int)param
{
    // 高 16 位表示控制动作，0 表示关闭，1 表示打开；
    // 低 16 位表示错误代码，0 表示成功，否则，表示失败。
    int ctl = (param & 0xFFFF0000) >> 16;
    int ret = param & 0x0000FFFF;
    TKLogInfo(@"用户音频控制回调,userId:%d 操作:%@ 结果:%@ ", userId, ctl == 0 ? @"关闭" : @"打开", ret == 0 ? @"成功" : @"失败");
    
    // NSLog(@"volumeChange OnUserAudioCtl");
}

-(void)OnUserVideoCtl:(int)userId :(int)param
{
    // 高 16 位表示控制动作，0 表示关闭，1 表示打开；
    // 低 16 位表示错误代码，0 表示成功，否则，表示失败。
    int ctl = (param & 0xFFFF0000) >> 16;
    int ret = param & 0x0000FFFF;
    TKLogInfo(@"用户视频控制回调,userId:%d 操作:%@ 结果:%@ ", userId, ctl == 0 ? @"关闭" : @"打开", ret == 0 ? @"成功" : @"失败");
}


// 用户退出房间消息
- (void)OnUserLeaveRoom: (int)userId
{
    NSString *msg = [NSString stringWithFormat:@"[%i]离开房间", userId];

    TKLogInfo(@"%@", msg);
    
    if (([self canReconnect] == NO && self.reconnecting == NO && self.connectReady == YES)
        || self.localDeviceIsOpen == YES  /*这个是取巧判断，代表进入录制页面*/) {
        dispatch_async(dispatch_get_main_queue(), ^{
            
            NSString *errorMsg = @"视频录制异常退出";
            
//            self.connectReady = NO; // 标记断开
            
            if (self.delegate && [self.delegate respondsToSelector:@selector(disconnectServerRoomDidComplete:errorMsg:)]) {
                [self.delegate disconnectServerRoomDidComplete:YES errorMsg:[NSString stringWithFormat:@"%@(%@)", errorMsg, @"坐席已离开房间"]];
            }
        });
    } else {
        [self reconnect];
    }
}

// 网络断开消息
- (void)OnLinkClose: (int)errorCode
{
    TKLogInfo(@"视频连接断开, errorCode = %i", errorCode);
    
    if ([self canReconnect] == YES
        && self.localDeviceIsOpen == NO  /*这个是取巧判断，代表未进入录制页面*/) {
        
        self.reconnecting = NO; // 标记断开TChat重连
        [self reconnect];
    } else {
        dispatch_async(dispatch_get_main_queue(), ^{
            
            self.isForceStopReconnect = YES;
            self.connectReady = NO; // 标记断开
            self.reconnecting = NO;
            TKLogInfo(@"思迪服务器录制日志：TChat已断开");
            
            NSString *errorMsg = @"视频录制异常退出";
            
            if (self.delegate && [self.delegate respondsToSelector:@selector(disconnectServerRoomDidComplete:errorMsg:)]) {
                [self.delegate disconnectServerRoomDidComplete:YES errorMsg:[NSString stringWithFormat:@"%@(%@)", errorMsg, [TKChatErrorConverter converTChatErrorCode:errorCode]]];
            }
        });
    }
}

// 网络码率
- (void) OnNetBitrate: (int)sendBps : (int)recvBps
{
    dispatch_async(dispatch_get_main_queue(), ^{
        
        if (self.delegate && [self.delegate respondsToSelector:@selector(OnNetBitrate:recvBps:)]) {
            [self.delegate OnNetBitrate:sendBps recvBps:recvBps];
        }
    });
    
}

// 音频数据中断
- (void) OnAudioInterrupt: (int)wparam : (int)lparam {
    TKLogInfo(@"音频断流, userid = %i, 状态 = %i", wparam, lparam);
    
//    // wparam-用户ID
//    // lparam-0:表示断流;1: 表示恢复
//    if (wparam == tcSeatId && lparam == 0) {
//        if (([self canReconnect] == NO && self.reconnecting == NO)
//            || self.localDeviceIsOpen == YES  /*这个是取巧判断，代表进入录制页面*/) {
//            dispatch_async(dispatch_get_main_queue(), ^{
//                
//                NSString *errorMsg = @"视频录制异常退出";
//                
////                self.connectReady = NO; // 标记断开
//                
//                if (self.delegate && [self.delegate respondsToSelector:@selector(disconnectServerRoomDidComplete:errorMsg:)]) {
//                    [self.delegate disconnectServerRoomDidComplete:YES errorMsg:[NSString stringWithFormat:@"%@(%@)", errorMsg, @"音频断流"]];
//                }
//            });
//        } else {
//            [self reconnect];
//        }
//    }
}

// 视频数据中断
- (void) OnVideoInterrupt: (int)wparam : (int)lparam {
    TKLogInfo(@"视频断流, userid = %i, 状态 = %i", wparam, lparam);
    
//    // wparam-用户ID
//    // lparam-0:表示断流;1: 表示恢复
//    if (wparam == tcSeatId && lparam == 0) {
//        if ([self canReconnect] == YES) {
//            
//            if (self.isForceStopReconnect) {
//                TKLogInfo(@"思迪服务器录制日志：重连已超时，强制断开，抛弃处理结果");
//                return;
//            }
//            
//            // 正在重连不予处理
//            if (self.reconnecting) {
//                TKLogInfo(@"思迪服务器录制日志：正在重连不予处理");
//                return;
//            }
//            
//            TKLogInfo(@"思迪服务器录制日志：正在运行TChat重连");
//            
//            self.connectReady = NO; // 标记断开
//            self.reconnecting = YES;
//            
//            dispatch_async(dispatch_get_main_queue(), ^{
//                if (self.delegate && [self.delegate respondsToSelector:@selector(connectServerRoomDidStart)]) {
//                    [self.delegate connectServerRoomDidStart];
//                }
//            });
//        } else {
//            TKLogInfo(@"思迪服务器录制日志：无须重连抛弃异常");
//        }
//    }
}

- (void)OnNetWorkInterrupt:(int)wparam :(int)lparam {
    TKLogInfo(@"网络断开");

    if ([self canReconnect] == YES
        && self.localDeviceIsOpen == NO  /*这个是取巧判断，代表未进入录制页面*/) {
        
        if (self.isForceStopReconnect) {
            TKLogInfo(@"思迪服务器录制日志：重连已超时，强制断开，抛弃处理结果");
            return;
        }
        
        // 正在重连不予处理
        if (self.reconnecting) {
            TKLogInfo(@"思迪服务器录制日志：正在重连不予处理");
            return;
        }
        
        TKLogInfo(@"思迪服务器录制日志：正在运行TChat重连");
        
        self.connectReady = NO; // 标记断开
        TKLogInfo(@"思迪服务器录制日志：TChat已断开");
        self.reconnecting = YES;
        self.currentReconnectNumber++;
        
        // 创建重连超时定时器
        if (self.reconnectTotalTimeOutTimer == nil) [self createReconnectTotalTimeOutTimer];
        
        dispatch_async(dispatch_get_main_queue(), ^{
            if (self.delegate && [self.delegate respondsToSelector:@selector(connectServerRoomDidStart)]) {
                [self.delegate connectServerRoomDidStart];
            }
        });
    } else {
//        TKLogInfo(@"思迪服务器录制日志：无须重连抛弃异常");
        dispatch_async(dispatch_get_main_queue(), ^{
            
            self.isForceStopReconnect = YES;
            self.connectReady = NO; // 标记断开
            TKLogInfo(@"思迪服务器录制日志：TChat已断开");
            self.reconnecting = NO;
            
            NSString *errorMsg = @"视频录制异常退出";
            
            if (self.delegate && [self.delegate respondsToSelector:@selector(disconnectServerRoomDidComplete:errorMsg:)]) {
                [self.delegate disconnectServerRoomDidComplete:YES errorMsg:[NSString stringWithFormat:@"%@(网络断开)", errorMsg]];
            }
        });
    }
}

//#pragma mark - TKCCTextMsgDelegate
//////////////////////文字信息协议
//- (void) OnTextMessageCallBack: (int)fromUserId : (int)toUserId : (BOOL)secret : (NSString*)msgBuf{
//
//    dispatch_async(dispatch_get_main_queue(), ^{
//        NSRange userRange = [msgBuf rangeOfString:@"USR:0:"];
//        if (userRange.length>0) {
////            NSString *msg = [msgBuf substringFromIndex:userRange.location+userRange.length];
////            [self showSeatMsgStr:msg];
//        }else{
////            [self showSeatMsgStr:msgBuf];
//        }
//    });
//
//}

#pragma mark - TKCCTransDataDelegate
- (void) OnTransBufferCallBack: (int)userId : (NSData*)buf{
    
    NSString *lpMsgBuf =  [[NSString alloc] initWithData:(buf) encoding:NSUTF8StringEncoding];
    TKLogInfo(@"来自%d的透明通道消息:%@", userId,lpMsgBuf);
    
    NSRange sysRange = [lpMsgBuf rangeOfString:@"h5ret@"];
    
    if (sysRange.length > 0) {  //见证返回的透明信息
        
        NSString *resultStr = [TKStringHelper subStringWith:lpMsgBuf fromIndex:sysRange.location + sysRange.length count:lpMsgBuf.length - sysRange.location - sysRange.length];
        NSDictionary *dic = [TKDataHelper jsonToDictionary:resultStr];
        
        NSString *request_id = [dic getStringWithKey:@"request_id"];
        NSString *type = dic[@"type"];
        
        NSTimeInterval delayTime = 0;
        // 部分回调需要延迟处理
        if ([type isEqualToString:@"tts_stop"] || [type isEqualToString:@"vh_stop"]) {
            delayTime = 0.5;
        }
        
        // 处理事件
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(delayTime * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            // 若已申请新请求，过滤之前的请求回调
            if ([TKStringHelper isNotEmpty:request_id]) {
                
                int requestID = [dic getIntWithKey:@"request_id"];
                if (requestID < self.requestID) {
                    TKLogInfo(@"id是%@的原请求已被丢弃，不需要处理原请求的回调, 最新请求的id是%d", request_id, (int)self.requestID);
                    return;
                }
            }
            
            if ([dic isKindOfClass:NSDictionary.class] && dic.allKeys.count > 0) {
                
                if ([type isEqualToString:@"record_start"]) {
                    int error_code = [dic[@"error_code"] intValue];
                    if (error_code != 0) {
                        [self recordErrorCallBack:[NSString stringWithFormat:@"%@(%@)", @"视频录制异常退出", [TKChatErrorConverter converTChatErrorCode:error_code]]];
                    } else {
                        [self startRecordCallBack];
                    }
                    
                } else if ([type isEqualToString:@"record_error"]) {
                    
                    int error_code = [dic[@"error_code"] intValue];
                    [self recordErrorCallBack:[NSString stringWithFormat:@"%@(%@)", @"视频录制异常退出", [TKChatErrorConverter converTChatErrorCode:error_code]]];
                } else if ([type isEqualToString:@"record_stop"]) {
                    
                    NSString *path = [dic getStringWithKey:@"path"];
                    int error_code = [dic[@"error_code"] intValue];
                    int videoLength = [dic[@"elapse"] intValue];//视频时长
                    int catonLength = [dic[@"caton"] intValue];//视频卡顿时长
                    [self stopRecordCallBack:path errorCode:error_code videoLength:videoLength catonLength:catonLength];
                    
                } else if ([type isEqualToString:@"tts_start"]) {
                    
    //                int requestID = [dic getIntWithKey:@"request_id"];
    //                if (requestID < self.requestID) return; // 原请求已被丢弃，不需要处理原请求的回调

                    [self ttsStartCallBack];
                } else if ([type isEqualToString:@"tts_stop"]) {
                    [self ttsStopCallBack];
                } else if ([type isEqualToString:@"tts_error"]) {
                    
                    int error_code = [dic[@"error_code"] intValue];
                    [self ttsErrorCallBack:[NSString stringWithFormat:@"%@(%@)", @"语音请求失败", [TKChatErrorConverter converTChatErrorCode:error_code]]];
                } else if ([type isEqualToString:@"asr_start"]) {
                    
    //                int requestID = [dic getIntWithKey:@"request_id"];
    //                if (requestID < self.requestID) return; // 原请求已被丢弃，不需要处理原请求的回调
                    
                    [self asrStartCallBack];
                } else if ([type isEqualToString:@"asr_stop"]) {
                    [self asrStopCallBack];
                } else if ([type isEqualToString:@"asr_error"]) {
                    int error_code = [dic[@"error_code"] intValue];
                    [self asrErrorCallBack:[NSString stringWithFormat:@"%@(%@)", @"语音识别失败", [TKChatErrorConverter converTChatErrorCode:error_code]]];
                } else if ([type isEqualToString:@"asr_result"]) {
                    
    //                int requestID = [dic getIntWithKey:@"request_id"];
    //                if (requestID < self.requestID) return; // 原请求已被丢弃，不需要处理原请求的回调
                    
                    NSString *result = [TKBase64Helper stringWithDecodeBase64String:dic[@"text"]];
                    [self asrResultCallBack:result];
                } else if ([type isEqualToString:@"net_qos"]) {
                    int quality = [dic getIntWithKey:@"quality"];
                    [self connectServerStautsCallBack:quality];
                } else if ([type isEqualToString:@"vh_start"]) {
                    
    //                int requestID = [dic getIntWithKey:@"request_id"];
    //                if (requestID < self.requestID) return; // 原请求已被丢弃，不需要处理原请求的回调
                    
                    [self virtualTtsStartCallBack];
                } else if ([type isEqualToString:@"vh_stop"]) {
                    [self ttsStopCallBack];
                } else if ([type isEqualToString:@"vh_error"]) {
                    
                    int error_code = [dic[@"error_code"] intValue];
                    [self virtualTtsErrorCallBack:[NSString stringWithFormat:@"%@(%@)", @"语音请求失败", [TKChatErrorConverter converTChatErrorCode:error_code]]];
                } else if ([type isEqualToString:@"mute_finish"]) {
                    // 静音完成事件
                    BOOL mute = [dic getBoolWithKey:@"mute"];
                    [self muteFinishCallBack:mute];
                }
            }
        });
    }else{ //其它消息
        
    }
}

- (void)muteFinishCallBack:(BOOL)mute
{
    // 目前只有主动开启收音的情况，对声音录制没有强要求，暂不处理回调
//    dispatch_async(dispatch_get_main_queue(), ^{
//
//
//    });
}

- (void)connectServerStautsCallBack:(int)quality
{
    
    dispatch_async(dispatch_get_main_queue(), ^{
        
        if (self.delegate && [self.delegate respondsToSelector:@selector(connectServerStautsDidChange:statusString:)]) {
            [self.delegate connectServerStautsDidChange:quality statusString:self.statusStringDic[@(quality)]];
        }
    });
}

- (void)startRecordCallBack
{
    dispatch_async(dispatch_get_main_queue(), ^{
        
        if (self.delegate && [self.delegate respondsToSelector:@selector(recordStartCallBack)]) {
            [self.delegate recordStartCallBack];
        }
    });
}

- (void)stopRecordCallBack:(NSString *)filePath errorCode:(int)errorCode videoLength:(int)videoLength catonLength:(int)catonLength
{
    if (errorCode == 0) {
            
        _finalVideoPath = filePath;
        dispatch_async(dispatch_get_main_queue(), ^{
            if (self.delegate && [self.delegate respondsToSelector:@selector(recordStopCallBack:fullFilePath:videoLenth:catonLength:)]) {
                [self.delegate recordStopCallBack:filePath fullFilePath:[self getFullVideoPath] videoLenth:videoLength catonLength:catonLength]; // 注意这里调用的是get方法
            }
        });
    } else {
        [self recordErrorCallBack:[NSString stringWithFormat:@"%@(%@)", @"视频录制异常退出", [TKChatErrorConverter converTChatErrorCode:errorCode]]];
    }
        
    TKLogInfo(@"思迪服务器录制日志：录制结束。errorCode = %i。生成的文件路径 = %@", errorCode, [self getFullVideoPath]);
//    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(2 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
//        [_handle closeFile];
//        _handle = nil;
//        NSLog(@"--------------------------文件写入完毕");
//    });
}

- (void)recordErrorCallBack:(NSString *)errorMsg
{
    dispatch_async(dispatch_get_main_queue(), ^{
        
        if (self.delegate && [self.delegate respondsToSelector:@selector(recordStopErrocCallBack:)]) {
            [self.delegate recordStopErrocCallBack:[NSString stringWithFormat:@"%@", errorMsg]];
        }
    });
}

- (void)ttsStartCallBack
{
    dispatch_async(dispatch_get_main_queue(), ^{
        
        if (self.delegate && [self.delegate respondsToSelector:@selector(speechSynthesisDidStart)]) {
            [self.delegate speechSynthesisDidStart];
        }
    });
}

- (void)ttsStopCallBack
{
    // 标记接收到tts播报播放结束回调
    self.isReceiveStopTTSMsg = YES;
    
    if (self.synthesisArray.count) {
//        NSLog(@"handleSpeechSynthesisDidPlayDoneWithIndex ttsStopCallBack index = %i, synthesisArray.count = %i",self.currentIndex, self.synthesisArray.count);
        
        // 回调上层
        int index = self.currentIndex;  // 先记录
        dispatch_async(dispatch_get_main_queue(), ^{
            if (self.delegate && [self.delegate respondsToSelector:@selector(speechSynthesisDidPlayDoneWithIndex:synthesisArray:)]) {
                [self.delegate speechSynthesisDidPlayDoneWithIndex:index synthesisArray:self.synthesisArray];
            }
        });
        
        if (self.currentIndex < self.synthesisArray.count - 1) {
            TKLogDebug(@"思迪语音合成调试:多段语音-继续播放下段语音");
            // 播放下一段语音
            self.currentIndex++;
            [self syntheticAndPlay:self.synthesisArray[self.currentIndex] tipSpeed:@""]; // 这里的tipSpeed没有作用
        } else {
            TKLogDebug(@"思迪语音合成调试:多段语音播放结束");
        }
        
    } else {
        TKLogDebug(@"思迪语音合成调试:播放结束");
        dispatch_async(dispatch_get_main_queue(), ^{
            if (self.delegate && [self.delegate respondsToSelector:@selector(speechSynthesisDidPlayDone)]) {
                [self.delegate speechSynthesisDidPlayDone];
            }
        });
    }
}

- (void)ttsErrorCallBack:(NSString *)errorMsg
{
    // 标记接收到tts播报结束回调
    self.isReceiveStopTTSMsg = YES;
    
    dispatch_async(dispatch_get_main_queue(), ^{
        
        if (self.delegate && [self.delegate respondsToSelector:@selector(speechSynthesisDidFail:)]) {
            [self.delegate speechSynthesisDidFail:errorMsg];
        }
    });
}

- (void)asrStartCallBack
{
    dispatch_async(dispatch_get_main_queue(), ^{
        
        if (self.delegate && [self.delegate respondsToSelector:@selector(speechRecognizeDidStart)]) {
            [self.delegate speechRecognizeDidStart];
        }
    });
}

- (void)asrErrorCallBack:(NSString *)errorMsg
{
    dispatch_async(dispatch_get_main_queue(), ^{
        
        if (self.delegate && [self.delegate respondsToSelector:@selector(speechRecognizeDidFail:)]) {
            [self.delegate speechRecognizeDidFail:errorMsg];
        }
    });
}

- (void)asrResultCallBack:(NSString *)result
{
    dispatch_async(dispatch_get_main_queue(), ^{
        
        if (self.delegate && [self.delegate respondsToSelector:@selector(speechRecognizeOnSliceRecognize:)]) {
            [self.delegate speechRecognizeOnSliceRecognize:result];
        }
    });
}

- (void)asrStopCallBack
{
    dispatch_async(dispatch_get_main_queue(), ^{
        
        if (self.delegate && [self.delegate respondsToSelector:@selector(speechRecognizeDidComplete)]) {
            [self.delegate speechRecognizeDidComplete];
        }
    });
}


- (void)virtualTtsStartCallBack
{
    dispatch_async(dispatch_get_main_queue(), ^{
        
        if (self.delegate && [self.delegate respondsToSelector:@selector(speechSynthesisDidStart)]) {
            [self.delegate speechSynthesisDidStart];
        }
    });
}

- (void)virtualTtsErrorCallBack:(NSString *)errorMsg
{
    dispatch_async(dispatch_get_main_queue(), ^{
        
        if (self.delegate && [self.delegate respondsToSelector:@selector(speechSynthesisDidFail:)]) {
            [self.delegate speechSynthesisDidFail:errorMsg];
        }
    });
}

#pragma mark - TKCCVideoDataDelegate
////// 实时视频数据
//- (void) OnVideoDataCallBack: (int)userId : (NSData*)buf :
//(int)len : (int)width : (int)height {
//
//}


#pragma mark - TKCCAudioDataDelegate
// 音频数据数据
//- (void) OnAudioDataCallBack: (int)userId : (NSData*)buf : (int)len : (int)channels : (int)samplesPerSec :
//(int)bitPerSample {
//
//    static long totallen = 0;
//
//    if (_handle == nil) {
//        NSString *path = [NSString stringWithFormat:@"%@/tempTChat.pcm", [TKFileHelper documentFolder]];
//        if ([TKFileHelper isFileExists:path] == YES) {
//
//            [TKFileHelper removeFile:path];
//        }
//        [TKFileHelper createFile:path isDirectory:NO];
//        _handle = [NSFileHandle fileHandleForWritingAtPath:path];
//        NSLog(@"----------------文件写入的路径是%@", path);
//    }
//
//    if (userId == tcSeatId) {
//        
//        NSError *error = nil;
//        [_handle writeData:buf error:&error];
//
//        totallen += len;
//        NSLog(@"----------------已写入的文件数据是%ld, error = %@", totallen, error);
//    }
//
//    if (self.delegate && [self.delegate respondsToSelector:@selector(OnAudioDataCallBack:)]) {
//        [self.delegate OnAudioDataCallBack:buf];
//    }
//}

#pragma mark - Setter && Getter
- (TKOpenAccountService *)openAccountService {
    if (_openAccountService == nil) {
        _openAccountService = [TKOpenAccountService new];
    }
    return _openAccountService;
}

- (void)setMute:(BOOL)mute {
    if (_mute != mute) {
        _mute = mute;
//        [TChatCore EnableSpeaker:!mute];
        [TChatCore SetSDKOptionInt:TKCC_SO_MUTE_PEER_AUDIO  :(mute ? 1 : 0)];  //关闭
    }
}

- (NSDictionary *)statusStringDic
{
    return @{
        // tchat返回的文案
//        @(TKChatVideoRecordNetworkStatusUnknown) : @"未知",
//        @(TKChatVideoRecordNetworkStatusVeryGood) : @"很好",
//        @(TKChatVideoRecordNetworkStatusGood) : @"较好",
//        @(TKChatVideoRecordNetworkStatusBad) : @"较差",
//        @(TKChatVideoRecordNetworkStatusVeryBad) : @"很差",
        // 项目真实使用的文案
        @(TKChatVideoRecordNetworkStatusUnknown) : @"未知", // 额外增加的。这种状态不展示
        @(TKChatVideoRecordNetworkStatusVeryGood) : @"网络正常",
        @(TKChatVideoRecordNetworkStatusGood) : @"网络正常",
        @(TKChatVideoRecordNetworkStatusBad) : @"网络不稳定",
        @(TKChatVideoRecordNetworkStatusVeryBad) : @"网络卡顿",
    };
}

@end
