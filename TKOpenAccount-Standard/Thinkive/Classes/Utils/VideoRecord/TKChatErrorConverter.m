//
//  TKChatErrorConverter.m
//  TKOpenAccount-Standard
//
//  Created by <PERSON> on 2023/12/18.
//  Copyright © 2023 thinkive. All rights reserved.
//

#import "TKChatErrorConverter.h"

@implementation TKChatErrorConverter

+ (NSString *)converTChatErrorCode:(int)errCode
{
    NSString *errorNo = [NSString stringWithFormat:@"%d", errCode];
    return [self converTChatError:errorNo];
}

+ (NSString *)converTChatError:(NSString *)errorNo
{
    NSString *errMsg = [TKChatErrorConverter chatErrorDic][errorNo];
    errMsg = [TKStringHelper isEmpty:errMsg] ? [NSString stringWithFormat:@"%@(%@)", @"其他错误", errorNo] : errMsg;
    return errMsg;
}

+ (NSDictionary *)chatErrorDic
{
    return @{
        @"0" : @"正常执行",
        @"-1" : @"未知错误",
        @"100" : @"连接服务器异常",
        @"101" : @"与服务器的连接中断",
        @"102" : @"域名解析失败",
        @"103" : @"连接服务器认证失败",
        @"104" : @"版本太旧，不允许连接",
        @"105" : @"与服务器的连接过期",
        @"106" : @"与服务器的连接变化",
        @"107" : @"连接数超过最大限制",
        @"200" : @"认证失败，用户名或密码有误",
        @"201" : @"游客登录被禁止",
        @"202" : @"用户已登录",
        @"203" : @"用户类型不支持",
        @"300" : @"房间密码错误，禁止进入",
        @"301" : @"房间已满员，不能进入",
        @"302" : @"禁止进入房间",
        @"303" : @"房间ID错误",
        @"304" : @"用户已在房间中",
        @"305" : @"房间不存在",
        @"306" : @"房间数已满",
        @"400" : @"用户不在房间内",
        @"401" : @"用户不在线",
        @"402" : @"用户ID错误",
        @"403" : @"用户已登录",
        @"500" : @"建立通道超时",
        @"600" : @"呼叫受限",
        @"601" : @"被叫繁忙",
        @"602" : @"被叫拒绝",
        @"603" : @"主叫取消",
        @"700" : @"录像创建失败",
        @"701" : @"录像认证失败",
        @"702" : @"文件创建失败",
        @"703" : @"文件写入失败",
        @"705" : @"录像响应超时",
        @"706" : @"录像任务无效",
        @"707" : @"录像尚未开始",
        @"708" : @"远端视频断流",
        @"709" : @"录像内部错误",
        @"710" : @"录像连接中断",
        @"711" : @"录像启动超时",
        @"712" : @"录像停止超时",
        @"713" : @"远端音频断流",
        @"714" : @"本地视频断流",
        @"715" : @"本地音频断流",
        @"716" : @"远端视频缺失",
        @"800" : @"无在线媒体服务器",
        @"801" : @"无在线录像服务器",
        @"802" : @"媒体服务器已下线",
        @"900" : @"媒体不存在",
        @"901" : @"媒体不支持",
        @"902" : @"媒体初始化失败",
        @"903" : @"语音合成启动失败",
        @"904" : @"语音识别启动失败",
        @"905" : @"语音合成请求失败",
        @"906" : @"语音识别请求失败",
        @"907" : @"媒体连接断开",
        @"908" : @"媒体内部错误",
    };
}

@end
