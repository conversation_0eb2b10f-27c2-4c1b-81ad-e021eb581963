//
//  TKSmartTwoVideoManagerProtocol.h
//  TKOpenAccount-Standard
//
//  Created by 夏博文 on 2023/1/28.
//  Copyright © 2023 thinkive. All rights reserved.
//
#import "TKOpenQueueView.h"

@protocol TKSmartTwoVideoManagerDelegate <NSObject>

@optional

//上传日志
-(void)uploadSmartTwoVideoLog:(NSString *)logString;

/**
 <AUTHOR> 2023年01月28日14:25:14
 @提示排队提示语
 @param tipString提示文本
 @param location排队位置
 @param status当前排队状态
 */
-(void)changeSmartTwoVideoTipText:(NSString *)tipString queueLocation:(int)location currentStatus:(TKOpenQueueStatus)status;

/**
 <AUTHOR> 2023年01月28日14:25:14
 @弹窗提示
 @param title标题
 @param describe描述
 @param cancelBtnTitle取消按钮文本
 @param takeBtnTitle确认按钮文本
 */
-(void)alertSmartTwoVideoTip:(NSString *)title  describe:(NSString *)describe cancelBtnTitle:(NSString *)cancelBtnTitle takeBtnTitle:(NSString *)takeBtnTitle;

/***
 启动视频见证
 */
- (void)tkStartSmartTwoVideo:(NSString*)sUrl withPort:(int)sPort;

/***
 添加视频画面到界面
 */
- (void)addSmartTwoVideoChatView;

/***
 视频画面显示
 */
- (void)showSmartTwoVideoChatView;

//显示网络状态展示
-(void)showSmartTwoVideoNetStatus:(NSString *)info withColor:(NSString *)color;

//显示网络上下行网速
-(void)smartTwoVideoNetWorkUpDownTip:(NSString *)string;

//显示风险协议阅读文本
-(void)showSmartTwoVideoRead:(NSMutableDictionary *)param;

//显示toast提示
-(void)showSmartTwoVideoToast:(NSString *)string;

//显示坐席消息
-(void)showSmartTwoVideoSeatMessage:(NSString *)message;

//显示坐席信息
-(void)showSmartTwoVideoServiceInfo:(NSString *)info;

//开始视频计时
-(void)startSmartTwoVidoeTime;

//关闭视频
-(void)endSmartTwoVidoe;

/**
*<AUTHOR> 根据坐席指令展示带标题的对准框
*/
-(void)showSmartTwoVideoTitleBox:(NSMutableDictionary *)param;

/**
*<AUTHOR> 根据坐席指令展示可滚动文本
*/
-(void)showSmartTwoVideoRollText:(NSMutableDictionary *)param;

/**
*<AUTHOR> 根据坐席指令展示进度条
*/
-(void)showSmartTwoVideoProcessNode:(NSMutableDictionary *)param;

/**
*<AUTHOR> 人数超限时候底部显示取消排队，自助见证安按钮
*/
-(void)showCancelBtn;


//显示底部阅读协议弹窗
-(void)showSmartTwoVideoBottomRead:(NSMutableDictionary *)param;
@end

@protocol TKSmartTwoVideoManagerProtocol <NSObject>


@required
/// 代理对象
@property (nonatomic, readwrite, weak) id<TKSmartTwoVideoManagerDelegate> delegate;

/// 配置字典
@property (nonatomic, readwrite, weak) NSMutableDictionary *requestParams;
/// 展示自己界面的contentView
@property (nonatomic, readwrite, weak) UIView *contentView;
/// 展示坐席/虚拟人界面的contentView
@property (nonatomic, readwrite, weak) UIView *remoteContentView;


/// 构造方法
/// @param configParam 初始化参数字典
- (instancetype)initWithConfig:(NSMutableDictionary *)requestParams;



#pragma mark 视频开启关闭相关
/***
 启动视频见证
 */
- (void)startSmartTwoVideo:(NSString*)sUrl withPort:(int)sPort;

/**
 *
 * @method tkStopTChatWitness
 *
 * @brief 结束视频见证
 *
 */
-(void)stopSmartTwoVideo;

/**
 *
 * @method tkSwitchCameraNewTChatWitness
 *
 * @brief 切换摄像头
 *
 */
-(void)switchCameraSmartTwoVideo:(BOOL)isFrontCamera;

/**
 *
 * @method sendMsgToVideoServer
 *
 * @brief 发送消息给坐席
 *
 */
-(void)sendMsgToVideoServer:(NSString *)msg;

@optional
#pragma mark 4.0排队相关接口
/**
 *
 * @method startLineingUp
 *
 * @brief 开始4.0视频排队接口
 *
 */
- (void)startLineingUp;


/**
 *
 * @method stopLineingUp
 *
 * @brief 结束4.0视频排队接口
 *
 */
- (void)stopLineingUp;

@end
