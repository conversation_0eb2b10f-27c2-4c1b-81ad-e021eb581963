//
//  TKTChatSmartTwoVideoManager.m
//  TKOpenAccount-Standard
//
//  Created by 夏博文 on 2023/1/28.
//  Copyright © 2023 thinkive. All rights reserved.
//

#import "TKTChatSmartTwoVideoManager.h"
#import "TKDirectVideoModel.h"
#import <TChat/TChatDefine.h>
#import <TChat/TChatErrorCode.h>
#import <TChat/TChatCore.h>
@interface TKTChatSmartTwoVideoManager()<TKCCNotifyMessageDelegate,TKCCTransDataDelegate,TKCCTextMsgDelegate,TKSmartTwoVideoManagerDelegate>
{
    NSTimer *waitSeatTimer;
}

@end


@implementation TKTChatSmartTwoVideoManager
@synthesize delegate=_delegate;
@synthesize requestParams=_requestParams;
@synthesize contentView=_contentView;
@synthesize remoteContentView=_remoteContentView;

#pragma mark - Init

/// 构造方法
/// @param configParam 初始化参数字典
- (instancetype)initWithConfig:(NSMutableDictionary *)requestParams {
    if (self = [self init]) {
        self.requestParams = requestParams;
    }
    return  self;
}

-(NSString *)getTimeStamp{
    // 设置想要的格式，hh与HH的区别:分别表示12小时制,24小时制
    //把NSDate按formatter格式转成NSString
    NSString *currentTime =[TKDateHelper formatDate:[NSDate date] format:@"yyyy-MM-dd HH:mm:ss"];
    return currentTime;
}

#pragma mark 视频开启关闭相关
/***
 启动视频见证
 */
- (void)startSmartTwoVideo:(NSString *)sUrl withPort:(int)sPort {
    [UIApplication sharedApplication].idleTimerDisabled = YES;
    int initFlag=[TChatCore InitSDK];
    //直接返回失败的时候就不走回调了
    if (initFlag!=0) {
        dispatch_async(dispatch_get_main_queue(), ^{
            
            NSString *witnessResult = nil;
            if([TKDirectVideoModel shareInstance].isDirectVideo){
                witnessResult = @"app:10002";
            }else{
                witnessResult = @"-3";//连接视频服务器失败
            }
            [self showConnectErrorTip:witnessResult witnessInfo:@"连接服务器失败" tipTitle:@"视频录制提示" tipDesc:@"初始化异常，请稍侯重试！"];
            
        });
    }
    [TChatCore shareTChatCore].notifyMsgDelegate = self;
    [TChatCore shareTChatCore].transDataDelegate = self;
    [TChatCore shareTChatCore].textMsgDelegate = self;
    [TChatCore SetServerAuthPass: @"123456"];
    [TChatCore Connect:sUrl :sPort];
    

    [TKDirectVideoModel shareInstance].isStartingVideo=NO;
    [TKDirectVideoModel shareInstance].isTransBufferMsg=NO;
//    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(becomeTChatActive:) name:UIApplicationDidBecomeActiveNotification object:nil];
    
    //记录开始连接视频服务器事件
    NSString *logString=[NSString stringWithFormat:@"TKMSG1004:网络情况%@|发起连接|%@",[TKNetHelper getNetworkTypeInfo],[self getTimeStamp]];

    if (self.delegate && [self.delegate respondsToSelector:@selector(uploadSmartTwoVideoLog:)]) {
        [self.delegate uploadSmartTwoVideoLog:logString];
    }
}

/**
 *  <AUTHOR> 2019年09月10日14:03:52
 *  App从后台返回前台
 *  @param notif
 */
-(void)becomeTChatActive:(NSNotification *)notif{
    
    [TChatCore UserVideoControl: -1 : NO];
    [TChatCore UserVideoControl: -1 : YES];

    [TChatCore ShowUserVideo:-1 :_contentView :[TKDirectVideoModel shareInstance].isFrontCamera];    // 切换摄像头，会提前修改摄像头标志位。因此这里，不再需要取反。前置摄像头镜像，后置不用镜像
}




/**
 *
 * @method tkStopTChatWitness
 *
 * @brief 结束视频见证
 *
 */
-(void)stopSmartTwoVideo{
    [self endTChatVideo];
}

/**
 *
 * @method tkSwitchCameraNewTChatWitness
 *
 * @brief 切换摄像头
 *
 */
-(void)switchCameraSmartTwoVideo:(BOOL)isFrontCamera{

    if (isFrontCamera) {
        // 改成后置摄像头
        NSString *camName = [TChatCore GetDeviceName:TKCC_DT_VIDEOCAPTURE :0];
        [TChatCore SelectDevice:TKCC_DT_VIDEOCAPTURE :camName];
        [TChatCore ShowUserVideo:-1 :_contentView :NO];    // 前置摄像头镜像，后置不用镜像
        TKLogInfo(@"切换摄像头为:%@",camName);
    }else{
        // 改成前置置摄像头
        NSString *camName = [TChatCore GetDeviceName:TKCC_DT_VIDEOCAPTURE :1];
        [TChatCore SelectDevice:TKCC_DT_VIDEOCAPTURE :camName];
        [TChatCore ShowUserVideo:-1 :_contentView :YES];    // 前置摄像头镜像，后置不用镜像
        TKLogInfo(@"切换摄像头为:%@",camName);
    }
}

/**
 *
 * @method sendMsgToVideoServer
 *
 * @brief 发送消息给坐席
 *
 */
-(void)sendMsgToVideoServer:(NSString *)msg{
    [TChatCore SendTextMessage:[TKDirectVideoModel shareInstance].seatVideoId :YES :msg];
}


- (void)showConnectErrorTip:(NSString *)witnessResult witnessInfo:(NSString *)witnessInfo tipTitle:(NSString *)tipTitle tipDesc:(NSString *)tipDesc
{
    if (![TKDirectVideoModel shareInstance].isShowAlert) {
        [TKDirectVideoModel shareInstance].isShowAlert = YES;
        
        [TKDirectVideoModel shareInstance].witnessResult = witnessResult;//连接视频服务器失败
        [TKDirectVideoModel shareInstance].witnessInfo=witnessInfo;
        
        if (self.delegate && [self.delegate respondsToSelector:@selector(alertSmartTwoVideoTip:describe:cancelBtnTitle:takeBtnTitle:)]) {
            [self.delegate alertSmartTwoVideoTip:tipTitle describe:tipDesc cancelBtnTitle:nil takeBtnTitle:@"确定"];
        }
    }
}

#pragma mark tchat delegate

// 连接服务器消息
- (void) OnConnect: (BOOL)success : (int)errorCode
{
    //记录开始连接视频服务器返回事件
    NSString *logString=[NSString stringWithFormat:@"TKMSG1005:网络情况%@|%d|%@",[TKNetHelper getNetworkTypeInfo],errorCode,[self getTimeStamp]];

    if (self.delegate && [self.delegate respondsToSelector:@selector(uploadSmartTwoVideoLog:)]) {
        [self.delegate uploadSmartTwoVideoLog:logString];
    }
    
    if(errorCode == 0){//连接视频服务器成功
        
        TKLogInfo(@"connect success.");
        
        

        if([TKDirectVideoModel shareInstance].isDirectVideo){
            [TChatCore Login:self.requestParams[@"loginName"] : self.requestParams[@"loginPwd"]?self.requestParams[@"loginPwd"]:@""];
        }else{
            NSString *loginId = [NSString stringWithFormat:@"%@", self.requestParams[@"user_id"]];
            
            [TChatCore Login:loginId : @""];
        }

        
        //记录开始登陆视频服务器
        NSString *logString=[NSString stringWithFormat:@"TKMSG1006:网络情况%@|开始登录服务器|%@",[TKNetHelper getNetworkTypeInfo],[self getTimeStamp]];

        if (self.delegate && [self.delegate respondsToSelector:@selector(uploadSmartTwoVideoLog:)]) {
            [self.delegate uploadSmartTwoVideoLog:logString];
        }
    }else{
        
        TKLogInfo(@"connect fail errorcode:%d",errorCode);
        
        dispatch_async(dispatch_get_main_queue(), ^{
            
            NSString *witnessResult = nil;
            if([TKDirectVideoModel shareInstance].isDirectVideo){
                witnessResult = @"app:10002";
            }else{
                witnessResult = @"-3";//连接视频服务器失败
            }
            [self showConnectErrorTip:witnessResult witnessInfo:@"连接视频服务器失败" tipTitle:@"视频录制提示" tipDesc:@"服务器异常，请稍侯重试！"];
            
        });
    }
}

// 用户登陆消息
- (void) OnLogin: (int)userId : (int)errorCode
{
    //记录开始登陆视频服务器返回结果
    NSString *logString=[NSString stringWithFormat:@"TKMSG1007:网络情况%@|登陆服务器结果%d|%@",[TKNetHelper getNetworkTypeInfo],errorCode,[self getTimeStamp]];

    if (self.delegate && [self.delegate respondsToSelector:@selector(uploadSmartTwoVideoLog:)]) {
        [self.delegate uploadSmartTwoVideoLog:logString];
    }
    
    if(errorCode == 0){//登录成功
        
        TKLogInfo(@"login success.");

        [TKDirectVideoModel shareInstance].userVideoId=userId;
        if([TKDirectVideoModel shareInstance].isDirectVideo){
            [TKDirectVideoModel shareInstance].witRoomId=self.requestParams[@"roomId"];
            [TChatCore EnterRoom:[[TKDirectVideoModel shareInstance].witRoomId intValue] :  self.requestParams[@"roomPwd"]?self.requestParams[@"roomPwd"]:@""];
        }else{
            [TChatCore EnterRoom:[[TKDirectVideoModel shareInstance].witRoomId intValue] : @""];
        }

        
        
        //记录开始进入房间

        NSString *logString=[NSString stringWithFormat:@"TKMSG1008:网络情况%@|开始进入房间|%@",[TKNetHelper getNetworkTypeInfo],[self getTimeStamp]];

        if (self.delegate && [self.delegate respondsToSelector:@selector(uploadSmartTwoVideoLog:)]) {
            [self.delegate uploadSmartTwoVideoLog:logString];
        }
    }else{
        
        TKLogInfo(@"login failed.");
        
        dispatch_async(dispatch_get_main_queue(), ^{
            
            NSString *witnessResult = nil;
            if([TKDirectVideoModel shareInstance].isDirectVideo){
                witnessResult = @"app:10002";
            }else{
                witnessResult = @"-4";//进入房间失败
            }
            NSString *msg = [NSString stringWithFormat:@"登陆服务器异常，请稍候重试！错误码:%d",errorCode];
            [self showConnectErrorTip:witnessResult witnessInfo:@"进入房间失败" tipTitle:@"视频录制提示" tipDesc:msg];
            
        });
    }
}

// 用户进入房间消息
- (void) OnEnterRoom: (int)roomId : (int)errorCode
{
    //记录开始进入房间返回结果
    NSString *logString=[NSString stringWithFormat:@"TKMSG1009:网络情况%@|进入房间结果%d|%@",[TKNetHelper getNetworkTypeInfo],errorCode,[self getTimeStamp]];

    if (self.delegate && [self.delegate respondsToSelector:@selector(uploadSmartTwoVideoLog:)]) {
        [self.delegate uploadSmartTwoVideoLog:logString];
    }
    
    if (errorCode == 0) {//进入房间成功
        TKLogInfo(@"已进入房间");
        
        // 埋点-双向-视频-进入房间
        NSMutableDictionary *eventDic = [NSMutableDictionary dictionaryWithDictionary:self.requestParams];
        [TKStatisticEventHelper sendEvent:TKPrivateEventVideoWitness subEventName:TKPrivateSubEventVideoWitnessPrepareVideo progress:TKPrivateEventProgressNone result:TKPrivateEventResultSuccess orientation:TKPrivateVideoOrientationPortrait oneWayVideoType:TKPrivateOneWayVideoTypeNone prepareVideoProgress:TKPrivatePrepareVideoProgressEnterRoom eventDic:eventDic];
        
    }else{
        
        //与服务器的连接变化不处理
        if (errorCode==TKCC_ERR_CONNECT_CHANGE) {
            return;
        }
        TKLogInfo(@"用户进入房间失败");
        
        dispatch_async(dispatch_get_main_queue(), ^{
            
            if (![TKDirectVideoModel shareInstance].isShowAlert) {
                
                [TKDirectVideoModel shareInstance].isShowAlert = YES;

                
                if (self.delegate && [self.delegate respondsToSelector:@selector(alertSmartTwoVideoTip:describe:cancelBtnTitle:takeBtnTitle:)]) {
                    [self.delegate alertSmartTwoVideoTip:@"视频录制提示" describe:@"网络异常,请稍侯重试" cancelBtnTitle:nil takeBtnTitle:@"确定"];
                }
            }
            
        });
    }
}


#pragma mark -等待坐席进入房间处理
- (void)handleWaitSeatTimeout:(NSTimer*)timer{
    NSMutableArray *onlineUser = [TChatCore GetRoomOnlineUser];//获取房间中在线用户（坐席）
    if (onlineUser.count < 2) {
        if ([TKDirectVideoModel shareInstance].tkCountDown > 0) {
            TKLogInfo(@"show Count Down = %d", [TKDirectVideoModel shareInstance].tkCountDown);
            if (self.delegate && [self.delegate respondsToSelector:@selector(changeSmartTwoVideoTipText:queueLocation:currentStatus:)]) {
                [TKDirectVideoModel shareInstance].queueSubMsg=@"";
                
                //提示语h5入参调整位置提示语优先级最高
                //针对h5参数类型容错处理
                NSString *queueWaitStaffSubMsg;
                if(self.requestParams[@"videoTipMsg"]&&[self.requestParams[@"videoTipMsg"] isKindOfClass:[NSDictionary class]]){
                    queueWaitStaffSubMsg=self.requestParams[@"videoTipMsg"][@"queueWaitStaffSubMsg"];
                }else{
                    queueWaitStaffSubMsg=[TKDataHelper jsonToDictionary:self.requestParams[@"videoTipMsg"]][@"queueWaitStaffSubMsg"];
                }
                
                if([TKStringHelper isNotEmpty:queueWaitStaffSubMsg]){
                    [TKDirectVideoModel shareInstance].queueSubMsg=queueWaitStaffSubMsg;
                }

                
                //提示语h5入参调整位置提示语优先级最高
                //针对h5参数类型容错处理
                NSString *queueWaitStaffMsg;
                if(self.requestParams[@"videoTipMsg"]&&[self.requestParams[@"videoTipMsg"] isKindOfClass:[NSDictionary class]]){
                    queueWaitStaffMsg=self.requestParams[@"videoTipMsg"][@"queueWaitStaffMsg"];
                }else{
                    queueWaitStaffMsg=[TKDataHelper jsonToDictionary:self.requestParams[@"videoTipMsg"]][@"queueWaitStaffMsg"];
                }
                
                if([TKStringHelper isNotEmpty:queueWaitStaffMsg]){
                    [TKDirectVideoModel shareInstance].queueBigMsg=[NSString stringWithFormat:@"%@（%d秒）",queueWaitStaffMsg,[TKDirectVideoModel shareInstance].tkCountDown];
                }else{
                    [TKDirectVideoModel shareInstance].queueBigMsg=[NSString stringWithFormat:@"正在接入%@…（%d秒）",[TKDirectVideoModel shareInstance].serviceTipString,[TKDirectVideoModel shareInstance].tkCountDown];
                }
                [self.delegate changeSmartTwoVideoTipText:[TKDirectVideoModel shareInstance].queueBigMsg  queueLocation:0 currentStatus:TKOpenQueueStatusGetService];
            }
            [TKDirectVideoModel shareInstance].tkCountDown--;
        }else{
            NSString *witnessResult = nil;
            if([TKDirectVideoModel shareInstance].isDirectVideo){
                witnessResult = @"app:10003";
            }else{
                witnessResult = @"-7";//坐席连接异常
            }
            NSString *msg = [NSString stringWithFormat:@"未匹配到%@,请稍侯重试",[TKDirectVideoModel shareInstance].serviceTipString];
            
            if (timer && timer.userInfo) {
                msg =  [NSString stringWithFormat:@"未匹配到%@,请稍侯重试[房间号：%ld]",[TKDirectVideoModel shareInstance].serviceTipString, (long)[timer.userInfo[@"roomId"] integerValue]];
            }
            [self showConnectErrorTip:witnessResult witnessInfo:[NSString stringWithFormat:@"%@超时未进入房间",[TKDirectVideoModel shareInstance].serviceTipString] tipTitle:@"视频录制提示" tipDesc:msg];
        }
    }else{
        if (waitSeatTimer) {
            [waitSeatTimer invalidate];
            waitSeatTimer = nil;
        }
    }


}

// 房间在线用户消息
- (void) OnRoomOnlineUser: (int)userNum : (int)roomId
{
    TKLogInfo(@"房间人数:%d,房间号:%d",userNum,roomId);
    //记录开始房间人数日志
    NSString *logString=[NSString stringWithFormat:@"TKMSG100901:网络情况%@|当前房间人数%d|%@",[TKNetHelper getNetworkTypeInfo],userNum,[self getTimeStamp]];

    if (self.delegate && [self.delegate respondsToSelector:@selector(uploadSmartTwoVideoLog:)]) {
        [self.delegate uploadSmartTwoVideoLog:logString];
    }
    
    if (userNum >= 2) {
        
        NSMutableArray *onlineUser = [TChatCore GetRoomOnlineUser];//获取房间中在线用户（坐席）
        
        if (onlineUser && onlineUser.count > 0){
            
            for (int i = 0; i< onlineUser.count; i++) {
                
                if ([[onlineUser objectAtIndex:i] intValue] != [TKDirectVideoModel shareInstance].userVideoId) {
                    
                    [TKDirectVideoModel shareInstance].seatVideoId = [[onlineUser objectAtIndex:i] intValue];
                    
                    break;
                }
            }
            
            TKLogInfo(@"坐席=%d",[TKDirectVideoModel shareInstance].seatVideoId);
            
            if (![TKDirectVideoModel shareInstance].isStartingVideo) {
                
                dispatch_async(dispatch_get_main_queue(), ^{
                    
                    [self startTChatVideo];
                });
                
            }
            
        }
        
    }else{
        
        dispatch_async(dispatch_get_main_queue(), ^{
            
            waitSeatTimer = [NSTimer scheduledTimerWithTimeInterval:1 target:self selector:@selector(handleWaitSeatTimeout:) userInfo:[NSDictionary dictionaryWithObjectsAndKeys:[NSNumber numberWithInt:roomId],@"roomId", nil] repeats:YES];
        });
    }
}

//有新用户（坐席）进入房间消息
- (void) OnUserEnterRoom: (int)userId
{
    NSString *l = [NSString stringWithFormat:@"坐席[%@]进入房间",[TChatCore GetUserStateString:userId :TKCC_USERSTATE_NICKNAME]];
    
    TKLogInfo(@"%@", l);
    
    if (![TKDirectVideoModel shareInstance].isStartingVideo) {
        
        if (waitSeatTimer) {
            
            [waitSeatTimer invalidate];
            
            waitSeatTimer = nil;
        }
        
        [TKDirectVideoModel shareInstance].seatVideoId = userId;
        
        
        dispatch_async(dispatch_get_main_queue(), ^{
            
            [self startTChatVideo];
        });
    }
}

-(void)OnLeaveRoom:(int)roomId
{
    TKLogInfo(@"退出房间回调:roomId-%d",roomId);
}

-(void)OnUserAudioCtl:(int)userId :(int)param
{
    TKLogInfo(@"用户音频控制回调,userId:%d param:%d",userId,param);
}

-(void)OnUserVideoCtl:(int)userId :(int)param
{
    TKLogInfo(@"用户视频控制回调,userId:%d param:%d",userId,param);
}


//音频中断
- (void)OnAudioInterrupt:(int)wparam :(int)lparam{
    
    if(wparam == -1 && lparam == 0){

        [TChatCore UserAudioControl:-1 :NO];
        [TChatCore UserAudioControl:-1 :YES];
        [TChatCore UserAudioControl:[TKDirectVideoModel shareInstance].seatVideoId :FALSE];
        [TChatCore UserAudioControl:[TKDirectVideoModel shareInstance].seatVideoId :TRUE];
        [TChatCore EnableSpeaker:TRUE];
        TKLogInfo(@"TChat OnAudioInterrupt !");
    }
}

// 视频数据中断
- (void) OnVideoInterrupt: (int)wparam : (int)lparam{
    
}

// 视频数据就绪
- (void) OnUserVideoDataReady: (int)userId : (int)dataInfo{
    dispatch_async(dispatch_get_main_queue(), ^{
        if (self.contentView.superview.hidden||[TKDirectVideoModel shareInstance].isDirectVideo) {
            if (self.delegate && [self.delegate respondsToSelector:@selector(showSmartTwoVideoChatView)]) {
                [self.delegate showSmartTwoVideoChatView];
            }
        }
        
        if (userId==-1) {
            //自己视频画面调整占用屏幕大小，以便展示出底层的白色背景
            float   widht= (dataInfo & 0xFFFF0000) >> 16;
            float     height= (dataInfo & 0x0000FFFF);
            TKLogInfo(@"视频调整前宽度：%f|视频调整前高度：%f",widht,height);
            float videoWidth;
            float videoHeight;
            float aspectRatio=[UIScreen mainScreen].bounds.size.height/[UIScreen mainScreen].bounds.size.width;//高除以宽的比例
            float ratioRequirements=height/widht;//高除以宽的要求比例
            //全屏等比拉伸
            if (aspectRatio>ratioRequirements) {
                videoHeight=[UIScreen mainScreen].bounds.size.height;
                videoWidth=videoHeight/ratioRequirements;
            }else{
                videoWidth=[UIScreen mainScreen].bounds.size.width;
                videoHeight=videoWidth*ratioRequirements;
            }

            [_contentView setFrameWidth:videoWidth];
            [_contentView setFrameHeight:videoHeight];
            TKLogInfo(@"视频调整后宽度：%f|视频调整后高度：%f",videoWidth,videoHeight);
            _contentView.center=CGPointMake(UISCREEN_WIDTH/2.0f, UISCREEN_HEIGHT/2.0f); ;
            
            [TChatCore ShowUserVideo:-1 :_contentView :[TKDirectVideoModel shareInstance].isFrontCamera];
            
        }else{
//            if (self.delegate && [self.delegate respondsToSelector:@selector(showSmartTwoVideoChatView)]) {
//                [self.delegate showSmartTwoVideoChatView];
//            }
            TKLogInfo(@"坐席画面准备就绪");
        }
    });
    
}

// 用户退出房间消息
- (void) OnUserLeaveRoom: (int)userId
{
    NSString *l = [NSString stringWithFormat:@"[%@]离开房间",[TChatCore GetUserStateString:userId :TKCC_USERSTATE_NICKNAME]];
    
    TKLogInfo(@"%@",l);
    
    dispatch_async(dispatch_get_main_queue(), ^{
        
        NSString *witnessResult = nil;
        if([TKDirectVideoModel shareInstance].isDirectVideo){
            witnessResult = @"app:10004";
        }else{
            witnessResult = @"-7";//坐席连接异常
        }
        NSString *msg=[NSString stringWithFormat:@"%@视频连接异常，请重试！",[TKDirectVideoModel shareInstance].serviceTipString];
        [self showConnectErrorTip:witnessResult witnessInfo:[NSString stringWithFormat:@"%@离开房间连接异常",[TKDirectVideoModel shareInstance].serviceTipString] tipTitle:@"视频录制提示" tipDesc:msg];
        
    });
}

// 网络断开消息
- (void) OnLinkClose: (int)errorCode
{
    //只要不是连接过期都不处理
    if (errorCode==TKCC_ERR_CONNECT_CHANGE) {
        return;
    }
    
    TKLogInfo(@"视频连接断开");
    
    dispatch_async(dispatch_get_main_queue(), ^{
        
        NSString *witnessResult = nil;
        if([TKDirectVideoModel shareInstance].isDirectVideo){
            witnessResult = @"app:10009";
        }else{
            witnessResult = @"-6";//客户连接异常
        }
        [self showConnectErrorTip:witnessResult witnessInfo:@"客户网络断开连接异常" tipTitle:@"视频录制提示" tipDesc:@"与视频服务器断开连接"];
        
    });
    
}

// 网络质量
- (void) OnNetQuality: (int)local : (int)qos {
    dispatch_async(dispatch_get_main_queue(), ^{
//        if (local) {
//            NSString *colorString;
//            if (qos == 1 || qos == 2) {
//                colorString=@"#1CAA3D";
//
//            } else if (qos == 3) {
//                colorString=@"#FFBE00";
//            } else {
//                colorString=@"#FF4951";
//            }
//            [self showVideoNetStatus:self.statusStringDic[@(qos)] withColor:colorString];
//        }
        //一般情况坐席在内网应该不会网络不好,所以不区分坐席还是自己
        NSString *netStatusString=local?@"当前网络质量不佳":@"当前网络质量不佳";
        if (qos==4) {
            //网络卡顿就toast提示
//            [self showVideoNetStatus:netStatusString withColor:@"#FD4D43"];
            if (self.delegate && [self.delegate respondsToSelector:@selector(showSmartTwoVideoNetStatus:withColor:)]) {
                [self.delegate showSmartTwoVideoNetStatus:netStatusString withColor:@"#FD4D43"];
            }
        }
        
    });
}

- (NSDictionary *)statusStringDic
{
    return @{
        // 项目真实使用的文案
        @(0) : @"未知", // 额外增加的。这种状态不展示
        @(1) : @"网络正常",
        @(2) : @"网络正常",
        @(3) : @"网络不稳定",
        @(4) : @"网络卡顿",
    };
}

// 网络码率
- (void) OnNetBitrate: (int)sendBps : (int)recvBps
{

    dispatch_async(dispatch_get_main_queue(), ^{
        
        if (self.delegate && [self.delegate respondsToSelector:@selector(smartTwoVideoNetWorkUpDownTip:)]) {
            [self.delegate smartTwoVideoNetWorkUpDownTip:[NSString stringWithFormat:@"上行:%dKB/s\n下行:%dKB/s",sendBps,recvBps]];
        }
        
    });
    
}

////////////////////文字信息协议
- (void) OnTextMessageCallBack: (int)fromUserId : (int)toUserId : (BOOL)secret : (NSString*)msgBuf{
    
    dispatch_async(dispatch_get_main_queue(), ^{
        
        if([msgBuf rangeOfString:@"USR:1000:"].location != NSNotFound){
            
            NSRange userRange = [msgBuf rangeOfString:@"USR:1000:"];
            NSString *msg = [msgBuf substringFromIndex:userRange.location+userRange.length];
            
            [self updateTipTCViewText:msg];
            
            return;
        }
        
        if([msgBuf rangeOfString:@"USR:1001:"].location !=NSNotFound){
            //风险协议阅读是否展示指令
            NSRange readAgreeRange=[msgBuf rangeOfString:@"USR:1001:"];
            NSString * readJsonString=[msgBuf substringFromIndex:(readAgreeRange.length)];
            NSMutableDictionary *readParm=(NSMutableDictionary *)[TKDataHelper jsonToDictionary:readJsonString];
            if (readParm&&[readParm isKindOfClass:[NSDictionary class]]) {
                readParm[@"mainColor"]=self.requestParams[@"mainColor"]?self.requestParams[@"mainColor"]:@"#0354C2";

                //兼容老的1001指令想展示底部时候
                if([[readParm getStringWithKey:@"showType"] isEqualToString:@"bottom"]){
                    readParm[@"instructionNo"]=@"1007";//坐席发过来的指令编号
                    if (self.delegate && [self.delegate respondsToSelector:@selector(showSmartTwoVideoBottomRead:)]) {
                        [self.delegate showSmartTwoVideoBottomRead:readParm];
                    }
                }else{
                    if (self.delegate && [self.delegate respondsToSelector:@selector(showSmartTwoVideoRead:)]) {
                        [self.delegate showSmartTwoVideoRead:readParm];
                    }
                }
            }else{

                if (self.delegate && [self.delegate respondsToSelector:@selector(showSmartTwoVideoToast:)]) {
                    [self.delegate showSmartTwoVideoToast:@"数据格式错误"];
                }
            }

            return;
        }
        
        if ([msgBuf rangeOfString:@"USR:1002:"].location !=NSNotFound) {
            //toast提示
            NSRange toastRange=[msgBuf rangeOfString:@"USR:1002:"];
            NSString *toastString=[msgBuf substringFromIndex:(toastRange.length)];

            if (self.delegate && [self.delegate respondsToSelector:@selector(showSmartTwoVideoToast:)]) {
                [self.delegate showSmartTwoVideoToast:toastString];
            }
            return;
        }
        
        if([msgBuf rangeOfString:@"USR:1003:"].location !=NSNotFound){
            //根据坐席指令展示带标题的对准框
            NSRange readAgreeRange=[msgBuf rangeOfString:@"USR:1003:"];
            NSString * readJsonString=[msgBuf substringFromIndex:(readAgreeRange.length)];
            NSMutableDictionary *readParm=(NSMutableDictionary *)[TKDataHelper jsonToDictionary:readJsonString];
            if (readParm&&[readParm isKindOfClass:[NSDictionary class]]) {
                readParm[@"mainColor"]=self.requestParams[@"mainColor"]?self.requestParams[@"mainColor"]:@"#0354C2";

                if (self.delegate && [self.delegate respondsToSelector:@selector(showSmartTwoVideoTitleBox:)]) {
                    [self.delegate showSmartTwoVideoTitleBox:readParm];
                }
            }else{
                if (self.delegate && [self.delegate respondsToSelector:@selector(showSmartTwoVideoToast:)]) {
                    [self.delegate showSmartTwoVideoToast:@"数据格式错误"];
                }
            }
            return;
        }
        
        if([msgBuf rangeOfString:@"USR:1004:"].location !=NSNotFound){
            //根据坐席指令展示进度条
            NSRange readAgreeRange=[msgBuf rangeOfString:@"USR:1004:"];
            NSString * readJsonString=[msgBuf substringFromIndex:(readAgreeRange.length)];
            NSMutableDictionary *readParm=(NSMutableDictionary *)[TKDataHelper jsonToDictionary:readJsonString];
            if (readParm&&[readParm isKindOfClass:[NSDictionary class]]) {
                readParm[@"mainColor"]=self.requestParams[@"mainColor"]?self.requestParams[@"mainColor"]:@"#0354C2";

                if (self.delegate && [self.delegate respondsToSelector:@selector(showSmartTwoVideoProcessNode:)]) {
                    [self.delegate showSmartTwoVideoProcessNode:readParm];
                }
            }else{
                if (self.delegate && [self.delegate respondsToSelector:@selector(showSmartTwoVideoToast:)]) {
                    [self.delegate showSmartTwoVideoToast:@"数据格式错误"];
                }
            }
            return;
        }
        
        if([msgBuf rangeOfString:@"USR:1005:"].location !=NSNotFound){
            //根据坐席指令展示可滚动文本
            NSRange readAgreeRange=[msgBuf rangeOfString:@"USR:1005:"];
            NSString * readJsonString=[msgBuf substringFromIndex:(readAgreeRange.length)];
            NSMutableDictionary *readParm=(NSMutableDictionary *)[TKDataHelper jsonToDictionary:readJsonString];
            if (readParm&&[readParm isKindOfClass:[NSDictionary class]]) {
                readParm[@"mainColor"]=self.requestParams[@"mainColor"]?self.requestParams[@"mainColor"]:@"#0354C2";

                if (self.delegate && [self.delegate respondsToSelector:@selector(showSmartTwoVideoRollText:)]) {
                    [self.delegate showSmartTwoVideoRollText:readParm];
                }
            }else{
                if (self.delegate && [self.delegate respondsToSelector:@selector(showSmartTwoVideoToast:)]) {
                    [self.delegate showSmartTwoVideoToast:@"数据格式错误"];
                }
            }
            return;
        }
        
        if ([msgBuf rangeOfString:@"USR:1006:"].location !=NSNotFound) {
            //显示确认弹窗
            NSRange readAgreeRange=[msgBuf rangeOfString:@"USR:1006:"];
            NSString * readJsonString=[msgBuf substringFromIndex:(readAgreeRange.length)];
            NSMutableDictionary *readParm=(NSMutableDictionary *)[TKDataHelper jsonToDictionary:readJsonString];
            if (readParm&&[readParm isKindOfClass:[NSDictionary class]]) {
                readParm[@"mainColor"]=self.requestParams[@"mainColor"]?self.requestParams[@"mainColor"]:@"#0354C2";
                readParm[@"instructionNo"]=@"1006";//坐席发过来的指令编号
                if (self.delegate && [self.delegate respondsToSelector:@selector(showSmartTwoVideoBottomRead:)]) {
                    [self.delegate showSmartTwoVideoBottomRead:readParm];
                }
            }else{

                if (self.delegate && [self.delegate respondsToSelector:@selector(showSmartTwoVideoToast:)]) {
                    [self.delegate showSmartTwoVideoToast:@"数据格式错误"];
                }
            }
            return;
        }
        
        if ([msgBuf rangeOfString:@"USR:1007:"].location !=NSNotFound) {
            //显示底部阅读协议弹窗
            NSRange readAgreeRange=[msgBuf rangeOfString:@"USR:1007:"];
            NSString * readJsonString=[msgBuf substringFromIndex:(readAgreeRange.length)];
            NSMutableDictionary *readParm=(NSMutableDictionary *)[TKDataHelper jsonToDictionary:readJsonString];
            if (readParm&&[readParm isKindOfClass:[NSDictionary class]]) {
                readParm[@"mainColor"]=self.requestParams[@"mainColor"]?self.requestParams[@"mainColor"]:@"#0354C2";
                readParm[@"instructionNo"]=@"1007";//坐席发过来的指令编号
                if (self.delegate && [self.delegate respondsToSelector:@selector(showSmartTwoVideoBottomRead:)]) {
                    [self.delegate showSmartTwoVideoBottomRead:readParm];
                }
            }else{

                if (self.delegate && [self.delegate respondsToSelector:@selector(showSmartTwoVideoToast:)]) {
                    [self.delegate showSmartTwoVideoToast:@"数据格式错误"];
                }
            }
            return;
        }
        
        NSRange userRange = [msgBuf rangeOfString:@"USR:0:"];
        if (userRange.length>0) {
             
            NSString *msg = [msgBuf substringFromIndex:userRange.location+userRange.length];
             
            [self updateTipTCViewText:[NSString stringWithFormat:@"%@  %@",[TKDirectVideoModel shareInstance].serviceTipString,msg]];

             
        }else{
            NSRange userRange2 = [msgBuf rangeOfString:@"USR:"];
            if (userRange2.length>0) {
                //针对USR:指令低版本SDK没有支持的类型兼容不做处理
            }else{
                [self updateTipTCViewText:[NSString stringWithFormat:@"%@  %@",[TKDirectVideoModel shareInstance].serviceTipString,msgBuf]];
            }


        }
    });
    
}

/**
 *  <AUTHOR> 2019年01月24日13:28:43
 *  更新提示语
 *  @return nil
 */
-(void)updateTipTCViewText:(NSString *)tip{

    if (self.delegate && [self.delegate respondsToSelector:@selector(showSmartTwoVideoSeatMessage:)]) {
        [self.delegate showSmartTwoVideoSeatMessage:tip];
    }
}

- (void) OnTransBufferCallBack: (int)userId : (NSData*)buf{
    
    TKLogInfo(@"transBuffer callback");
    
    [TKDirectVideoModel shareInstance].isTransBufferMsg = YES;
    
    NSString *lpMsgBuf=  [[NSString alloc] initWithData:(buf) encoding:NSUTF8StringEncoding];
    
    TKLogInfo(@"来自%d的透明通道消息:%@", userId,lpMsgBuf);
    
    NSRange sysRange = [lpMsgBuf rangeOfString:@"SYS:"];
    
    [TKDirectVideoModel shareInstance].witnessResult = lpMsgBuf;
    
    // 埋点-单向_请求房间_结果
    NSMutableDictionary *eventDic = [NSMutableDictionary dictionaryWithDictionary:self.requestParams];
    eventDic[@"message"] = lpMsgBuf;
    TKPrivateEventResult result = TKPrivateEventResultNone;
    [TKStatisticEventHelper sendEvent:TKPrivateEventVideoWitness
                         subEventName:TKPrivateSubEventVideoWitnessReceiveCMD
                             progress:TKPrivateEventProgressNone
                               result:result
                          orientation:TKPrivateVideoOrientationPortrait
                      oneWayVideoType:TKPrivateOneWayVideoTypeTChatSmart
                 prepareVideoProgress:TKPrivatePrepareVideoProgressNone
                             eventDic:eventDic];
    
    if (sysRange.length > 0) {  //见证返回的透明信息
        
    }else{ //其它消息
        
    }
    
    dispatch_async(dispatch_get_main_queue(), ^{
        [self endTChatVideo];
    });
}

#pragma mark -启动TChat视频
- (void)startTChatVideo{
    // 埋点-双向-视频-开始
    NSMutableDictionary *eventDic = [NSMutableDictionary dictionaryWithDictionary:self.requestParams];
    [TKStatisticEventHelper sendEvent:TKPrivateEventVideoWitness subEventName:TKPrivateSubEventVideoWitnessPrepareVideo progress:TKPrivateEventProgressStart result:TKPrivateEventResultNone eventDic:eventDic];
    
    // 打开本地摄像头
    NSString *deviceName = [TChatCore GetDeviceName:TKCC_DT_VIDEOCAPTURE :1];
    [TChatCore SelectDevice:TKCC_DT_VIDEOCAPTURE :deviceName];
    int selfVideoErrorCode= [TChatCore UserVideoControl:-1 : YES];
    NSString *logVideoString=[NSString stringWithFormat:@"TKMSG1010:打开远本地摄像头情况%d|%@",selfVideoErrorCode,[self getTimeStamp]];
    if (selfVideoErrorCode != 0) {
        NSString *witnessResult = nil;
        if([TKDirectVideoModel shareInstance].isDirectVideo){
            witnessResult = @"app:10002";
        }else{
            witnessResult = @"-3";//连接视频服务器失败
        }
        [self showConnectErrorTip:witnessResult witnessInfo:@"打开本地麦克风失败" tipTitle:@"视频录制提示" tipDesc:@"服务器异常，请稍侯重试！"];
        return;
    }

    if (self.delegate && [self.delegate respondsToSelector:@selector(uploadSmartTwoVideoLog:)]) {
        [self.delegate uploadSmartTwoVideoLog:logVideoString];
    }
    
    // 打开本地麦克风
    int selfAudioErrorCode= [TChatCore UserAudioControl:-1 :YES];
    NSString *logAudioString=[NSString stringWithFormat:@"TKMSG1011:打开本地麦克风情况%d|%@",selfAudioErrorCode,[self getTimeStamp]];
    if (selfAudioErrorCode != 0) {
        NSString *witnessResult = nil;
        if([TKDirectVideoModel shareInstance].isDirectVideo){
            witnessResult = @"app:10002";
        }else{
            witnessResult = @"-3";//连接视频服务器失败
        }
        [self showConnectErrorTip:witnessResult witnessInfo:@"打开本地麦克风失败" tipTitle:@"视频录制提示" tipDesc:@"服务器异常，请稍侯重试！"];
        return;
    }

    if (self.delegate && [self.delegate respondsToSelector:@selector(uploadSmartTwoVideoLog:)]) {
        [self.delegate uploadSmartTwoVideoLog:logAudioString];
    }
    
    if (self.delegate && [self.delegate respondsToSelector:@selector(addSmartTwoVideoChatView)]) {
        [self.delegate addSmartTwoVideoChatView];
    }
    
    [TChatCore EnableSpeaker:YES];
    
    //请求远端硬件
    int seatVideoErrorCode=[TChatCore UserVideoControl:[TKDirectVideoModel shareInstance].seatVideoId :YES];
    NSString *logRemoteVideoString=[NSString stringWithFormat:@"TKMSG1010:打开远程摄像头情况%d|%@",seatVideoErrorCode,[self getTimeStamp]];

    if (self.delegate && [self.delegate respondsToSelector:@selector(uploadSmartTwoVideoLog:)]) {
        [self.delegate uploadSmartTwoVideoLog:logRemoteVideoString];
    }
    

    int seatAudioErrorCode=[TChatCore UserAudioControl:[TKDirectVideoModel shareInstance].seatVideoId :YES];
    NSString *logRemoteAudioString=[NSString stringWithFormat:@"TKMSG1011:打开远程麦克风情况%d|%@",seatAudioErrorCode,[self getTimeStamp]];
//    if (selfVideoErrorCode == 0) {
//        NSString *witnessResult = nil;
//        if([TKDirectVideoModel shareInstance].isDirectVideo){
//            witnessResult = @"app:10002";
//        }else{
//            witnessResult = @"-3";//连接视频服务器失败
//        }
//        [self showConnectErrorTip:witnessResult witnessInfo:@"打开本地麦克风失败" tipTitle:@"视频录制提示" tipDesc:@"服务器异常，请稍侯重试！"];
//        return;
//    }
    
    if (self.delegate && [self.delegate respondsToSelector:@selector(uploadSmartTwoVideoLog:)]) {
        [self.delegate uploadSmartTwoVideoLog:logRemoteAudioString];
    }
    
    
    NSString *uName = [TChatCore GetUserStateString:[TKDirectVideoModel shareInstance].seatVideoId :TKCC_USERSTATE_NICKNAME];
    
    NSString *serviceInfo;
    if ([TKStringHelper isEmpty:[TKDirectVideoModel shareInstance].staffTips]) {
        serviceInfo=[NSString stringWithFormat:@"客服：%@",uName];
    }else{
        serviceInfo=[TKDirectVideoModel shareInstance].staffTips;
    }
   
    if([TKDirectVideoModel shareInstance].isDirectVideo){
        //如果需要展示坐席信息
        if (self.requestParams[@"showStaffInfo"]) {
            serviceInfo=self.requestParams[@"showStaffInfo"];
        }
    }

    
    if (self.delegate && [self.delegate respondsToSelector:@selector(showSmartTwoVideoServiceInfo:)]) {
        [self.delegate showSmartTwoVideoServiceInfo:serviceInfo];
    }
    
    
    [TChatCore ShowUserVideo:[TKDirectVideoModel shareInstance].seatVideoId :_remoteContentView :NO];
    

    
    //开始见证计时
    
    if (self.delegate && [self.delegate respondsToSelector:@selector(startSmartTwoVidoeTime)]) {
        [self.delegate startSmartTwoVidoeTime];
    }
    
    [TKDirectVideoModel shareInstance].isStartingVideo = YES;
}

#pragma mark -结束视频
- (void)endTChatVideo{

    TKLogInfo(@"end TChat video.");
    
    [UIApplication sharedApplication].idleTimerDisabled = NO;
    
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(200 * NSEC_PER_MSEC)), dispatch_get_global_queue(0, 0), ^{
        

        
        if ([TKDirectVideoModel shareInstance].witRoomId) {
            
            if(![TKDirectVideoModel shareInstance].isTransBufferMsg)
            {
                [TChatCore TransBuffer:[TKDirectVideoModel shareInstance].seatVideoId :[@"SYS:10002" dataUsingEncoding:NSUTF8StringEncoding]];
            }
            [TChatCore UserVideoControl:-1 :NO];
            [TChatCore UserAudioControl:-1 :NO];
            [TChatCore EnableSpeaker:NO];
            [TChatCore UserVideoControl:[TKDirectVideoModel shareInstance].seatVideoId :NO];
            [TChatCore UserAudioControl:[TKDirectVideoModel shareInstance].seatVideoId :NO];
            [TChatCore LeaveRoom];
            [TChatCore Logout];
            [TChatCore Release];
        }
    });
    
    if (self.delegate && [self.delegate respondsToSelector:@selector(endSmartTwoVidoe)]) {
        [self.delegate endSmartTwoVidoe];
    }
}
@end
