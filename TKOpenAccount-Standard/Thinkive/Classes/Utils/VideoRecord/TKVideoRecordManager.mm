//
//  TKVideoRecordManager.m
//  TKOpenAccount-Standard
//
//  Created by <PERSON> on 2022/3/29.
//  Copyright © 2022 thinkive. All rights reserved.
//

#import "TKVideoRecordManager.h"
#import "TKSampleBufferConverter.h"


// 语音合成
#if __has_include(<TKSpeechSynthesisManager.h>)
#define IsImportTKSpeechUtil 1
#import "TKSpeechSynthesisManager.h"
#import "TKSpeechRecognizeManager.h"
#import "TKringBuf.h"
RINGBUFFER_NEW(tk_video_record_buf, 16000 * 1000)

#elif __has_include("TKSpeechSynthesisManager.h")
#define IsImportTKSpeechUtil 1
#import "TKSpeechSynthesisManager.h"
#import "TKSpeechRecognizeManager.h"
#import "TKringBuf.h"
RINGBUFFER_NEW(tk_video_record_buf, 16000 * 1000)

#else
#define IsImportTKSpeechUtil 0
#endif


#if IsImportTKSpeechUtil
@interface TKVideoRecordManager()<AVCaptureVideoDataOutputSampleBufferDelegate, AVCaptureAudioDataOutputSampleBufferDelegate, AVAudioPlayerDelegate, TKSpeechSynthesisManagerDelegate, TKSpeechRecognizeManagerDelegate>
#else
@interface TKVideoRecordManager()<AVCaptureVideoDataOutputSampleBufferDelegate, AVCaptureAudioDataOutputSampleBufferDelegate, AVAudioPlayerDelegate>

#endif
{
    
}

@property (nonatomic, strong) AVCaptureSession *avSession;//,用于捕捉视频和音频,协调视频和音频的输入和输出流
@property (nonatomic, strong) AVCaptureVideoPreviewLayer *avPreviewLayer;//视频预览图层
@property (nonatomic, strong) AVCaptureDeviceInput *avDeviceInput;//设备输入流
@property (strong, nonatomic) AVCaptureDeviceInput       *audioMicInput;//麦克风输入

@property (strong, nonatomic) dispatch_queue_t outPutQueue;//输出流线程队列

@property (strong, nonatomic) AVCaptureVideoDataOutput *videoOutput;//视频输出流
@property (strong, nonatomic) AVCaptureAudioDataOutput *audioOutput;//音频输出流
@property (strong, nonatomic) AVAssetWriter *assetWriter;//写流
@property (strong, nonatomic) AVAssetWriterInput *videoWriterInput;//视频输入流
@property (strong, nonatomic) AVAssetWriterInput *audioWriterInput;//音频输入流

#if IsImportTKSpeechUtil
@property (nonatomic, readwrite, strong) id<TKSpeechRecognizeManagerProtocol> speechRecognizeManager;
@property (nonatomic, readwrite, strong) id<TKSpeechSynthesisManagerProtocol> speechSynthesisManager;
#endif


@property (nonatomic, assign) BOOL isGetImg;//是否截取过照片
@property (nonatomic, strong) UIImage *videoGetImg;//视频录制中获取的照片

@property (nonatomic, assign) BOOL isRecording;//是否正在录制
@property (nonatomic, readwrite, assign) BOOL isRelease;    // 是否已经释放资源

@property (nonatomic, assign) CFAbsoluteTime recordStartTime;//视频开始录制时间

@property (nonatomic, strong) AVPlayer *mp3Player;//MP3文件播放
@property (nonatomic, readwrite, assign) BOOL isRecgnizing; // 是否正在识别

@property (nonatomic, readwrite, assign) CMTime lastSampleTime;
@property (nonatomic, readwrite, strong) TKSampleBufferConverter *sampleBufferConverter;
@property (nonatomic, readwrite, assign) int targetSampleRate; // 目标采样率，默认16000

//@property (nonatomic, readwrite, assign) CMSampleBufferRef emptySampleBuffer;
@property (nonatomic, strong) NSMutableArray *bufferPointers;  // 语音合成buffer数组

@end

@implementation TKVideoRecordManager
@synthesize delegate = _delegate;
@synthesize configParam = _configParam;
@synthesize contentView = _contentView;
@synthesize videoFilePath = _videoFilePath;
@synthesize isLandscape = _isLandscape;
@synthesize isFrontCamera = _isFrontCamera;
@synthesize disableMicrophone = _disableMicrophone;

#pragma mark - Init && Dealloc
- (nonnull instancetype)initWithConfig:(nonnull NSDictionary *)configParam {
    
    if (self = [super init]) {
        self.isGetImg = NO;
        self.configParam = configParam;
        
        if ([TKStringHelper isNotEmpty:self.configParam[@"aliTTSSampleRate"]]) {
            //语音合成采样率，默认值是16000，支持8000
            self.targetSampleRate = [self.configParam[@"aliTTSSampleRate"] intValue];
        } else {
            self.targetSampleRate = 16000;
        }
    }
    
    return self;
}



#pragma mark - Selector
- (void)bootDevcie:(BOOL)isFirst {
    
//     [TKFileHelper removeFile:[[TKFileHelper tempFolder] stringByAppendingFormat:@"thinkive/speechSynthesis/%@",@"test1.pcm"]];
//    [TKFileHelper removeFile:[[TKFileHelper tempFolder] stringByAppendingFormat:@"thinkive/speechSynthesis/%@",@"test2.pcm"]];
//    [TKFileHelper removeFile:[[TKFileHelper tempFolder] stringByAppendingFormat:@"thinkive/speechSynthesis/%@",@"test3.pcm"]];
    
    
    // 第二次启动的时候直接激活设备
    if (isFirst == NO) {
        [self activeDevice];
    }
    
    [self.contentView.layer addSublayer:self.avPreviewLayer];
    self.avPreviewLayer.frame = self.contentView.bounds;
    
    if (self.isLandscape) {
        
        CGFloat contentViewX = self.contentView.TKLeft;
        CGFloat contentViewY = self.contentView.TKTop;

        CGAffineTransform transform = CGAffineTransformMakeRotation((-90.0f * M_PI) / 180.0f);
        self.contentView.transform = transform;
        self.contentView.frame = CGRectMake(contentViewX, contentViewY, MAX(self.contentView.TKWidth, self.contentView.TKHeight), MIN(self.contentView.TKWidth, self.contentView.TKHeight));
        self.avPreviewLayer.frame = self.contentView.bounds;
//        self.avPreviewLayer.frame = CGRectMake(0, 0, MAX(self.contentView.TKWidth, self.contentView.TKHeight), MIN(self.contentView.TKWidth, self.contentView.TKHeight));
    }
}


- (void)stopDevice:(BOOL)needRelease {
    [self deactiveDevice];
    
    if (needRelease) {
        self.avSession = nil;
        self.isRelease = YES;
    }
    
//    NSString *errorMsg = @"视频录制异常退出";
//    if (self.delegate && [self.delegate respondsToSelector:@selector(disconnectServerRoomDidComplete:errorMsg:)]) {
//        [self.delegate disconnectServerRoomDidComplete:YES errorMsg:[NSString stringWithFormat:@"%@(%d)", errorMsg, errorCode]];
//    }
    
}

- (void)activeDevice {
    
    if (!self.avSession.isRunning) {
        __weak typeof (self) weakSelf = self;
        dispatch_async(dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_BACKGROUND, 0), ^{
            
            [weakSelf.avSession startRunning];
            
            dispatch_async(dispatch_get_main_queue(), ^{
                
                if (self.delegate && [self.delegate respondsToSelector:@selector(connectServerRoomDidComplete:errorMsg:)]) {
                    [self.delegate connectServerRoomDidComplete:YES errorMsg:@""];
                }
            });
        });
    }
}

- (void)deactiveDevice {
    if (self.avSession.isRunning) {
        
        __weak typeof (self) weakSelf = self;
        dispatch_async(dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_BACKGROUND, 0), ^{
            
            [weakSelf.avSession stopRunning];
        });
    }
}


- (void)startRecord {
    
    // 已经开始录制，无须重复开始
    if (self.isRecording == YES) {return;}
    
    // 已经释放，无需开始指令
    if (self.isRelease == YES) {return;}
    
    self.isRecording = YES;
    
    self.recordStartTime = CFAbsoluteTimeGetCurrent();
    
    if (self.delegate && [self.delegate respondsToSelector:@selector(recordStartCallBack)]) {
        [self.delegate recordStartCallBack];
    }
}

- (void)stopRecord {
    
    // 未录制，不需要处理
    if (self.isRecording == NO) {
        return;
    };
    
    self.isRecording = NO;
    
    dispatch_async(self.outPutQueue, ^{
        // 记录开始时间
        TKLogInfo(@"思迪录制日志：stopRecord. 当前写入状态:%ld, error = %@", (long)self.assetWriter.status, self.assetWriter.error.description);
        
        @try{
            if(self.assetWriter.status == AVAssetWriterStatusWriting)
            {
                dispatch_semaphore_t wait = dispatch_semaphore_create(0);
                
                TKLogInfo(@"思迪录制日志：信号-挂起");
                    
                [self.videoWriterInput markAsFinished];
                [self.audioWriterInput markAsFinished];
                
                [self.assetWriter endSessionAtSourceTime:self.lastSampleTime];
                
                TKLogInfo(@"思迪录制日志：写视频数据结束. 当前写入状态:%ld, error = %@", (long)self.assetWriter.status, self.assetWriter.error.description);
                [self.assetWriter finishWritingWithCompletionHandler:^{
                     
                    TKLogInfo(@"思迪录制日志：停止录制视频文件.当前写入状态:%ld", (long)self.assetWriter.status);
                    self.assetWriter = nil;
                    
                    [self.sampleBufferConverter releaseAllBufferPointers];
                    [self releaseAllBufferPointers];
                    
                    CFAbsoluteTime currentTime = CFAbsoluteTimeGetCurrent();
                    int seconds = currentTime - self.recordStartTime;
                    
                    dispatch_async(dispatch_get_main_queue(), ^{
                        
                        if (self.delegate && [self.delegate respondsToSelector:@selector(recordStopCallBack:fullFilePath:videoLenth:catonLength:)]) {
                            [self.delegate recordStopCallBack:[self getFullVideoPath] fullFilePath:[self getFullVideoPath] videoLenth:seconds * 1000 catonLength:0];    // 返回的时间单位是毫秒
                        }
                    });
                    
                    dispatch_semaphore_signal(wait);
                    TKLogInfo(@"思迪录制日志：信号-释放");
                }];
                
                TKLogInfo(@"思迪录制日志：信号-等待");
                dispatch_semaphore_wait(wait, DISPATCH_TIME_FOREVER);
            }
            else if (self.assetWriter.status == AVAssetWriterStatusFailed)
            {
                [self.sampleBufferConverter releaseAllBufferPointers];
                [self releaseAllBufferPointers];
                
                dispatch_async(dispatch_get_main_queue(), ^{
                    
                    if (self.delegate && [self.delegate respondsToSelector:@selector(recordStopErrocCallBack:)]) {
                        [self.delegate recordStopErrocCallBack:@"视频录制异常退出(文件写入失败)"];
                    }
                });
                
            } else {
                TKLogInfo(@"思迪录制日志：当前写入状态:%ld", (long)self.assetWriter.status);
            }
        }
        @catch(NSException *exception) {
            [self.sampleBufferConverter releaseAllBufferPointers];
            [self releaseAllBufferPointers];
            
            TKLogInfo(@"思迪录制日志：exception:%@", exception);
            
    //        [self handleDeviceRunFail:exception.description];
        }
        @finally {
            TKLogInfo(@"思迪录制日志：stopRecord");
        }
    });
}

- (void)playVideo:(nonnull NSString *)flag {
    
    // 已经释放，无需开始指令
    if (self.isRelease == YES) {return;}
    
    if ([flag isEqualToString:@"1"]) {
        //开始滴一声
        [self handleAudioPlay:@"tk_oneVideo_start"];
    }else if ([flag isEqualToString:@"2"]) {
        //本地音频开始准备话术
        [self handleAudioPlay:@"tk_oneVideo_remind" withType:@"mp3" sourceDirectory:@"Resources/TKOpenPlugin60030"];
    }else if ([flag isEqualToString:@"3"]) {
        //结束滴一声
        [self handleAudioPlay:@"tk_oneVideo_end"];
    }
}

- (void)syntheticAndPlay:(nonnull NSString *)text tipSpeed:(NSString *)tipSpeed {
    
    // 已经释放，无需开始指令
    if (self.self.isRelease == YES) {return;}
#if IsImportTKSpeechUtil
    [self.speechSynthesisManager syntheticAndPlay:text tipSpeed:tipSpeed];
#endif
}

- (void)syntheticAndPlayContents:(NSArray *)texts tipSpeed:(NSString *)tipSpeed {
    // 已经释放，无需开始指令
    if (self.self.isRelease == YES) {return;}
#if IsImportTKSpeechUtil
    [self.speechSynthesisManager syntheticAndPlayContents:texts tipSpeed:tipSpeed];
#endif
}

- (void)stopPlayVideo {
    [self.mp3Player pause];
}

- (void)startRecognize {
    
#if IsImportTKSpeechUtil
    [self.speechRecognizeManager start];
#else
    self.isRecgnizing = YES;
#endif

//    [self speechRecognizeDidStart];
    
//    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(2 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
//        [self speechRecognizeOnSliceRecognize:@"是的"];
//    });
}

- (void)stopRecognize {
    
    //结束识别 停止录音，停止识别请求
#if IsImportTKSpeechUtil
    [self.speechRecognizeManager stop];
#endif
    
    self.isRecgnizing = NO;
}


- (void)stopSyntheticAndPlay {
    
    // 已经释放，无需开始指令
    if (self.isRelease == YES) {return;}
    
    //结束语音合成
#if IsImportTKSpeechUtil
    [self.speechSynthesisManager stop];
#endif
}

///// 设置静音
//- (void)enableMute:(BOOL)mute {
//
//}


- (nonnull UIImage *)getLocalVideoPreViewImage:(nonnull NSString *)filePath {
    
    return self.videoGetImg;
}

- (nonnull UIImage *)getVideoPreViewImage:(nonnull NSURL *)path {
    return self.videoGetImg;
}

//切换摄像头
-(void)tkSwitchVideoCamera:(BOOL)isFrontCamera{
    [_avSession beginConfiguration];
    [self.avSession removeInput:_avDeviceInput];
    NSError *error=nil;
    NSString *errorMsg;
    if (isFrontCamera) {
        // 改成后置摄像头
        self.avDeviceInput=[AVCaptureDeviceInput deviceInputWithDevice:[self getCamera:AVCaptureDevicePositionBack] error:&error];

        errorMsg=@"初始化设备失败(后置摄像头)";
    }else{
        // 改成前置置摄像头
        self.avDeviceInput=[AVCaptureDeviceInput deviceInputWithDevice:[self getCamera:AVCaptureDevicePositionFront] error:&error];
        errorMsg=@"初始化设备失败(前置摄像头)";
    }
    
    if (error) {
        TKLogInfo(@"TKSmartOneVideo:取摄像头时出现问题");
        
        dispatch_async(dispatch_get_main_queue(), ^{
            
            if (self.delegate && [self.delegate respondsToSelector:@selector(connectServerRoomDidComplete:errorMsg:)]) {
                [self.delegate connectServerRoomDidComplete:NO errorMsg:errorMsg];
            }
        });
    }else{
        if ([self.avSession canAddInput:self.avDeviceInput]) {
            [self.avSession addInput:self.avDeviceInput];
        }
    }
    
//    if ([_avSession canAddOutput:self.videoOutput]) {
//        [_avSession addOutput:self.videoOutput];


        AVCaptureConnection *connection = [self.videoOutput connectionWithMediaType:AVMediaTypeVideo];
        [connection setEnabled:YES];
        if ([connection isVideoOrientationSupported]) {
            if (self.isLandscape) {

//                connection.videoOrientation = AVCaptureVideoOrientationLandscapeRight;
                    connection.videoOrientation = AVCaptureVideoOrientationLandscapeRight;
            } else {

                connection.videoOrientation = AVCaptureVideoOrientationPortrait;
            }
        }

//    }
    [_avSession commitConfiguration];
}

/**
 <AUTHOR> 2019年03月02日16:46:58
 @视频镜像处理
 @return CGAffineTransform
 */
- (CGAffineTransform)transformFromVideoBufferOrientationToOrientation:(AVCaptureVideoOrientation)orientation withAutoMirroring:(BOOL)mirror
{
    CGAffineTransform transform = CGAffineTransformIdentity;
    //树立摄像头顺时针旋转180度，这样后台看视频是正的
    transform = CGAffineTransformMakeRotation(M_PI/180.0*180.0);
    
    if ([[self getCamera:AVCaptureDevicePositionFront] position] == AVCaptureDevicePositionFront )
    {
        if ( mirror ) {
            transform = CGAffineTransformScale(transform, -1, 1);
        }
        else {
            if ( orientation ==AVCaptureVideoOrientationPortrait ) {
                transform = CGAffineTransformRotate( transform, M_PI );
            }
        }
    }
    transform = CGAffineTransformScale(transform, 1, 1);
    return transform;
}

/**
 <AUTHOR> 2019年03月02日11:04:39
 @获取指定摄像头
 @return AVCaptureDevice
 */
- (AVCaptureDevice *)getCamera:(AVCaptureDevicePosition) position
{
    //获取前置摄像头设备
    
    //返回和视频录制相关的所有默认设备
    NSArray *devices = [AVCaptureDevice devicesWithMediaType:AVMediaTypeVideo];
    //遍历这些设备返回跟position相关的设备
    for (AVCaptureDevice *device in devices) {
        if ([device position] == position) {
            return device;
        }
    }
    return nil;
}

//用AVFoundation捕捉视频帧，很多时候需要把某一帧转换成UIImage，用此函数：
 - (UIImage *) imageFromSampleBuffer:(CMSampleBufferRef) sampleBuffer {
    // 为媒体数据设置一个CMSampleBuffer的Core Video图像缓存对象
    CVImageBufferRef imageBuffer = CMSampleBufferGetImageBuffer(sampleBuffer);
    // 锁定pixel buffer的基地址
    CVPixelBufferLockBaseAddress(imageBuffer, 0);
    // 得到pixel buffer的基地址
    void *baseAddress = CVPixelBufferGetBaseAddress(imageBuffer);
    // 得到pixel buffer的行字节数
    size_t bytesPerRow = CVPixelBufferGetBytesPerRow(imageBuffer);
    // 得到pixel buffer的宽和高
    size_t width = CVPixelBufferGetWidth(imageBuffer);
    size_t height = CVPixelBufferGetHeight(imageBuffer);
    // 创建一个依赖于设备的RGB颜色空间
    CGColorSpaceRef colorSpace = CGColorSpaceCreateDeviceRGB();
    // 用抽样缓存的数据创建一个位图格式的图形上下文（graphics context）对象
    CGContextRef context = CGBitmapContextCreate(baseAddress, width, height, 8,bytesPerRow, colorSpace, kCGBitmapByteOrder32Little | kCGImageAlphaPremultipliedFirst);
    // 根据这个位图context中的像素数据创建一个Quartz image对象
    CGImageRef quartzImage = CGBitmapContextCreateImage(context);
    // 解锁pixel buffer
    CVPixelBufferUnlockBaseAddress(imageBuffer,0);
    // 释放context和颜色空间
    CGContextRelease(context);
    CGColorSpaceRelease(colorSpace);
    // 用Quartz image创建一个UIImage对象image
    UIImage *image = [UIImage imageWithCGImage:quartzImage];
    // 释放Quartz image对象
    CGImageRelease(quartzImage);
    return (image);
}


- (NSString *)getFullVideoPath {
    
    if ([TKStringHelper isNotEmpty:self.videoFilePath]) {return self.videoFilePath;}
    
    return [NSHomeDirectory() stringByAppendingFormat:@"/Documents/%@.mp4", TK_ONE_WAY_TEMP_MOVE_NAME];
}

- (CMSampleBufferRef)convertToBluetoothHeadphoneRecording:(CMSampleBufferRef)sampleBuffer
{
    CMSampleBufferRef newSampleBuffer = nil;
    
#if IsImportTKSpeechUtil
    
    int sampleRate = self.targetSampleRate;
    if (self.isRecgnizing)
    {
//        TKLogDebug(@"写入音频-录音数据");
        newSampleBuffer = [self.sampleBufferConverter convertSamplesFromSampleBuffer:sampleBuffer toSampleRate:sampleRate];
    } else {

        // 获取原采样率
        AudioStreamBasicDescription inputFormat = *CMAudioFormatDescriptionGetStreamBasicDescription(CMSampleBufferGetFormatDescription(sampleBuffer));
        int originSampleRate = inputFormat.mSampleRate;
        
        size_t sampleSize = CMSampleBufferGetTotalSampleSize(sampleBuffer);
        int len = (int)sampleSize * 1.0 / originSampleRate * sampleRate;
        if (len % 2 != 0) {  // 判断是否是2的倍数
            len += 1;  // 不是2的倍数则加1
        }
        char *buffer = (char *)calloc(len, sizeof(char));
        int ret = tk_ringbuffer_read(&tk_video_record_buf, (unsigned char*)buffer, len);
//        TKLogDebug(@"蓝牙测试：从缓存区读取长度为%d的数据写入文件", ret);
        if (ret > 0) {
            // TKLogDebug(@"写入音频-填充合成数据");
            
            // 记录指针，数据源需要手工管理
            [self addBufferPointer:buffer];
            
            newSampleBuffer = [self.sampleBufferConverter convertPCMStrmToCMSampleBufferRefWithBuffer:buffer len:len lastSampleTime:self.lastSampleTime toSampleRate:self.targetSampleRate];
        } else {
            // TKLogDebug(@"写入音频-填充非语音合成数据");
//
            newSampleBuffer = [self.sampleBufferConverter convertSamplesFromSampleBuffer:sampleBuffer toSampleRate:sampleRate];
            if (newSampleBuffer) {
                
                CMSampleBufferRef tempSampleBuffer = [self.sampleBufferConverter createEmptySampleBufferWithSampleBuffer:newSampleBuffer toSampleRate:self.targetSampleRate];
                CFRelease(newSampleBuffer);
                newSampleBuffer = tempSampleBuffer;
            }
            
            // 原始数据需要手动管理，这里并没有原始数据，直接释放
            if (buffer) {
                free(buffer);
            }
        }

        
    }
#endif
    
    return newSampleBuffer;
}

- (void)addBufferPointer:(char *)ptr {
    if (ptr == NULL) return;
    [self.bufferPointers addObject:[NSValue valueWithPointer:ptr]];
}

- (void)releaseAllBufferPointers {
    for (NSValue *addressValue in  self.bufferPointers) {
        char *ptr = (char *)[addressValue pointerValue];
        if (ptr) free(ptr);
    }
    [self.bufferPointers removeAllObjects];
}

#pragma mark 播放本地mp3处理
//转接等待中播放音乐
- (void)handleAudioPlay:(NSString*)voiceName
{
    [self handleAudioPlay:voiceName withType:@"mp3" sourceDirectory:@"Resources/TKOpenPlugin60006"];
}

- (void)handleAudioPlay:(NSString*)voiceName sourceDirectory:(NSString *)sourceDirectory
{
    [self handleAudioPlay:voiceName withType:@"mp3" sourceDirectory:sourceDirectory];
}

/**
 <AUTHOR> 2020年03月07日17:54:19
 @播放本地语音文件
 */
- (void)handleAudioPlay:(NSString*)voiceName withType:(NSString*)type sourceDirectory:(NSString *)sourceDirectory {
    NSBundle *bundle = [NSBundle bundleWithURL:[[NSBundle mainBundle] URLForResource:[NSString stringWithFormat:@"%@", TK_OPEN_RESOURCE_NAME] withExtension:@"bundle"]];
    NSURL *aUrl = nil;
    NSString* fPath = [bundle pathForResource:voiceName ofType:type inDirectory:sourceDirectory];
    
    //朗读单向播放本地音频支持准备话术使用h5传入的base64音频
    if([TKStringHelper isNotEmpty:self.configParam[@"prepareTipAudio"]]&&[voiceName isEqualToString:@"tk_oneVideo_remind"]){
        NSString *document = NSSearchPathForDirectoriesInDomains(NSDocumentDirectory, NSUserDomainMask, true)[0];
        NSString *filePath = [document stringByAppendingString:@"/readPrepareTipAudio.mp3"];
        
        NSFileManager *fileManager = [NSFileManager defaultManager];
        BOOL fileExists = [fileManager fileExistsAtPath:filePath];
        
        if (fileExists != NO) {
            // 删除原有的视频
            NSError *error = nil;
            [fileManager removeItemAtPath:filePath error:&error];
            if (error) {
                TKLogInfo(@"删除活体音频文件出错%@", error.description);
            } else {
                fileExists = NO;
                TKLogInfo(@"活体音频文件删除成功");
            }
        }

        NSData *audioData=[TKBase64Helper dataWithDecodeBase64String:self.configParam[@"prepareTipAudio"]];
        if(audioData){
            [audioData writeToFile:filePath atomically:YES];
            fPath = filePath;
        }
    }

    
    NSFileManager *fm = [[NSFileManager alloc] init];
    
    if ([fm fileExistsAtPath:fPath]) {
        aUrl = [NSURL fileURLWithPath:fPath];
    }
    
    AVPlayerItem * songItem = [[AVPlayerItem alloc]initWithURL:aUrl];
    self.mp3Player = [[AVPlayer alloc]initWithPlayerItem:songItem];
    [self.mp3Player play];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(moviePlayDidEnd:) name:AVPlayerItemDidPlayToEndTimeNotification object:self.mp3Player.currentItem];
}

//音频循环播放
- (void)moviePlayDidEnd:(NSNotification*)notification{
    dispatch_async(dispatch_get_main_queue(), ^{
        
        if (self.delegate && [self.delegate respondsToSelector:@selector(speechSynthesisDidPlayDone)]) {
            [self.delegate speechSynthesisDidPlayDone];
        }
    });
}

//停止播放音乐
- (void)handleAudioStopPlay{
//    [[NSNotificationCenter defaultCenter] removeObserver:self];
    [[NSNotificationCenter defaultCenter] removeObserver:self name:AVPlayerItemDidPlayToEndTimeNotification object:nil];
    [self.mp3Player pause];
}



#pragma mark - AVCaptureVideoDataOutputSampleBufferDelegate
- (void)captureOutput:(AVCaptureOutput *)output didOutputSampleBuffer:(CMSampleBufferRef)sampleBuffer fromConnection:(AVCaptureConnection *)connection
{
    @autoreleasepool
    {
        // 抓取数据定时人脸检测
        if (output == self.videoOutput)
        {
            UIImage *faceDetectImage = [self imageFromSampleBuffer:sampleBuffer];
            dispatch_async(dispatch_get_main_queue(), ^{

                if (self.delegate && [self.delegate respondsToSelector:@selector(OnVideoDataCallBack:)]) {
                    [self.delegate OnVideoDataCallBack:faceDetectImage];
                }
            });
        }
        
        CMTime lastSampleTime = CMSampleBufferGetPresentationTimeStamp(sampleBuffer);
        self.lastSampleTime = lastSampleTime;
        
//        TKLogInfo(@"思迪录制日志：当前写入状态:%ld", (long)self.assetWriter.status);
        if (self.isRecording)
        {
            // 写入视频
            if (output == self.videoOutput)
            {
                // 只有视频帧时才开始录制，防止首帧视频帧为空的情况
                if(self.assetWriter.status == AVAssetWriterStatusUnknown)
                {
                    BOOL canStartWrite = [self.assetWriter startWriting];
                    if (canStartWrite) {

                        [self.assetWriter startSessionAtSourceTime:lastSampleTime];
                        TKLogInfo(@"思迪录制日志：写视频数据开始");
                    } else {
                        TKLogInfo(@"思迪录制日志：self.assetWriter can't start write");
                    }
                }
                
                if (!self.isGetImg) {
                    self.isGetImg = YES;
                    self.videoGetImg = [self imageFromSampleBuffer:sampleBuffer];
                }

                if (self.assetWriter.status == AVAssetWriterStatusWriting && [self.videoWriterInput isReadyForMoreMediaData])
                {
                    if(![self.videoWriterInput appendSampleBuffer:sampleBuffer])
                    {

                        [self recordErrorCallBack:[NSString stringWithFormat:@"%@(%@)", @"视频录制异常退出", @"无法写入视频数据"]];
                        TKLogInfo(@"思迪录制日志：无法写入视频输入");
                    }
//                    else {
//                        TKLogInfo(@"思迪录制日志：写入视频输入");
//                    }
                }
//                else {
//                    TKLogInfo(@"思迪录制日志：videoWriterInput 未ready");
//                }
            }
            // 写入音频
            else if (output == self.audioOutput)
            {
                // 录入识别的声音
                if (self.assetWriter.status == AVAssetWriterStatusWriting && [self.audioWriterInput isReadyForMoreMediaData])
                {
#if IsImportTKSpeechUtil
                    CMSampleBufferRef newSampleBuffer = sampleBuffer;

                    if (speechRecognizeToolType == 1 &&
                        [[self.configParam getStringWithKey:@"enableHeadphone"] isEqualToString:@"1"]) { // 阿里语音
                        newSampleBuffer = [self convertToBluetoothHeadphoneRecording:sampleBuffer];
                    }
                    
                    if( ![self.audioWriterInput appendSampleBuffer:newSampleBuffer] )
                    {
                        [self recordErrorCallBack:[NSString stringWithFormat:@"%@(%@)", @"视频录制异常退出", @"无法写入音频数据"]];
                    } else {
                        //                            TKLogInfo(@"思迪录制日志：audioWriterInput is writing data(record)");
                    }
                    if (speechRecognizeToolType == 1 &&
                        [[self.configParam getStringWithKey:@"enableHeadphone"] isEqualToString:@"1"]) { // 阿里语音
                        
                        if (newSampleBuffer) CFRelease(newSampleBuffer); // 销毁转换生成的newSampleBuffer
                        
                        // 原始数据需要手动管理，在释放newSampleBuffer之后再释放原始数据
//                        if (_lastConvertBuffer) {
//                            free(_lastConvertBuffer);
//                            _lastConvertBuffer = nil;
//                        }
                    }
#else
                    if( ![self.audioWriterInput appendSampleBuffer:sampleBuffer] )
                    {
                        [self recordErrorCallBack:[NSString stringWithFormat:@"%@(%@)", @"视频录制异常退出", @"无法写入音频数据"]];
                    } else {
                        //                            TKLogInfo(@"思迪录制日志：audioWriterInput is writing data(record)");
                    }
#endif
                }
            }
            
            if(self.assetWriter.status > AVAssetWriterStatusWriting)
            {
                if (self.assetWriter.status == AVAssetWriterStatusCompleted)
                {
                    TKLogInfo(@"思迪录制日志：写视频数据完成");
                }else if(self.assetWriter.status == AVAssetWriterStatusFailed )
                {
                    
                    TKLogInfo(@"思迪录制日志：写视频数据失败:%@", self.assetWriter.error);
                    [self recordErrorCallBack:[NSString stringWithFormat:@"%@(%ld)", @"视频录制异常退出", (long)self.assetWriter.error.code]];
                }
                
                return;
            }
        }
    }
}

- (void)recordErrorCallBack:(NSString *)errorMsg
{
    if (self.isRecording == YES) {
        self.isRecording = NO;
        
        dispatch_async(dispatch_get_main_queue(), ^{
            
            if (self.delegate && [self.delegate respondsToSelector:@selector(recordStopErrocCallBack:)]) {
                [self.delegate recordStopErrocCallBack:[NSString stringWithFormat:@"%@", errorMsg]];
            }
        });
    }
    
}


#pragma mark TKSpeechSynthesisManagerDelegate
// 语音合成开始回调
- (void)speechSynthesisDidStart{

#if IsImportTKSpeechUtil
    tk_ringbuffer_reset(&tk_video_record_buf);
    // TKLogDebug(@"蓝牙测试：清空待写入数据");
#endif
    
    dispatch_async(dispatch_get_main_queue(), ^{
        
        if (self.delegate && [self.delegate respondsToSelector:@selector(speechSynthesisDidStart)]) {
            [self.delegate speechSynthesisDidStart];
        }
    });
}

// 多段语音合成开始回调
- (void)speechSynthesisDidStartWithIndex:(int)index synthesisArray:(NSArray *)synthesisArray {
    
    dispatch_async(dispatch_get_main_queue(), ^{
        
        if (self.delegate && [self.delegate respondsToSelector:@selector(speechSynthesisDidStartWithIndex:synthesisArray:)]) {
            [self.delegate speechSynthesisDidStartWithIndex:index synthesisArray:synthesisArray];
        }
    });
}


// 语音合成失败
- (void)speechSynthesisDidFail:(NSString *)errorMsg {

#if IsImportTKSpeechUtil
    tk_ringbuffer_reset(&tk_video_record_buf);
    // TKLogDebug(@"蓝牙测试：清空待写入数据");

    [self.speechSynthesisManager stop];
#endif
    
    dispatch_async(dispatch_get_main_queue(), ^{
        
        if (self.delegate && [self.delegate respondsToSelector:@selector(speechSynthesisDidFail:)]) {
            [self.delegate speechSynthesisDidFail:errorMsg];
        }
        
    });
    
}

// 语音合成播放结束回调
- (void)speechSynthesisDidPlayDone{
    
    dispatch_async(dispatch_get_main_queue(), ^{
        
        if (self.delegate && [self.delegate respondsToSelector:@selector(speechSynthesisDidPlayDone)]) {
            [self.delegate speechSynthesisDidPlayDone];
        }
    });
}

- (void)speechSynthesisDidPlayDoneWithIndex:(int)index synthesisArray:(NSArray *)synthesisArray {
    dispatch_async(dispatch_get_main_queue(), ^{
        
        if (self.delegate && [self.delegate respondsToSelector:@selector(speechSynthesisDidPlayDoneWithIndex:synthesisArray:)]) {
            [self.delegate speechSynthesisDidPlayDoneWithIndex:index synthesisArray:synthesisArray];
        }
    });
}


// 语音合成buffer
- (void)speechSynthesisDidDataBuffer:(char*)buffer len:(int)len {

#if IsImportTKSpeechUtil
    if (self.isRecording) {
        int ret = tk_ringbuffer_write(&tk_video_record_buf, (unsigned char*)buffer, len);
        // TKLogDebug(@"蓝牙测试：待写入合成数据长度%d", ret);
        
        if (ret == 0) {
            // TKLogDebug(@"蓝牙测试：数据已满，休眠");
            CGFloat sampleTime = 2048.0 / 44100 * 0.5;
            int len_44100_to_target = sampleTime * 2 * self.targetSampleRate;
            if (len_44100_to_target % 2 != 0) {  // 判断是否是2的倍数
                len_44100_to_target += 1;  // 不是2的倍数则加1
            }
            
            [NSThread sleepForTimeInterval:(len / len_44100_to_target - 3) * sampleTime]; // 提早三次获取数据时醒来
            // TKLogDebug(@"蓝牙测试：休眠结束");
            
            if (self.isRecording == NO || self.isRelease == YES) {
                // TKLogDebug(@"蓝牙测试：抛弃合成数据，有问题！！！");
                return;
            }

            // 继续调用
            [self speechSynthesisDidDataBuffer:buffer len:len];
        }
    }
#endif
}


#pragma mark  TKSpeechRecognizeManagerDelegate
/// 语音识别开始回调
- (void)speechRecognizeDidStart {
    self.isRecgnizing = YES;
    
    dispatch_async(dispatch_get_main_queue(), ^{
        
        if (self.delegate && [self.delegate respondsToSelector:@selector(speechRecognizeDidStart)]) {
            [self.delegate speechRecognizeDidStart];
        }
    });
}

// 每个语音包分片识别结果
// @param result 一段语音每次识别结果
- (void)speechRecognizeOnSliceRecognize:(NSString *)result {
    dispatch_async(dispatch_get_main_queue(), ^{
        
        if (self.delegate && [self.delegate respondsToSelector:@selector(speechRecognizeOnSliceRecognize:)]) {
            [self.delegate speechRecognizeOnSliceRecognize:result];
        }
    });
}

// 一次语音识别结果
// @param reslut 一次识别结果字符串
- (void)speechRecognizeDidRecognize:(NSString *)result{
    
}

// 语音识别结束回调
- (void)speechRecognizeDidComplete {
    self.isRecgnizing = NO;
    
    dispatch_async(dispatch_get_main_queue(), ^{
        
        if (self.delegate && [self.delegate respondsToSelector:@selector(speechRecognizeDidComplete)]) {
            [self.delegate speechRecognizeDidComplete];
        }
    });
}

// 语音识别失败回调
- (void)speechRecognizeDidFail:(NSString *)errorMsg{
    
#if IsImportTKSpeechUtil
    [self.speechRecognizeManager stop];
#endif
    
    dispatch_async(dispatch_get_main_queue(), ^{
        
        if (self.delegate && [self.delegate respondsToSelector:@selector(speechRecognizeDidFail:)]) {
            [self.delegate speechRecognizeDidFail:errorMsg];
        }
    });
}


#pragma mark - lazyloading
/**
 <AUTHOR> 2019年03月02日10:48:12
 @懒加载初始化AVCaptureSession
 @return AVCaptureSession
 */
- (AVCaptureSession *)avSession{
    if (!_avSession) {
        _avSession=[[AVCaptureSession alloc] init];
        [_avSession beginConfiguration];
        if ([_avSession canSetSessionPreset:AVCaptureSessionPreset640x480]) {//设置分辨率
            _avSession.sessionPreset=AVCaptureSessionPreset640x480;
            
        }

        if ([_avSession canAddInput:self.avDeviceInput]) {
            [_avSession addInput:self.avDeviceInput];
        }

        
        if ([_avSession canAddInput:self.audioMicInput]) {
            [_avSession addInput:self.audioMicInput];
        }
        
        if ([_avSession canAddOutput:self.videoOutput]) {
            [_avSession addOutput:self.videoOutput];


            AVCaptureConnection *connection = [self.videoOutput connectionWithMediaType:AVMediaTypeVideo];
            [connection setEnabled:YES];
            if ([connection isVideoOrientationSupported]) {
                if (self.isLandscape) {

                    connection.videoOrientation = AVCaptureVideoOrientationLandscapeRight;
//                    connection.videoOrientation = AVCaptureVideoOrientationPortraitUpsideDown;
                } else {

                    connection.videoOrientation = AVCaptureVideoOrientationPortrait;
                }
            }

        }
        if ([_avSession canAddOutput:self.audioOutput]) {
            [_avSession addOutput:self.audioOutput];
        }

        [_avSession commitConfiguration];
    }
    return _avSession;
}



/**
 <AUTHOR> 2019年03月02日10:57:35
 @初始化懒加载AVCaptureDeviceInput摄像头
 @return AVCaptureDeviceInput
 */
- (AVCaptureDeviceInput *)avDeviceInput{
    if (!_avDeviceInput) {
        NSError *error=nil;
        _avDeviceInput=[AVCaptureDeviceInput deviceInputWithDevice:[self getCamera:AVCaptureDevicePositionFront] error:&error];
        if (error) {
            TKLogInfo(@"思迪录制日志：取前置摄像头时出现问题");
            
            dispatch_async(dispatch_get_main_queue(), ^{
                
                if (self.delegate && [self.delegate respondsToSelector:@selector(connectServerRoomDidComplete:errorMsg:)]) {
                    [self.delegate connectServerRoomDidComplete:NO errorMsg:@"初始化设备失败(前置摄像头)"];
                }
            });
            
            
            return nil;
        }
    }
    return _avDeviceInput;
}


/**
 <AUTHOR> 2019年03月02日10:57:35
 @初始化懒加载AVCaptureDeviceInput麦克风
 @return AVCaptureDeviceInput
 */
- (AVCaptureDeviceInput *)audioMicInput {
    if (!_audioMicInput) {
        
        // 先获取蓝牙耳机
        AVCaptureDevice *audioDevice = [self findBluetoothDevice];
        NSError *error = nil;
        _audioMicInput = [AVCaptureDeviceInput deviceInputWithDevice:audioDevice error:&error];
        if (error) {
            // TKLogDebug(@"获取蓝牙耳机失败 : %@", error.localizedDescription);
            
            // 获取麦克风
            AVCaptureDevice *mic = [AVCaptureDevice defaultDeviceWithMediaType:AVMediaTypeAudio];
            NSError *error;
            _audioMicInput = [AVCaptureDeviceInput deviceInputWithDevice:mic error:&error];
            if (error) {

                dispatch_async(dispatch_get_main_queue(), ^{

                    if (self.delegate && [self.delegate respondsToSelector:@selector(connectServerRoomDidComplete:errorMsg:)]) {
                        [self.delegate connectServerRoomDidComplete:NO errorMsg:@"初始化设备失败(麦克风)"];
                    }
                });
                TKLogInfo(@"思迪录制日志：获取麦克风失败");
                return nil;
            }
        }
    }
    return _audioMicInput;
}

- (AVCaptureDevice *)findBluetoothDevice {

    NSArray *inputs = [[AVAudioSession sharedInstance] availableInputs];
    for (AVAudioSessionPortDescription *port in inputs) {
        if ([port.portType isEqualToString:AVAudioSessionPortBluetoothHFP] ||
            [port.portType isEqualToString:AVAudioSessionPortBluetoothA2DP] ||
            [port.portType isEqualToString:AVAudioSessionPortBluetoothLE]) {

            AVCaptureDevice *audioDevice = [AVCaptureDevice deviceWithUniqueID:port.UID];
            return audioDevice;
        }
    }
    return nil;
}


/**
 <AUTHOR> 2019年03月02日16:20:36
 @初始化懒加载AVCaptureVideoDataOutput输出流l线程队列
 @return AVCaptureVideoDataOutput
 */
- (dispatch_queue_t)outPutQueue{
    if (!_outPutQueue) {
        _outPutQueue = dispatch_queue_create("outPutQueue", DISPATCH_QUEUE_SERIAL);
    }
    return _outPutQueue;
}


/**
 <AUTHOR> 2019年03月02日16:25:15
 @初始化懒加载AVCaptureVideoDataOutput视频输出流
 @return AVCaptureVideoDataOutput
 */
- (AVCaptureVideoDataOutput *)videoOutput{
    if (!_videoOutput) {
        _videoOutput = [[AVCaptureVideoDataOutput alloc] init];
        NSDictionary *rgbOutputSettings = @{(id) kCVPixelBufferPixelFormatTypeKey: [NSNumber numberWithInt:kCVPixelFormatType_32BGRA]};
        [_videoOutput setVideoSettings:rgbOutputSettings];
        [_videoOutput setAlwaysDiscardsLateVideoFrames:YES];
        [_videoOutput setSampleBufferDelegate:self queue:self.outPutQueue];
        
//        [[_videoOutput connectionWithMediaType:AVMediaTypeVideo] setEnabled:YES];

        
    }
    return _videoOutput;
}


/**
 <AUTHOR> 2019年03月02日16:21:36
 @初始化懒加载AVCaptureVideoDataOutput音频输出流
 @return AVCaptureVideoDataOutput
 */
- (AVCaptureAudioDataOutput *)audioOutput{
    if (!_audioOutput) {
        // 添加音频输出
        _audioOutput = [[AVCaptureAudioDataOutput alloc] init];
        [_audioOutput setSampleBufferDelegate:self queue:self.outPutQueue];
        
        [_audioOutput recommendedAudioSettingsForAssetWriterWithOutputFileType:AVFileTypeQuickTimeMovie];
    }
    return _audioOutput;
}

/**
 <AUTHOR> 2019年03月02日16:36:51
 @初始化懒加载AVAssetWriter写流
 @return AVAssetWriter
 */
- (AVAssetWriter *)assetWriter{
    if (!_assetWriter) {
        NSError *error = nil;
        _assetWriter = [[AVAssetWriter alloc] initWithURL:[NSURL fileURLWithPath:[self getFullVideoPath]] fileType:AVFileTypeMPEG4 error:&error];
        NSParameterAssert(_assetWriter);
        if(error)
        {
            TKLogInfo(@"思迪录制日志：assetWriter:%@", [error localizedDescription]);
            dispatch_async(dispatch_get_main_queue(), ^{
                
                if (self.delegate && [self.delegate respondsToSelector:@selector(connectServerRoomDidComplete:errorMsg:)]) {
                    [self.delegate connectServerRoomDidComplete:NO errorMsg:@"初始化设备失败(无法创建资源写入器)"];
                }
            });
            
            
        }else{
            // add input
            if ([_assetWriter canAddInput:self.videoWriterInput])
            {
                [_assetWriter addInput:self.videoWriterInput];
              
            }
            
            if ([_assetWriter canAddInput:self.audioWriterInput])
            {
                [_assetWriter addInput:self.audioWriterInput];
            
            }

        }
    }
    return _assetWriter;
}

/**
 <AUTHOR> 2019-03-02 16:44:01
 @初始化懒加载AVAssetWriterInput视频写入流
 @return AVAssetWriterInput
 */
- (AVAssetWriterInput *)videoWriterInput{
    if (!_videoWriterInput) {
        // 添加视频输入
        NSDictionary *videoCompressionProps = [NSDictionary dictionaryWithObjectsAndKeys:[NSNumber numberWithDouble:512.0*1024.0],AVVideoAverageBitRateKey,  nil ];
        
        int videoWidth = [TKStringHelper isNotEmpty:self.configParam[@"videoWidth"]] ? [self.configParam getIntWithKey:@"videoWidth"] : 480;
        int videoheight = [TKStringHelper isNotEmpty:self.configParam[@"videoHeight"]] ? [self.configParam getIntWithKey:@"videoHeight"] : 640;
        
        if (self.isLandscape) {
            CGFloat temp = videoWidth;
            videoWidth = videoheight;
            videoheight = temp;
        }
        NSDictionary *videoSettings = [NSDictionary dictionaryWithObjectsAndKeys:AVVideoCodecH264, AVVideoCodecKey,[NSNumber numberWithInt:videoWidth], AVVideoWidthKey,[NSNumber numberWithInt:videoheight],AVVideoHeightKey,videoCompressionProps, AVVideoCompressionPropertiesKey, nil];
        
        _videoWriterInput = [AVAssetWriterInput assetWriterInputWithMediaType:AVMediaTypeVideo outputSettings:videoSettings];
        
        _videoWriterInput.expectsMediaDataInRealTime = YES;// 是否裁剪视频输入
        
        NSParameterAssert(_videoWriterInput);
        
//        // 反转镜像
//        BOOL isMirrored = NO;
//
//        if ([self.avPreviewLayer respondsToSelector:@selector(connection)])
//        {
//            isMirrored = self.avPreviewLayer.connection.isVideoMirrored;
//        }
//
//        _videoWriterInput.transform
//        = [self transformFromVideoBufferOrientationToOrientation:
//           AVCaptureVideoOrientationPortrait withAutoMirroring:isMirrored];
    }
    return _videoWriterInput;
}

/**
 <AUTHOR> 2019年03月02日16:44:39
 @初始化懒加载AVAssetWriterInput音频写入流
 @return AVAssetWriterInput
 */
- (AVAssetWriterInput *)audioWriterInput{
    if (!_audioWriterInput) {
        // 添加音频输入
        //阿里质检语音识别要用单声道，采样率 16000
        AudioChannelLayout acl;
        
        bzero( &acl, sizeof(acl));
        
        acl.mChannelLayoutTag = kAudioChannelLayoutTag_Mono;
        
        NSDictionary* audioOutputSettings = nil;
        
        audioOutputSettings = [ NSDictionary dictionaryWithObjectsAndKeys:
                               [ NSNumber numberWithInt: kAudioFormatMPEG4AAC ], AVFormatIDKey,
//                               [ NSNumber numberWithInt: kAudioFormatLinearPCM ], AVFormatIDKey,
//                               [ NSNumber numberWithInt: kAudioFormatLinearPCM ], AVLinearPCMIsBigEndianKey,
                               //                               [ NSNumber numberWithInt:32000], AVEncoderBitRateKey,
                               [ NSNumber numberWithFloat: 16000 ], AVSampleRateKey,
                               [ NSNumber numberWithInt:1 ], AVNumberOfChannelsKey,
                               [ NSData dataWithBytes: &acl length: sizeof( acl ) ], AVChannelLayoutKey,
                               nil ];
        
        _audioWriterInput = [AVAssetWriterInput assetWriterInputWithMediaType: AVMediaTypeAudio
                                                               outputSettings: audioOutputSettings];
        _audioWriterInput.expectsMediaDataInRealTime = YES;
        
    }
    return _audioWriterInput;
}

/**
 <AUTHOR> 2019年03月02日11:04:22
 @初始化懒加载AVCaptureVideoPreviewLayer
 @return AVCaptureVideoPreviewLayer
 */
-(AVCaptureVideoPreviewLayer *)avPreviewLayer{
    if (!_avPreviewLayer) {
        _avPreviewLayer= [[AVCaptureVideoPreviewLayer alloc] initWithSession:self.avSession];
//        _avPreviewLayer.frame = [UIScreen mainScreen].bounds;
        _avPreviewLayer.videoGravity= AVLayerVideoGravityResizeAspectFill;
//        _avPreviewLayer.connection.videoOrientation = AVCaptureVideoOrientationPortrait;
        
    }
    return _avPreviewLayer;
}

#if IsImportTKSpeechUtil
/**
<AUTHOR> 2021年01月15日14:47:12
@初始化懒加语音合成对象
@return 语音合成对象
*/
- (id)speechSynthesisManager {
    if (!_speechSynthesisManager) {
        _speechSynthesisManager = [[TKSpeechSynthesisManager alloc] initWithConfig:self.configParam];
        _speechSynthesisManager.delegate = self;
        
    }
    return _speechSynthesisManager;
}

/**
<AUTHOR> 2021年01月15日14:47:12
@初始化懒加语音识别对象
@return 语音识别对象
*/
- (id)speechRecognizeManager {
    if (!_speechRecognizeManager) {
        _speechRecognizeManager = [[TKSpeechRecognizeManager alloc] initWithConfig:self.configParam];
        _speechRecognizeManager.delegate = self;
    }
    return _speechRecognizeManager;
}
#endif


- (TKSampleBufferConverter *)sampleBufferConverter {
    if (!_sampleBufferConverter) {
        _sampleBufferConverter = [TKSampleBufferConverter new];
    }
    
    return _sampleBufferConverter;
}

- (NSMutableArray *)bufferPointers {
    if (!_bufferPointers) {
        _bufferPointers = [NSMutableArray array];
    }
    return _bufferPointers;
}

@end
