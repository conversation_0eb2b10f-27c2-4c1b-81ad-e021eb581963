//
//  TKChatLocalVideoRecordManager.m
//  TKOpenAccount-Standard
//
//  Created by <PERSON> on 2023/8/29.
//  Copyright © 2023 thinkive. All rights reserved.
//

#import "TKChatLocalVideoRecordManager.h"
#import "TKSampleBufferConverter.h"

#import <TChat/TChatDefine.h>
#import <TChat/TChatErrorCode.h>
#import <TChat/TChatCore.h>

#import "TKOpenAccountService.h"

// 语音合成
#if __has_include(<TKSpeechSynthesisManager.h>)
#define IsImportTKSpeechUtil 1
#import "TKSpeechSynthesisManager.h"
#import "TKSpeechRecognizeManager.h"
#import "TKringBuf.h"
#define kMaxBufferSize (16000 * 16)    // 256000
#define kDownloadLenth (1024 * 64)      // 65536
RINGBUFFER_NEW(tk_chat_local_video_record_buf, kMaxBufferSize)
#import "TKNLSPlayAudio.h"

#elif __has_include("TKSpeechSynthesisManager.h")
#define IsImportTKSpeechUtil 1
#import "TKSpeechSynthesisManager.h"
#import "TKSpeechRecognizeManager.h"
#import "TKringBuf.h"
#define kMaxBufferSize (16000 * 16)    // 256000
#define kDownloadLenth (1024 * 64)      // 65536
RINGBUFFER_NEW(tk_chat_local_video_record_buf, kMaxBufferSize)
#import "TKNLSPlayAudio.h"

#else
#define IsImportTKSpeechUtil 0
#endif


#if IsImportTKSpeechUtil
@interface TKChatLocalVideoRecordManager()<AVAudioPlayerDelegate, TKSpeechSynthesisManagerDelegate, TKSpeechRecognizeManagerDelegate, TKCCNotifyMessageDelegate, TKCCTextMsgDelegate, TKCCTransDataDelegate, TKCCVideoDataDelegate, TKCCAudioDataDelegate, TKCCSnapShotDelegate, TKCCRecordDelegate, NlsPlayerDelegate>
#else
@interface TKChatLocalVideoRecordManager()<AVAudioPlayerDelegate, TKCCNotifyMessageDelegate, TKCCTextMsgDelegate, TKCCTransDataDelegate, TKCCVideoDataDelegate, TKCCAudioDataDelegate, TKCCSnapShotDelegate, TKCCRecordDelegate>

#endif
{
    dispatch_queue_t _tchatReleaseQueue;
    dispatch_queue_t _addSubtitlesToTheVideoQueue;
    dispatch_queue_t _addPCMToTheVideoQueue;
    dispatch_queue_t _audioDownloadQueue;
    dispatch_queue_t _audioPlayQueue;
    
}

#if IsImportTKSpeechUtil
@property (nonatomic, readwrite, strong) id<TKSpeechRecognizeManagerProtocol> speechRecognizeManager;
@property (nonatomic, readwrite, strong) id<TKSpeechSynthesisManagerProtocol> speechSynthesisManager;
@property(nonatomic, strong) TKNLSPlayAudio *nlsAudioPlayer;//语音播放工具类
#endif


@property (nonatomic, assign) BOOL isGetImg;//是否截取过照片
@property (nonatomic, strong) UIImage *videoGetImg;//视频录制中获取的照片

@property (nonatomic, assign) BOOL isRecording;//是否正在录制
@property (nonatomic, readwrite, assign) BOOL isRelease;    // 是否已经释放资源

@property (nonatomic, assign) CFAbsoluteTime recordStartTime;//视频开始录制时间

@property (nonatomic, strong) AVPlayer *mp3Player;//MP3文件播放
@property (nonatomic, readwrite, assign) BOOL isRecgnizing; // 是否正在识别

@property (nonatomic, readwrite, assign) CMTime lastSampleTime;
@property (nonatomic, readwrite, assign) int targetSampleRate; // 目标采样率，默认16000

@property (nonatomic, readwrite, assign) int recordTaskID;    // 录制任务id
@property (nonatomic, readwrite, copy) NSString *finalVideoPath; // 最终录制的视频录制
@property (nonatomic, readwrite, assign) BOOL isDisconnecting; // 正在断开链接
@property (nonatomic, readwrite, assign) BOOL isFirstReleaseTChat; // 是否第一次释放tchat

@property (nonatomic, readwrite, strong) NSTimer *takePicturesTimer; // 超时定时器
@property (nonatomic, readwrite, strong) dispatch_source_t addSubtitlesToTheVideoTimer; // 添加字幕定时器
@property (nonatomic, readwrite, strong) dispatch_source_t addPCMToTheVideoTimer; // 添加pcm定时器

@property (nonatomic, readwrite, strong) UITextView *subtitlesTextView;
@property (nonatomic, assign) int changeReadTextViewWords; // 变色的字数
@property (nonatomic, readwrite, strong) NSTimer *changeReadTextViewOffSetTimer; // testView滚动定时器

@property (nonatomic, strong) TKOpenAccountService * _Nonnull openAccountService;
@property (nonatomic, readwrite, strong) NSData *emptyData; // 空数据包
@property (nonatomic, readwrite, strong) NSURLSessionDataTask *dataTask;
@property (nonatomic, readwrite, assign) BOOL isForceStopPlayVideo; // 是否强制暂停播放
//@property (nonatomic, readwrite, strong) dispatch_source_t videoRecordBufferCheckTimer; // 视频录制buffer检查定时器
@property (nonatomic, readwrite, strong) NSURLSession *session; // 下载session


@end

@implementation TKChatLocalVideoRecordManager
@synthesize delegate = _delegate;
@synthesize configParam = _configParam;
@synthesize contentView = _contentView;
@synthesize videoFilePath = _videoFilePath;
@synthesize isLandscape = _isLandscape;
@synthesize isFrontCamera = _isFrontCamera;
//@synthesize disableMicrophone = _disableMicrophone;
@synthesize subtitlesScrollSpeed = _subtitlesScrollSpeed;

// 定义 WAV 文件头部结构
typedef struct {
    char chunkID[4];
    uint32_t chunkSize;
    char format[4];
    char subchunk1ID[4];
    uint32_t subchunk1Size;
    uint16_t audioFormat;
    uint16_t numChannels;
    uint32_t sampleRate;
    uint32_t byteRate;
    uint16_t blockAlign;
    uint16_t bitsPerSample;
    char subchunk2ID[4];
    uint32_t subchunk2Size;
} WAVHeader;

#pragma mark - Init && Dealloc
- (void)dealloc {
    
    [self stopAddPCMToTheVideoTimer];
    [self stopAddSubtitlesToTheVideoTimer];
    [self stopTakePicturesTimer];
}


- (nonnull instancetype)initWithConfig:(nonnull NSDictionary *)configParam {
    
    if (self = [super init]) {
        self.isGetImg = NO;
        self.configParam = configParam;
        self.isFrontCamera = NO;
        
        if ([TKStringHelper isNotEmpty:self.configParam[@"aliTTSSampleRate"]]) {
            //语音合成采样率，默认值是16000，支持8000
            self.targetSampleRate = [self.configParam[@"aliTTSSampleRate"] intValue];
        } else {
            self.targetSampleRate = 16000;
        }
        
        _tchatReleaseQueue = dispatch_queue_create("com.thinkive.TKChatLocalVideoRecordManager.tchatReleaseQueue", DISPATCH_QUEUE_SERIAL);
        _addSubtitlesToTheVideoQueue = dispatch_queue_create("com.thinkive.TKChatLocalVideoRecordManager.addSubtitlesToTheVideoQueue", DISPATCH_QUEUE_SERIAL);
        _addPCMToTheVideoQueue = dispatch_queue_create("com.thinkive.TKChatLocalVideoRecordManager.addPCMToTheVideoQueue", DISPATCH_QUEUE_SERIAL);
        _audioDownloadQueue = dispatch_queue_create("com.thinkive.TKChatLocalVideoRecordManager.audioDownloadQueue", DISPATCH_QUEUE_SERIAL);
        _audioPlayQueue = dispatch_queue_create("com.thinkive.TKChatLocalVideoRecordManager.audioPlayQueue", DISPATCH_QUEUE_SERIAL);
    }
    
    return self;
}



#pragma mark - Selector
- (void)bootDevcie:(BOOL)isFirst {

    // 第二次启动的时候直接激活设备
    if (isFirst == YES) {
        TKLogInfo(@"思迪服务器录制日志：正在初始化TChat");
        
        int initFlag = [TChatCore InitSDK];
        
        //直接返回失败的时候就不走回调了
        if (initFlag!=0) {
            TKLogInfo(@"思迪服务器录制日志：初始化TChat失败");
            
            dispatch_async(dispatch_get_main_queue(), ^{
                
                NSString *errorMsg = @"初始化视频出错";
                
                if (self.delegate && [self.delegate respondsToSelector:@selector(connectServerRoomDidComplete:errorMsg:)]) {
                    [self.delegate connectServerRoomDidComplete:NO errorMsg:[NSString stringWithFormat:@"%@(%d)", errorMsg, initFlag]];
                }
            });
            return;
        }
        
        [TChatCore shareTChatCore].notifyMsgDelegate = self;
        [TChatCore shareTChatCore].transDataDelegate = self;
        [TChatCore shareTChatCore].textMsgDelegate = self;
        [TChatCore shareTChatCore].videodataDelegate = self;
    //    [TChatCore shareTChatCore].audiodataDelegate = self;
        [TChatCore shareTChatCore].snapshotDelegate = self;
        [TChatCore shareTChatCore].recordDelegate = self;
        
//        [TChatCore SetSDKOptionInt:TKCC_SO_OUTPUT_AUDIO_DATA :1]; // 设置音频输出设备
//        [TChatCore SetSDKOptionInt:TKCC_SO_OUTPUT_VIDEO_DATA :1]; // 设置视频输出设备
//    //    [TChatCore SetSDKOptionInt:TKCC_SO_OUTPUT_PEER_AUDIO_DATA :1]; // 获取对端音频数据
//
//        [TChatCore SetSDKOptionInt:TKCC_SO_LOCAL_VIDEO_FPS : 30]; // 设置视频编码帧率
//        [TChatCore SetSDKOptionInt:TKCC_SO_LOCAL_AUDIO_AEC_LEVEL : -1]; // 关闭单向视频中的回声消除，降低CPU
        
        [TChatCore ActiveCallLog:NO];
        
        [TChatCore SetSDKOptionInt:TKCC_SO_RECORD_VIDEO_FORMAT :12];
        [TChatCore SetSDKOptionInt:TKCC_SO_RECORD_VIDEO_WIDTH :480];
        [TChatCore SetSDKOptionInt:TKCC_SO_RECORD_VIDEO_HEIGHT :640];
        [TChatCore SetSDKOptionInt:TKCC_SO_RECORD_VIDEO_BITRATE :800];
        [TChatCore SetSDKOptionString:TKCC_SO_TMPDIR_RECORD :[NSHomeDirectory() stringByAppendingFormat:@"/Documents/thinkive"]];
        
//        [TChatCore SetSDKOptionInt:TKCC_SO_RECONNECT : 0]; // 关闭重连
//        if (self.isLandscape == YES) [TChatCore RotateUserVideo:-1 :90];
//
        CGFloat videoWidth = 480;
        CGFloat videoHeight = 640;
        [TChatCore SetSDKOptionInt:TKCC_SO_LOCAL_VIDEO_WIDTH : [TKStringHelper isNotEmpty:self.configParam[@"videoWidth"]] ? [self.configParam getIntWithKey:@"videoWidth"] : videoWidth]; // 设置视频输出设备
        [TChatCore SetSDKOptionInt:TKCC_SO_LOCAL_VIDEO_HEIGHT : [TKStringHelper isNotEmpty:self.configParam[@"videoHeight"]] ? [self.configParam getIntWithKey:@"videoHeight"] :videoHeight]; // 设置视频输出设备
        
//        [TChatCore UserAudioControl:-1 :YES];
//        [TChatCore UserVideoControl: -1 : YES];
//        [TChatCore EnableSpeaker:YES];
//
//        [self showViewWithWidth:480 height:640];
    } else {
        [self activeDevice];
    }
}


- (void)stopDevice:(BOOL)needRelease {
    
    [self disconnectServer:needRelease];
}

- (void)activeDevice {
    
    // 视频设备
    [self tkSwitchVideoCamera:NO];
    
    // 音频设备
//    if (self.disableMicrophone) {
//        [TChatCore SetSDKOptionInt:TKCC_SO_ENABLE_NETEQ:0]; // 关闭neteq
//        [TChatCore SelectDevice:TKCC_DT_AUDIOCAPTURE :@""]; // 初始化虚拟音频设备，不录制声音
//    }
//    else {
//        [TChatCore SetSDKOptionInt:TKCC_SO_ENABLE_NETEQ:1]; // 关闭neteq
//    }
    [TChatCore UserAudioControl:-1 :YES];
    [TChatCore UserVideoControl: -1 : YES];
    [self showViewWithWidth:480 height:640];
    [TChatCore ShowUserVideo: -1 : self.contentView:TRUE];
    [TChatCore EnableSpeaker:YES];
    
    dispatch_async(dispatch_get_main_queue(), ^{
        if (self.delegate && [self.delegate respondsToSelector:@selector(connectServerRoomDidComplete:errorMsg:)]) {
            [self.delegate connectServerRoomDidComplete:YES errorMsg:@""];
        }
    });
}

- (void)deactiveDevice {

}


- (void)startRecord {
    
    [self enableMute:YES];
    
    int user_array[] = {-1, 10086};
    int startRes = [TChatCore StartRecord:user_array :2 :TKCC_RECORD_FLAGS_CLIENT :0 :@""];
    if (startRes != 0) {
        [self recordErrorCallBack:[NSString stringWithFormat:@"视频录制出错(%d)", startRes]];
    } else {
        int res_init = [TChatCore StreamPlayInit:10086 :@"" :TKCC_STREAMPLAY_FLAGS_INPUT :@""];
        TKLogDebug(@"TChatCore StreamPlayInit: %d", res_init);
        
#if IsImportTKSpeechUtil
    tk_ringbuffer_reset(&tk_chat_local_video_record_buf);
    // NSLog(@"蓝牙测试：清空待写入数据");
#endif

        [self startAddPCMToTheVideoTimer];
        [self startAddSubtitlesToTheVideoTimer];
    }
}

- (void)stopRecord {
    
    int stopRes = [TChatCore StopRecord:_recordTaskID];
    if (stopRes == 0) {
        
        [self stopAddSubtitlesToTheVideoTimer];
        [self stopAddPCMToTheVideoTimer];

        int res_destroy = [TChatCore StreamPlayDestroy:10086];
        TKLogDebug(@"TChatCore StreamPlayDestroy: %d", res_destroy);
    } else {
        [self recordErrorCallBack:[NSString stringWithFormat:@"视频录制出错(%d)", stopRes]];
    }
    
    [self enableMute:NO];
    
    if (_subtitlesTextView) {
        [self.subtitlesTextView removeFromSuperview];
        _subtitlesTextView = nil;
    }
}

- (void)startAddSubtitlesToTheVideoTimer
{
    if(self.addSubtitlesToTheVideoTimer == nil)
    {
        __weak typeof(self) weakSelf = self;
//        dispatch_queue_t queue = dispatch_queue_create("com.thinkive.TKChatLocalVideoRecordManager.addSubtitlesToTheVideoTimer", DISPATCH_QUEUE_SERIAL);
        dispatch_queue_t queue = dispatch_get_main_queue();
//        dispatch_queue_t queue = _addSubtitlesToTheVideoQueue;
        dispatch_source_t timer = dispatch_source_create(DISPATCH_SOURCE_TYPE_TIMER, 0, 0, queue);
        dispatch_source_set_timer(timer, dispatch_time(DISPATCH_TIME_NOW, 0 * NSEC_PER_MSEC), 0.1 * NSEC_PER_SEC, 0);
        dispatch_source_set_event_handler(timer, ^{

//             NSLog(@"定时器调试：dispatch_source_set_event_handler");

            // 每隔一段时间重置flag
            [weakSelf addSubtitlesToTheVideo];
        });
        self.addSubtitlesToTheVideoTimer = timer;
        dispatch_resume(self.addSubtitlesToTheVideoTimer);
    }
}

- (void)startAddPCMToTheVideoTimer
{
    if(self.addPCMToTheVideoTimer == nil)
    {
        __weak typeof(self) weakSelf = self;
        dispatch_queue_t queue = dispatch_queue_create("com.thinkive.TKChatLocalVideoRecordManager.addPCMToTheVideoTimer", DISPATCH_QUEUE_SERIAL);
        dispatch_source_t timer = dispatch_source_create(DISPATCH_SOURCE_TYPE_TIMER, 0, 0, queue);
        dispatch_source_set_timer(timer, dispatch_time(DISPATCH_TIME_NOW, 0 * NSEC_PER_MSEC), 20 * NSEC_PER_MSEC, 0);
        dispatch_source_set_event_handler(timer, ^{

//             NSLog(@"定时器调试：dispatch_source_set_event_handler");

            // 每隔一段时间重置flag
            [weakSelf addPCMToTheVideo];
        });
        self.addPCMToTheVideoTimer = timer;
        dispatch_resume(self.addPCMToTheVideoTimer);
    }
}

- (void)stopAddSubtitlesToTheVideoTimer
{
    if(self.addSubtitlesToTheVideoTimer){
        dispatch_source_cancel(self.addSubtitlesToTheVideoTimer);
        self.addSubtitlesToTheVideoTimer = nil;
    }
}

- (void)stopAddPCMToTheVideoTimer
{
    if(self.addPCMToTheVideoTimer){
        dispatch_source_cancel(self.addPCMToTheVideoTimer);
        self.addPCMToTheVideoTimer = nil;
    }
}

//- (void)startVideoRecordBufferCheckTimer:(char*)buffer len:(int)len
//{
//    [self stopVideoRecordBufferCheckTimer];
//    
//    if(self.videoRecordBufferCheckTimer == nil)
//    {
//        NSLog(@"创建视频录制buffer检查定时器");
//        
//        __weak typeof(self) weakSelf = self;
//        dispatch_queue_t queue = dispatch_queue_create("com.thinkive.TKChatLocalVideoRecordManager.startVideoRecordBufferCheckTimer", DISPATCH_QUEUE_SERIAL);
//        dispatch_source_t timer = dispatch_source_create(DISPATCH_SOURCE_TYPE_TIMER, 0, 0, queue);
//        dispatch_source_set_timer(timer, dispatch_time(DISPATCH_TIME_NOW, 0 * NSEC_PER_MSEC), 2 * NSEC_PER_SEC, 0);
//        dispatch_source_set_event_handler(timer, ^{
//
////             NSLog(@"定时器调试：dispatch_source_set_event_handler");
//
//            // 每隔一段时间重置flag
//            [weakSelf speechSynthesisDidDataBuffer:buffer len:len];
//        });
//        self.videoRecordBufferCheckTimer = timer;
//        dispatch_resume(self.videoRecordBufferCheckTimer);
//    }
//}

//- (void)stopVideoRecordBufferCheckTimer
//{
//    if(self.videoRecordBufferCheckTimer){
//        NSLog(@"销毁视频录制buffer检查定时器");
//        
//        dispatch_source_cancel(self.videoRecordBufferCheckTimer);
//        self.videoRecordBufferCheckTimer = nil;
//    }
//}

- (void)playVideo:(nonnull NSString *)flag {
    
//    // 已经释放，无需开始指令
//    if (self.isRelease == YES) {return;}
//
//    if ([flag isEqualToString:@"1"]) {
//        //开始滴一声
//        [self handleAudioPlay:@"tk_oneVideo_start"];
//    } else if ([flag isEqualToString:@"2"]) {
//        //本地音频开始准备话术
//        [self handleAudioPlay:@"tk_oneVideo_remind" withType:@"mp3" sourceDirectory:@"Resources/TKOpenPlugin60030"];
//    } else if ([flag isEqualToString:@"3"]) {
//        //结束滴一声
//        [self handleAudioPlay:@"tk_oneVideo_end"];
//    } else {
//
//    }
    [self speechSynthesisDidPlayDone];
}

- (void)syntheticAndPlay:(nonnull NSString *)text tipSpeed:(NSString *)tipSpeed {
    [self enableMute:YES];
    
    // 已经释放，无需开始指令
    if (self.self.isRelease == YES) {return;}
#if IsImportTKSpeechUtil
    [self.speechSynthesisManager syntheticAndPlay:text tipSpeed:tipSpeed];
#endif
}

- (void)syntheticAndPlayContents:(NSArray *)texts tipSpeed:(NSString *)tipSpeed {
    [self enableMute:YES];
    
    // 已经释放，无需开始指令
    if (self.isRelease == YES) {return;}
#if IsImportTKSpeechUtil
    [self.speechSynthesisManager syntheticAndPlayContents:texts tipSpeed:tipSpeed];
#endif
}

- (void)playRemoteUrl:(NSString *)url tipSpeed:(NSString *)tipSpeed needRequestToken:(BOOL)needRequestToken {
    [self enableMute:YES];
    
    self.isForceStopPlayVideo = NO;
    
    [self speechSynthesisDidStart];
    
    __weak typeof(self) weakSelf = self;
    
    // 直接播放文件
    if (needRequestToken == NO) {
        
        __block NSMutableString *newVoiceUrl = url.mutableCopy;
        
//        NSDictionary *requestHeader = (NSDictionary *)[self.configParam getObjectWithKey:@"requestHeaders"];
//        if ([requestHeader isKindOfClass:NSDictionary.class] && requestHeader.allKeys.count > 0) {
//            [requestHeader enumerateKeysAndObjectsUsingBlock:^(id  _Nonnull key, id  _Nonnull obj, BOOL * _Nonnull stop) {
//                
//                newVoiceUrl = [TKCommonUtil url:newVoiceUrl appendingParamStr:[NSString stringWithFormat:@"%@=%@", key, obj]].mutableCopy;
//            }];
//        }
//        
        newVoiceUrl = [TKStringHelper encodeURL:newVoiceUrl].mutableCopy;
        
        // 下载音频文件头
        [self downloadOneWayVideo:newVoiceUrl downloadRange:NSMakeRange(0, sizeof(WAVHeader)) totalSize:sizeof(WAVHeader) keepDownloading:NO callBack:^(BOOL success, NSString *errorMsg, NSData *data) {
            
            if ([weakSelf checkCanPlayRemoteVideo] == NO) {
                return;
            }

            if (success) {

                // 处理wav头
                [weakSelf handleWAVAudioFormat:newVoiceUrl data:data];
            } else {
//                    [weakSelf speechSynthesisDidFail:@"语音请求失败(加载云端音频失败)"];
                [weakSelf speechSynthesisDidFail:errorMsg];
            }
        }];
        return;
    }
    
    // 先获取token,再播放文件
    [self getVideoUrl:url callBackFunc:^(BOOL isSuccess, NSString *errorMsg, NSString *newVoiceUrl) {
        if (isSuccess) {
            
            TKLogInfo(@"思迪录制日志:生成的缓存tts路径=%@", newVoiceUrl);
            //    [weakSelf handleAudioPlayWithUrl:[NSURL URLWithString:newVoiceUrl]];
            
            if ([weakSelf checkCanPlayRemoteVideo] == NO) {
                return;
            }

            // 下载音频文件头
            [weakSelf downloadOneWayVideo:newVoiceUrl downloadRange:NSMakeRange(0, sizeof(WAVHeader)) totalSize:sizeof(WAVHeader) keepDownloading:NO callBack:^(BOOL success, NSString *errorMsg, NSData *data) {
                
                if ([weakSelf checkCanPlayRemoteVideo] == NO) {
                    return;
                }

                if (success) {

                    // 处理wav头
                    [weakSelf handleWAVAudioFormat:newVoiceUrl data:data];
                } else {
//                    [weakSelf speechSynthesisDidFail:@"语音请求失败(加载云端音频失败)"];
                    [weakSelf speechSynthesisDidFail:errorMsg];
                }
            }];
        } else {
            [weakSelf speechSynthesisDidFail:errorMsg];
        }
    }];
}

- (void)handleWAVAudioFormat:(NSString *)remoteUrlStr data:(NSData *)data
{
    __weak typeof(self) weakSelf = self;
    dispatch_async(self->_audioPlayQueue, ^{
        
        __strong typeof(weakSelf) strongSelf = weakSelf;
        // 解析 WAV 文件头部
        WAVHeader *header = (WAVHeader *)[data bytes];
        
        if (header == nil) {
            TKLogInfo(@"思迪录制日志:解析音频头失败，数据为空");
            [weakSelf speechSynthesisDidFail:@"语音请求失败(加载云端音频失败)"];
            return;
        }
        
        // 创建 AudioStreamBasicDescription 结构
        AudioStreamBasicDescription audioDesc;
        memset(&audioDesc, 0, sizeof(audioDesc)); // 初始化为零
        
        audioDesc.mFormatID = kAudioFormatLinearPCM;
        audioDesc.mFormatFlags = kAudioFormatFlagIsSignedInteger | kAudioFormatFlagIsPacked;
        audioDesc.mBytesPerPacket = (header->bitsPerSample / 8) * header->numChannels;
        audioDesc.mFramesPerPacket = 1;
        audioDesc.mBytesPerFrame = audioDesc.mBytesPerPacket;
        audioDesc.mChannelsPerFrame = header->numChannels;
        audioDesc.mBitsPerChannel = header->bitsPerSample;
        audioDesc.mSampleRate = (Float64)header->sampleRate;
        
        // 更新播放器
        weakSelf.nlsAudioPlayer.audioDescription = audioDesc;
        [weakSelf.nlsAudioPlayer setstate:(PlayerState)playing];
        [weakSelf.nlsAudioPlayer play];
        
        if ([weakSelf checkCanPlayRemoteVideo] == NO) {
            return;
        }
        
        // 下载音频文件源
        [self downloadOneWayVideo:remoteUrlStr downloadRange:NSMakeRange(sizeof(WAVHeader), kDownloadLenth) totalSize:header->chunkSize keepDownloading:YES callBack:^(BOOL success, NSString *errorMsg, NSData *data) {
            
            __strong typeof (weakSelf) strongSelf = weakSelf;
            if (!strongSelf) return;
            
            if ([strongSelf checkCanPlayRemoteVideo] == NO) {
                return;
            }
            
            TKLogInfo(@"思迪录制日志:异步处理下载的云端音频数据");
            dispatch_async(strongSelf->_audioPlayQueue, ^{
//                NSLog(@"思迪录制日志:audioPlayQueue准备处理数据");
                if (success) {
                    
                    // 播放工具写入数据
                    if (data.length > 0) {
//                        NSLog(@"思迪录制日志:音频播放工具写入数据准备播放");
                        
                        [strongSelf.nlsAudioPlayer write:(char *)data.bytes Length:(unsigned int)data.length];
                        // 继续调用
                        [strongSelf speechSynthesisDidDataBuffer:(char *)data.bytes len:(int)data.length];
//                        NSLog(@"思迪录制日志:音频播放工具写入数据准备播放完毕");
                    } else {
//                        NSLog(@"思迪录制日志:没有播放，标记音频播放工具可以停止播放");
                        // 标记下载结束
                        [strongSelf.nlsAudioPlayer drain];
                    }
                } else {
//                    NSLog(@"思迪录制日志:加载云端音频失败");
                    [strongSelf speechSynthesisDidFail:@"语音请求失败(加载云端音频失败)"];
                    // 标记下载结束
                    [strongSelf.nlsAudioPlayer stop];
                }
            });
        }];
    });
}

- (BOOL)checkCanPlayRemoteVideo
{
    if (self.isForceStopPlayVideo) {
        TKLogInfo(@"思迪录制日志:强制停止播放，不予处理");
        return NO;
    }
    return YES;
}


- (void)downloadOneWayVideo:(NSString *)remoteUrlStr downloadRange:(NSRange)downloadRange totalSize:(unsigned long)totalSize keepDownloading:(BOOL)keepDownloading callBack:(void(^)(BOOL success, NSString *errorMsg, NSData *data))callBack
{
    if ([self checkCanPlayRemoteVideo] == NO) {
        return;
    }
    
//    NSTimeInterval start = [[NSDate date] timeIntervalSince1970];
    
    // 下载播放
    // 异步处理
    __weak typeof(self) weakSelf = self;
    dispatch_async(self->_audioDownloadQueue, ^{

//            TKLogInfo(@"思迪播放器日志(%@):视频文件本地不存在---(%@)，开始下载---(%@)", weakSelf, filePath, remoteUrlStr);
        
        // 2. 创建NSMutableURLRequest并设置URL
        NSMutableURLRequest *request = [NSMutableURLRequest requestWithURL:[NSURL URLWithString:remoteUrlStr]];

        // 3. 设置HTTP Range头来请求文件的一部分
        NSRange range = NSMakeRange(0, kDownloadLenth); // 从0字节开始，下载kDownloadLenth B的内容
        range = downloadRange.length > 0 ? downloadRange : range;
        NSInteger lenth = range.length;
        lenth = (range.location + lenth) > totalSize ? (totalSize - range.location) : lenth;    // 可下载长度不足时，调整Lenth
        NSString *rangeHeaderValue = [NSString stringWithFormat:@"bytes=%lu-%lu", (unsigned long)range.location, (unsigned long)(range.location + lenth - 1)];
        [request setValue:rangeHeaderValue forHTTPHeaderField:@"Range"];
        TKLogInfo(@"rangeHeaderValue = %@", rangeHeaderValue);

        // 4. 创建数据任务
        weakSelf.dataTask = [weakSelf.session dataTaskWithRequest:request completionHandler:^(NSData *data, NSURLResponse *response, NSError *error) {
            
//            NSLog(@"网络请求回调");
            if (!error) {
                if (data.length > 0 && data.length < kDownloadLenth) {
                    NSString *dataStr = [[NSString alloc] initWithData:data encoding:NSUTF8StringEncoding];
                    NSDictionary *dic = [TKDataHelper jsonToDictionary:dataStr];
                    if ([dic isKindOfClass:NSDictionary.class] && dic.allKeys.count) {
                        BOOL success = [dic getIntWithKey:@"success"];
                        NSString *msg = [dic getStringWithKey:@"message"];
                        if (callBack) {
                            callBack(success, msg, nil);
                        }
                        return;
                    }
                }
                
//                NSTimeInterval end = [[NSDate date] timeIntervalSince1970];
                //NSLog(@"advised:start =%.2f, end = %.2f, 耗时%.2f", start, end, end - start);
                
                TKLogInfo(@"--------返回的数据长度为%ld", data.length);
                
                // 回调外层播放
                if (callBack) {
                    callBack(YES, @"", data);
                }
                
                // 检测是否需要继续下载
                if (keepDownloading && data.length > 0) {
                #if IsImportTKSpeechUtil
                    while (1) {
                        if ([weakSelf checkCanPlayRemoteVideo] == NO) {
                            return;
                        }
                        
                        BOOL isFull = [weakSelf checkBufferSizeIsFull];
                        if (isFull) {
                            TKLogInfo(@"--------等待2s再检查");
                            sleep(2);
                            TKLogInfo(@"--------已休眠2s");
                            continue;
                        } else {
                            TKLogInfo(@"--------停止休眠");
                            break;
                        }
                    }
                    
                    TKLogInfo(@"--------继续下载");
                    // 7. 如果需要继续下载下一部分，请更新range并再次发起请求
                    [weakSelf downloadOneWayVideo:remoteUrlStr downloadRange:NSMakeRange(downloadRange.location + data.length, kDownloadLenth) totalSize:totalSize keepDownloading:keepDownloading callBack:^(BOOL success, NSString *errorMsg, NSData *data) {
                        if (callBack) {callBack(success, errorMsg, data);}
                    }];
                #endif
                }
                return;
            }
            
            if (callBack) {
                callBack(NO, error.localizedDescription, nil);
            }
        }];

        // 8. 启动数据任务
        [weakSelf.dataTask resume];
    });
}

- (BOOL)checkBufferSizeIsFull
{
    int ret = tk_ringbuffer_get_filled(&tk_chat_local_video_record_buf);
    TKLogInfo(@"--------缓存区已写入%i, 缓存区最大值为%i, 剩余可写入%i, 2倍下载量为%i", ret, kMaxBufferSize, kMaxBufferSize - ret, kDownloadLenth * 2);
    if ((kMaxBufferSize - ret) < (kDownloadLenth * 2)) {
        return YES;
    } else {
        return NO;
    }
}

- (void)getVideoUrl:(NSString *)url callBackFunc:(void(^)(BOOL isSuccess, NSString *errorMsg, NSString *newVoiceUrl))callBackFunc
{
    NSMutableString *tokenUrl = [self.configParam getStringWithKey:@"voiceTokenUrl"].mutableCopy;
//    url = @"https://regard01.95579.com/filecenter/file/tyspzt/2023/08/20230824/response.wav";   // 调试用
    tokenUrl = [TKCommonUtil url:tokenUrl appendingParamStr:[NSString stringWithFormat:@"voiceUrl=%@",url]].mutableCopy;
    
    NSDictionary *requestHeader = (NSDictionary *)[self.configParam getObjectWithKey:@"requestHeaders"];
    if ([requestHeader isKindOfClass:NSDictionary.class] && requestHeader.allKeys.count > 0) {
        [requestHeader enumerateKeysAndObjectsUsingBlock:^(id  _Nonnull key, id  _Nonnull obj, BOOL * _Nonnull stop) {
            [tokenUrl appendFormat:@"&%@=%@", key, obj];
        }];
    }
    
    NSMutableDictionary *params = [NSMutableDictionary dictionary];
    
    [self.openAccountService getVideoTokenWithURL:tokenUrl param:params callBackFunc:^(ResultVo *resultVo) {
        
        NSArray *results = (NSArray *)resultVo.results;
        if (resultVo.errorNo == 0) {
            NSDictionary *dic = results.firstObject;
            
            NSString *time = [dic getStringWithKey:@"time"];
            NSString *token = [dic getStringWithKey:@"token"];
            NSString *newVoiceUrl = [TKCommonUtil url:url appendingParamStr:[NSString stringWithFormat:@"t=%@&token=%@",time,token]];
            
            if (callBackFunc) {
                callBackFunc(YES, @"", newVoiceUrl);
            }
        } else {
            NSString *errorInfo = resultVo.errorInfo;
            if (callBackFunc) {
                callBackFunc(NO, errorInfo, @"");
            }
        }
    }];
}

- (void)stopPlayVideo {
//    [self.mp3Player pause];
    [self.nlsAudioPlayer stop];
    
    self.isForceStopPlayVideo = YES;
    
    if (self.dataTask) {
        [self.dataTask cancel];
        self.dataTask = nil;
    }
}

- (void)startRecognize {
    
#if IsImportTKSpeechUtil
    [self.speechRecognizeManager start];
#else
    self.isRecgnizing = YES;
#endif

    [self enableMute:NO];
    
//    [self speechRecognizeDidStart];
    
//    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(2 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
//        [self speechRecognizeOnSliceRecognize:@"是的"];
//    });
}

- (void)stopRecognize {
    
    //结束识别 停止录音，停止识别请求
#if IsImportTKSpeechUtil
    [self.speechRecognizeManager stop];
#endif
    
    self.isRecgnizing = NO;
    
    [self enableMute:YES];
}


- (void)stopSyntheticAndPlay {
    
    // 已经释放，无需开始指令
    if (self.isRelease == YES) {return;}
    
    //结束语音合成
#if IsImportTKSpeechUtil
    [self.speechSynthesisManager stop];
#endif
}

/// 设置静音
- (void)enableMute:(BOOL)mute {
//    [TChatCore AudioSetVolume:TKCC_AD_WAVEIN :mute ? 0 : 100];
    [TChatCore SetSDKOptionInt:TKCC_SO_MUTE_SELF_AUDIO :mute ? 1 : 0];
}


- (nonnull UIImage *)getLocalVideoPreViewImage:(nonnull NSString *)filePath {
    
    return self.videoGetImg;
}

- (nonnull UIImage *)getVideoPreViewImage:(nonnull NSURL *)path {
    return self.videoGetImg;
}

//切换摄像头
- (void)tkSwitchVideoCamera:(BOOL)isFrontCamera{
    if (isFrontCamera) {
        // 改成后置摄像头
        NSString *camName = [TChatCore GetDeviceName:TKCC_DT_VIDEOCAPTURE :0];
        [TChatCore SelectDevice:TKCC_DT_VIDEOCAPTURE :camName];
    //    [TChatCore ShowUserVideo:-1 :self.contentView :NO];    // 前置摄像头镜像，后置不用镜像
        TKLogInfo(@"切换摄像头为:%@",camName);
        if (self.isLandscape == YES) [TChatCore RotateUserVideo:-1 :270];
    }else{
        // 改成前置置摄像头
        NSString *camName = [TChatCore GetDeviceName:TKCC_DT_VIDEOCAPTURE :1];
        [TChatCore SelectDevice:TKCC_DT_VIDEOCAPTURE :camName];
  //      [TChatCore ShowUserVideo:-1 :self.contentView :YES];    // 前置摄像头镜像，后置不用镜像
        TKLogInfo(@"切换摄像头为:%@",camName);
        if (self.isLandscape == YES) [TChatCore RotateUserVideo:-1 :90];
    }
}



//- (NSString *)getFullVideoPath {
//    
//    if ([TKStringHelper isNotEmpty:self.videoFilePath]) {return self.videoFilePath;}
//    
//    return [NSHomeDirectory() stringByAppendingFormat:@"/Documents/%@.mp4", TK_ONE_WAY_TEMP_MOVE_NAME];
//}


/// 断开视频服务器并销毁释放资源
- (void)disconnectServer:(BOOL)needRelease {
    
    self.isDisconnecting = YES;
    
    UIView *snapshot = [self.contentView snapshotViewAfterScreenUpdates:NO];
    snapshot.frame = self.contentView.bounds;
    snapshot.autoresizingMask = UIViewAutoresizingFlexibleWidth | UIViewAutoresizingFlexibleHeight;
    [self.contentView addSubview:snapshot];
    
    [TChatCore UserVideoControl:-1 :NO];
    [TChatCore UserAudioControl:-1 :NO];
    [TChatCore StopUserVideo:-1];

    // 异步处理
    dispatch_async(_tchatReleaseQueue, ^{
        
        TKLogInfo(@"思迪服务器录制日志：断开TChat服务器");
        
        [self stopTakePicturesTimer];
        
        // 重置
        [TChatCore EnableSpeaker:NO];
        [TChatCore LeaveRoom];
        [TChatCore Logout];
        
//            self.contentView = nil;
//            self.remoteContentView = nil;
    
        self.recordTaskID = 0;
        
        [[NSNotificationCenter defaultCenter] removeObserver:self];
        
        TKLogInfo(@"思迪服务器录制日志：断开TChat服务器完毕");
        
        if (needRelease && self.isFirstReleaseTChat == NO) {
            
            self.isFirstReleaseTChat = YES;
            self.isRelease = YES;
            
            [TChatCore Release];   // 释放资源
            TKLogInfo(@"思迪服务器录制日志：释放TChat完毕");
        }
        
        self.isDisconnecting = NO;
    });
}

- (void)stopTakePicturesTimer
{
    if (self.takePicturesTimer) {
        TKLogInfo(@"takePictures:stopTakePicturesTimer");
        
        [self.takePicturesTimer invalidate];
        self.takePicturesTimer = nil;
    }
}

- (void)createSubtitles:(NSString *)text showTime:(NSString *)showTime questionOneWordSpeed:(NSString *)questionOneWordSpeed
{
    TKLogInfo(@"TChatCore createSubtitles: %@", text);
    
    if ([TKStringHelper isEmpty:text]) return;
    
    if (_subtitlesTextView == nil) {
        
        CGFloat margin = 10 * UIScreen.mainScreen.scale;
        UITextView *textView = [[UITextView alloc] initWithFrame:CGRectMake(0, 0, 480 - margin, 640 - margin * 3.5)]; // 显示在屏幕外
//        UITextView *textView = [[UITextView alloc] initWithFrame:CGRectMake(0, 0, 480, 210)]; // 显示在屏幕外
        self.subtitlesTextView = textView;
//        [self.contentView addSubview:self.subtitlesTextView];   // 调试用
        self.subtitlesTextView.center = UIApplication.sharedApplication.keyWindow.center;
        [UIApplication.sharedApplication.keyWindow insertSubview:self.subtitlesTextView atIndex:0];
//        self.subtitlesTextView.textContainerInset = UIEdgeInsetsMake(margin, margin, margin * 3, 0);
        self.subtitlesTextView.font = [UIFont fontWithName:@"PingFang SC" size:36];
        self.subtitlesTextView.backgroundColor = UIColor.blackColor;
    }
    
    // 先停止滚动
    [self stopTextViewScroll];
    
    // 重置滚动值
    self.subtitlesTextView.attributedText = nil;
//    [self.subtitlesTextView scrollRangeToVisible:NSMakeRange(0, 0)];
    self.subtitlesTextView.contentOffset = CGPointZero;
    // 生成富文本
    NSMutableAttributedString *attStr = [self convertTextToHtmlString:[TKCommonUtil switchLabelToSpan:text] textColor:@"#FFFFFF"];
    attStr = [self convertTextToHtmlString:attStr.string textColor:@"#FFFFFF"]; // 去掉原来的html字符配置，强制转成白色字体
    self.subtitlesTextView.attributedText = attStr;
    
    // 马上写入一次字幕
    [self addSubtitlesToTheVideo];

    // 调试用
//    NSLog(@"syntheticAndPlay text = %@", text);
    
    // 开始滚动
    float subtitlesScrollSpeed = 0.19f;  // 0.19s/1个字
    if ([TKStringHelper isNotEmpty:showTime]) {
        
        subtitlesScrollSpeed = showTime.floatValue / attStr.length;
    } else if ([TKStringHelper isNotEmpty:questionOneWordSpeed]) {
    
        subtitlesScrollSpeed = questionOneWordSpeed.floatValue;    // 优先使用外层配置的
        
    }
    self.subtitlesScrollSpeed = subtitlesScrollSpeed;
    //NSLog(@"changeReadTextViewOffSet durationTime = %.2f, self.subtitlesScrollSpeed = %.2f", durationTime, self.subtitlesScrollSpeed);
    [self changeReadTextViewOffSet];
}

- (void)addSubtitlesToTheVideo
{
    @autoreleasepool {
    //    // 时间统计
    //    CFAbsoluteTime startTime = CFAbsoluteTimeGetCurrent();
        
        // 创建一个UIImage对象，指定图片的大小和比例
    //    CGSize imageSize = visibleRect.size;
        CGFloat scale = UIScreen.mainScreen.scale;
        CGSize imageSize = CGSizeMake(480.0 / scale, 640.0 / scale);
        UIGraphicsBeginImageContextWithOptions(imageSize, NO, 0);
        CGContextRef context = UIGraphicsGetCurrentContext();
        [[UIColor blackColor] setFill];
        CGContextFillRect(context, CGRectMake(0, 0, imageSize.width, imageSize.height));

        // 创建一个UIGraphicsImageRenderer对象，指定图片的大小和比例
    //    UIGraphicsImageRenderer *renderer = [[UIGraphicsImageRenderer alloc] initWithSize:imageSize];

        CGFloat margin = 10;
        // 调用UIGraphicsImageRenderer的image方法，传入一个闭包，在闭包中进行绘制操作
    //    UIImage *image = [renderer imageWithActions:^(UIGraphicsImageRendererContext * _Nonnull context) {
            // 在闭包中进行绘制操作
            [self.subtitlesTextView drawViewHierarchyInRect:CGRectMake(margin, margin, self.subtitlesTextView.TKWidth / scale, self.subtitlesTextView.TKHeight / scale) afterScreenUpdates:YES];
    //    }];
        
        // 获取绘制好的图片
        UIImage *image = UIGraphicsGetImageFromCurrentImageContext();

        // 结束上下文
        UIGraphicsEndImageContext();
        
        NSData *imageData = UIImageJPEGRepresentation(image, 1);
        
    //    [imageData writeToFile:[NSHomeDirectory() stringByAppendingFormat:@"/Documents/thinkive/100%i.jpg", i] atomically:YES];
    //    NSString *base64 = [TKBase64Helper stringWithEncodeBase64Data:imageData];
    //    NSLog(@"localDetectFace = %@", base64);
        
    //    //这部分为需要统计时间的代码
    //    CFAbsoluteTime endTime = (CFAbsoluteTimeGetCurrent() - startTime);
    //    NSLog(@"方法耗时: %f ms", endTime * 1000.0);
        
        if (imageData) {
            
            dispatch_async(_addSubtitlesToTheVideoQueue, ^{
                
                int res_video_input = [TChatCore StreamPlayInputVideoData:10086 :0 : 480 : 640 : 20 : 0 : imageData : (int)[imageData length] :0];
                TKLogDebug(@"TChatCore StreamPlayInputVideoData: %d", res_video_input);
//                NSLog(@"TChatCore StreamPlayInputVideoData: %d", res_video_input);
            });
        }
    }
}


- (void)addPCMToTheVideo
{
    dispatch_async(_addPCMToTheVideoQueue, ^{
        @autoreleasepool {
        //    // 时间统计
        //    CFAbsoluteTime startTime = CFAbsoluteTimeGetCurrent();

            int len = 640;
            char *buffer = (char *)calloc(len, sizeof(char));
            int ret = 0;
        #if IsImportTKSpeechUtil
            ret = tk_ringbuffer_read(&tk_chat_local_video_record_buf, (unsigned char*)buffer, len);
        #endif

        //    //这部分为需要统计时间的代码
        //    CFAbsoluteTime endTime = (CFAbsoluteTimeGetCurrent() - startTime);
        //    NSLog(@"方法耗时: %f ms", endTime * 1000.0);

            if (ret > 0) {
                NSData *audioData = [NSData dataWithBytes:buffer length:len];
                int res_audio_input = [TChatCore StreamPlayInputAudioData:10086 :0 :1 :16000 :16 :0 :audioData :ret :0];
                TKLogDebug(@"TChatCore StreamPlayInputAudioData: %d", res_audio_input);
            } else {
                
                int res_audio_input = [TChatCore StreamPlayInputAudioData:10086 :0 :1 :16000 :16 :0 :self.emptyData :(int)self.emptyData.length :0];
                TKLogDebug(@"TChatCore StreamPlayInputAudioData: %d", res_audio_input);
            }
            
            free(buffer);
        }
    });
}




- (NSMutableAttributedString *)convertTextToHtmlString:(NSString *)text textColor:(NSString *)colorString
{
    NSString *tempDivString = [NSString stringWithFormat:@"<span style=\"font-size:%ipx;color:%@;font-family:PingFang SC\">%@</span>", (int)self.subtitlesTextView.font.pointSize, colorString, text];
   NSData *tempData = [tempDivString dataUsingEncoding:NSUnicodeStringEncoding];
   NSDictionary *options = @{NSDocumentTypeDocumentAttribute: NSHTMLTextDocumentType,
                             NSCharacterEncodingDocumentAttribute:[NSNumber numberWithInt:NSUTF8StringEncoding]
   };
   NSMutableAttributedString *tempattStr = [[NSMutableAttributedString alloc]initWithData:tempData options:options documentAttributes:nil error:nil];
    
    return tempattStr;
}

/**
@Auther Vie 2021年07月29日13:56:44
改变阅读文本空间显示
*/
- (void)changeReadTextViewOffSet {
   
   //计算是否要滚动换行
   //多久渐变一个字
   NSTimeInterval repeactTime = 1.0;
   if (self.subtitlesScrollSpeed <= 0.06) repeactTime = 0.1;    // 滚动速度变大，刷新频率也更新
   int wordPerSecond = ceilf(repeactTime / self.subtitlesScrollSpeed);

   if (self.changeReadTextViewWords < self.subtitlesTextView.attributedText.string.length) {
       // -1是防止到最后一个字再滚动的话会有点显示问题。会一整行滚动一次，又展示
        //NSLog(@"思迪文案滚动动画：wordPerSecond = %i, self.changeReadTextViewWords = %i, self.bottomShowLabel.attributedText.string.length = %ld, self.subtitlesScrollSpeed.floatValue= %.2f", wordPerSecond, self.changeReadTextViewWords, self.subtitlesTextView.attributedText.string.length, self.subtitlesScrollSpeed);
       
       NSRange rang = NSMakeRange(self.changeReadTextViewWords, wordPerSecond);
       [self.subtitlesTextView scrollRangeToVisible:rang];
       
       //计算是否要滚动换行
//        CGSize labelSize=[self.bottomShowLabel sizeThatFits:CGSizeMake(self.bottomShowLabel.TKWidth, MAXFLOAT)];
//        float diffY=labelSize.height-self.bottomShowLabel.TKHeight;
//        if (diffY>0) {
//            NSRange rang=NSMakeRange(self.changeReadTextViewWords, wordPerSecond);
//            [self.bottomShowLabel scrollRangeToVisible:rang];
//        }
       
       self.changeReadTextViewWords = self.changeReadTextViewWords + wordPerSecond;
       //NSLog(@"思迪文案滚动动画：滚动后self.changeReadTextViewWords = %i", self.changeReadTextViewWords);

//        NSLog(@"思迪文案滚动动画：创建定时器");
       if (self.changeReadTextViewOffSetTimer == nil) {
           
           self.changeReadTextViewOffSetTimer = [NSTimer timerWithTimeInterval:repeactTime target:self selector:@selector(changeReadTextViewOffSet) userInfo:nil repeats:YES];
           [[NSRunLoop mainRunLoop] addTimer:self.changeReadTextViewOffSetTimer forMode:NSRunLoopCommonModes];
       }
   } else {
       
//        NSLog(@"思迪文案滚动动画：销毁定时器");
       [self stopTextViewScroll];
   }
}


- (void)stopTextViewScroll
{
    self.changeReadTextViewWords = 0;
    [self.changeReadTextViewOffSetTimer invalidate];
    self.changeReadTextViewOffSetTimer = nil;
}


#pragma mark 播放本地mp3处理
//转接等待中播放音乐
- (void)handleAudioPlay:(NSString*)voiceName
{
    [self handleAudioPlay:voiceName withType:@"mp3" sourceDirectory:@"Resources/TKOpenPlugin60006"];
}

- (void)handleAudioPlay:(NSString*)voiceName sourceDirectory:(NSString *)sourceDirectory
{
    [self handleAudioPlay:voiceName withType:@"mp3" sourceDirectory:sourceDirectory];
}

/**
 <AUTHOR> 2020年03月07日17:54:19
 @播放本地语音文件
 */
- (void)handleAudioPlay:(NSString*)voiceName withType:(NSString*)type sourceDirectory:(NSString *)sourceDirectory {
    NSBundle *bundle = [NSBundle bundleWithURL:[[NSBundle mainBundle] URLForResource:[NSString stringWithFormat:@"%@", TK_OPEN_RESOURCE_NAME] withExtension:@"bundle"]];
    NSURL *aUrl = nil;
    NSString* fPath = [bundle pathForResource:voiceName ofType:type inDirectory:sourceDirectory];
    
    NSFileManager *fm = [[NSFileManager alloc] init];
    
    if ([fm fileExistsAtPath:fPath]) {
        aUrl = [NSURL fileURLWithPath:fPath];
    }
    
    [self handleAudioPlayWithUrl:aUrl];
}

- (void)handleAudioPlayWithUrl:(NSURL *)aUrl
{
    AVPlayerItem * songItem = [[AVPlayerItem alloc]initWithURL:aUrl];
    self.mp3Player = [[AVPlayer alloc]initWithPlayerItem:songItem];
    [self.mp3Player play];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(moviePlayDidEnd:) name:AVPlayerItemDidPlayToEndTimeNotification object:self.mp3Player.currentItem];
    
    [self speechSynthesisDidStart];
}

//音频循环播放
- (void)moviePlayDidEnd:(NSNotification*)notification{
    dispatch_async(dispatch_get_main_queue(), ^{
        
        if (self.delegate && [self.delegate respondsToSelector:@selector(speechSynthesisDidPlayDone)]) {
            [self.delegate speechSynthesisDidPlayDone];
        }
    });
}


//停止播放音乐
- (void)handleAudioStopPlay{
//    [[NSNotificationCenter defaultCenter] removeObserver:self];
    [[NSNotificationCenter defaultCenter] removeObserver:self name:AVPlayerItemDidPlayToEndTimeNotification object:nil];
    [self.mp3Player pause];
}



#pragma mark TKSpeechSynthesisManagerDelegate
// 语音合成开始回调
- (void)speechSynthesisDidStart{
    
    dispatch_async(dispatch_get_main_queue(), ^{
        
        if (self.delegate && [self.delegate respondsToSelector:@selector(speechSynthesisDidStart)]) {
            [self.delegate speechSynthesisDidStart];
        }
    });
}

// 多段语音合成开始回调
- (void)speechSynthesisDidStartWithIndex:(int)index synthesisArray:(NSArray *)synthesisArray {
    
    dispatch_async(dispatch_get_main_queue(), ^{
        
        if (self.delegate && [self.delegate respondsToSelector:@selector(speechSynthesisDidStartWithIndex:synthesisArray:)]) {
            [self.delegate speechSynthesisDidStartWithIndex:index synthesisArray:synthesisArray];
        }
    });
}


// 语音合成失败
- (void)speechSynthesisDidFail:(NSString *)errorMsg {

    [self stopTextViewScroll];
    
#if IsImportTKSpeechUtil
    
    // NSLog(@"蓝牙测试：清空待写入数据");
    
    [self.speechSynthesisManager stop];
#endif
    
    dispatch_async(dispatch_get_main_queue(), ^{
        
        if (self.delegate && [self.delegate respondsToSelector:@selector(speechSynthesisDidFail:)]) {
            [self.delegate speechSynthesisDidFail:errorMsg];
        }
        
    });
    
}

// 语音合成播放结束回调
- (void)speechSynthesisDidPlayDone{
    
    [self stopTextViewScroll];
    
    dispatch_async(dispatch_get_main_queue(), ^{
        
        if (self.delegate && [self.delegate respondsToSelector:@selector(speechSynthesisDidPlayDone)]) {
            [self.delegate speechSynthesisDidPlayDone];
        }
    });
}

- (void)speechSynthesisDidPlayDoneWithIndex:(int)index synthesisArray:(NSArray *)synthesisArray {
    dispatch_async(dispatch_get_main_queue(), ^{
        
        if (self.delegate && [self.delegate respondsToSelector:@selector(speechSynthesisDidPlayDoneWithIndex:synthesisArray:)]) {
            [self.delegate speechSynthesisDidPlayDoneWithIndex:index synthesisArray:synthesisArray];
        }
    });
}

// 语音合成buffer
- (void)speechSynthesisDidDataBuffer:(char*)buffer len:(int)len {

#if IsImportTKSpeechUtil
    if (self.isRecording) {
        int ret = tk_ringbuffer_write(&tk_chat_local_video_record_buf, (unsigned char*)buffer, len);
         TKLogInfo(@"视频合成字幕：写入合成数据长度%d，未写入数据长度为%d", ret, len - ret);
        
//        if (ret == 0) {
//            NSLog(@"蓝牙测试：数据已满，休眠");
//
//            [self startVideoRecordBufferCheckTimer:buffer len:len];
//            
//            CGFloat sampleTime = 640.0 / 16000 * 0.5;
//            int len_44100_to_target = sampleTime * 2 * self.targetSampleRate;
//            if (len_44100_to_target % 2 != 0) {  // 判断是否是2的倍数
//                len_44100_to_target += 1;  // 不是2的倍数则加1
//            }
//            
//            [NSThread sleepForTimeInterval:(len / len_44100_to_target - 3) * sampleTime]; // 提早三次获取数据时醒来
//            // NSLog(@"蓝牙测试：休眠结束");
//            
//            
//            if (self.isRecording == NO || self.isRelease == YES) {
//                // NSLog(@"蓝牙测试：抛弃合成数据，有问题！！！");
//                return;
//            }
//
//            // 继续调用
//            [self speechSynthesisDidDataBuffer:buffer len:len];
//        }
    }
#endif
}


#pragma mark  TKSpeechRecognizeManagerDelegate
/// 语音识别开始回调
- (void)speechRecognizeDidStart {
    self.isRecgnizing = YES;
    
    dispatch_async(dispatch_get_main_queue(), ^{
        
        if (self.delegate && [self.delegate respondsToSelector:@selector(speechRecognizeDidStart)]) {
            [self.delegate speechRecognizeDidStart];
        }
    });
}

// 每个语音包分片识别结果
// @param result 一段语音每次识别结果
- (void)speechRecognizeOnSliceRecognize:(NSString *)result {
    dispatch_async(dispatch_get_main_queue(), ^{
        
        if (self.delegate && [self.delegate respondsToSelector:@selector(speechRecognizeOnSliceRecognize:)]) {
            [self.delegate speechRecognizeOnSliceRecognize:result];
        }
    });
}

// 一次语音识别结果
// @param reslut 一次识别结果字符串
- (void)speechRecognizeDidRecognize:(NSString *)result{
    
}

// 语音识别结束回调
- (void)speechRecognizeDidComplete {
    self.isRecgnizing = NO;
    
    dispatch_async(dispatch_get_main_queue(), ^{
        
        if (self.delegate && [self.delegate respondsToSelector:@selector(speechRecognizeDidComplete)]) {
            [self.delegate speechRecognizeDidComplete];
        }
    });
}

// 语音识别失败回调
- (void)speechRecognizeDidFail:(NSString *)errorMsg{
    
#if IsImportTKSpeechUtil
    [self.speechRecognizeManager stop];
#endif
    
    dispatch_async(dispatch_get_main_queue(), ^{
        
        if (self.delegate && [self.delegate respondsToSelector:@selector(speechRecognizeDidFail:)]) {
            [self.delegate speechRecognizeDidFail:errorMsg];
        }
    });
}

#pragma mark 视频录制事件
// 开始录制
- (void) OnStartRecordCallBack: (int)taskId : (NSString*)filePath :
    (int)errorCode : (int)flags : (int)param : (NSString*)userStr
{
    _recordTaskID = taskId;
    self.isRecording = YES;
    
    if (errorCode == 0) {
        
        [self startRecordCallBack];
    } else {
        [self recordErrorCallBack:[NSString stringWithFormat:@"%@(%i)", @"视频录制异常退出", errorCode]];
    }
}

// 停止录制
- (void) OnStopRecordCallBack: (int)taskId : (NSString*)filePath :
    (int)errorCode : (int)elapse : (int)flags : (int)param : (NSString*)userStr
{
    [self stopRecordCallBack:filePath errorCode:errorCode videoLength:elapse catonLength:0];
    
    self.isRecording = NO;
}

// 录制出错
- (void) OnRecordErrorCallBack: (int)taskId : (NSString*)filePath :
    (int)errorCode : (int)flags : (int)param : (NSString*)userStr
{
    [self recordErrorCallBack:[NSString stringWithFormat:@"%@(%i)", @"视频录制异常退出", errorCode]];
    
    self.isRecording = NO;
}

//// 视频数据就绪
//- (void) OnUserVideoDataReady: (int)userId : (int)dataInfo{
//    
//    int ret = dataInfo & 0x0000FFFF;
//    TKLogInfo(@"思迪服务器录制日志：视频数据 userId = %i, dataInfo = %i, ret = %i", userId, dataInfo, ret);
//    
//    //自己视频画面调整占用屏幕大小，以便展示出底层的白色背景
//    float  width = (dataInfo & 0xFFFF0000) >> 16;
//    float  height   = (dataInfo & 0x0000FFFF);
//    TKLogInfo(@"视频调整前宽度：%f|视频调整前高度：%f",width,height);
//    
//    dispatch_async(dispatch_get_main_queue(), ^{
//        if (userId == -1) {
//            
//            [self showViewWithWidth:width height:height];
//        }
//    });
//}

/*
userid:            用户编号，为-1时，表示本地视频
filepath:            快照标志为0，表示文件路径；否则，表示文件Base64数据
errorcode:        错误代码
flags:            快照标志
param:            用户自定义参数（整型）
userstr:            用户自定义参数（字符串）
 */
- (void) OnSnapShotCallBack: (int)taskId : (NSString*)filePath :
    (int)errorCode : (int)flags : (int)param : (NSString*)userStr {
    
    if (taskId == -1 && errorCode == 0 && flags == TKCC_SNAPSHOT_FLAGS_DATA) {
        @autoreleasepool
        {
            UIImage *image = [UIImage imageWithData:[TKBase64Helper dataWithDecodeBase64String:filePath]];
    //        NSLog(@"image = %@", image);

            dispatch_async(dispatch_get_main_queue(), ^{

                if (self.delegate && [self.delegate respondsToSelector:@selector(OnVideoDataCallBack:)]) {
                    [self.delegate OnVideoDataCallBack:image];
                }
            });
        }
    }
}

- (void)showViewWithWidth:(CGFloat)width height:(CGFloat)height
{
    float videoWidth;
    float videoHeight;
    
    CGPoint center = self.contentView.center;

    float aspectRatio = self.contentView.TKHeight / self.contentView.TKWidth;//高除以宽的比例
    float ratioRequirements=height/width;//高除以宽的要求比例
    //全屏等比拉伸
    if (aspectRatio > ratioRequirements) {
        videoHeight = self.contentView.TKHeight;
        videoWidth = videoHeight/ratioRequirements;
    }else{
        videoWidth = self.contentView.TKWidth;
        videoHeight = videoWidth*ratioRequirements;
    }
    [self.contentView setFrameWidth:videoWidth];
    [self.contentView setFrameHeight:videoHeight];
    
    TKLogInfo(@"视频调整后宽度：%f|视频调整后高度：%f",videoWidth,videoHeight);
    self.contentView.center = center;
    
//    [TChatCore ShowUserVideo:-1 :self.contentView :self.isFrontCamera];
    
    // 创建拍照定时器,获取在框检测图片
    [self stopTakePicturesTimer];
    // NSLog(@"takePictures:create takePictures timer");
    self.takePicturesTimer = [NSTimer timerWithTimeInterval:0.2 target:self selector:@selector(takePictures) userInfo:nil repeats:YES];
    [[NSRunLoop mainRunLoop] addTimer:self.takePicturesTimer forMode:NSRunLoopCommonModes];
}

- (void)takePictures
{
    int result = [TChatCore Snapshot:-1 :TKCC_SNAPSHOT_FLAGS_DATA :0 :nil];
//    [TChatCore Snapshot:tcSeatId :TKCC_SNAPSHOT_FLAGS_DATA :0 :@""];
    
//    NSLog(@"takePictures:takePictures result = %i", result);
}

- (void)startRecordCallBack
{
    dispatch_async(dispatch_get_main_queue(), ^{
        
        if (self.delegate && [self.delegate respondsToSelector:@selector(recordStartCallBack)]) {
            [self.delegate recordStartCallBack];
        }
    });
}

- (void)stopRecordCallBack:(NSString *)filePath errorCode:(int)errorCode videoLength:(int)videoLength catonLength:(int)catonLength
{
    if (errorCode == 0) {
            
        _finalVideoPath = filePath;
        dispatch_async(dispatch_get_main_queue(), ^{
            if (self.delegate && [self.delegate respondsToSelector:@selector(recordStopCallBack:fullFilePath:videoLenth:catonLength:)]) {
                [self.delegate recordStopCallBack:filePath fullFilePath:filePath videoLenth:videoLength catonLength:catonLength]; // 注意这里调用的是get方法
            }
        });
    } else {
        [self recordErrorCallBack:[NSString stringWithFormat:@"%@(%i)", @"视频录制异常退出", errorCode]];
    }
        
    TKLogInfo(@"思迪服务器录制日志：录制结束。errorCode = %i。生成的文件路径 = %@", errorCode, filePath);
//    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(2 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
//        [_handle closeFile];
//        _handle = nil;
//        NSLog(@"--------------------------文件写入完毕");
//    });
}

- (void)recordErrorCallBack:(NSString *)errorMsg
{
    dispatch_async(dispatch_get_main_queue(), ^{
        
        if (self.delegate && [self.delegate respondsToSelector:@selector(recordStopErrocCallBack:)]) {
            [self.delegate recordStopErrocCallBack:[NSString stringWithFormat:@"%@", errorMsg]];
        }
    });
}


#pragma mark NlsPlayerDelegate
//合成语音播放完成
- (void)playerDidFinish {
    
    TKLogDebug(@"思迪阿里语音工具调试:实时播放结束");
    
    [self speechSynthesisDidPlayDone];
}

- (void)playerDidTimeOut {
//    NSLog(@"思迪阿里语音工具调试:语音播报超时");
    [self speechSynthesisDidFail:@"网络不稳定，请切换网络再试"];
}

- (void)playerDidFail:(NSString *)errMsg {
//    NSLog(@"思迪阿里语音工具调试:%@", errMsg);
    [self speechSynthesisDidFail:errMsg];
}


#pragma mark - lazyloading


#if IsImportTKSpeechUtil
/**
<AUTHOR> 2021年01月15日14:47:12
@初始化懒加语音合成对象
@return 语音合成对象
*/
- (id)speechSynthesisManager {
    if (!_speechSynthesisManager) {
        _speechSynthesisManager = [[TKSpeechSynthesisManager alloc] initWithConfig:self.configParam];
        _speechSynthesisManager.delegate = self;
        
    }
    return _speechSynthesisManager;
}

/**
<AUTHOR> 2021年01月15日14:47:12
@初始化懒加语音识别对象
@return 语音识别对象
*/
- (id)speechRecognizeManager {
    if (!_speechRecognizeManager) {
        _speechRecognizeManager = [[TKSpeechRecognizeManager alloc] initWithConfig:self.configParam];
        _speechRecognizeManager.delegate = self;
    }
    return _speechRecognizeManager;
}

- (TKNLSPlayAudio *)nlsAudioPlayer {
    if (!_nlsAudioPlayer) {
        int aliTTSSampleRate = 16000;

        self.nlsAudioPlayer = [[TKNLSPlayAudio alloc]init];
        self.nlsAudioPlayer.delegate = self;
        
        if ([TKStringHelper isNotEmpty:self.configParam[@"aliTTSSampleRate"]]) {
            //语音合成采样率，默认值是16000，支持8000
            aliTTSSampleRate = [self.configParam[@"aliTTSSampleRate"] intValue];
            self.nlsAudioPlayer.mSampleRate = aliTTSSampleRate;
        }
        
//        int timeOut = [self.configParam getIntWithKey:@"timeOut"];
//        timeOut = timeOut == 0 ? 3 : timeOut;
//        self.nlsAudioPlayer.timeOut = timeOut;
        
        // 测试数据
//        int openFill = [self.configParam getIntWithKey:@"openFill"];
//        self.nlsAudioPlayer.openFill = openFill;
    }
    return _nlsAudioPlayer;
}
#endif

- (TKOpenAccountService *)openAccountService {
    if (_openAccountService == nil) {
        _openAccountService = [TKOpenAccountService new];
    }
    return _openAccountService;
}

- (void)setSubtitlesScrollSpeed:(float)subtitlesScrollSpeed {

    _subtitlesScrollSpeed = subtitlesScrollSpeed  <= 0 ? 0.19f : subtitlesScrollSpeed;
}

- (NSData *)emptyData {
    if (!_emptyData) {
        Byte data[640] ={0};
        NSData *audioData = [NSData dataWithBytes:data length:640];
        _emptyData = audioData;
    }
    return _emptyData;
}

- (NSURLSession *)session {
    if (!_session) {
        // 1. 创建NSURLSession对象
        // 创建NSURLSessionConfiguration对象
        NSURLSessionConfiguration *configuration = [NSURLSessionConfiguration defaultSessionConfiguration];

        // 设置超时时间为10秒
        configuration.timeoutIntervalForRequest = 10.0;

        // 创建NSURLSession对象
        _session = [NSURLSession sessionWithConfiguration:configuration];
    }
    
    return _session;
}

@end
