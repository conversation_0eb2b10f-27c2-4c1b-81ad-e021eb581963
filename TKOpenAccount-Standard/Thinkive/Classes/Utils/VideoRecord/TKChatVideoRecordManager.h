//
//  TKChatVideoRecordManager.h
//  TKOpenAccount-Standard
//
//  Created by <PERSON> on 2021/4/17.
//  Copyright © 2021 thinkive. All rights reserved.
//

#import <UIKit/UIKit.h>
#import <Foundation/Foundation.h>
#import <AVFoundation/AVAsset.h>
#import "TKRecordManagerProtocol.h"

NS_ASSUME_NONNULL_BEGIN

typedef enum : NSUInteger {
    TKChatVideoRecordManagerScalingModeAsceptFill,
    TKChatVideoRecordManagerScalingModeAsceptFit,
    TKChatVideoRecordManagerScalingModeFill,
} TKChatVideoRecordManagerScalingMode;


@protocol TKChatVideoRecordManagerDelegate <TKRecordManagerDelegate>

@optional

// tchat回到前台
//- (void)TChatBecomeActive;

/// 视频服务器连接状态回调
// status:    网络质量；0：未知；1：很好；2：较好；3：较差，4：很差
- (void)connectServerStautsDidChange:(TKChatVideoRecordNetworkStatus)status statusString:(NSString *)statusString;

/// 网络速率回调
/// @param sendBps 上行速率
/// @param recvBps 下行速率
- (void)OnNetBitrate:(int)sendBps recvBps:(int)recvBps;

//自己的视频画面准备就绪
-(void)contentViewVideoDataReady;
@end


@interface TKChatVideoRecordManager : NSObject<TKRecordManagerProtocol>

/// 代码
@property (nonatomic, readwrite, weak) id<TKChatVideoRecordManagerDelegate> delegate;

/// 是否禁用获取图片，默认是NO
@property (nonatomic, readwrite, assign) BOOL disableFetchPicture;

// 是否静音
@property (nonatomic, readwrite, assign) BOOL mute;

// 是否延迟开启麦克风和摄像头
@property (nonatomic, readwrite, assign) BOOL delayOpenLoaclDevice;

/// 本地视频拉伸模式
@property (nonatomic, readwrite, assign) TKChatVideoRecordManagerScalingMode scalingMode;

/// 远程视频拉伸模式
@property (nonatomic, readwrite, assign) TKChatVideoRecordManagerScalingMode remoteScalingMode;

/// 最大重连次数，默认0
@property (nonatomic, readwrite, assign) int maximumReconnections;

/// 是否申请数字人房间，默认NO
@property (nonatomic, readwrite, assign) int enableVh;

// 是否正在重连,默认为NO
@property (nonatomic, readwrite, assign) BOOL reconnecting;

// 是否连接服务器,默认为NO
@property (nonatomic, readwrite, assign) BOOL connectReady;

/// 数据统计用，是否服务端活体
@property (nonatomic, readwrite, assign) BOOL isLiveDetect;

/// 最大重连时间，默认30
@property (nonatomic, readwrite, assign) int maximumReconnectionsDuration;

// 下面的API是当Token由工具类自行申请时调用
/// 根据设备id获取视频房间token，获取成功后运行TChat
/// - Parameter isFirstInitTChat: 是否首次运行TChat
- (void)sendRequestToGetServerRoomTokenByDeviceID:(BOOL)isFirstInitTChat;
// H5传入新的token，发送网络请求获取录制视频地址，和H5传入的Token绑定录制信息
- (void)sendRequestToRequestVideoPathWithUrlStr:(NSString *)urlStr callBack:(void((^)(BOOL success, NSInteger errorNo, NSString *errorMsg)))callback;

// 下面的API是已有Token时调用
// 请求视频房间号
- (void)requestServerRoomWithUrlStr:(NSString *)urlStr enableVh:(BOOL)enableVh callBack:(void((^)(BOOL success, NSInteger errorNo, NSString *errorMsg)))callback;
- (void)requestServerRoomWithUrlStr:(NSString *)urlStr callBack:(void((^)(BOOL success, NSInteger errorNo, NSString *errorMsg)))callback;

/// 初始化TChat并连接服务器
- (void)initTChatWitness;

/// 连接视频服务器
- (void)connectServer;

/// 断开视频服务器并销毁释放资源
- (void)disconnectServer:(BOOL)needRelease;

/// 虚拟人语音播报
/// @param text 播报的内容
/// @param text 播报的文件
/// @param fileName 文件来源，1:服务器视频文件，2:视频流
- (void)virtualSyntheticAndPlay:(NSString *)text fileName:(NSString *)fileName fileSource:(NSString *)fileSource;

/// 停止虚拟人语音播报
- (void)stopVirtualSyntheticAndPlay;

/// 开始抓取图片
- (void)startFetchPicture;

/// 停止抓取图片
- (void)stopFetchPicture;

/// 重连服务
- (void)reconnectServer;

/// 打开本地麦克风和摄像头，只有delayOpenLoaclDevice == YES调用才有意义
/// - Parameter isOpen: 打开/关闭
- (void)openLocalDevice:(BOOL)isOpen;

@end

NS_ASSUME_NONNULL_END
