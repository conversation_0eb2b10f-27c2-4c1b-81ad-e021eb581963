//
//  TKTChatRtcSmartTwoVideoManager.m
//  TKOpenAccount-Standard
//
//  Created by 夏博文 on 2023/1/28.
//  Copyright © 2023 thinkive. All rights reserved.
//

#import "TKZegoSmartTwoVideoManager.h"
#import "TKDirectVideoModel.h"
#import "TKChatTokenHelper.h"
#import <ZegoLiveRoom/ZegoLiveRoom.h>
#import <ZegoQueue/ZegoQueue.h>

@interface TKZegoSmartTwoVideoManager()<TKSmartTwoVideoManagerDelegate,ZegoRoomDelegate,ZGQueueDelegate,ZegoLivePublisherDelegate,ZegoLivePlayerDelegate,ZegoIMDelegate>
{
    NSTimer *waitSeatTimer;
    ZegoLiveRoomApi *_liveRoomApi;
    double sentkb,recvkb;//网络上下行
}

@end


@implementation TKZegoSmartTwoVideoManager
@synthesize delegate=_delegate;
@synthesize requestParams=_requestParams;
@synthesize contentView=_contentView;
@synthesize remoteContentView=_remoteContentView;

#pragma mark - Init

/// 构造方法
/// @param configParam 初始化参数字典
- (instancetype)initWithConfig:(NSMutableDictionary *)requestParams {
    if (self = [self init]) {
        self.requestParams = requestParams;
        sentkb=recvkb=0;
    }
    return  self;
}

-(NSString *)getTimeStamp{
    // 设置想要的格式，hh与HH的区别:分别表示12小时制,24小时制
    //把NSDate按formatter格式转成NSString
    NSString *currentTime =[TKDateHelper formatDate:[NSDate date] format:@"yyyy-MM-dd HH:mm:ss"];
    return currentTime;
}

//原生测试代码方法，走h5联调或者生产就注释掉
- (void)TestGetTChatRtcToken
{
    //即构视频连接初始化
    [TKDirectVideoModel shareInstance].userVideoIdString= [NSString stringWithFormat:@"%@", self.requestParams[@"user_id"]];
    [ZegoLiveRoomApi setUserID: [TKDirectVideoModel shareInstance].userVideoIdString userName:[TKDirectVideoModel shareInstance].userVideoIdString];
    [ZegoLiveRoomApi setDomainName:@"experience.zegonetwork.com:15443" useHttps:YES useLocalNetwork:NO logDomainName:nil];
    
    _liveRoomApi = [[ZegoLiveRoomApi alloc] initWithAppID:1234567 appSignature:@"12345678901234567890123456789012" completionBlock:^(int errorCode) {
        if (errorCode!=0) {
            dispatch_async(dispatch_get_main_queue(), ^{
                
                NSString *witnessResult = nil;
                if([TKDirectVideoModel shareInstance].isDirectVideo){
                    witnessResult = @"app:10002";
                }else{
                    witnessResult = @"-3";//连接视频服务器失败
                }
                
                [self showConnectErrorTip:witnessResult witnessInfo:[NSString stringWithFormat:@"即构视频连接初始化失败(%d)",errorCode] tipTitle:@"视频录制提示" tipDesc:@"初始化异常，请稍侯重试！"];
                
            });
        }
    }];
    
    //设置为无排队模式
    [ZGQueue.sharedQueue start:ZGUserTypeUserTypeCustomer];
    //SDK 的登录和排队事件通知。
    [ZGQueue.sharedQueue setQueueDelegate:self];
    //设置实时消息代理对象
    [_liveRoomApi setIMDelegate:self];
    //设置音视频房间的回调
    [_liveRoomApi setRoomDelegate:self];

    // 和 web 互通，推荐 LOW3（opus）
    [_liveRoomApi setLatencyMode:ZEGOAPI_LATENCY_MODE_LOW3];
    [self fetchQueueLoginTokenWithUserid:[TKDirectVideoModel shareInstance].userVideoIdString userName:[TKDirectVideoModel shareInstance].userVideoIdString appID:1234567 callback:^(NSString *token, NSError *error) {
        
        // 登录队列服务器。
        [ZGQueue.sharedQueue userLogin:token];
        

    }];


    [TKDirectVideoModel shareInstance].isStartingVideo=NO;
    [TKDirectVideoModel shareInstance].isTransBufferMsg=NO;
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(becomeTChatActive:) name:UIApplicationDidBecomeActiveNotification object:nil];

}

-(void)fetchQueueLoginTokenWithUserid:(NSString*)userid userName:(NSString*)username appID:(unsigned int)appID callback:(void(^)(NSString *token, NSError *error))callback {
    int QUEUE_ROLE_CUSTOMER = 10;
    NSDictionary *params = @{
        @"timestamp":@((int32_t)NSDate.date.timeIntervalSince1970),
        @"app_id":@(appID),
        @"user_id":userid,
        @"user_name":username,
        @"queue_role":@(QUEUE_ROLE_CUSTOMER),
        @"room_role":@(ZEGO_AUDIENCE),
        };

    NSData *jsonInfo = [NSJSONSerialization dataWithJSONObject:params options:0 error:nil];

    NSURL *url ;
//    if([TKStringHelper isEmpty:[TKDirectVideoModel shareInstance].videoServerInfo[@"tokenServer"]]){
//        url= [NSURL URLWithString:@"https://experience.zegonetwork.com:16443/logintoken"];
//    }else{
        
        url = [NSURL URLWithString:[NSString stringWithFormat:@"%@/logintoken",[TKDirectVideoModel shareInstance].videoServerInfo[@"tokenServer"]]];
//    }
    

    NSMutableURLRequest *request = [NSMutableURLRequest requestWithURL:url];
    request.HTTPMethod = @"POST";
    request.HTTPBody = jsonInfo;

    NSURLSessionConfiguration *config = NSURLSessionConfiguration.defaultSessionConfiguration;
    config.timeoutIntervalForRequest = 10;
    config.HTTPAdditionalHeaders = @{@"Content-Type":@"application/json"};

    NSURLSession *session = [NSURLSession sessionWithConfiguration:config];
    [[session dataTaskWithRequest:request completionHandler:^(NSData * _Nullable data, NSURLResponse * _Nullable response, NSError * _Nullable error) {
        if (error) {
            if (callback) {
                callback(nil, error);
            }
        } else {
            NSError *_inErr = nil;
            NSDictionary *dict = [NSJSONSerialization JSONObjectWithData:data options:0 error:&_inErr];
            NSString *token = nil;
            if (!_inErr) {
                token = dict[@"login_token"];
            }
            if (callback) {
                callback(token, error);
            }
        }
    }] resume];
}

#pragma mark 视频开启关闭相关
/***
 启动视频见证
 */
- (void)startSmartTwoVideo:(NSString *)sUrl withPort:(int)sPort {
    
    TKLogInfo(@"思迪双向日志：正在启动视频见证");
    
    [UIApplication sharedApplication].idleTimerDisabled = YES;
    
//    //原生测试代码方法，走h5联调或者生产就注释掉
//    [self TestGetTChatRtcToken];
//    return;
    
    if (self.delegate && [self.delegate respondsToSelector:@selector(addSmartTwoVideoChatView)]) {
        [self.delegate addSmartTwoVideoChatView];
    }


    //即构视频连接初始化
    [TKDirectVideoModel shareInstance].userVideoIdString= [NSString stringWithFormat:@"%@", self.requestParams[@"user_id"]];
    [ZegoLiveRoomApi setUserID: [TKDirectVideoModel shareInstance].userVideoIdString userName:[TKDirectVideoModel shareInstance].userVideoIdString];
    NSString *videoPath=[TKDirectVideoModel shareInstance].videoServerInfo[@"dispatchServer"];
    if([videoPath hasPrefix:@"https://"]){
        videoPath=[videoPath stringByReplacingOccurrencesOfString:@"https://" withString:@""];
    }else if([videoPath hasPrefix:@"http://"]){
        videoPath=[videoPath stringByReplacingOccurrencesOfString:@"http://" withString:@""];
    }
    
    [ZegoLiveRoomApi setDomainName:videoPath useHttps:YES useLocalNetwork:NO logDomainName:nil];
    
    _liveRoomApi = [[ZegoLiveRoomApi alloc] initWithAppID:[[TKDirectVideoModel shareInstance].videoServerInfo[@"appId"] intValue] appSignature:[TKDirectVideoModel shareInstance].videoServerInfo[@"appSignature"] completionBlock:^(int errorCode) {
        if (errorCode!=0) {
            dispatch_async(dispatch_get_main_queue(), ^{
                
                NSString *witnessResult = nil;
                if([TKDirectVideoModel shareInstance].isDirectVideo){
                    witnessResult = @"app:10002";
                }else{
                    witnessResult = @"-3";//连接视频服务器失败
                }
                
                [self showConnectErrorTip:witnessResult witnessInfo:[NSString stringWithFormat:@"即构视频连接初始化失败(%d)",errorCode] tipTitle:@"视频录制提示" tipDesc:@"初始化异常，请稍侯重试！"];
            });
        } else {
            TKLogInfo(@"思迪双向日志：初始化即构SDK完成");
        }
    }];
    
    //设置为无排队模式
    [ZGQueue.sharedQueue start:ZGUserTypeUserTypeCustomer];
    //SDK 的登录和排队事件通知。
    [ZGQueue.sharedQueue setQueueDelegate:self];
    //设置实时消息代理对象
    [_liveRoomApi setIMDelegate:self];
    //设置音视频房间的回调
    [_liveRoomApi setRoomDelegate:self];
    // 和 web 互通，推荐 LOW3（opus）
    [_liveRoomApi setLatencyMode:ZEGOAPI_LATENCY_MODE_LOW3];
    //登录大厅
    [self fetchQueueLoginTokenWithUserid:[TKDirectVideoModel shareInstance].userVideoIdString userName:[TKDirectVideoModel shareInstance].userVideoIdString appID:[[TKDirectVideoModel shareInstance].videoServerInfo[@"appId"] intValue] callback:^(NSString *token, NSError *error) {
        
        if (error == nil) {
            // 登录队列服务器。
            [ZGQueue.sharedQueue userLogin:token];
            
            TKLogInfo(@"思迪双向日志：获取token完毕，开始登陆");
        } else {
            dispatch_async(dispatch_get_main_queue(), ^{
                
                NSString *witnessResult = nil;
                if([TKDirectVideoModel shareInstance].isDirectVideo){
                    witnessResult = @"app:10002";
                }else{
                    witnessResult = @"-3";//连接视频服务器失败
                }
                
                [self showConnectErrorTip:witnessResult witnessInfo:[NSString stringWithFormat:@"即构视频连接初始化失败(%ld)",(long)error.code] tipTitle:@"视频录制提示" tipDesc:@"初始化异常，请稍侯重试！"];
            });
        }
    }];

    [TKDirectVideoModel shareInstance].isStartingVideo=NO;
    [TKDirectVideoModel shareInstance].isTransBufferMsg=NO;
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(becomeTChatActive:) name:UIApplicationDidBecomeActiveNotification object:nil];

    
    //记录开始连接视频服务器事件
    NSString *logString=[NSString stringWithFormat:@"TKMSG1004:网络情况%@|发起连接|%@",[TKNetHelper getNetworkTypeInfo],[self getTimeStamp]];

    if (self.delegate && [self.delegate respondsToSelector:@selector(uploadSmartTwoVideoLog:)]) {
        [self.delegate uploadSmartTwoVideoLog:logString];
    }
}

/**
 *  <AUTHOR> 2019年09月10日14:03:52
 *  App从后台返回前台
 *  @param notif
 */
-(void)becomeTChatActive:(NSNotification *)notif{

}




/**
 *
 * @method tkStopTChatWitness
 *
 * @brief 结束视频见证
 *
 */
-(void)stopSmartTwoVideo{
    [self endTChatVideo];
}

/**
 *
 * @method tkSwitchCameraNewTChatWitness
 *
 * @brief 切换摄像头
 *
 */
-(void)switchCameraSmartTwoVideo:(BOOL)isFrontCamera{
    TKLogInfo(@"思迪双向日志：切换摄像头，isFrontCamera = %i", isFrontCamera);
    if (isFrontCamera) {
        [_liveRoomApi setFrontCam:NO];
    }else{
        [_liveRoomApi setFrontCam:YES];
    }
}

/**
 *
 * @method sendMsgToVideoServer
 *
 * @brief 发送消息给坐席
 *
 */
-(void)sendMsgToVideoServer:(NSString *)msg{
    
    [_liveRoomApi sendRoomMessage:msg type:ZEGO_TEXT category:ZEGO_CHAT completion:nil];
}

- (void)showConnectErrorTip:(NSString *)witnessResult witnessInfo:(NSString *)witnessInfo tipTitle:(NSString *)tipTitle tipDesc:(NSString *)tipDesc
{
    if (![TKDirectVideoModel shareInstance].isShowAlert) {
        [TKDirectVideoModel shareInstance].isShowAlert = YES;
        
        [TKDirectVideoModel shareInstance].witnessResult = witnessResult;//连接视频服务器失败
        [TKDirectVideoModel shareInstance].witnessInfo=witnessInfo;
        
        if (self.delegate && [self.delegate respondsToSelector:@selector(alertSmartTwoVideoTip:describe:cancelBtnTitle:takeBtnTitle:)]) {
            [self.delegate alertSmartTwoVideoTip:tipTitle describe:tipDesc cancelBtnTitle:nil takeBtnTitle:@"确定"];
        }
    }
}


#pragma mark ZGQueueDelegate

/**
 * 登录返回
 *
 * @param error 错误码，0-成功，1-⽹络连接错误，2-⽹络重连错误，3-登录请求错误，4-登 录超时错误，5-⼼跳超时错误，6-⽹络中断错误，7-调度错误
 */
- (void)onUserLogin:(int32_t)error{
    if(error==0){
        TKLogInfo(@"思迪双向日志：已进入房间");
        
        // 埋点-双向-视频-进入房间
        NSMutableDictionary *eventDic = [NSMutableDictionary dictionaryWithDictionary:self.requestParams];
        [TKStatisticEventHelper sendEvent:TKPrivateEventVideoWitness subEventName:TKPrivateSubEventVideoWitnessPrepareVideo progress:TKPrivateEventProgressNone result:TKPrivateEventResultSuccess orientation:TKPrivateVideoOrientationPortrait oneWayVideoType:TKPrivateOneWayVideoTypeNone prepareVideoProgress:TKPrivatePrepareVideoProgressEnterRoom eventDic:eventDic];
        
    
        //进入房间
        [_liveRoomApi loginRoom:[TKDirectVideoModel shareInstance].witRoomId role:ZEGO_AUDIENCE withCompletionBlock:^(int errorCode, NSArray<ZegoStream *> *streamList) {
            // streamList，内部封装了 userID、userName、streamID 和 extraInfo。
            // 登录房间成功后，开发者可通过 streamList 获取到当前房间推流信息，便于后续的拉流操作。
            // 当 streamList 为空时说明当前房间没有⼈推流
            if (errorCode == 0) {
                //登录房间成功
                [_liveRoomApi setPreviewView:_contentView];
                [_liveRoomApi setPreviewViewMode:ZegoVideoViewModeScaleAspectFill];
                BOOL ret = [_liveRoomApi startPreview];
                //推视频流
                //设置走前置摄像头
                [_liveRoomApi setFrontCam:YES];
                [_liveRoomApi setPublisherDelegate:self];
                [_liveRoomApi startPublishing:[TKDirectVideoModel shareInstance].userVideoIdString title:nil flag:ZEGO_JOIN_PUBLISH extraInfo:nil];
                
                if(streamList.count<1){
                    //坐席先进了房间推送了视频流，这里直接倒计时，最后以收到坐席视频流为准
                    dispatch_async(dispatch_get_main_queue(), ^{
                        
                        waitSeatTimer = [NSTimer scheduledTimerWithTimeInterval:20 target:self selector:@selector(handleWaitSeatTimeout:) userInfo:[NSDictionary dictionaryWithObjectsAndKeys:[TKDirectVideoModel shareInstance].witRoomId,@"roomId", nil] repeats:NO];
                    });
                }else{
                    if ([TKDirectVideoModel shareInstance].isStartingVideo == NO) {
                        ZegoStream *zegoStream=streamList.firstObject;
                        [TKDirectVideoModel shareInstance].isStartingVideo = YES;
                        
                        [_liveRoomApi setPlayerDelegate:self];
                        [TKDirectVideoModel shareInstance].seatVideoIdString = zegoStream.streamID;
                        //开始拉流
                        int ret = [_liveRoomApi startPlayingStream:zegoStream.streamID inView:_remoteContentView];
                        //设置拉流视图显示模式
                        [_liveRoomApi setViewMode:ZegoVideoViewModeScaleAspectFill ofStream:zegoStream.streamID];
                        
                    }
                }
                
            }else {
                    //登录房间失败
                    TKLogInfo(@"思迪双向日志：用户进入大厅失败");
                    dispatch_async(dispatch_get_main_queue(), ^{
                        
                        NSString *witnessResult = nil;
                        if([TKDirectVideoModel shareInstance].isDirectVideo){
                            witnessResult = @"app:10002";
                        }else{
                            witnessResult = @"-4";//连接视频服务器失败
                        }
                        
                        [self showConnectErrorTip:witnessResult witnessInfo:[NSString stringWithFormat:@"用户进入房间失败(%d)",errorCode] tipTitle:@"视频录制提示" tipDesc:@"服务器异常，请稍侯重试！"];
                    });
            }
        }];
    }else{
        
        TKLogInfo(@"思迪双向日志：用户进入队列失败");
        dispatch_async(dispatch_get_main_queue(), ^{
            
            NSString *witnessResult = nil;
            if([TKDirectVideoModel shareInstance].isDirectVideo){
                witnessResult = @"app:10002";
            }else{
                witnessResult = @"-3";//连接视频服务器失败
            }
            
            [self showConnectErrorTip:witnessResult witnessInfo:[NSString stringWithFormat:@"用户进入队列失败(%d)",error] tipTitle:@"视频录制提示" tipDesc:@"服务器异常，请稍侯重试！"];
        });
    }
}
/**
 * 排队连接状态更新通知
 *
 * @param errorCode 错误码，0-成功
 * @param state 连接状态， -1 - 被挤掉线，1 - 掉线后sdk内部重连并重进队列成功，2 -
临时中断，此时sdk内部尝试重连恢复队列，直到重连失败才会触发掉线通知，3 - 掉线通知，sdk内 部重连失败后才会抛出的掉线，11 - 重连登录成功，但是恢复进⼊队列失败
 */
- (void)onConnectState:(int32_t)state
             errorCode:(int32_t)errorCode{
    if (errorCode == 0) {//进入房间成功
        
         TKLogInfo(@"思迪双向日志：用户进入大厅成功");
        
    }else{
        
        TKLogInfo(@"思迪双向日志：用户进入大厅失败");
        dispatch_async(dispatch_get_main_queue(), ^{
            
            NSString *witnessResult = nil;
            if([TKDirectVideoModel shareInstance].isDirectVideo){
                witnessResult = @"app:10002";
            }else{
                witnessResult = @"-3";//连接视频服务器失败
            }
            
            [self showConnectErrorTip:witnessResult witnessInfo:[NSString stringWithFormat:@"用户进入大厅失败(%d)",errorCode] tipTitle:@"视频录制提示" tipDesc:@"服务器异常，请稍侯重试！"];
        });
    }
}


#pragma mark ZegoRoomDelegate
/**
 与 server 断开通知
 
 @param errorCode 错误码，0 表示无错误
 @param roomID 房间 ID
 @discussion 建议开发者在此通知中进行重新登录、推/拉流、报错、友好性提示等其他恢复逻辑。与 server 断开连接后，SDK 会进行重试，重试失败抛出此错误。请注意，此时 SDK 与服务器的所有连接均会断开
 */
- (void)onDisconnect:(int)errorCode roomID:(NSString *)roomID{
    TKLogInfo(@"思迪双向日志：视频连接断开");

    if(errorCode==0){
        return;
    }

    
    dispatch_async(dispatch_get_main_queue(), ^{
        
        
        NSString *witnessResult = nil;
        if([TKDirectVideoModel shareInstance].isDirectVideo){
            witnessResult = @"app:10009";
        }else{
            witnessResult = @"-6";//连接视频服务器失败
        }
        
        [self showConnectErrorTip:witnessResult witnessInfo:[NSString stringWithFormat:@"与视频服务器断开连接(%d)",errorCode] tipTitle:@"视频录制提示" tipDesc:@"与视频服务器断开连接"];
        
    });
}

/**
 用户被踢出房间
 
 @param reason 被踢出原因，16777219 表示该账户多点登录被踢出，16777220 表示房间关闭被踢出，16777221 表示房间会话错误被踢出，16777222 表示推送通道被踢出。
 @param roomID 房间 ID
 @discussion 可在该回调中处理用户被踢出房间后的下一步处理（例如报错、重新登录提示等）
 */
- (void)onKickOut:(int)reason roomID:(NSString *)roomID{
    TKLogInfo(@"思迪双向日志：用户被踢出房间");
    dispatch_async(dispatch_get_main_queue(), ^{
        
        if (![TKDirectVideoModel shareInstance].isShowAlert) {
            
            [TKDirectVideoModel shareInstance].isShowAlert = YES;

            
            if (self.delegate && [self.delegate respondsToSelector:@selector(alertSmartTwoVideoTip:describe:cancelBtnTitle:takeBtnTitle:)]) {
                [self.delegate alertSmartTwoVideoTip:@"视频录制提示" describe:[NSString stringWithFormat:@"%@网络异常,请稍侯重试",[TKDirectVideoModel shareInstance].serviceTipString] cancelBtnTitle:nil takeBtnTitle:@"确定"];
            }
        }
        
    });
}

/**
 流信息更新
 
 @param type 更新类型，详见 ZegoStreamType 定义
 @param streamList 直播流列表，列表中包含的是变更流的信息，非房间全部流信息
 @param roomID 房间 ID
 @discussion 房间内增加流、删除流，均会触发此更新。主播推流，自己不会收到此回调，房间内其他成员会收到。建议对流增加和流删除分别采取不同的处理。
 */
- (void)onStreamUpdated:(int)type streams:(NSArray<ZegoStream*> *)streamList roomID:(NSString *)roomID{
    TKLogInfo(@"思迪双向日志：流信息更新");
    if (streamList.count > 0) {
        // 拉流，1v1 拉第一个流即可
        if ([TKDirectVideoModel shareInstance].isStartingVideo == NO) {
            [TKDirectVideoModel shareInstance].isStartingVideo = YES;
            ZegoStream *firstOne = streamList.firstObject;
            [_liveRoomApi setPlayerDelegate:self];
            [TKDirectVideoModel shareInstance].seatVideoIdString = firstOne.streamID;
            //开始拉流
            int ret = [_liveRoomApi startPlayingStream:firstOne.streamID inView:_remoteContentView];
            //设置拉流视图显示模式
            [_liveRoomApi setViewMode:ZegoVideoViewModeScaleAspectFill ofStream:firstOne.streamID];
 
        }
    }
}

// ZegoLiveRoomApi.h

/**
 收到自定义信令

 * 调用 -sendCustomCommand:content:completion: 发送自定义信令后，消息列表中的用户会触发此回调，用户自己发送的消息不会通过此回调得到通知。

 @param fromUserID 消息来源 UserID
 @param fromUserName 消息来源 UserName
 @param content 消息内容
 @param roomID 房间 ID
 */
- (void)onReceiveCustomCommand:(NSString *)fromUserID userName:(NSString *)fromUserName content:(NSString*)content roomID:(NSString *)roomID{
    TKLogInfo(@"思迪双向日志：收到自定义信令");
}

#pragma mark ZegoLivePublisherDelegate
/**
 推流状态更新
 
 @param stateCode 状态码
 @param streamID 流 ID
 @param info 推流信息
 @discussion 主播调用 [ZegoLiveRoomApi (Publisher) -startPublishing:title:flag:] 推流成功后，通过该 API 通知主播方
 @note 推流状态码及其含义如下：
 stateCode = 0，直播开始。
 stateCode = 3，直播遇到严重问题（如出现，请联系 ZEGO 技术支持）。
 stateCode = 4，创建直播流失败。
 stateCode = 5，获取流信息失败。
 stateCode = 6，无流信息。
 stateCode = 7，媒体服务器连接失败（请确认推流端是否正常推流、正式环境和测试环境是否设置同一个、网络是否正常）。
 stateCode = 8，DNS 解析失败。
 stateCode = 9，未登录就直接拉流。
 stateCode = 10，逻辑服务器网络错误(网络断开时间过长时容易出现此错误)。
 stateCode = 105，发布流名被占用。
 */
- (void)onPublishStateUpdate:(int)stateCode streamID:(NSString *)streamID streamInfo:(NSDictionary *)info{
    TKLogInfo(@"思迪双向日志：推流回调 streamID: %@, stateCode: %d", streamID, stateCode);
    if([[TKDirectVideoModel shareInstance].userVideoIdString isEqualToString:streamID]){
        
        if (stateCode == 0) {
            
      
            
            if (![[TKDirectVideoModel shareInstance].userVideoIdString isEqualToString:streamID]) {
                if ([TKDirectVideoModel shareInstance].isStartingVideo == NO) {
                    [TKDirectVideoModel shareInstance].isStartingVideo = YES;

                    [_liveRoomApi setPlayerDelegate:self];
                    [TKDirectVideoModel shareInstance].seatVideoIdString = streamID;
                    //开始拉流
                    int ret = [_liveRoomApi startPlayingStream:streamID inView:_remoteContentView];
                    //设置拉流视图显示模式
                    [_liveRoomApi setViewMode:ZegoVideoViewModeScaleAspectFill ofStream:streamID];
         
                }
            }
            
            return;

        }
        
        
        if (![TKDirectVideoModel shareInstance].isShowAlert&&![TKDirectVideoModel shareInstance].isTransBufferMsg) {
            
            NSString *witnessResult = nil;
            if([TKDirectVideoModel shareInstance].isDirectVideo){
                witnessResult = @"app:10002";
            }else{
                witnessResult = @"-6";//连接视频服务器失败
            }
            
            [self showConnectErrorTip:witnessResult witnessInfo:[NSString stringWithFormat:@"推送视频失败(%i)", stateCode] tipTitle:@"视频录制提示" tipDesc:[NSString stringWithFormat:@"推送视频失败(%i)", stateCode]];
        }
    }else{
        //坐席推流
//        if ([TKStringHelper isEmpty:[TKDirectVideoModel shareInstance].seatVideoIdString]) {
//            if([_clientRtc GetRoomOnlineUser] != nil && [[_clientRtc GetRoomOnlineUser] count] > 0){
//                NSString *userID = [_clientRtc GetRoomOnlineUser][0];
//                if (![userID isEqualToString:[TKDirectVideoModel shareInstance].seatVideoIdString]) {
//                    [TKDirectVideoModel shareInstance].seatVideoIdString = userID;
//                }
//            }
//        }
        

    }
}


/**
 发布质量更新
 
 @param quality 发布质量，0 ~ 3 分别对应优、良、中、差
 @param streamID 发布流 ID
 @param fps 帧率(frame rate)
 @param kbs 码率(bit rate) kb/s
 @discussion 调用者可以在此回调中获取当前的视频质量数据，加以处理
 @note 不建议使用，请用 onPublishQualityUpdate:quality: 代替
 */
- (void)onPublishQualityUpdate:(int)quality stream:(NSString *)streamID videoFPS:(double)fps videoBitrate:(double)kbs{
    NSString *netStatusString=@"当前您的网络不佳";
    if (quality==3) {
        //网络卡顿就toast提示
        if (self.delegate && [self.delegate respondsToSelector:@selector(showSmartTwoVideoNetStatus:withColor:)]) {
            [self.delegate showSmartTwoVideoNetStatus:netStatusString withColor:@"#FD4D43"];
        }
    }
    //自己的视频流就是上行，(目前测试这个就只是对方视频流)
    sentkb=kbs;

        
    dispatch_async(dispatch_get_main_queue(), ^{
        if (self.delegate && [self.delegate respondsToSelector:@selector(smartTwoVideoNetWorkUpDownTip:)]) {
            [self.delegate smartTwoVideoNetWorkUpDownTip:[NSString stringWithFormat:@"上行:%dKB/s\n下行:%dKB/s",(int)sentkb,(int)recvkb]];
        }
        
    });
}
#pragma mark ZegoLivePlayerDelegate
/**
 播放流事件
 
 @param stateCode 播放状态码，0 表示拉流成功
 @param streamID 流 ID
 @discussion 观众调用 [ZegoLiveRoomApi (Player) -startPlayingStream:inView:] 或 [ZegoLiveRoomApi (Player) -startPlayingStream:inView:params:] 拉流成功后，通过该 API 通知
 @note 拉流状态码及其含义如下:
 stateCode = 0，直播开始。
 stateCode = 3，直播遇到严重问题（如出现，请联系 ZEGO 技术支持）。
 stateCode = 4，创建直播流失败。
 stateCode = 5，获取流信息失败。
 stateCode = 6，无流信息。
 stateCode = 7，媒体服务器连接失败（请确认推流端是否正常推流、正式环境和测试环境是否设置同一个、网络是否正常）。
 stateCode = 8，DNS 解析失败。
 stateCode = 9，未登录就直接拉流。
 stateCode = 10，逻辑服务器网络错误(网络断开时间过长时容易出现此错误)。
 */
- (void)onPlayStateUpdate:(int)stateCode streamID:(NSString *)streamID{
    TKLogInfo(@"思迪双向日志：拉流回调 streamID: %@, stateCode: %d", streamID, stateCode);
    
    if([[TKDirectVideoModel shareInstance].seatVideoIdString isEqualToString:streamID]){
        
        if (stateCode == 0) {
            
            dispatch_async(dispatch_get_main_queue(), ^{
                
                [self startTChatVideo];
            });
            
            return;
        }

        
        if (![TKDirectVideoModel shareInstance].isShowAlert&&![TKDirectVideoModel shareInstance].isTransBufferMsg) {
            
            NSString *witnessResult = nil;
            if([TKDirectVideoModel shareInstance].isDirectVideo){
                witnessResult = @"app:10002";
            }else{
                witnessResult = @"-7";//连接视频服务器失败
            }
            
            [self showConnectErrorTip:witnessResult witnessInfo:[NSString stringWithFormat:@"拉取视频失败(%i)", stateCode] tipTitle:@"视频录制提示" tipDesc:[NSString stringWithFormat:@"拉取视频失败(%i)", stateCode]];
        }
    }
}

/**
 观看质量更新
 
 @param quality 0 ~ 3 分别对应优、良、中、差
 @param streamID 观看流ID
 @param fps 帧率(frame rate)
 @param kbs 码率(bit rate) kb/s
 @discussion startPlay 后，该回调会被多次调用，调用周期取决于 [ZegoLiveRoomApi (Player) setPlayQualityMonitorCycle] 设置的周期。开发者可以在该回调中获取当前的视频质量数据，加以处理
 */
- (void)onPlayQualityUpdate:(int)quality stream:(NSString *)streamID videoFPS:(double)fps videoBitrate:(double)kbs{

    NSString *netStatusString=@"当前对方网络不佳";
    if (quality==3) {
        //网络卡顿就toast提示
        if (self.delegate && [self.delegate respondsToSelector:@selector(showSmartTwoVideoNetStatus:withColor:)]) {
            [self.delegate showSmartTwoVideoNetStatus:netStatusString withColor:@"#FD4D43"];
        }
    }
    

    //对方的视频流就是下行
    recvkb=kbs;

        
    dispatch_async(dispatch_get_main_queue(), ^{
        if (self.delegate && [self.delegate respondsToSelector:@selector(smartTwoVideoNetWorkUpDownTip:)]) {
            [self.delegate smartTwoVideoNetWorkUpDownTip:[NSString stringWithFormat:@"上行:%dKB/s\n下行:%dKB/s",(int)sentkb,(int)recvkb]];
        }
        
    });
}

/**
 视频宽高变化通知
 
 @param size 视频大小
 @param streamID 流的唯一标识
 @discussion startPlay 后，以下情况下，播放端会收到该通知：1. SDK 在获取到第一帧数据后 2. 直播过程中视频宽高发生变化。从播放第一条流，到获得第一帧数据，中间可能出现一个短暂的时间差（具体时长取决于当前的网络状态），推荐在进入直播页面时加载一张预览图以提升用户体验，然后在本回调中去掉预览图
 */
- (void)onVideoSizeChangedTo:(CGSize)size ofStream:(NSString *)streamID{
    
}

#pragma mark ZegoIMDelegate
/**
 房间成员更新回调
 
 @param userList 成员更新列表
 @param type  更新类型(增量，全量)
 @discussion 当房间成员变化（例如用户进入、退出房间）时，会触发此通知
 */
- (void)onUserUpdate:(NSArray<ZegoUserState *> *)userList updateType:(ZegoUserUpdateType)type{
    TKLogInfo(@"思迪双向日志：人数更新，房间人数:%d,房间号:%@",userList.count,[TKDirectVideoModel shareInstance].witRoomId);
    //这里回调人数一直都是1所以不作为坐席没进房间标准
    if (userList.count >= 1) {

        

            for (int i = 0; i< userList.count; i++) {

                if (![[userList objectAtIndex:i].userID isEqualToString:[TKDirectVideoModel shareInstance].seatVideoIdString]) {

                    [TKDirectVideoModel shareInstance].seatVideoIdString = [userList objectAtIndex:i].userID;

                    break;
                }
            }


    }else{

        dispatch_async(dispatch_get_main_queue(), ^{
            
            waitSeatTimer = [NSTimer scheduledTimerWithTimeInterval:20 target:self selector:@selector(handleWaitSeatTimeout:) userInfo:[NSDictionary dictionaryWithObjectsAndKeys:[TKDirectVideoModel shareInstance].witRoomId,@"roomId", nil] repeats:NO];
        });

    }
}

/**
 收到房间的广播消息
 
 @param roomId 房间 Id
 @param messageList 消息列表，包括消息内容，消息分类，消息类型，发送者等信息
 @discussion 调用 [ZegoLiveRoomApi (IM) -sendRoomMessage:type:category:priority:completion:] 发送消息，会触发此通知
 */
- (void)onRecvRoomMessage:(NSString *)roomId messageList:(NSArray<ZegoRoomMessage*> *)messageList{
   //待确认是取最后一条消息还是遍历
    
    for (ZegoRoomMessage *msg in messageList) {
        if(msg.category==ZEGO_CHAT){
            [self pullServerMsg:msg];
        }else if(msg.category==ZEGO_SYSTEM){
            [self pullServerBuff:msg];
        }
    }
}

//处理文本消息
-(void)pullServerMsg:(ZegoRoomMessage *)msg{
    if(msg.type==ZEGO_TEXT){
        dispatch_async(dispatch_get_main_queue(), ^{
            NSString *msgBuf=msg.content;
            if([msgBuf rangeOfString:@"USR:1000:"].location != NSNotFound){
                
                NSRange userRange = [msgBuf rangeOfString:@"USR:1000:"];
                NSString *msg = [msgBuf substringFromIndex:userRange.location+userRange.length];
                
                [self updateTipTCViewText:msg];
                
                return;
            }
            
            if([msgBuf rangeOfString:@"USR:1001:"].location !=NSNotFound){
                //风险协议阅读是否展示指令
                NSRange readAgreeRange=[msgBuf rangeOfString:@"USR:1001:"];
                NSString * readJsonString=[msgBuf substringFromIndex:(readAgreeRange.length)];
                NSMutableDictionary *readParm=(NSMutableDictionary *)[TKDataHelper jsonToDictionary:readJsonString];
                if (readParm&&[readParm isKindOfClass:[NSDictionary class]]) {
                    readParm[@"mainColor"]=self.requestParams[@"mainColor"]?self.requestParams[@"mainColor"]:@"#0354C2";

                    //兼容老的1001指令想展示底部时候
                    if([[readParm getStringWithKey:@"showType"] isEqualToString:@"bottom"]){
                        readParm[@"instructionNo"]=@"1007";//坐席发过来的指令编号
                        if (self.delegate && [self.delegate respondsToSelector:@selector(showSmartTwoVideoBottomRead:)]) {
                            [self.delegate showSmartTwoVideoBottomRead:readParm];
                        }
                    }else{
                        if (self.delegate && [self.delegate respondsToSelector:@selector(showSmartTwoVideoRead:)]) {
                            [self.delegate showSmartTwoVideoRead:readParm];
                        }
                    }
                }else{

                    if (self.delegate && [self.delegate respondsToSelector:@selector(showSmartTwoVideoToast:)]) {
                        [self.delegate showSmartTwoVideoToast:@"数据格式错误"];
                    }
                }

                return;
            }
            
            if ([msgBuf rangeOfString:@"USR:1002:"].location !=NSNotFound) {
                //toast提示
                NSRange toastRange=[msgBuf rangeOfString:@"USR:1002:"];
                NSString *toastString=[msgBuf substringFromIndex:(toastRange.length)];

                if (self.delegate && [self.delegate respondsToSelector:@selector(showSmartTwoVideoToast:)]) {
                    [self.delegate showSmartTwoVideoToast:toastString];
                }
                return;
            }
            
            if([msgBuf rangeOfString:@"USR:1003:"].location !=NSNotFound){
                //根据坐席指令展示带标题的对准框
                NSRange readAgreeRange=[msgBuf rangeOfString:@"USR:1003:"];
                NSString * readJsonString=[msgBuf substringFromIndex:(readAgreeRange.length)];
                NSMutableDictionary *readParm=(NSMutableDictionary *)[TKDataHelper jsonToDictionary:readJsonString];
                if (readParm&&[readParm isKindOfClass:[NSDictionary class]]) {
                    readParm[@"mainColor"]=self.requestParams[@"mainColor"]?self.requestParams[@"mainColor"]:@"#0354C2";

                    if (self.delegate && [self.delegate respondsToSelector:@selector(showSmartTwoVideoTitleBox:)]) {
                        [self.delegate showSmartTwoVideoTitleBox:readParm];
                    }
                }else{
                    if (self.delegate && [self.delegate respondsToSelector:@selector(showSmartTwoVideoToast:)]) {
                        [self.delegate showSmartTwoVideoToast:@"数据格式错误"];
                    }
                }
                return;
            }
            
            if([msgBuf rangeOfString:@"USR:1004:"].location !=NSNotFound){
                //根据坐席指令展示进度条
                NSRange readAgreeRange=[msgBuf rangeOfString:@"USR:1004:"];
                NSString * readJsonString=[msgBuf substringFromIndex:(readAgreeRange.length)];
                NSMutableDictionary *readParm=(NSMutableDictionary *)[TKDataHelper jsonToDictionary:readJsonString];
                if (readParm&&[readParm isKindOfClass:[NSDictionary class]]) {
                    readParm[@"mainColor"]=self.requestParams[@"mainColor"]?self.requestParams[@"mainColor"]:@"#0354C2";

                    if (self.delegate && [self.delegate respondsToSelector:@selector(showSmartTwoVideoProcessNode:)]) {
                        [self.delegate showSmartTwoVideoProcessNode:readParm];
                    }
                }else{
                    if (self.delegate && [self.delegate respondsToSelector:@selector(showSmartTwoVideoToast:)]) {
                        [self.delegate showSmartTwoVideoToast:@"数据格式错误"];
                    }
                }
                return;
            }
            
            if([msgBuf rangeOfString:@"USR:1005:"].location !=NSNotFound){
                //根据坐席指令展示可滚动文本
                NSRange readAgreeRange=[msgBuf rangeOfString:@"USR:1005:"];
                NSString * readJsonString=[msgBuf substringFromIndex:(readAgreeRange.length)];
                NSMutableDictionary *readParm=(NSMutableDictionary *)[TKDataHelper jsonToDictionary:readJsonString];
                if (readParm&&[readParm isKindOfClass:[NSDictionary class]]) {
                    readParm[@"mainColor"]=self.requestParams[@"mainColor"]?self.requestParams[@"mainColor"]:@"#0354C2";

                    if (self.delegate && [self.delegate respondsToSelector:@selector(showSmartTwoVideoRollText:)]) {
                        [self.delegate showSmartTwoVideoRollText:readParm];
                    }
                }else{
                    if (self.delegate && [self.delegate respondsToSelector:@selector(showSmartTwoVideoToast:)]) {
                        [self.delegate showSmartTwoVideoToast:@"数据格式错误"];
                    }
                }
                return;
            }
            
            if ([msgBuf rangeOfString:@"USR:1006:"].location !=NSNotFound) {
                //显示确认弹窗
                NSRange readAgreeRange=[msgBuf rangeOfString:@"USR:1006:"];
                NSString * readJsonString=[msgBuf substringFromIndex:(readAgreeRange.length)];
                NSMutableDictionary *readParm=(NSMutableDictionary *)[TKDataHelper jsonToDictionary:readJsonString];
                if (readParm&&[readParm isKindOfClass:[NSDictionary class]]) {
                    readParm[@"mainColor"]=self.requestParams[@"mainColor"]?self.requestParams[@"mainColor"]:@"#0354C2";
                    readParm[@"instructionNo"]=@"1006";//坐席发过来的指令编号
                    if (self.delegate && [self.delegate respondsToSelector:@selector(showSmartTwoVideoBottomRead:)]) {
                        [self.delegate showSmartTwoVideoBottomRead:readParm];
                    }
                }else{

                    if (self.delegate && [self.delegate respondsToSelector:@selector(showSmartTwoVideoToast:)]) {
                        [self.delegate showSmartTwoVideoToast:@"数据格式错误"];
                    }
                }
                return;
            }
            
            if ([msgBuf rangeOfString:@"USR:1007:"].location !=NSNotFound) {
                //显示底部阅读协议弹窗
                NSRange readAgreeRange=[msgBuf rangeOfString:@"USR:1007:"];
                NSString * readJsonString=[msgBuf substringFromIndex:(readAgreeRange.length)];
                NSMutableDictionary *readParm=(NSMutableDictionary *)[TKDataHelper jsonToDictionary:readJsonString];
                if (readParm&&[readParm isKindOfClass:[NSDictionary class]]) {
                    readParm[@"mainColor"]=self.requestParams[@"mainColor"]?self.requestParams[@"mainColor"]:@"#0354C2";
                    readParm[@"instructionNo"]=@"1007";//坐席发过来的指令编号
                    if (self.delegate && [self.delegate respondsToSelector:@selector(showSmartTwoVideoBottomRead:)]) {
                        [self.delegate showSmartTwoVideoBottomRead:readParm];
                    }
                }else{

                    if (self.delegate && [self.delegate respondsToSelector:@selector(showSmartTwoVideoToast:)]) {
                        [self.delegate showSmartTwoVideoToast:@"数据格式错误"];
                    }
                }
                return;
            }
            
            NSRange userRange = [msgBuf rangeOfString:@"USR:0:"];
            if (userRange.length>0) {
                 
                NSString *msg = [msgBuf substringFromIndex:userRange.location+userRange.length];
                 
                [self updateTipTCViewText:[NSString stringWithFormat:@"%@  %@",[TKDirectVideoModel shareInstance].serviceTipString,msg]];

                 
            }else{
                [self updateTipTCViewText:[NSString stringWithFormat:@"%@  %@",[TKDirectVideoModel shareInstance].serviceTipString,msgBuf]];

            }
        });
    }
}

//处理视频结束消息
-(void)pullServerBuff:(ZegoRoomMessage *)msg{
    if(msg.type==ZEGO_TEXT){
        TKLogInfo(@"transBuffer callback");
        
        [TKDirectVideoModel shareInstance].isTransBufferMsg = YES;
        
        NSString *lpMsgBuf=msg.content;
        
        TKLogInfo(@"来自%@的透明通道消息:%@", msg.fromUserId,lpMsgBuf);
        
        NSRange sysRange = [lpMsgBuf rangeOfString:@"SYS:"];
        
        [TKDirectVideoModel shareInstance].witnessResult = lpMsgBuf;
        
        // 埋点-单向_请求房间_结果
        NSMutableDictionary *eventDic = [NSMutableDictionary dictionaryWithDictionary:self.requestParams];
        eventDic[@"message"] = lpMsgBuf;
        TKPrivateEventResult result = TKPrivateEventResultNone;
        [TKStatisticEventHelper sendEvent:TKPrivateEventVideoWitness
                             subEventName:TKPrivateSubEventVideoWitnessReceiveCMD
                                 progress:TKPrivateEventProgressNone
                                   result:result
                              orientation:TKPrivateVideoOrientationPortrait
                          oneWayVideoType:TKPrivateOneWayVideoTypeTChatSmart
                     prepareVideoProgress:TKPrivatePrepareVideoProgressNone
                                 eventDic:eventDic];
        
        if (sysRange.length > 0) {  //见证返回的透明信息
            
        }else{ //其它消息
            
        }
        
        dispatch_async(dispatch_get_main_queue(), ^{
            [self endTChatVideo];
        });
    }
}

/**
 收到在线人数更新
 
 @param onlineCount 在线人数
 @param roomId 房间 Id
 */
- (void)onUpdateOnlineCount:(int)onlineCount room:(NSString *)roomId{
    TKLogInfo(@"思迪双向日志：房间总人数:%d,房间号:%@",onlineCount,roomId);

}

/**
 收到房间的不可靠消息广播
 
 @param roomId 房间 Id
 @param messageList 消息列表，包括消息内容，消息分类，消息类型，发送者等信息
 @discussion 调用 [ZegoLiveRoomApi (IM) -sendBigRoomMessage:type:category:completion:] 发送消息，会触发此通知
 */
- (void)onRecvBigRoomMessage:(NSString *)roomId messageList:(NSArray<ZegoBigRoomMessage*> *)messageList{
    TKLogInfo(@"思迪双向日志：收到房间的不可靠消息广播");
}



#pragma mark -等待坐席进入房间处理
- (void)handleWaitSeatTimeout:(NSTimer*)timer{

    NSString *msg = [NSString stringWithFormat:@"未匹配到%@,请稍侯重试",[TKDirectVideoModel shareInstance].serviceTipString];
    
    if (timer && timer.userInfo) {
        msg =
        [NSString stringWithFormat:@"未匹配到%@,请稍侯重试[房间号：%ld]",[TKDirectVideoModel shareInstance].serviceTipString ,(long)[timer.userInfo[@"roomId"] integerValue]];
    }
    
    
    NSString *witnessResult = nil;
    if([TKDirectVideoModel shareInstance].isDirectVideo){
        witnessResult = @"app:10003";
    }else{
        witnessResult = @"-5";//连接视频服务器失败
    }
    
    [self showConnectErrorTip:witnessResult witnessInfo:[NSString stringWithFormat:@"%@超时未进入房间",[TKDirectVideoModel shareInstance].serviceTipString] tipTitle:@"视频录制提示" tipDesc:msg];
}


/**
 *  <AUTHOR> 2019年01月24日13:28:43
 *  更新提示语
 *  @return nil
 */
-(void)updateTipTCViewText:(NSString *)tip{
    if (self.delegate && [self.delegate respondsToSelector:@selector(showSmartTwoVideoSeatMessage:)]) {
        [self.delegate showSmartTwoVideoSeatMessage:tip];
    }
}

#pragma mark -启动TChat视频
- (void)startTChatVideo{
    if (waitSeatTimer) {
        
        [waitSeatTimer invalidate];
        
        waitSeatTimer = nil;
    }
    
    //如果需要展示坐席信息
    NSString *serviceInfo=[TKDirectVideoModel shareInstance].staffTips;
    
    if([TKDirectVideoModel shareInstance].isDirectVideo){
        //如果需要展示坐席信息
        if (self.requestParams[@"showStaffInfo"]) {
            serviceInfo=self.requestParams[@"showStaffInfo"];
        }
    }

    
    if (self.delegate && [self.delegate respondsToSelector:@selector(showSmartTwoVideoServiceInfo:)]) {
        TKLogInfo(@"思迪双向日志：展示坐席信息");
        [self.delegate showSmartTwoVideoServiceInfo:serviceInfo];
    }
    

    
    //开始见证计时
    
    if (self.delegate && [self.delegate respondsToSelector:@selector(startSmartTwoVidoeTime)]) {
        TKLogInfo(@"思迪双向日志：开始见证计时");
        [self.delegate startSmartTwoVidoeTime];
    }
    
    //显示视频画面
    if (self.delegate && [self.delegate respondsToSelector:@selector(showSmartTwoVideoChatView)]) {
        TKLogInfo(@"思迪双向日志：显示视频画面");
        [self.delegate showSmartTwoVideoChatView];
    }
}

#pragma mark -结束视频
- (void)endTChatVideo{

    TKLogInfo(@"end ZegoRtc video.");
    
    [UIApplication sharedApplication].idleTimerDisabled = NO;
    
    
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(200 * NSEC_PER_MSEC)), dispatch_get_global_queue(0, 0), ^{
        
       
        
        if ([TKDirectVideoModel shareInstance].witRoomId) {
            
            if(![TKDirectVideoModel shareInstance].isTransBufferMsg&&[TKStringHelper isNotEmpty:[TKDirectVideoModel shareInstance].seatVideoIdString])
            {
                [_liveRoomApi sendRoomMessage:@"SYS:10002" type:ZEGO_TEXT category:ZEGO_SYSTEM completion:nil];
            }

            // 停止推流。
            [_liveRoomApi stopPublishing];
            [_liveRoomApi stopPlayingStream:[TKDirectVideoModel shareInstance].seatVideoIdString];
            [_liveRoomApi logoutRoom];
            // 登出 ZegoQueue
            [ZGQueue.sharedQueue userLogout];
            // 反初始化 ZegoQueue
            // 请⼀定要置空 QueueDelegate，内部会强引⽤
            [ZGQueue.sharedQueue setQueueDelegate:nil];
            // 停⽌队列服务
            [ZGQueue.sharedQueue stop];
            _liveRoomApi = nil;
            
            TKLogInfo(@"思迪双向日志：断开和释放视频资源");
        }
    });
    

    if (self.delegate && [self.delegate respondsToSelector:@selector(endSmartTwoVidoe)]) {
        TKLogInfo(@"思迪双向日志：结束双向视频");
        [self.delegate endSmartTwoVidoe];
    }

}
@end
