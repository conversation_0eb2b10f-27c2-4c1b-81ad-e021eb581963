//
//  TKTChatRtcSmartTwoVideoManager.m
//  TKOpenAccount-Standard
//
//  Created by 夏博文 on 2023/1/28.
//  Copyright © 2023 thinkive. All rights reserved.
//

#import "TKTChatRtcSmartTwoVideoManager.h"
#import "TKDirectVideoModel.h"
#import <TChatRtc/TChatCore.h>
#import <TChatRtc/TChatDefine.h>
//#import "TKChatTokenHelper.h"

@interface TKTChatRtcSmartTwoVideoManager()<TKSmartTwoVideoManagerDelegate,TKCCTextMsgDelegate,TKCCTransDataDelegate,TKCCNotifyMessageDelegate,TKCCReportStatsDelegate>
{
//    TChatPeerClient *_clientRtc;
    NSTimer *waitSeatTimer;
    RTCCameraPreviewView * localVideoView;
}

@end


@implementation TKTChatRtcSmartTwoVideoManager
@synthesize delegate=_delegate;
@synthesize requestParams=_requestParams;
@synthesize contentView=_contentView;
@synthesize remoteContentView=_remoteContentView;

#pragma mark - Init

/// 构造方法
/// @param configParam 初始化参数字典
- (instancetype)initWithConfig:(NSMutableDictionary *)requestParams {
    if (self = [self init]) {
        self.requestParams = requestParams;
    }
    return  self;
}

-(NSString *)getTimeStamp{
    // 设置想要的格式，hh与HH的区别:分别表示12小时制,24小时制
    //把NSDate按formatter格式转成NSString
    NSString *currentTime =[TKDateHelper formatDate:[NSDate date] format:@"yyyy-MM-dd HH:mm:ss"];
    return currentTime;
}



#pragma mark 视频开启关闭相关
/***
 启动视频见证
 */
- (void)startSmartTwoVideo:(NSString *)sUrl withPort:(int)sPort {
    
    [UIApplication sharedApplication].idleTimerDisabled = YES;
    
    if (self.delegate && [self.delegate respondsToSelector:@selector(addSmartTwoVideoChatView)]) {
        [self.delegate addSmartTwoVideoChatView];
    }

    localVideoView= [[RTC_OBJC_TYPE(RTCCameraPreviewView) alloc] initWithFrame:CGRectZero];
    [_contentView addSubview:localVideoView];
    //坐席远端画面

    RTCEAGLVideoView *remoteVideoView = [[RTC_OBJC_TYPE(RTCEAGLVideoView) alloc] initWithFrame:CGRectMake(0, 0, _remoteContentView.TKWidth, _remoteContentView.TKHeight)];
    [_remoteContentView addSubview:remoteVideoView];

    //初始化SDK
    int initFlag=[TChatCore InitSDK:localVideoView :remoteVideoView];
    
    if (initFlag!=0) {
        dispatch_async(dispatch_get_main_queue(), ^{
            
            NSString *witnessResult = nil;
            if([TKDirectVideoModel shareInstance].isDirectVideo){
                witnessResult = @"app:10002";
            }else{
                witnessResult = @"-3";//连接视频服务器失败
            }
            [self showConnectErrorTip:witnessResult witnessInfo:@"连接anychat服务器失败" tipTitle:@"视频录制提示" tipDesc:@"初始化异常，请稍侯重试！"];
            
        });
    }
    
    //获取状态信息
    [TChatCore UserStatsReportControl:YES :2];
    //开启日志
    [TChatCore ActiveCallLog:NO];
    [TChatCore shareTChatCore].notifyMsgDelegate = self;
    [TChatCore shareTChatCore].transDataDelegate = self;
    [TChatCore shareTChatCore].textMsgDelegate = self;
    [TChatCore shareTChatCore].reportDelegate = self;
    
    


    //登录
    [TChatCore SetServerAuthPass: @"123456"];
    [TChatCore Connect:sUrl :sPort];

    [TKDirectVideoModel shareInstance].isStartingVideo=NO;
    [TKDirectVideoModel shareInstance].isTransBufferMsg=NO;
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(becomeTChatActive:) name:UIApplicationDidBecomeActiveNotification object:nil];

    
    //记录开始连接视频服务器事件
    NSString *logString=[NSString stringWithFormat:@"TKMSG1004:网络情况%@|发起连接|%@",[TKNetHelper getNetworkTypeInfo],[self getTimeStamp]];

    if (self.delegate && [self.delegate respondsToSelector:@selector(uploadSmartTwoVideoLog:)]) {
        [self.delegate uploadSmartTwoVideoLog:logString];
    }
}

/**
 *  <AUTHOR> 2019年09月10日14:03:52
 *  App从后台返回前台
 *  @param notif
 */
-(void)becomeTChatActive:(NSNotification *)notif{

}




/**
 *
 * @method tkStopTChatWitness
 *
 * @brief 结束视频见证
 *
 */
-(void)stopSmartTwoVideo{
    [self endTChatVideo];
}

/**
 *
 * @method tkSwitchCameraNewTChatWitness
 *
 * @brief 切换摄像头
 *
 */
-(void)switchCameraSmartTwoVideo:(BOOL)isFrontCamera{
    if (isFrontCamera) {
        [TChatCore SelectDevice:1 :@"front"];
    }else{
        [TChatCore SelectDevice:1 :@"back"];
    }
}

/**
 *
 * @method sendMsgToVideoServer
 *
 * @brief 发送消息给坐席
 *
 */
-(void)sendMsgToVideoServer:(NSString *)msg{
    [TChatCore SendTextMessage:[TKDirectVideoModel shareInstance].seatVideoId :YES :msg];
}

- (void)showConnectErrorTip:(NSString *)witnessResult witnessInfo:(NSString *)witnessInfo tipTitle:(NSString *)tipTitle tipDesc:(NSString *)tipDesc
{
    if (![TKDirectVideoModel shareInstance].isShowAlert) {
        [TKDirectVideoModel shareInstance].isShowAlert = YES;
        
        [TKDirectVideoModel shareInstance].witnessResult = witnessResult;//连接视频服务器失败
        [TKDirectVideoModel shareInstance].witnessInfo=witnessInfo;
        
        if (self.delegate && [self.delegate respondsToSelector:@selector(alertSmartTwoVideoTip:describe:cancelBtnTitle:takeBtnTitle:)]) {
            [self.delegate alertSmartTwoVideoTip:tipTitle describe:tipDesc cancelBtnTitle:nil takeBtnTitle:@"确定"];
        }
    }
}


#pragma mark TKCCNotifyMessageDelegate
// 连接服务器
- (void) OnConnect: (BOOL)success : (int)errorCode{
    //记录开始连接视频服务器返回事件
    NSString *logString=[NSString stringWithFormat:@"TKMSG1005:网络情况%@|%d|%@",[TKNetHelper getNetworkTypeInfo],errorCode,[self getTimeStamp]];

    if (self.delegate && [self.delegate respondsToSelector:@selector(uploadSmartTwoVideoLog:)]) {
        [self.delegate uploadSmartTwoVideoLog:logString];
    }
    
    if(errorCode == 0){//连接视频服务器成功
        
        TKLogInfo(@"connect success.");
        
        

        if([TKDirectVideoModel shareInstance].isDirectVideo){
            [TChatCore Login:self.requestParams[@"loginName"] : self.requestParams[@"loginPwd"]?self.requestParams[@"loginPwd"]:@""];
        }else{
            NSString *loginId = [NSString stringWithFormat:@"%@", self.requestParams[@"user_id"]];
            
            [TChatCore Login:loginId : @""];
        }

        
        //记录开始登陆视频服务器
        NSString *logString=[NSString stringWithFormat:@"TKMSG1006:网络情况%@|开始登录服务器|%@",[TKNetHelper getNetworkTypeInfo],[self getTimeStamp]];

        if (self.delegate && [self.delegate respondsToSelector:@selector(uploadSmartTwoVideoLog:)]) {
            [self.delegate uploadSmartTwoVideoLog:logString];
        }
    }else{
        
        TKLogInfo(@"connect fail errorcode:%d",errorCode);
        
        dispatch_async(dispatch_get_main_queue(), ^{
            
            NSString *witnessResult = nil;
            if([TKDirectVideoModel shareInstance].isDirectVideo){
                witnessResult = @"app:10002";
            }else{
                witnessResult = @"-3";//连接视频服务器失败
            }
            [self showConnectErrorTip:witnessResult witnessInfo:@"连接视频服务器失败" tipTitle:@"视频录制提示" tipDesc:@"服务器异常，请稍侯重试！"];
            
        });
    }
}

// 用户登陆消息
- (void) OnLogin: (int)userId : (int)errorCode
{
    //记录开始登陆视频服务器返回结果
    NSString *logString=[NSString stringWithFormat:@"TKMSG1007:网络情况%@|登陆服务器结果%d|%@",[TKNetHelper getNetworkTypeInfo],errorCode,[self getTimeStamp]];

    if (self.delegate && [self.delegate respondsToSelector:@selector(uploadSmartTwoVideoLog:)]) {
        [self.delegate uploadSmartTwoVideoLog:logString];
    }
    
    if(errorCode == 0){//登录成功
        
        TKLogInfo(@"login success.");

        [TKDirectVideoModel shareInstance].userVideoId=userId;
        if([TKDirectVideoModel shareInstance].isDirectVideo){
            [TKDirectVideoModel shareInstance].witRoomId=self.requestParams[@"roomId"];
            [TChatCore EnterRoom:[[TKDirectVideoModel shareInstance].witRoomId intValue] :  self.requestParams[@"roomPwd"]?self.requestParams[@"roomPwd"]:@""];
        }else{
            [TChatCore EnterRoom:[[TKDirectVideoModel shareInstance].witRoomId intValue] : @""];
        }

        
        
        //记录开始进入房间

        NSString *logString=[NSString stringWithFormat:@"TKMSG1008:网络情况%@|开始进入房间|%@",[TKNetHelper getNetworkTypeInfo],[self getTimeStamp]];

        if (self.delegate && [self.delegate respondsToSelector:@selector(uploadSmartTwoVideoLog:)]) {
            [self.delegate uploadSmartTwoVideoLog:logString];
        }
    }else{
        
        TKLogInfo(@"login failed.");
        
        dispatch_async(dispatch_get_main_queue(), ^{
            
            NSString *witnessResult = nil;
            if([TKDirectVideoModel shareInstance].isDirectVideo){
                witnessResult = @"app:10002";
            }else{
                witnessResult = @"-4";//进入房间失败
            }
            NSString *msg = [NSString stringWithFormat:@"登陆服务器异常，请稍候重试！错误码:%d",errorCode];
            [self showConnectErrorTip:witnessResult witnessInfo:@"进入房间失败" tipTitle:@"视频录制提示" tipDesc:msg];
            
        });
    }
}

// 用户进入房间消息
- (void) OnEnterRoom: (int)roomId : (int)errorCode
{
    //记录开始进入房间返回结果
    NSString *logString=[NSString stringWithFormat:@"TKMSG1009:网络情况%@|进入房间结果%d|%@",[TKNetHelper getNetworkTypeInfo],errorCode,[self getTimeStamp]];

    if (self.delegate && [self.delegate respondsToSelector:@selector(uploadSmartTwoVideoLog:)]) {
        [self.delegate uploadSmartTwoVideoLog:logString];
    }
    
    if (errorCode == 0) {//进入房间成功
        TKLogInfo(@"已进入房间");
        
        // 埋点-双向-视频-进入房间
        NSMutableDictionary *eventDic = [NSMutableDictionary dictionaryWithDictionary:self.requestParams];
        [TKStatisticEventHelper sendEvent:TKPrivateEventVideoWitness subEventName:TKPrivateSubEventVideoWitnessPrepareVideo progress:TKPrivateEventProgressNone result:TKPrivateEventResultSuccess orientation:TKPrivateVideoOrientationPortrait oneWayVideoType:TKPrivateOneWayVideoTypeNone prepareVideoProgress:TKPrivatePrepareVideoProgressEnterRoom eventDic:eventDic];
        //发布媒体流
        [TChatCore UserPushControl:[TKDirectVideoModel shareInstance].userVideoId :YES];
    }else{
        TKLogInfo(@"用户进入房间失败");
        
        dispatch_async(dispatch_get_main_queue(), ^{
            
            if (![TKDirectVideoModel shareInstance].isShowAlert) {
                
                [TKDirectVideoModel shareInstance].isShowAlert = YES;

                
                if (self.delegate && [self.delegate respondsToSelector:@selector(alertSmartTwoVideoTip:describe:cancelBtnTitle:takeBtnTitle:)]) {
                    [self.delegate alertSmartTwoVideoTip:@"视频录制提示" describe:@"网络异常,请稍侯重试" cancelBtnTitle:nil takeBtnTitle:@"确定"];
                }
            }
            
        });
    }
}

// 房间在线用户消息
- (void) OnRoomOnlineUser: (int)userNum : (int)roomId
{
    TKLogInfo(@"房间人数:%d,房间号:%d",userNum,roomId);
    //记录开始房间人数日志
    NSString *logString=[NSString stringWithFormat:@"TKMSG100901:网络情况%@|当前房间人数%d|%@",[TKNetHelper getNetworkTypeInfo],userNum,[self getTimeStamp]];

    if (self.delegate && [self.delegate respondsToSelector:@selector(uploadSmartTwoVideoLog:)]) {
        [self.delegate uploadSmartTwoVideoLog:logString];
    }
    
    if (userNum >= 2) {
        
        NSMutableArray *onlineUser = [TChatCore GetRoomOnlineUser];//获取房间中在线用户（坐席）
        
        if (onlineUser && onlineUser.count > 0){
            
            for (int i = 0; i< onlineUser.count; i++) {
                
                if ([[onlineUser objectAtIndex:i] intValue] != [TKDirectVideoModel shareInstance].userVideoId) {
                    
                    [TKDirectVideoModel shareInstance].seatVideoId = [[onlineUser objectAtIndex:i] intValue];
                    
                    break;
                }
            }
            
            TKLogInfo(@"坐席=%d",[TKDirectVideoModel shareInstance].seatVideoId);
            //rtc通过视频流状态来开启视频
            
        }
        
    }else{
        
        dispatch_async(dispatch_get_main_queue(), ^{
            
            waitSeatTimer = [NSTimer scheduledTimerWithTimeInterval:1 target:self selector:@selector(handleWaitSeatTimeout:) userInfo:[NSDictionary dictionaryWithObjectsAndKeys:[NSNumber numberWithInt:roomId],@"roomId", nil] repeats:YES];
        });
    }
}

//有新用户（坐席）进入房间消息
- (void) OnUserEnterRoom: (int)userId
{
}


// 视频数据就绪
- (void) OnUserVideoDataReady: (int)userId : (int)dataInfo{
    dispatch_async(dispatch_get_main_queue(), ^{
        if (self.contentView.superview.hidden||[TKDirectVideoModel shareInstance].isDirectVideo){
            if (self.delegate && [self.delegate respondsToSelector:@selector(showSmartTwoVideoChatView)]) {
                [self.delegate showSmartTwoVideoChatView];
            }
        }
        
        if (userId==[TKDirectVideoModel shareInstance].userVideoId) {
            //自己视频画面调整占用屏幕大小，以便展示出底层的白色背景
            float   widht= (dataInfo & 0xFFFF0000) >> 16;
            float     height= (dataInfo & 0x0000FFFF);
            TKLogInfo(@"视频调整前宽度：%f|视频调整前高度：%f",widht,height);
            float videoWidth;
            float videoHeight;
            float aspectRatio=[UIScreen mainScreen].bounds.size.height/[UIScreen mainScreen].bounds.size.width;//高除以宽的比例
            float ratioRequirements=height/widht;//高除以宽的要求比例
            //全屏等比拉伸
            if (aspectRatio>ratioRequirements) {
                videoHeight=[UIScreen mainScreen].bounds.size.height;
                videoWidth=videoHeight/ratioRequirements;
            }else{
                videoWidth=[UIScreen mainScreen].bounds.size.width;
                videoHeight=videoWidth*ratioRequirements;
            }

            [localVideoView setFrameWidth:videoWidth];
            [localVideoView setFrameHeight:videoHeight];
            TKLogInfo(@"视频调整后宽度：%f|视频调整后高度：%f",videoWidth,videoHeight);
            localVideoView.center=CGPointMake(UISCREEN_WIDTH/2.0f, UISCREEN_HEIGHT/2.0f); ;

            
        }else{
//            if (self.delegate && [self.delegate respondsToSelector:@selector(showSmartTwoVideoChatView)]) {
//                [self.delegate showSmartTwoVideoChatView];
//            }
            TKLogInfo(@"坐席画面准备就绪");
        }
    });
    
}

// 用户退出房间消息
- (void) OnUserLeaveRoom: (int)userId
{
    NSString *l = [NSString stringWithFormat:@"[%@]离开房间",[TChatCore GetUserStateString:userId :TKCC_USERSTATE_NICKNAME]];
    
    TKLogInfo(@"%@",l);
    
    dispatch_async(dispatch_get_main_queue(), ^{
        
        NSString *witnessResult = nil;
        if([TKDirectVideoModel shareInstance].isDirectVideo){
            witnessResult = @"app:10004";
        }else{
            witnessResult = @"-7";//坐席连接异常
        }
        NSString *msg=[NSString stringWithFormat:@"%@视频连接异常，请重试！",[TKDirectVideoModel shareInstance].serviceTipString];
        [self showConnectErrorTip:witnessResult witnessInfo:[NSString stringWithFormat:@"%@离开房间连接异常",[TKDirectVideoModel shareInstance].serviceTipString] tipTitle:@"视频录制提示" tipDesc:msg];
        
    });
}

//推流状态
- (void) OnUserPushCtrl: (bool)open : (int)userId : (int)errorCode{
    
    TKLogInfo(@"思迪双向日志：OnUserPushCtrl open: %d, open: %d", open, errorCode);
    if(open){
        
        if (errorCode == 0) {

            if ([TKDirectVideoModel shareInstance].isStartingVideo == NO) {
                [TKDirectVideoModel shareInstance].isStartingVideo = YES;
                [TChatCore UserPullControl:[TKDirectVideoModel shareInstance].seatVideoId :YES];
            }
            return;

        }
        

        
        if (![TKDirectVideoModel shareInstance].isShowAlert&&![TKDirectVideoModel shareInstance].isTransBufferMsg) {
            
            [TKDirectVideoModel shareInstance].isShowAlert = YES;
            
            if([TKDirectVideoModel shareInstance].isDirectVideo){
                [TKDirectVideoModel shareInstance].witnessResult = @"app:10002";
            }else{
                [TKDirectVideoModel shareInstance].witnessResult = @"-6";//客户连接异常
            }
            [TKDirectVideoModel shareInstance].witnessInfo=[NSString stringWithFormat:@"推送视频失败(%i)", errorCode];
            
            if (self.delegate && [self.delegate respondsToSelector:@selector(alertSmartTwoVideoTip:describe:cancelBtnTitle:takeBtnTitle:)]) {
                [self.delegate alertSmartTwoVideoTip:@"视频录制提示" describe:[NSString stringWithFormat:@"推送视频失败(%i)", errorCode] cancelBtnTitle:nil takeBtnTitle:@"确定"];
            }

        }
    }
}

//拉流状态
- (void) OnUserPullCtrl: (bool)open : (int)errorCode{
    TKLogInfo(@"思迪双向日志：onPullStatus open: %d, open: %d", open, errorCode);
    
    if(open){
        
        if (errorCode == 0) {
            
            //开启视频画面在视频准备就绪里面处理，
            return;
        }
    
        
        if (![TKDirectVideoModel shareInstance].isShowAlert&&![TKDirectVideoModel shareInstance].isTransBufferMsg) {
            
            [TKDirectVideoModel shareInstance].isShowAlert = YES;
            
            if([TKDirectVideoModel shareInstance].isDirectVideo){
                [TKDirectVideoModel shareInstance].witnessResult = @"app:10002";
            }else{
                [TKDirectVideoModel shareInstance].witnessResult = @"-7";//坐席连接异常
            }
            [TKDirectVideoModel shareInstance].witnessInfo=[NSString stringWithFormat:@"拉取视频失败(%i)", errorCode];
            
            if (self.delegate && [self.delegate respondsToSelector:@selector(alertSmartTwoVideoTip:describe:cancelBtnTitle:takeBtnTitle:)]) {
                [self.delegate alertSmartTwoVideoTip:@"视频录制提示" describe:[NSString stringWithFormat:@"拉取视频失败(%i)", errorCode] cancelBtnTitle:nil takeBtnTitle:@"确定"];
            }

        }
    }
}


#pragma mark  TKCCReportStatsDelegate
// 网络码率
- (void) OnReportStatsCallBack: (int)sentkb : (int)recvkb
{

    dispatch_async(dispatch_get_main_queue(), ^{
        
        if (self.delegate && [self.delegate respondsToSelector:@selector(smartTwoVideoNetWorkUpDownTip:)]) {
            [self.delegate smartTwoVideoNetWorkUpDownTip:[NSString stringWithFormat:@"上行:%dKB/s\n下行:%dKB/s",sentkb,recvkb]];
        }
        
    });
    
}

#pragma mark   TKCCTextMsgDelegate
////////////////////文字信息协议
- (void) OnTextMessageCallBack: (int)fromUserId : (int)toUserId : (BOOL)secret : (NSString*)msgBuf{
    
    dispatch_async(dispatch_get_main_queue(), ^{
        
        if([msgBuf rangeOfString:@"USR:1000:"].location != NSNotFound){
            
            NSRange userRange = [msgBuf rangeOfString:@"USR:1000:"];
            NSString *msg = [msgBuf substringFromIndex:userRange.location+userRange.length];
            
            [self updateTipTCViewText:msg];
            
            return;
        }
        
        if([msgBuf rangeOfString:@"USR:1001:"].location !=NSNotFound){
            //风险协议阅读是否展示指令
            NSRange readAgreeRange=[msgBuf rangeOfString:@"USR:1001:"];
            NSString * readJsonString=[msgBuf substringFromIndex:(readAgreeRange.length)];
            NSMutableDictionary *readParm=(NSMutableDictionary *)[TKDataHelper jsonToDictionary:readJsonString];
            if (readParm&&[readParm isKindOfClass:[NSDictionary class]]) {
                readParm[@"mainColor"]=self.requestParams[@"mainColor"]?self.requestParams[@"mainColor"]:@"#0354C2";

                //兼容老的1001指令想展示底部时候
                if([[readParm getStringWithKey:@"showType"] isEqualToString:@"bottom"]){
                    readParm[@"instructionNo"]=@"1007";//坐席发过来的指令编号
                    if (self.delegate && [self.delegate respondsToSelector:@selector(showSmartTwoVideoBottomRead:)]) {
                        [self.delegate showSmartTwoVideoBottomRead:readParm];
                    }
                }else{
                    if (self.delegate && [self.delegate respondsToSelector:@selector(showSmartTwoVideoRead:)]) {
                        [self.delegate showSmartTwoVideoRead:readParm];
                    }
                }
            }else{

                if (self.delegate && [self.delegate respondsToSelector:@selector(showSmartTwoVideoToast:)]) {
                    [self.delegate showSmartTwoVideoToast:@"数据格式错误"];
                }
            }

            return;
        }
        
        if ([msgBuf rangeOfString:@"USR:1002:"].location !=NSNotFound) {
            //toast提示
            NSRange toastRange=[msgBuf rangeOfString:@"USR:1002:"];
            NSString *toastString=[msgBuf substringFromIndex:(toastRange.length)];

            if (self.delegate && [self.delegate respondsToSelector:@selector(showSmartTwoVideoToast:)]) {
                [self.delegate showSmartTwoVideoToast:toastString];
            }
            return;
        }
        
        if([msgBuf rangeOfString:@"USR:1003:"].location !=NSNotFound){
            //根据坐席指令展示带标题的对准框
            NSRange readAgreeRange=[msgBuf rangeOfString:@"USR:1003:"];
            NSString * readJsonString=[msgBuf substringFromIndex:(readAgreeRange.length)];
            NSMutableDictionary *readParm=(NSMutableDictionary *)[TKDataHelper jsonToDictionary:readJsonString];
            if (readParm&&[readParm isKindOfClass:[NSDictionary class]]) {
                readParm[@"mainColor"]=self.requestParams[@"mainColor"]?self.requestParams[@"mainColor"]:@"#0354C2";

                if (self.delegate && [self.delegate respondsToSelector:@selector(showSmartTwoVideoTitleBox:)]) {
                    [self.delegate showSmartTwoVideoTitleBox:readParm];
                }
            }else{
                if (self.delegate && [self.delegate respondsToSelector:@selector(showSmartTwoVideoToast:)]) {
                    [self.delegate showSmartTwoVideoToast:@"数据格式错误"];
                }
            }
            return;
        }
        
        if([msgBuf rangeOfString:@"USR:1004:"].location !=NSNotFound){
            //根据坐席指令展示进度条
            NSRange readAgreeRange=[msgBuf rangeOfString:@"USR:1004:"];
            NSString * readJsonString=[msgBuf substringFromIndex:(readAgreeRange.length)];
            NSMutableDictionary *readParm=(NSMutableDictionary *)[TKDataHelper jsonToDictionary:readJsonString];
            if (readParm&&[readParm isKindOfClass:[NSDictionary class]]) {
                readParm[@"mainColor"]=self.requestParams[@"mainColor"]?self.requestParams[@"mainColor"]:@"#0354C2";

                if (self.delegate && [self.delegate respondsToSelector:@selector(showSmartTwoVideoProcessNode:)]) {
                    [self.delegate showSmartTwoVideoProcessNode:readParm];
                }
            }else{
                if (self.delegate && [self.delegate respondsToSelector:@selector(showSmartTwoVideoToast:)]) {
                    [self.delegate showSmartTwoVideoToast:@"数据格式错误"];
                }
            }
            return;
        }
        
        if([msgBuf rangeOfString:@"USR:1005:"].location !=NSNotFound){
            //根据坐席指令展示可滚动文本
            NSRange readAgreeRange=[msgBuf rangeOfString:@"USR:1005:"];
            NSString * readJsonString=[msgBuf substringFromIndex:(readAgreeRange.length)];
            NSMutableDictionary *readParm=(NSMutableDictionary *)[TKDataHelper jsonToDictionary:readJsonString];
            if (readParm&&[readParm isKindOfClass:[NSDictionary class]]) {
                readParm[@"mainColor"]=self.requestParams[@"mainColor"]?self.requestParams[@"mainColor"]:@"#0354C2";

                if (self.delegate && [self.delegate respondsToSelector:@selector(showSmartTwoVideoRollText:)]) {
                    [self.delegate showSmartTwoVideoRollText:readParm];
                }
            }else{
                if (self.delegate && [self.delegate respondsToSelector:@selector(showSmartTwoVideoToast:)]) {
                    [self.delegate showSmartTwoVideoToast:@"数据格式错误"];
                }
            }
            return;
        }
        
        if ([msgBuf rangeOfString:@"USR:1006:"].location !=NSNotFound) {
            //显示确认弹窗
            NSRange readAgreeRange=[msgBuf rangeOfString:@"USR:1006:"];
            NSString * readJsonString=[msgBuf substringFromIndex:(readAgreeRange.length)];
            NSMutableDictionary *readParm=(NSMutableDictionary *)[TKDataHelper jsonToDictionary:readJsonString];
            if (readParm&&[readParm isKindOfClass:[NSDictionary class]]) {
                readParm[@"mainColor"]=self.requestParams[@"mainColor"]?self.requestParams[@"mainColor"]:@"#0354C2";
                readParm[@"instructionNo"]=@"1006";//坐席发过来的指令编号
                if (self.delegate && [self.delegate respondsToSelector:@selector(showSmartTwoVideoBottomRead:)]) {
                    [self.delegate showSmartTwoVideoBottomRead:readParm];
                }
            }else{

                if (self.delegate && [self.delegate respondsToSelector:@selector(showSmartTwoVideoToast:)]) {
                    [self.delegate showSmartTwoVideoToast:@"数据格式错误"];
                }
            }
            return;
        }
        
        if ([msgBuf rangeOfString:@"USR:1007:"].location !=NSNotFound) {
            //显示底部阅读协议弹窗
            NSRange readAgreeRange=[msgBuf rangeOfString:@"USR:1007:"];
            NSString * readJsonString=[msgBuf substringFromIndex:(readAgreeRange.length)];
            NSMutableDictionary *readParm=(NSMutableDictionary *)[TKDataHelper jsonToDictionary:readJsonString];
            if (readParm&&[readParm isKindOfClass:[NSDictionary class]]) {
                readParm[@"mainColor"]=self.requestParams[@"mainColor"]?self.requestParams[@"mainColor"]:@"#0354C2";
                readParm[@"instructionNo"]=@"1007";//坐席发过来的指令编号
                if (self.delegate && [self.delegate respondsToSelector:@selector(showSmartTwoVideoBottomRead:)]) {
                    [self.delegate showSmartTwoVideoBottomRead:readParm];
                }
            }else{

                if (self.delegate && [self.delegate respondsToSelector:@selector(showSmartTwoVideoToast:)]) {
                    [self.delegate showSmartTwoVideoToast:@"数据格式错误"];
                }
            }
            return;
        }
        
        
        NSRange userRange = [msgBuf rangeOfString:@"USR:0:"];
        if (userRange.length>0) {
             
            NSString *msg = [msgBuf substringFromIndex:userRange.location+userRange.length];
             
            [self updateTipTCViewText:[NSString stringWithFormat:@"%@  %@",[TKDirectVideoModel shareInstance].serviceTipString,msg]];

             
        }else{
            [self updateTipTCViewText:[NSString stringWithFormat:@"%@  %@",[TKDirectVideoModel shareInstance].serviceTipString,msgBuf]];

        }
    });
    
}

/**
 *  <AUTHOR> 2019年01月24日13:28:43
 *  更新提示语
 *  @return nil
 */
-(void)updateTipTCViewText:(NSString *)tip{

    if (self.delegate && [self.delegate respondsToSelector:@selector(showSmartTwoVideoSeatMessage:)]) {
        [self.delegate showSmartTwoVideoSeatMessage:tip];
    }
}

#pragma mark   TKCCTextMsgDelegate
- (void) OnTransBufferCallBack: (int)userId : (NSData*)buf{
    
    TKLogInfo(@"transBuffer callback");
    
    [TKDirectVideoModel shareInstance].isTransBufferMsg = YES;
    
    NSString *lpMsgBuf=  [[NSString alloc] initWithData:(buf) encoding:NSUTF8StringEncoding];
    
    TKLogInfo(@"来自%d的透明通道消息:%@", userId,lpMsgBuf);
    
    NSRange sysRange = [lpMsgBuf rangeOfString:@"SYS:"];
    
    [TKDirectVideoModel shareInstance].witnessResult = lpMsgBuf;
    
    // 埋点-单向_请求房间_结果
    NSMutableDictionary *eventDic = [NSMutableDictionary dictionaryWithDictionary:self.requestParams];
    eventDic[@"message"] = lpMsgBuf;
    TKPrivateEventResult result = TKPrivateEventResultNone;
    [TKStatisticEventHelper sendEvent:TKPrivateEventVideoWitness
                         subEventName:TKPrivateSubEventVideoWitnessReceiveCMD
                             progress:TKPrivateEventProgressNone
                               result:result
                          orientation:TKPrivateVideoOrientationPortrait
                      oneWayVideoType:TKPrivateOneWayVideoTypeTChatSmart
                 prepareVideoProgress:TKPrivatePrepareVideoProgressNone
                             eventDic:eventDic];
    
    if (sysRange.length > 0) {  //见证返回的透明信息
        
    }else{ //其它消息
        
    }
    
    dispatch_async(dispatch_get_main_queue(), ^{
        [self endTChatVideo];
    });
}


#pragma mark -等待坐席进入房间处理
- (void)handleWaitSeatTimeout:(NSTimer*)timer{
    NSMutableArray *onlineUser = [TChatCore GetRoomOnlineUser];//获取房间中在线用户（坐席）
    if (onlineUser.count < 2) {
        if ([TKDirectVideoModel shareInstance].tkCountDown > 0) {
            TKLogInfo(@"show Count Down = %d", [TKDirectVideoModel shareInstance].tkCountDown);
            if (self.delegate && [self.delegate respondsToSelector:@selector(changeSmartTwoVideoTipText:queueLocation:currentStatus:)]) {
                [TKDirectVideoModel shareInstance].queueSubMsg=@"";
                
                //提示语h5入参调整位置提示语优先级最高
                //针对h5参数类型容错处理
                NSString *queueWaitStaffSubMsg;
                if(self.requestParams[@"videoTipMsg"]&&[self.requestParams[@"videoTipMsg"] isKindOfClass:[NSDictionary class]]){
                    queueWaitStaffSubMsg=self.requestParams[@"videoTipMsg"][@"queueWaitStaffSubMsg"];
                }else{
                    queueWaitStaffSubMsg=[TKDataHelper jsonToDictionary:self.requestParams[@"videoTipMsg"]][@"queueWaitStaffSubMsg"];
                }
                if([TKStringHelper isNotEmpty:queueWaitStaffSubMsg]){
                    [TKDirectVideoModel shareInstance].queueSubMsg=queueWaitStaffSubMsg;
                }

                //提示语h5入参调整位置提示语优先级最高
                //针对h5参数类型容错处理
                NSString *queueWaitStaffMsg;
                if(self.requestParams[@"videoTipMsg"]&&[self.requestParams[@"videoTipMsg"] isKindOfClass:[NSDictionary class]]){
                    queueWaitStaffMsg=self.requestParams[@"videoTipMsg"][@"queueWaitStaffMsg"];
                }else{
                    queueWaitStaffMsg=[TKDataHelper jsonToDictionary:self.requestParams[@"videoTipMsg"]][@"queueWaitStaffMsg"];
                }
                
                if([TKStringHelper isNotEmpty:queueWaitStaffMsg]){
                    [TKDirectVideoModel shareInstance].queueBigMsg=[NSString stringWithFormat:@"%@（%d秒）",queueWaitStaffMsg,[TKDirectVideoModel shareInstance].tkCountDown];
                }else{
                    [TKDirectVideoModel shareInstance].queueBigMsg=[NSString stringWithFormat:@"正在接入%@…（%d秒）",[TKDirectVideoModel shareInstance].serviceTipString,[TKDirectVideoModel shareInstance].tkCountDown];
                }
                [self.delegate changeSmartTwoVideoTipText:[TKDirectVideoModel shareInstance].queueBigMsg  queueLocation:0 currentStatus:TKOpenQueueStatusGetService];
            }
            [TKDirectVideoModel shareInstance].tkCountDown--;
        }else{
            NSString *msg = [NSString stringWithFormat:@"未匹配到%@,请稍侯重试",[TKDirectVideoModel shareInstance].serviceTipString];
            
            if (timer && timer.userInfo) {
                msg =  [NSString stringWithFormat:@"未匹配到%@,请稍侯重试[房间号：%ld]",[TKDirectVideoModel shareInstance].serviceTipString, (long)[timer.userInfo[@"roomId"] integerValue]];
            }
            
            if([TKDirectVideoModel shareInstance].isDirectVideo){
                [TKDirectVideoModel shareInstance].witnessResult = @"app:10003";
            }else{
                [TKDirectVideoModel shareInstance].witnessResult = @"-7";//坐席连接异常
            }
            [TKDirectVideoModel shareInstance].witnessInfo=[NSString stringWithFormat:@"%@超时未进入房间",[TKDirectVideoModel shareInstance].serviceTipString];

            if (self.delegate && [self.delegate respondsToSelector:@selector(alertSmartTwoVideoTip:describe:cancelBtnTitle:takeBtnTitle:)]) {
                [self.delegate alertSmartTwoVideoTip:@"视频录制提示" describe:msg cancelBtnTitle:nil takeBtnTitle:@"确定"];
            }
        }
    }else{
        if (waitSeatTimer) {
            [waitSeatTimer invalidate];
            waitSeatTimer = nil;
        }
    }

}


#pragma mark -启动TChat视频
- (void)startTChatVideo{
    if (waitSeatTimer) {
        
        [waitSeatTimer invalidate];
        
        waitSeatTimer = nil;
    }
    
    //如果需要展示坐席信息
    NSString *serviceInfo=[TKDirectVideoModel shareInstance].staffTips;
    
    if([TKDirectVideoModel shareInstance].isDirectVideo){
        //如果需要展示坐席信息
        if (self.requestParams[@"showStaffInfo"]) {
            serviceInfo=self.requestParams[@"showStaffInfo"];
        }
    }

    
    if (self.delegate && [self.delegate respondsToSelector:@selector(showSmartTwoVideoServiceInfo:)]) {
        [self.delegate showSmartTwoVideoServiceInfo:serviceInfo];
    }
    
    
    //开始见证计时
    
    if (self.delegate && [self.delegate respondsToSelector:@selector(startSmartTwoVidoeTime)]) {
        [self.delegate startSmartTwoVidoeTime];
    }
    
    //显示视频画面
    if (self.delegate && [self.delegate respondsToSelector:@selector(showSmartTwoVideoChatView)]) {
        [self.delegate showSmartTwoVideoChatView];
    }
}

#pragma mark -结束视频
- (void)endTChatVideo{

    TKLogInfo(@"end TChatRtc video.");
    
    [UIApplication sharedApplication].idleTimerDisabled = NO;
    
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(200 * NSEC_PER_MSEC)), dispatch_get_global_queue(0, 0), ^{
        
       
        
        if ([TKDirectVideoModel shareInstance].witRoomId) {
            
            if(![TKDirectVideoModel shareInstance].isTransBufferMsg)
            {
                [TChatCore TransBuffer:[TKDirectVideoModel shareInstance].seatVideoId :[@"SYS:10002" dataUsingEncoding:NSUTF8StringEncoding]];
            }

            [TChatCore Release];
        }
    });
    

    if (self.delegate && [self.delegate respondsToSelector:@selector(endSmartTwoVidoe)]) {
        [self.delegate endSmartTwoVidoe];
    }

}
@end
