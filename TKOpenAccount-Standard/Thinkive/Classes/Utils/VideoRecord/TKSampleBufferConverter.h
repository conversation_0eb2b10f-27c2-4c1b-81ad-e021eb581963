//
//  TKSampleBufferConverter.h
//  TKOpenAccount-Standard
//
//  Created by <PERSON> on 2023/2/15.
//  Copyright © 2023 thinkive. All rights reserved.
//

#import <Foundation/Foundation.h>
#import <CoreMedia/CoreMedia.h>

NS_ASSUME_NONNULL_BEGIN


@interface TKSampleBufferConverter : NSObject


// 转换采样数据
- (CMSampleBufferRef)convertSamplesFromSampleBuffer:(CMSampleBufferRef)sampleBuffer toSampleRate:(double)sampleRate;

/// 创建空白数据
/// - Parameter sampleBuffer: 原始buffer
- (CMSampleBufferRef)createEmptySampleBufferWithSampleBuffer:(CMSampleBufferRef)sampleBuffer toSampleRate:(double)sampleRate;

/// 转换Pcm数据成CMSampleBufferRef
/// - Parameters:
///   - buffer: 原始数据
///   - len: 长度
///   - lastSampleTime: 原始数据最后的采样时间
- (CMSampleBufferRef)convertPCMStrmToCMSampleBufferRefWithBuffer:(char *)buffer len:(int)len lastSampleTime:(CMTime)lastSampleTime toSampleRate:(double)sampleRate;

/// 合适时机释放所有语音识别转换的数据源
- (void)releaseAllBufferPointers;

@end


NS_ASSUME_NONNULL_END
