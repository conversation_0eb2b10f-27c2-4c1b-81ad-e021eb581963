//
//  TKYKHEmbedHelper.h
//  TKOpenAccount-Standard
//
//  Created by <PERSON>lover on 15/12/23.
//  Copyright © 2015年 thinkive. All rights reserved.
//

#import <Foundation/Foundation.h>

typedef void (^AuthenFinishBlock) (id authenResult);

@interface TKYKHEmbedHelper : NSObject

/**
 *
 *  @method startTKYKHSDKWithParam:completion:
 *  @brief  云平台渠道校验
 *  @param mParam 校验所需参数（channel_url:校验地址，channel_key:校验key值，sec_name_en:券商英文名称）
 *  @param hController 启动控制器对象
 *  @param authenBlock 校验结果回调方法
 *
 *  @return
 */

+ (void)startTKYKHSDKWithParam:(id)mParam handleController:(id)hController completion:(AuthenFinishBlock)authenBlock;

@end
