//
//  TKChatTokenHelper.m
//  TKOpenAccount-Standard
//
//  Created by <PERSON>ie on 2022/11/4.
//  Copyright © 2022 thinkive. All rights reserved.
//

#import "TKChatTokenHelper.h"
#import "TKOpenAccountService.h"
@interface TKChatTokenHelper()
@property (nonatomic, assign) int requestTimeOut;//请求总共时间耗费
@property (nonatomic, strong) NSTimer *requestTimeOutTimer;//请求总计超时定时器
@property (nonatomic, assign) int requestNumber;//请求次数
@property (nonatomic, strong) TKOpenAccountService *mService;
@property (nonatomic, strong) NSMutableDictionary *requestParam;//请求参数
@property (nonatomic,copy)   CallBackFunc backCall;//记录block
@end

@implementation TKChatTokenHelper

/// @param param 入参
-(instancetype)initWithParam:(NSMutableDictionary *)param{
    self=[super init];
    if (self) {
        self.requestNumber=3;
        if ([TKStringHelper isNotEmpty:param[@"requestNumber"]]) {
            self.requestNumber=3;
            [param removeObjectForKey:@"requestNumber"];
        }
        
        self.requestTimeOut=15;
        if ([TKStringHelper isNotEmpty:param[@"requestTimeOutTimer"]]) {
            self.requestTimeOut=[param[@"requestTimeOutTimer"] intValue];
            [param removeObjectForKey:@"requestTimeOutTimer"];
        }
        if ([TKStringHelper isEmpty:param[@"funcNo"]]) {
            param[@"funcNo"]=@"********";
        }
        self.requestParam=param;
        self.mService=[[TKOpenAccountService alloc] init];
    }
    return self;
}

/**
 *  <AUTHOR> 2015-04-21 17:12:59
 *  @param callBackFunc 回调函数
 */
-(void)getTChatToken:(CallBackFunc)callBackFunc{
    __weak typeof(self) weakSelf = self;
    if (_requestTimeOutTimer==nil) {
        self.requestTimeOutTimer=[NSTimer scheduledTimerWithTimeInterval:self.requestTimeOut target:self selector:@selector(timeOutEnd) userInfo:nil repeats:NO];
    }

    if (!_backCall) {
        _backCall=callBackFunc;
    }
    
    [self.mService handleNetworkWithURL:self.requestParam[@"url"] param:self.requestParam callBackFunc:^(ResultVo *resultVo) {
        self.requestNumber--;
        if (resultVo.errorNo==0) {
            if (self.requestTimeOutTimer !=nil)
            {
                [self.requestTimeOutTimer invalidate];
                self.requestTimeOutTimer = nil;
            }
            _backCall(resultVo);
        }else{
            //请求失败可以继续请求到超过次数为止
            if (self.requestNumber<=0) {
                if (self.requestTimeOutTimer !=nil)
                {
                    [self.requestTimeOutTimer invalidate];
                    self.requestTimeOutTimer = nil;
                }
                _backCall(resultVo);
            }else{
                dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                    [weakSelf getTChatToken:_backCall];
                });
                
            }
        }
    }];
}

-(void)timeOutEnd{
    [self.mService cancelAllRequest];
    ResultVo *resultVo=[[ResultVo alloc] init];
    resultVo.errorNo=-100002;
    resultVo.errorInfo=@"请求token超时";
    _backCall(resultVo);
}
@end
