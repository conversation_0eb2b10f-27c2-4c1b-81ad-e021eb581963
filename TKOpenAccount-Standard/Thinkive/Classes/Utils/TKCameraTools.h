//
//  TKCameraTools.h
//  TKApp
//
//  Created by 叶璐 on 15/7/7.
//  Copyright (c) 2015年 liubao. All rights reserved.
//

#import <Foundation/Foundation.h>
#import <AVFoundation/AVFoundation.h>

@protocol TKCameraToolsDelegate <NSObject>

@optional
/**
 *  <AUTHOR> 2015-12-24 00:12:37
 *
 *  功能：当前人脸数量代理(调用startFaceDetectInDuration才会有回调)
 *  (触发时间决定于startFaceDetectInDuration中指定的时间)
 *  @param count：人脸数量
 *
 *  @return 
 */
- (void)currentFaceNumber:(NSInteger)count;

@end

typedef void (^TKCameraToolsImageBlock)(UIImage *image);

@interface TKCameraTools : NSObject<AVCaptureVideoDataOutputSampleBufferDelegate, AVCaptureAudioDataOutputSampleBufferDelegate>

@property (nonatomic, assign)id<TKCameraToolsDelegate> delegate;

/**
 *  <AUTHOR> 2015-12-24 00:12:37
 *
 *  功能： 初始化摄像头
 *
 *  @param superView：显示的试图
 *  @param frame：视频显示的位置
 *
 *  @return YES,NO
 */
-(void)setupCameraInView:(UIView *)previewView andFrame:(CGRect)frame;

/**
 *  <AUTHOR> 2015-12-24 00:12:37
 *
 *  功能：销毁摄像头
 *
 *  @param
 *  @param
 *
 *  @return
 */
- (void)teardownCamera;

/**
 *  <AUTHOR> 2015-12-24 00:12:37
 *
 *  功能：开始/停止人脸识别
 *  @param        interval  :人脸识别间隔
 *  @return
 */
- (void)startFaceDetectInDuration:(NSTimeInterval) interval;
- (void)stopFaceDetect;

/**
 *  <AUTHOR> 2015-12-24 00:12:37
 *
 *  功能：抓拍照片
 *
 *  @param
 *  @param
 *
 *  @return
 */
- (void)takePhotos:(TKCameraToolsImageBlock) image;

/**
 *  <AUTHOR> 2015-12-24 00:12:37
 *
 *  功能： 获取带人脸的照片
 *
 *  @param
 *  @param
 *
 *  @return
 */
- (void)getFaceImage:(TKCameraToolsImageBlock)imageCallBack;

/**
 *  <AUTHOR> 2015-12-24 00:12:37
 *
 *  功能：录像
 *
 *  @param path：录像存储路径(全路径)
 *
 *  @return
 */
- (void)startRecordInFilePath:(NSString *)path;

- (void)reRecord;

- (void)stopRecord;

- (UIImage*)fetchFacePhoto;

@end
