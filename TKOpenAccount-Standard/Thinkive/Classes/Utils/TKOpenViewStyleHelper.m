//
//  TKOpenViewStyleHelper.m
//  TKOpenAccount-Standard
//
//  Created by Vie on 2022/10/8.
//  Copyright © 2022 thinkive. All rights reserved.
//

#import "TKOpenViewStyleHelper.h"

@implementation TKOpenViewStyleHelper
+(TKOpenViewStyleHelper *)shareInstance
{
    static dispatch_once_t once;
    static id instance;
    dispatch_once(&once, ^{
        instance = [[self alloc]init];
    });
    return instance;
}

-(BOOL)isElder{
    //_elder主题包含要适老化
    NSRange tipRange=[[TKThemeManager shareInstance].theme rangeOfString:@"_elder"];
    if (tipRange.length>0) {
        _isElder=true;
    }else{
       _isElder=false;
    }
    return _isElder;
}
@end
