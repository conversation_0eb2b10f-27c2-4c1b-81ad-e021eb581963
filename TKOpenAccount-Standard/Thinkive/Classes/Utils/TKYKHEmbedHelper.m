//
//  TKYKHEmbedHelper.m
//  TKOpenAccount-Standard
//
//  Created by <PERSON><PERSON> on 15/12/23.
//  Copyright © 2015年 thinkive. All rights reserved.
//

#import "TKYKHEmbedHelper.h"
#import "TKOpenController.h"
#import "MNavViewController.h"

@implementation TKYKHEmbedHelper

+ (void)startTKYKHSDKWithParam:(id)mParam handleController:(id)hController completion:(AuthenFinishBlock)authenBlock
{
    if (mParam && [mParam isKindOfClass:[NSDictionary class]] && hController && [hController isKindOfClass:[UIViewController class]]) {
        
        dispatch_async(dispatch_get_main_queue(), ^{
            
            UIViewController *vController = (UIViewController*)hController;
            
            if (vController.navigationController) {
                
                if (!vController.navigationController.navigationBarHidden) {
                    
                    [vController.navigationController setNavigationBarHidden:YES animated:NO];
                    
                    [vController.navigationItem setHidesBackButton:YES animated:NO];
                    
                    mParam[@"navigationBarHidden"]=@"1";
                }
            }
            
            TKOpenController *mCtl = [[TKOpenController alloc] initWithParams:mParam];
            
            id<TKOpenDelegate> oDelegate = hController ;
            
            mCtl.oDelegate = oDelegate;
            
            mCtl.statusBarBgColor = mParam[@"statusBarBgColorStr"]? [TKUIHelper colorWithHexString:mParam[@"statusBarBgColorStr"]]:[UIColor whiteColor];
            
            mCtl.statusBarStyle = mParam[@"statusBarStyle"]? [mParam[@"statusBarStyle"] integerValue]:  TKUIStatusBarStyleDefault;
            
            if (vController.navigationController) {
                
                [vController.navigationController pushViewController:mCtl animated:YES];
                
            }else{
                
                MNavViewController *navCtl = [[MNavViewController alloc] initWithRootViewController:mCtl];
                
                [navCtl setNavigationBarHidden:YES];
                
                [vController presentViewController:navCtl animated:YES completion:nil];
                
            }
        });
        
    }else{
    
        if (authenBlock) {
            
            authenBlock(@"入参异常");
        }
    }
}

#pragma mark -组装请求参数
+ (id)wraperReqParams:(id)wParams{
    //设备型号
    NSString *devicePlatform = [TKDeviceHelper getDevicePlatform];
    //操作系统版本
    NSString *deviceSysVersion = [TKDeviceHelper getDeviceSysVersion];
    //设备分辨率
    NSString *deviceResoluation = [TKDeviceHelper getDeviceResoluationDescription];
    //    //是否越狱
    //    NSString *isDeviceJailBreak = [NSString stringWithFormat:@"%d",[TKDeviceHelper isDeviceJailBreak]];
    //获取软件展示版本号
    NSString *softVersion = [TKSystemHelper getVersion];
    //获取软件下载的内部版本序号
    NSString *softVersionSn = [TKSystemHelper getVersionSn];
    //软件名称
    NSString *softName = [TKSystemHelper getAppName];
    //获取IP
    NSString *ip = [TKNetHelper getIP];
    
    NSString *uuid = [TKDeviceHelper getDeviceUUID];
    
    //手机型号
    NSString *deviceType = [TKDeviceHelper getDevicePlatformInfo];
    
    NSMutableDictionary *rParams = [NSMutableDictionary dictionaryWithDictionary:wParams];
    
    rParams[@"user_ip"]=ip;
    
    rParams[@"device_id"]=uuid;
    
    rParams[@"device_platform"]=devicePlatform;
    
    rParams[@"device_resoluation"]=deviceResoluation;
    
    rParams[@"soft_version_sn"]=softVersionSn;
    
    rParams[@"soft_version"]=softVersion;
    
    rParams[@"soft_name"]=softName;
    
    rParams[@"sys_version"]=deviceSysVersion;
    
    rParams[@"mobile_type"]=deviceType;
    
    NSString *reqParam = nil;
    
    NSEnumerator * enumeratorKey = [rParams keyEnumerator];
    
    for (NSString *object in enumeratorKey) {
        
        if ([object isEqualToString:@"channel_url"]) {
            
            continue;
        }
        
        if (reqParam == nil) {
            
            reqParam = [NSString stringWithFormat:@"%@=%@", object, [self encodeToPercentEscapeString:[self encodeToPercentEscapeString:rParams[object]]]];
            
        }else{
            
            reqParam = [NSString stringWithFormat:@"%@&%@=%@", reqParam,object, [self encodeToPercentEscapeString:[self encodeToPercentEscapeString:rParams[object]]]];
        }
        
    }
    
    return reqParam;
    
}

#pragma mark - 对字符串做网络字符串编码
+ (NSString *)encodeToPercentEscapeString:(NSString *) input
{
    NSString *outputStr = (NSString *)
    CFBridgingRelease(CFURLCreateStringByAddingPercentEscapes(
                      kCFAllocatorDefault,
                      (CFStringRef)input,
                      NULL,
                      CFSTR(":/?#[]@!$ &'()*+,;=\"<>%{}|\\^~`"),
                      CFStringConvertNSStringEncodingToEncoding(NSUTF8StringEncoding)));
    
    return outputStr;
    
}


@end
