//
//  TKSmartQuestionModel.h
//  TKOpenAccount-Standard
//
//  Created by <PERSON> on 2021/11/1.
//  Copyright © 2021 thinkive. All rights reserved.
//

@interface TKSmartErrorPageTipModel : NSObject
// Base
@property (nonatomic, readwrite, strong) NSString *errorTitle;    //标题
@property (nonatomic, readwrite, strong) NSString *errorMsg;    //内容


/// 构造方法
/// @param dic 字典
- (instancetype)initWithDic:(NSDictionary *)dic;
@end

typedef enum {
    TKOneWayProcessIdle = 0, // 闲置
    TKOneWayProcessPlayPrompt = 1, // 开始录制前播放提示音
    TKOneWayProcessStartRecord, // 开始录制
    TKOneWayProcessPlayQuestion, // 播放问题
    TKOneWayProcessRecordDone,  // 录制完毕
    TKOneWayProcessOneWayDone,  // 单向完毕
    TKOneWayProcessRecordError, //  录制报错
    TKOneWayProcessRecordCancel, //  录制取消
    TKOneWayProcessRecordDonePrompt, //  录制结束提示语音。不会录制到视频中
}TKOneWayProcess;

typedef enum {
    TKPlayQuessionProcessIdle = 0, // 闲置
    TKPlayQuessionProcessStart = 1,  // 开始播放语音
    TKPlayQuessionProcessPrompt,  // ”滴“提示音
    TKPlayQuessionProcessAsrStart,  // 开始语音识别
    TKPlayQuessionProcessAsrEnd,  // 开始语音识别
    TKPlayQuessionProcessRead,  // 朗读/阅读
    TKPlayQuessionProcessDone,  // 播放流程结束
}TKPlayQuessionProcess;

typedef enum : NSUInteger {
    TKAsrResultNo,          // 没有识别
    TKAsrResultNoIdentify,  // 无结果
    TKAsrResultSuccess,     // 识别通过
    TKAsrResultFail,        // 识别失败
} TKAsrResult;

typedef enum : NSInteger {
    TKRecordTypeUnknown=-1, // 未知
    TKRecordTypeTTSAndAsr=0,  // 智能问答
    TKRecordTypeReadAloud,  // 朗读
    TKRecordTypeRead,        // 阅读
} TKRecordType;



@interface TKSmartQuestionModel : NSObject

// Base
@property (nonatomic, readwrite, strong) NSString *fileSource;    // 文件来源，1:服务器视频文件，2:视频流
@property (nonatomic, readwrite, strong) NSString *fileName;    //文件来源是服务器视频文件时，传服务器的文件名称
@property (nonatomic, readwrite, strong) NSString *tipContent;    // 语音播报内容和展示文案
@property (nonatomic, readwrite, strong) NSArray *tipContentList;    // 语音播报内容和展示文案数字
@property (nonatomic, readwrite, strong) NSString *tipTitleStr;    // 优先展示的文案(纯文本)，存在多音字的发音提示
@property (nonatomic, readwrite, strong) NSString *tipTitleHtml;    //阅读话术的html文本
@property (nonatomic, readwrite, strong) NSString *fileFlag;    // 播放本地音频文件

// 带问题的
//@property (nonatomic, readwrite, strong) NSString *prompt;    // 回答提示
@property (nonatomic, readwrite, strong) NSString *promptStr;    // 回答提示
@property (nonatomic, readwrite, strong) NSString *promptHtml;    // 回答提示
@property (nonatomic, readwrite, strong) NSString *failans;    //错误回答正则
@property (nonatomic, readwrite, strong) NSString *standardans;    //正确回答正则
@property (nonatomic, readwrite, strong) NSString *waitTime;    //语音识别时长
@property (nonatomic, readwrite, assign) int pauseTime;    // 播报完停顿时间（秒）
@property (nonatomic, readwrite, strong) NSString *longPauseTime;    // 是否长期停顿状态，1：是 2|空：否
@property (nonatomic, readwrite, strong) NSString *longPauseBtn;    // 长期停顿显示按钮文案，默认继续播报
@property (nonatomic, readwrite, strong) NSString *noAnswerPromptTime;    // 无回答提示时间，必须比waitTime小
@property (nonatomic, readwrite, assign) TKRecordType type;    //录制模式。// 0-一问一答；1-朗读 2-阅读
@property (nonatomic, readwrite, strong) NSString *readPromptBtnTitle;    // 阅读/朗读提示按钮标题（默认"开始朗读"｜"开始阅读"）
@property (nonatomic, readwrite, strong) NSString *readContent;    // 阅读/朗读内容
@property (nonatomic, readwrite, strong) NSString *readConfirmBtnTitle;    // 阅读/朗读确认按钮标题（默认"我已朗读"｜"我已阅读"）
@property (nonatomic, readwrite, strong) NSString *readTitle;    // 阅读/朗读标题（默认"请使用普通话朗读"｜"请完整阅读"）
@property (nonatomic, readwrite, strong) NSString *readTime;    // 最少朗读/阅读实际，单位秒，默认5秒
@property (nonatomic, readwrite, strong) NSString *tipSpeed;    // 语音播报速度，相当于全局aliSpeedLevel｜iflySpeedLevel
@property (nonatomic, readwrite, strong) NSString *wordSpeed;    // 话术每个字滚动时间，相当于全局questionOneWordSpeed
@property (nonatomic, readwrite, strong) NSString *voiceUrl;    // 语音访问地址
@property (nonatomic, readwrite, assign) BOOL isNeedVoiceToken;    // 访问远程文件是否需要token
@property (nonatomic, readwrite, strong) NSString *errorReason;    // 错误页标题


@property (nonatomic, readwrite, strong) TKSmartQuestionModel *errorTipModel;    //错误回答语音模型
@property (nonatomic, readwrite, strong) TKSmartQuestionModel *noVoiceTipModel;    //无关回答语音模型
@property (nonatomic, readwrite, strong) TKSmartQuestionModel *standardansTipModel;    //正确回答语音模型

// 扩展字段
@property (nonatomic, readonly, strong) NSString *displayTip;   // 有tipTitleHtml展示tipTitleHtml，否则为tipTitleStr,若也无展示tipContent
@property (nonatomic, readonly, strong) NSString *displayPrompt;   // 有promptHtml展示promptHtml，否则为promptStr
@property (nonatomic, readwrite, assign) TKAsrResult asrResult;     // Asr识别结果
@property (nonatomic, readonly, strong) TKSmartQuestionModel *asrResultModel;     // errorTipModel、noVoiceTipModel、standardansTipModel中的一个或者nil

/// 构造方法
/// @param dic 字典
- (instancetype)initWithDic:(NSDictionary *)dic;

/// 转换字典数组->模型数组
/// @param dicArr 字典数组
+ (NSMutableArray *)convertDicArrToModels:(NSArray *)dicArr;

/// 是否需要语音识别
- (BOOL)isNeedAsr;

/// 是否有tipTitleHtml作为html文本
- (BOOL)isHtmlTip;

/// 是否有promptHtml作为html文本
- (BOOL)isHtmlPrompt;

/// 是否需要播放语音识别后的提示
- (BOOL)isNeedPlayAsrResultPrompt;

/// 是否需要暂停
- (BOOL)isNeedPause;

/// 是否需要延迟，延迟时间为self.pauseTime
- (BOOL)isNeedDelay;

@end


