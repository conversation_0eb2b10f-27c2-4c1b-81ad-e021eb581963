//
//  TKOpenAccountService.h
//  TKApp
//
//  Created by 叶璐 on 15/4/21.
//  Copyright (c) 2015年 liubao. All rights reserved.
//

#import <Foundation/Foundation.h>

/**
 *  <AUTHOR> 2015-04-21 17:12:02
 *
 *  开户服务类
 */
@interface TKOpenAccountService : TKCommonService

/**
 *  <AUTHOR> 2015-04-21 17:12:59
 *
 *  上传照片
 *  @param url           url
 *  @param param        上传参数
 *  @param callBackFunc 回调函数
 */
-(void)UploadFileWithURL:(NSString *)url param:(NSMutableDictionary *)reqParam callBackFunc:(CallBackFunc)callBackFunc;

-(void)uploadFileWithURL:(NSString *)url delegate:(id<TKUploadDelegate>)mDelegate param:(NSMutableDictionary *)reqParam callBackFunc:(CallBackFunc)callBackFunc;


/**
 *  <AUTHOR> 2015-04-21 17:12:59
 *
 *  人脸识别
 *
 *  @param url          url
 *  @param param        上传参数
 *  @param callBackFunc 回调函数
 */
-(void)FaceDetectWithURL:(NSString *)url andParam:(NSMutableDictionary *)reqParam callBackFunc:(CallBackFunc)callBackFunc;

-(void)faceDetectWithURL:(NSString *)url andParam:(NSMutableDictionary *)reqParam callBackFunc:(CallBackFunc)callBackFunc;

-(void)uploadFileWithURL:(NSString *)url param:(NSMutableDictionary *)reqParam callBackFunc:(CallBackFunc)callBackFunc;

-(void)handleNetworkWithURL:(NSString *)url param:(NSMutableDictionary *)reqParam callBackFunc:(CallBackFunc)callBackFunc;

/// 发送Raw方式请求
/// @param url url
/// @param headerFieldDic 请求头
/// @param reqParam 请求参数
/// @param callBackFunc 回调
-(void)handleRawNetworkWithURL:(NSString *)url headerFieldDic:(NSDictionary *)headerFieldDic param:(NSMutableDictionary *)reqParam callBackFunc:(CallBackFunc)callBackFunc;

/// 请求房间号信息
/// @param url url
/// @param reqParam 请求参数
/// @param callBackFunc 回调
-(void)requestServerRoomWithURL:(NSString *)url param:(NSMutableDictionary *)reqParam callBackFunc:(CallBackFunc)callBackFunc;


/// 统计事件请求
/// @param url 请求地址
/// @param reqParam 请求参数
/// @param callBackFunc 请求回调
- (void)handleStatisticEventWithURL:(NSString *)url param:(NSMutableDictionary *)reqParam callBackFunc:(CallBackFunc)callBackFunc;

/// 录制失败请求
/// @param url 请求地址
/// @param reqParam 请求参数
/// @param callBackFunc 请求回调
- (void)recordFailureReportWithURL:(NSString *)url param:(NSMutableDictionary *)reqParam callBackFunc:(CallBackFunc)callBackFunc;

/// 获取音（视）频token
/// @param url 音（视）频地址
/// @param reqParam 请求参数
/// @param callBackFunc 回调
- (void)getVideoTokenWithURL:(NSString *)url param:(NSMutableDictionary *)reqParam callBackFunc:(CallBackFunc)callBackFunc;

/**
 *  <AUTHOR> 2015-04-21 17:12:59
 *
 *  ios调用Js
 *
 *  @param reqParam     传入参数
 *  @param callBackFunc 回调函数
 */
-(void)iosCallJsWithDic:(NSMutableDictionary *)reqParam callBackFunc:(CallBackFunc)callBackFunc;



- (void)handleHttpWithReq:(ReqParamVo *)reqParam callBackFunc:(CallBackFunc)callBackFunc;

- (void)iosCallJsWithReq:(ReqParamVo *)reqParam callBackFunc:(CallBackFunc)callBackFunc;

/// 过滤请求字典
/// @param originParam 原始大字典
+ (NSMutableDictionary *)filterRequestParam:(NSDictionary *)originParam;


@end
