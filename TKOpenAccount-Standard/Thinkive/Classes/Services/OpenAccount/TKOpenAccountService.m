//
//  TKOpenAccountService.m
//  TKApp
//
//  Created by 叶璐 on 15/4/21.
//  Copyright (c) 2015年 liubao. All rights reserved.
//

#import "TKOpenAccountService.h"

@implementation TKOpenAccountService

/**
 *  <AUTHOR> 2015-04-21 17:12:59
 *
 *  上传照片
 *  @param url           url
 *  @param param        上传参数
 *  @param callBackFunc 回调函数
 */
-(void)UploadFileWithURL:(NSString *)url param:(NSMutableDictionary *)reqParam callBackFunc:(CallBackFunc)callBackFunc
{
    ReqParamVo *reqParamVo = [self createReqParamVo];
    reqParamVo.url = url;
    reqParamVo.protocol = TKDaoType_Http;
    id extParams = reqParam[@"extParams"];
//    if (extParams && [extParams isKindOfClass:[NSDictionary class]]) {
//        [reqParam addEntriesFromDictionary:extParams];
//    }
    if (reqParam[@"serverAccessType"] && [reqParam[@"serverAccessType"] integerValue] == 1) {
        reqParamVo.protocol  = TKDaoType_Socket;
        if (extParams && [extParams isKindOfClass:[NSDictionary class]]) {
            if (extParams[@"url"]) {
                reqParamVo.url = extParams[@"url"];
            }
            reqParamVo.companyId = extParams[@"companyId"];
            reqParamVo.systemId  = extParams[@"systemId"];
        }
        reqParamVo.dataType  = TKDataType_Compress_Encryt;
    }
    reqParamVo.isShowWait = YES;
    reqParamVo.isUpload = YES;
    NSMutableDictionary *tempReqParam = [reqParam mutableCopy];
//    if (extParams) {
//        [tempReqParam removeObjectForKey:@"extParams"];
//    }
    reqParamVo.reqParam = tempReqParam;
    [self invoke:reqParamVo callBackFunc:callBackFunc];
}

-(void)uploadFileWithURL:(NSString *)url param:(NSMutableDictionary *)reqParam callBackFunc:(CallBackFunc)callBackFunc
{
    NSMutableDictionary *newParam = [NSMutableDictionary dictionaryWithDictionary:reqParam];
    
    ReqParamVo *reqParamVo = [self createReqParamVo];
    reqParamVo.url = url;
    reqParamVo.protocol = TKDaoType_Http;
    id extParams = newParam[@"extParams"];
//    if (extParams && [extParams isKindOfClass:[NSDictionary class]]) {
//        [newParam addEntriesFromDictionary:extParams];
//    }
    if (newParam[@"serverAccessType"] && [newParam[@"serverAccessType"] integerValue] == 1) {
        reqParamVo.protocol  = TKDaoType_Socket;
        if (extParams && [extParams isKindOfClass:[NSDictionary class]]) {
            if (extParams[@"url"]) {
                reqParamVo.url = extParams[@"url"];
            }
            reqParamVo.companyId = extParams[@"companyId"];
            reqParamVo.systemId  = extParams[@"systemId"];
        }
        reqParamVo.dataType  = TKDataType_Compress_Encryt;
    }
    reqParamVo.isShowWait = NO;
    reqParamVo.isUpload = YES;
    
    // 处理特殊参数-requestHeaders
    if (newParam[@"requestHeaders"]) {
        if ([newParam[@"requestHeaders"] isKindOfClass:[NSString class]]) {
            reqParamVo.headerFieldDic= [TKDataHelper jsonToDictionary:newParam[@"requestHeaders"]];
        }else{
            reqParamVo.headerFieldDic = (NSDictionary *)newParam[@"requestHeaders"];
        }
        
        [newParam removeObjectForKey:@"requestHeaders"];
    }
    // 处理特殊参数-requestParams
    if (newParam[@"requestParams"]) {
        if ([newParam[@"requestParams"] isKindOfClass:[NSString class]]) {
            [newParam addEntriesFromDictionary:[TKDataHelper jsonToDictionary:newParam[@"requestParams"]]];
        }else{
            [newParam addEntriesFromDictionary:(NSDictionary *)newParam[@"requestParams"]];
        }
        
        [newParam removeObjectForKey:@"requestParams"];
    }
    
    if ([[TKSystemHelper getConfigByKey:@"networkRequest.isRestFull"] intValue] == 1) {
        reqParamVo.isRestFull = YES;
        reqParamVo.contentType = TKContentType_WWW_FORM;
    }

    //h5参数单独控制是否走微服务
    if ([TKStringHelper isNotEmpty:newParam[@"isRestFull"]]) {
        if ([newParam[@"isRestFull"] intValue]==0) {
            reqParamVo.isRestFull = NO;
            reqParamVo.contentType = TKContentType_NONE;
        }else if ([newParam[@"isRestFull"] intValue]==1) {
            reqParamVo.isRestFull = YES;
            reqParamVo.contentType = TKContentType_WWW_FORM;
        }
        
        if ([TKStringHelper isNotEmpty:newParam[@"requestSignAppId"]]) {
            reqParamVo.signAppId=newParam[@"requestSignAppId"];
            reqParamVo.isURLSign=YES;
        }
        
        //不传走配置，传了空字符串就不加密
        if ([newParam objectForKey:@"requestSignKey"]) {
            if ([TKStringHelper isNotEmpty:newParam[@"requestSignKey"]]) {
                reqParamVo.signKey=newParam[@"requestSignKey"];
                reqParamVo.isURLSign=YES;
            }else{
                reqParamVo.isURLSign=NO;
            }
        }
    }
    
    NSMutableDictionary *tempReqParam = [newParam mutableCopy];
//    if (extParams) {
//        [tempReqParam removeObjectForKey:@"extParams"];
//    }
    reqParamVo.reqParam = tempReqParam;
    
    [self invoke:reqParamVo callBackFunc:[self handleGeneralNetworkError:callBackFunc]];
}

-(void)uploadFileWithURL:(NSString *)url delegate:(id<TKUploadDelegate>)mDelegate param:(NSMutableDictionary *)reqParam callBackFunc:(CallBackFunc)callBackFunc
{
    ReqParamVo *reqParamVo = [self createReqParamVo];
    reqParamVo.url = url;
    reqParamVo.protocol = TKDaoType_Http;
    id extParams = reqParam[@"extParams"];
//    if (extParams && [extParams isKindOfClass:[NSDictionary class]]) {
//        [reqParam addEntriesFromDictionary:extParams];
//    }
    if (reqParam[@"serverAccessType"] && [reqParam[@"serverAccessType"] integerValue] == 1) {
        reqParamVo.protocol  = TKDaoType_Socket;
        if (extParams && [extParams isKindOfClass:[NSDictionary class]]) {
            if (extParams[@"url"]) {
                reqParamVo.url = extParams[@"url"];
            }
            reqParamVo.companyId = extParams[@"companyId"];
            reqParamVo.systemId  = extParams[@"systemId"];
        }
        reqParamVo.dataType  = TKDataType_Compress_Encryt;
    }
    reqParamVo.isShowWait = NO;
    reqParamVo.isUpload = YES;
    
    if (reqParam[@"requestHeaders"]) {
        if ([reqParam[@"requestHeaders"] isKindOfClass:[NSString class]]) {
            reqParamVo.headerFieldDic= [TKDataHelper jsonToDictionary:reqParam[@"requestHeaders"]];
        }else{
            reqParamVo.headerFieldDic=(NSDictionary *)reqParam[@"requestHeaders"];
        }
    }
    
    NSMutableDictionary *tempReqParam = [reqParam mutableCopy];
//    if (extParams) {
//        [tempReqParam removeObjectForKey:@"extParams"];
//    }
    reqParamVo.reqParam = tempReqParam;;
    reqParamVo.uploadDelegate = mDelegate;
    if (reqParam[@"timeout"]) {
        reqParamVo.timeOut=[reqParam[@"timeout"] intValue];
    }
    [self invoke:reqParamVo callBackFunc:callBackFunc];
}

-(void)handleNetworkWithURL:(NSString *)url param:(NSMutableDictionary *)reqParam callBackFunc:(CallBackFunc)callBackFunc
{
    NSMutableDictionary *newParam = [NSMutableDictionary dictionaryWithDictionary:reqParam];
    
    ReqParamVo *reqParamVo = [self createReqParamVo];
    reqParamVo.url = url;
    reqParamVo.protocol = TKDaoType_Http;
    id extParams = newParam[@"extParams"];
//    if (extParams && [extParams isKindOfClass:[NSDictionary class]]) {
//        [newParam addEntriesFromDictionary:extParams];
//    }
    if (newParam[@"serverAccessType"] && [newParam[@"serverAccessType"] integerValue] == 1) {
        reqParamVo.protocol  = TKDaoType_Socket;
        if (extParams && [extParams isKindOfClass:[NSDictionary class]]) {
            if (extParams[@"url"]) {
                reqParamVo.url = extParams[@"url"];
            }
            reqParamVo.companyId = extParams[@"companyId"];
            reqParamVo.systemId  = extParams[@"systemId"];
        }
        reqParamVo.dataType  = TKDataType_Compress_Encryt;
    }
    reqParamVo.isShowWait = NO;
    reqParamVo.isUpload = NO;
    reqParamVo.timeOut = 10.0f;
    
    // 处理特殊参数-requestHeaders
    if (newParam[@"requestHeaders"]) {
        if ([newParam[@"requestHeaders"] isKindOfClass:[NSString class]]) {
            reqParamVo.headerFieldDic= [TKDataHelper jsonToDictionary:newParam[@"requestHeaders"]];
        }else{
            reqParamVo.headerFieldDic = (NSDictionary *)newParam[@"requestHeaders"];
        }
        
        [newParam removeObjectForKey:@"requestHeaders"];
    }
    // 处理特殊参数-requestParams
    if (newParam[@"requestParams"]) {
        if ([newParam[@"requestParams"] isKindOfClass:[NSString class]]) {
            [newParam addEntriesFromDictionary:[TKDataHelper jsonToDictionary:newParam[@"requestParams"]]];
        }else{
            [newParam addEntriesFromDictionary:(NSDictionary *)newParam[@"requestParams"]];
        }
        
        [newParam removeObjectForKey:@"requestParams"];
    }
    
    if ([[TKSystemHelper getConfigByKey:@"networkRequest.isRestFull"] intValue] == 1) {
        reqParamVo.isRestFull = YES;
        reqParamVo.contentType = TKContentType_WWW_FORM;
    }
    
    //h5参数单独控制是否走微服务
    if ([TKStringHelper isNotEmpty:newParam[@"isRestFull"]]) {
        if ([newParam[@"isRestFull"] intValue]==0) {
            reqParamVo.isRestFull = NO;
            reqParamVo.contentType = TKContentType_NONE;
        }else if ([newParam[@"isRestFull"] intValue]==1) {
            reqParamVo.isRestFull = YES;
            reqParamVo.contentType = TKContentType_WWW_FORM;
        }
        
        if ([TKStringHelper isNotEmpty:newParam[@"requestSignAppId"]]) {
            reqParamVo.signAppId=newParam[@"requestSignAppId"];
            reqParamVo.isURLSign=YES;
        }
        
        //不传走配置，传了空字符串就不加密
        if ([newParam objectForKey:@"requestSignKey"]) {
            if ([TKStringHelper isNotEmpty:newParam[@"requestSignKey"]]) {
                reqParamVo.signKey=newParam[@"requestSignKey"];
                reqParamVo.isURLSign=YES;
            }else{
                reqParamVo.isURLSign=NO;
            }
        }
    }
    
    NSMutableDictionary *tempReqParam = [newParam mutableCopy];
//    if (extParams) {
//        [tempReqParam removeObjectForKey:@"extParams"];
//    }
    reqParamVo.reqParam = tempReqParam;
    reqParamVo.isFilterRepeatRequest=NO;
    
    [self invoke:reqParamVo callBackFunc:[self handleGeneralNetworkError:callBackFunc]];
}


-(void)handleRawNetworkWithURL:(NSString *)url headerFieldDic:(NSDictionary *)headerFieldDic param:(NSMutableDictionary *)reqParam callBackFunc:(CallBackFunc)callBackFunc
{
    ReqParamVo *reqParamVo = [self createReqParamVo];
    reqParamVo.url = url;
    reqParamVo.protocol = TKDaoType_Http;
    id extParams = reqParam[@"extParams"];
//    if (extParams && [extParams isKindOfClass:[NSDictionary class]]) {
//        [reqParam addEntriesFromDictionary:extParams];
//    }
    if (reqParam[@"serverAccessType"] && [reqParam[@"serverAccessType"] integerValue] == 1) {
        reqParamVo.protocol  = TKDaoType_Socket;
        if (extParams && [extParams isKindOfClass:[NSDictionary class]]) {
            if (extParams[@"url"]) {
                reqParamVo.url = extParams[@"url"];
            }
            reqParamVo.companyId = extParams[@"companyId"];
            reqParamVo.systemId  = extParams[@"systemId"];
        }
        reqParamVo.dataType  = TKDataType_Compress_Encryt;
    }
    reqParamVo.isShowWait = NO;
    reqParamVo.isUpload = NO;
    reqParamVo.timeOut = 10.0f;
    reqParamVo.isURLEncode = NO;
    
    if (headerFieldDic) {
        reqParamVo.headerFieldDic = headerFieldDic;
    }
    
    NSMutableDictionary *tempReqParam = [reqParam mutableCopy];
//    if (extParams) {
//        [tempReqParam removeObjectForKey:@"extParams"];
//    }
    reqParamVo.reqParam = tempReqParam;
    
    [self invoke:reqParamVo callBackFunc:callBackFunc];
}

-(void)requestServerRoomWithURL:(NSString *)url param:(NSMutableDictionary *)reqParam callBackFunc:(CallBackFunc)callBackFunc
{
    NSMutableDictionary *newParam = [NSMutableDictionary dictionaryWithDictionary:reqParam];
    
    ReqParamVo *reqParamVo = [self createReqParamVo];
    reqParamVo.url = url;
    reqParamVo.protocol = TKDaoType_Http;
    reqParamVo.httpMethod = @"post";
    id extParams = newParam[@"extParams"];
//    if (extParams && [extParams isKindOfClass:[NSDictionary class]]) {
//        [newParam addEntriesFromDictionary:extParams];
//    }
    if (newParam[@"serverAccessType"] && [newParam[@"serverAccessType"] integerValue] == 1) {
        reqParamVo.protocol  = TKDaoType_Socket;
        if (extParams && [extParams isKindOfClass:[NSDictionary class]]) {
            if (extParams[@"url"]) {
                reqParamVo.url = extParams[@"url"];
            }
            reqParamVo.companyId = extParams[@"companyId"];
            reqParamVo.systemId  = extParams[@"systemId"];
        }
        reqParamVo.dataType  = TKDataType_Compress_Encryt;
    }
    reqParamVo.isShowWait = NO;
    reqParamVo.isUpload = NO;
    reqParamVo.timeOut = 10.0f;
    
    // 处理特殊参数-requestHeaders
    if (newParam[@"requestHeaders"]) {
        if ([newParam[@"requestHeaders"] isKindOfClass:[NSString class]]) {
            reqParamVo.headerFieldDic= [TKDataHelper jsonToDictionary:newParam[@"requestHeaders"]];
        }else{
            reqParamVo.headerFieldDic = (NSDictionary *)newParam[@"requestHeaders"];
        }
        
        [newParam removeObjectForKey:@"requestHeaders"];
    }
    // 处理特殊参数-requestParams
    if (newParam[@"requestParams"]) {
        if ([newParam[@"requestParams"] isKindOfClass:[NSString class]]) {
            [newParam addEntriesFromDictionary:[TKDataHelper jsonToDictionary:newParam[@"requestParams"]]];
        }else{
            [newParam addEntriesFromDictionary:(NSDictionary *)newParam[@"requestParams"]];
        }
        
        [newParam removeObjectForKey:@"requestParams"];
    }
    
    if ([[TKSystemHelper getConfigByKey:@"networkRequest.isRestFull"] intValue] == 1) {
        reqParamVo.isRestFull=YES;
        reqParamVo.contentType = TKContentType_WWW_FORM;
    }

    //h5参数单独控制是否走微服务
    if ([TKStringHelper isNotEmpty:newParam[@"isRestFull"]]) {
        if ([newParam[@"isRestFull"] intValue]==0) {
            reqParamVo.isRestFull = NO;
            reqParamVo.contentType = TKContentType_NONE;
        }else if ([newParam[@"isRestFull"] intValue]==1) {
            reqParamVo.isRestFull = YES;
            reqParamVo.contentType = TKContentType_WWW_FORM;
        }
        
        if ([TKStringHelper isNotEmpty:newParam[@"requestSignAppId"]]) {
            reqParamVo.signAppId=newParam[@"requestSignAppId"];
            reqParamVo.isURLSign=YES;
        }
        
        //不传走配置，传了空字符串就不加密
        if ([newParam objectForKey:@"requestSignKey"]) {
            if ([TKStringHelper isNotEmpty:newParam[@"requestSignKey"]]) {
                reqParamVo.signKey=newParam[@"requestSignKey"];
                reqParamVo.isURLSign=YES;
            }else{
                reqParamVo.isURLSign=NO;
            }
        }
    }
    
    NSMutableDictionary *tempReqParam = [newParam mutableCopy];
//    if (extParams) {
//        [tempReqParam removeObjectForKey:@"extParams"];
//    }
    reqParamVo.reqParam = tempReqParam;
    
    [self invoke:reqParamVo callBackFunc:[self handleGeneralNetworkError:callBackFunc]];
}


/**
 *  <AUTHOR> 2015-04-21 17:12:59
 *
 *  人脸识别
 *
 *  @param url          url
 *  @param param        上传参数
 *  @param callBackFunc 回调函数
 */
-(void)FaceDetectWithURL:(NSString *)url andParam:(NSMutableDictionary *)reqParam callBackFunc:(CallBackFunc)callBackFunc
{
    ReqParamVo *reqParamVo = [self createReqParamVo];
    //    reqParamVo.url = [TKSystemHelper getConfigByKey:@"server.url"];
    reqParamVo.url = url;
    reqParamVo.protocol = TKDaoType_Http;
    id extParams = reqParam[@"extParams"];
//    if (extParams && [extParams isKindOfClass:[NSDictionary class]]) {
//        [reqParam addEntriesFromDictionary:extParams];
//    }
    if (reqParam[@"serverAccessType"] && [reqParam[@"serverAccessType"] integerValue] == 1) {
        reqParamVo.protocol  = TKDaoType_Socket;
        if (extParams && [extParams isKindOfClass:[NSDictionary class]]) {
            if (extParams[@"url"]) {
                reqParamVo.url = extParams[@"url"];
            }
            reqParamVo.companyId = extParams[@"companyId"];
            reqParamVo.systemId  = extParams[@"systemId"];
        }
        reqParamVo.dataType  = TKDataType_Compress_Encryt;
    }
    reqParamVo.isShowWait = NO;
    NSMutableDictionary *tempReqParam = [reqParam mutableCopy];
//    if (extParams) {
//        [tempReqParam removeObjectForKey:@"extParams"];
//    }
    reqParamVo.reqParam = tempReqParam;
    [self invoke:reqParamVo callBackFunc:callBackFunc];
}

-(void)faceDetectWithURL:(NSString *)url andParam:(NSMutableDictionary *)reqParam callBackFunc:(CallBackFunc)callBackFunc
{
    ReqParamVo *reqParamVo = [self createReqParamVo];
    //    reqParamVo.url = [TKSystemHelper getConfigByKey:@"server.url"];
    reqParamVo.url = url;
    reqParamVo.protocol = TKDaoType_Http;
    id extParams = reqParam[@"extParams"];
//    if (extParams && [extParams isKindOfClass:[NSDictionary class]]) {
//        [reqParam addEntriesFromDictionary:extParams];
//    }
    if (reqParam[@"serverAccessType"] && [reqParam[@"serverAccessType"] integerValue] == 1) {
        reqParamVo.protocol  = TKDaoType_Socket;
        if (extParams && [extParams isKindOfClass:[NSDictionary class]]) {
            if (extParams[@"url"]) {
                reqParamVo.url = extParams[@"url"];
            }
            reqParamVo.companyId = extParams[@"companyId"];
            reqParamVo.systemId  = extParams[@"systemId"];
        }
        reqParamVo.dataType  = TKDataType_Compress_Encryt;
    }
    reqParamVo.isShowWait = NO;
    reqParamVo.isAsync = YES;
    NSMutableDictionary *tempReqParam = [reqParam mutableCopy];
//    if (extParams) {
//        [tempReqParam removeObjectForKey:@"extParams"];
//    }
    reqParamVo.reqParam = tempReqParam;
    [self invoke:reqParamVo callBackFunc:callBackFunc];
}

- (void)handleStatisticEventWithURL:(NSString *)url param:(NSMutableDictionary *)reqParam callBackFunc:(CallBackFunc)callBackFunc
{
    NSMutableDictionary *newParam = [NSMutableDictionary dictionaryWithDictionary:reqParam];
    
    ReqParamVo *reqParamVo = [self createReqParamVo];
    reqParamVo.url = url;
    reqParamVo.protocol = TKDaoType_Http;
    reqParamVo.isPost = YES;
    

    reqParamVo.isShowWait = NO;
    reqParamVo.isUpload = NO;
    reqParamVo.timeOut = 10.0f;
    
    // 处理特殊参数-requestHeaders
    if (newParam[@"requestHeaders"]) {
        if ([newParam[@"requestHeaders"] isKindOfClass:[NSString class]]) {
            reqParamVo.headerFieldDic= [TKDataHelper jsonToDictionary:newParam[@"requestHeaders"]];
        }else{
            reqParamVo.headerFieldDic = (NSDictionary *)newParam[@"requestHeaders"];
        }
        
        [newParam removeObjectForKey:@"requestHeaders"];
    }
    
    if ([[TKSystemHelper getConfigByKey:@"networkRequest.isRestFull"] intValue] == 1) {
        reqParamVo.isRestFull = YES;
    }
    
    //h5参数单独控制是否走微服务
    if ([TKStringHelper isNotEmpty:newParam[@"isRestFull"]]) {
        if ([newParam[@"isRestFull"] intValue]==0) {
            reqParamVo.isRestFull = NO;
        }else if ([newParam[@"isRestFull"] intValue]==1) {
            reqParamVo.isRestFull = YES;
        }
        
        if ([TKStringHelper isNotEmpty:newParam[@"requestSignAppId"]]) {
            reqParamVo.signAppId=newParam[@"requestSignAppId"];
            reqParamVo.isURLSign=YES;
        }
        
        //不传走配置，传了空字符串就不加密
        if ([newParam objectForKey:@"requestSignKey"]) {
            if ([TKStringHelper isNotEmpty:newParam[@"requestSignKey"]]) {
                reqParamVo.signKey=newParam[@"requestSignKey"];
                reqParamVo.isURLSign=YES;
            }else{
                reqParamVo.isURLSign=NO;
            }
        }
    }
    
    NSMutableDictionary *tempReqParam = [newParam mutableCopy];

    reqParamVo.reqParam = tempReqParam;
//    [self invoke:reqParamVo callBackFunc:[self handleGeneralNetworkError:callBackFunc]];
    [self invoke:reqParamVo callBackFunc:callBackFunc];
}

- (void)getVideoTokenWithURL:(NSString *)url param:(NSMutableDictionary *)reqParam callBackFunc:(CallBackFunc)callBackFunc {
    
    ReqParamVo *reqParamVo = [self createReqParamVo];
    reqParamVo.url = url;
    reqParamVo.protocol = TKDaoType_Http;
    reqParamVo.isShowWait = YES;
    reqParamVo.httpMethod = @"get";
    
    NSMutableDictionary *tempReqParam = [reqParam mutableCopy];

    reqParamVo.reqParam = tempReqParam;
    [self invoke:reqParamVo callBackFunc:callBackFunc];
}

- (void)recordFailureReportWithURL:(NSString *)url param:(NSMutableDictionary *)reqParam callBackFunc:(CallBackFunc)callBackFunc
{
    NSMutableDictionary *newParam = [NSMutableDictionary dictionaryWithDictionary:reqParam];
    
    ReqParamVo *reqParamVo = [self createReqParamVo];
    reqParamVo.url = url;
    reqParamVo.protocol = TKDaoType_Http;
    reqParamVo.isPost = YES;
    
    reqParamVo.isShowWait = NO;
    reqParamVo.isUpload = NO;
    reqParamVo.timeOut = 10.0f;
    
    // 处理特殊参数-requestHeaders
    if (newParam[@"requestHeaders"]) {
        if ([newParam[@"requestHeaders"] isKindOfClass:[NSString class]]) {
            reqParamVo.headerFieldDic= [TKDataHelper jsonToDictionary:newParam[@"requestHeaders"]];
        }else{
            reqParamVo.headerFieldDic = (NSDictionary *)newParam[@"requestHeaders"];
        }
        
        [newParam removeObjectForKey:@"requestHeaders"];
    }
    
    if ([[TKSystemHelper getConfigByKey:@"networkRequest.isRestFull"] intValue] == 1) {
        reqParamVo.isRestFull = YES;
    }
    
    //h5参数单独控制是否走微服务
    if ([TKStringHelper isNotEmpty:newParam[@"isRestFull"]]) {
        if ([newParam[@"isRestFull"] intValue]==0) {
            reqParamVo.isRestFull = NO;
        }else if ([newParam[@"isRestFull"] intValue]==1) {
            reqParamVo.isRestFull = YES;
        }
        
        if ([TKStringHelper isNotEmpty:newParam[@"requestSignAppId"]]) {
            reqParamVo.signAppId=newParam[@"requestSignAppId"];
            reqParamVo.isURLSign=YES;
        }
        
        //不传走配置，传了空字符串就不加密
        if ([newParam objectForKey:@"requestSignKey"]) {
            if ([TKStringHelper isNotEmpty:newParam[@"requestSignKey"]]) {
                reqParamVo.signKey=newParam[@"requestSignKey"];
                reqParamVo.isURLSign=YES;
            }else{
                reqParamVo.isURLSign=NO;
            }
        }
    }
    
    NSMutableDictionary *tempReqParam = [newParam mutableCopy];

    reqParamVo.reqParam = tempReqParam;
//    [self invoke:reqParamVo callBackFunc:[self handleGeneralNetworkError:callBackFunc]];
    [self invoke:reqParamVo callBackFunc:callBackFunc];
}

/**
 *
 *  http 请求处理
 *
 *  @param reqParam     请求对象
 *  @param callBackFunc 回调处理
 */
- (void)handleHttpWithReq:(ReqParamVo *)reqParam callBackFunc:(CallBackFunc)callBackFunc
{
    reqParam.protocol = TKDaoType_Http;
    id extParams = reqParam.reqParam[@"extParams"];
//    if (extParams && [extParams isKindOfClass:[NSDictionary class]]) {
//        [reqParam.reqParam addEntriesFromDictionary:extParams];
//    }
    if (reqParam.reqParam[@"serverAccessType"] && [reqParam.reqParam[@"serverAccessType"] integerValue] == 1) {
        reqParam.protocol  = TKDaoType_Socket;
        if (extParams && [extParams isKindOfClass:[NSDictionary class]]) {
            if (extParams[@"url"]) {
                reqParam.url = extParams[@"url"];
            }
            reqParam.companyId = extParams[@"companyId"];
            reqParam.systemId  = extParams[@"systemId"];
        }
        reqParam.dataType  = TKDataType_Compress_Encryt;
    }
    [self invoke:reqParam callBackFunc:callBackFunc];
}

/**
 *  <AUTHOR> 2015-04-21 17:12:59
 *
 *  ios调用Js
 *
 *  @param reqParam     传入参数
 *  @param callBackFunc 回调函数
 */
- (void)iosCallJsWithDic:(NSMutableDictionary *)reqParam callBackFunc:(CallBackFunc)callBackFunc
{
    ReqParamVo *reqParamVo = [self createReqParamVo];
    if (reqParam[@"tkuuid"]) {
        reqParamVo.url = reqParam[@"tkuuid"];
    }else{
        reqParamVo.url = reqParam[@"moduleName"]? reqParam[@"moduleName"]:@"open";
    }
    reqParamVo.protocol = TKDaoType_Javascript;
    reqParamVo.isAsync = YES;
    reqParamVo.reqParam = reqParam;
    [self invoke:reqParamVo callBackFunc:callBackFunc isReturnList:NO];
}

/**
 *
 *   ios调用Js处理
 *
 *  @param reqParam     请求对象
 *  @param callBackFunc 回调处理
 */
- (void)iosCallJsWithReq:(ReqParamVo *)reqParam callBackFunc:(CallBackFunc)callBackFunc
{
  
    reqParam.protocol = TKDaoType_Javascript;
    
    reqParam.isAsync = YES;
    
    [self invoke:reqParam callBackFunc:callBackFunc isReturnList:NO];
}

- (CallBackFunc)handleGeneralNetworkError:(CallBackFunc)originCallBackFunc
{
    CallBackFunc newCallBackFunc = ^(ResultVo *resultVo) {
        
        if (resultVo.errorNo == 920 ||  // 用户未登陆
            resultVo.errorNo == 922 ||  // 无效的Token
            resultVo.errorNo == 924) {  // 用户Token已过期
            
            [[NSNotificationCenter defaultCenter] postNotificationName:TK_OPEN_TOKEN_INVALID_NOTIFICATION object:@{@"errorNo" : @(resultVo.errorNo), @"errorInfo" : [TKStringHelper isNotEmpty:resultVo.errorInfo] ? resultVo.errorInfo : @""}];
            
        }
        
        if (originCallBackFunc) originCallBackFunc(resultVo);
    };
    
    return newCallBackFunc;
}

+ (NSMutableDictionary *)filterRequestParam:(NSDictionary *)originParam
{
    NSMutableDictionary *param = [NSMutableDictionary dictionary];  // 是否申请虚拟人房间号
    
    if (originParam[@"requestHeaders"]) {
        
        param[@"requestHeaders"] = [originParam getObjectWithKey:@"requestHeaders"];
    }
    if (originParam[@"requestParams"]) {
        
        param[@"requestParams"] = [originParam getObjectWithKey:@"requestParams"];
    }
    if (originParam[@"isRestFull"]) {
        
        param[@"isRestFull"] = [originParam getObjectWithKey:@"isRestFull"];
    }
    if (originParam[@"requestSignKey"]) {
        
        param[@"requestSignKey"] = [originParam getObjectWithKey:@"requestSignKey"];
    }
    if (originParam[@"requestSignAppId"]) {
        
        param[@"requestSignAppId"] = [originParam getObjectWithKey:@"requestSignAppId"];
    }
    
    return param;
}



@end
