//
//  TKOpenDelegateManager.h
//  TKOpenAccount-Standard
//  处理单独的打开50115需要登录的管理类(商城不走TKOpenController打开，但是要走开户登录交互的场景)
//  Created by 夏博文 on 2024/12/9.
//  Copyright © 2024 thinkive. All rights reserved.
//

#import "TKOpenDelegate.h"

@interface TKOpenDelegateManager : NSObject
@property (nonatomic, weak) id <TKOpenDelegate> tkOpenDelegate;

// 单例
+ (TKOpenDelegateManager *)shareInstance;

/**
 *  <AUTHOR> 2024年12月09日14:37:40
 *  将60099收到的通知，delegate给集成方实现具体功能
 *  @param param  h5调用60099的参数
 *  @param hController 调用60099的当前web控制器
 *  return nil
 */
-(void)tkWebviewExternalCall:(NSMutableDictionary *)param withCtr:(UIViewController*)hController;


/**
 *  <AUTHOR> 2024年12月09日13:53:09
 *  登陆信息通知H5登陆
 *  @param loginInfoParam 登陆信息，登陆与退出登陆要传（如token、账号、密码等需要多个传多个，例：@{@"token":@"123456",@"phoneNo":@"***********"}）
 *  return nil
 */
-(void)loginInfoWithParam:(NSDictionary*)loginInfoParam;


/**
 *  <AUTHOR> 2024年12月09日13:53:15
 *  登陆信息存储或清空，要是页面存在会通知js
 *  @param accountType 不为空就是fxcAccountInfo_xxx;为空就是默认fxcAccountInfo
 *  @param loginInfoParam（默认存账户类型key为：fxcAccountInfo） 登陆信息，传nil就会清理旧的登陆信息，要是loginAccountType也为空就会清理所有带fxcAccountInfo的登陆信息（如token、账号、密码等需要多个传多个，例：@{@"token":@"123456",@"phoneNo":@"***********"}）
 *  return nil
 */
-(void)loginAccountType:(NSString *)accountType loginInfoWithParam:(NSDictionary*)loginInfoParam;

/**
 *  <AUTHOR> 2024年12月09日13:55:26
 *  退出登陆并清理账号相关信息（当该控制器实例存在时调用）
 *  return nil
 */
-(void)loginOut;

/**
 *  <AUTHOR> 2024年12月09日13:55:33
 *  存储登录信息，不通知js的60098方法
 *  @param loginInfoParam 登陆信息，登陆与退出登陆要传（如token、账号、密码等需要多个传多个，例：@{@"token":@"123456",@"phoneNo":@"***********"}）
 *  return nil
 */
-(void)loginInfoWithParamNOCallJs:(NSDictionary*)loginInfoParam;

/**
 *  <AUTHOR> 2024年12月09日13:55:38
 *   delegate事件结束后通知h5结果
 *  @param param h5需要获取的事件结果信息
 *  return nil
 */
-(void)openDelegateActionCallback:(NSDictionary*)param;

/**
 *  <AUTHOR> 2024年12月09日14:23:53
 *  将登陆信息Map转换成Json字符串后存储下来
 */
-(void)handlingAccountInfoWithParam:(NSDictionary*)loginInfoParam loginAccountType:(NSString *)accountType;

/**
 *  <AUTHOR> 2019年11月27日18:30:00
 *  登陆信息存储或清空，要是页面存在会通知js
 *  @param loginInfoParam（默认存账户类型key为：fxcAccountInfo） 登陆信息，登陆与退出登陆要传,传nil的话就是清空登陆信息（如token、账号、密码等需要多个传多个，例：@{@"token":@"123456",@"phoneNo":@"***********"}）
 *  return nil
 */
+(void)loginInfoWithParam:(NSDictionary*)loginInfoParam;

/**
 *  <AUTHOR> 2019年11月27日18:30:00
 *  登陆信息存储或清空，要是页面存在会通知js
 *  @param accountType 不为空就是fxcAccountInfo_xxx;为空就是默认fxcAccountInfo
 *  @param loginInfoParam（默认存账户类型key为：fxcAccountInfo） 登陆信息，传nil就会清理旧的登陆信息，要是loginAccountType也为空就会清理所有带fxcAccountInfo的登陆信息（如token、账号、密码等需要多个传多个，例：@{@"token":@"123456",@"phoneNo":@"***********"}）
 *  return nil
 */
+(void)loginAccountType:(NSString *)accountType loginInfoWithParam:(NSDictionary*)loginInfoParam;
@end


