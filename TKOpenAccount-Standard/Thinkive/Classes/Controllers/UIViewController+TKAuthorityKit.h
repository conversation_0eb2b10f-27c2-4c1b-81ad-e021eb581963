//
//  UIViewController+TKAuthorityKit.h
//  TKOpenAccount-Standard
//
//  Created by <PERSON><PERSON> on 2017/6/9.
//  Copyright © 2017年 thinkive. All rights reserved.
//

#import <UIKit/UIKit.h>

typedef void(^TKPermissionsBlock)();
typedef void(^TKPermissionsResultBlock)(BOOL flag);//是否有权限

/**
 *
 * @class UIViewController (TKAuthorityKit)
 *
 * @description 用户权限检测类别
 *
 *
 */
#ifdef TK_OPEN_Authority_UIAlertView
@interface UIViewController (TKAuthorityKit)<UIAlertViewDelegate>
#else
@interface UIViewController (TKAuthorityKit)
#endif



/**
 *
 * @method tkIsMicrophonePermissions:
 *
 * @brief 麦克风权限检测
 *
 * @param pBlock 权限已允许回调
 *
 */
- (void)tkIsMicrophonePermissions:(TKPermissionsBlock)pBlock;

/**
 *
 * @method tkIsCameraPermissions:
 *
 * @brief 相机权限检测
 *
 * @param pBlock 权限已允许回调
 *
 */
- (void)tkIsCameraPermissions:(TKPermissionsBlock)pBlock;

/**
 *
 * @method tkIsPhotoLibraryPermissions:
 *
 * @brief 相册权限检测
 *
 * @param pBlock 权限已允许回调
 *
 */
- (void)tkIsPhotoLibraryPermissions:(TKPermissionsBlock)pBlock;

/**
 *
 * @method tkIsPhotoLibraryPermissions:
 *
 * @brief 网络权限检测
 *
 * @param pBlock 权限已允许回调
 *
 */
- (void)tkIsNetWorkPermissions:(TKPermissionsBlock)pBlock;


/**
 *
 * @method tkIsMicrophonePermissions:
 *
 * @brief 麦克风权限检测
 *
 * @param 权限是否允许回调
 *
 */
- (void)tkIsMicrophonePermissionsFlag:(TKPermissionsResultBlock)flagBlock;

/**
 *
 * @method tkIsCameraPermissions:
 *
 * @brief 相机权限检测
 *
 * @param 权限是否允许回调
 *
 */
- (void)tkIsCameraPermissionsFlag:(TKPermissionsResultBlock)flagBlock;

/**
 *
 * @method tkIsPhotoLibraryPermissions:
 *
 * @brief 相册权限检测
 *
 * @param 权限是否允许回调
 *
 */
- (void)tkIsPhotoLibraryPermissionsFlag:(TKPermissionsResultBlock)flagBlock;

/**
 *
 * @method tkIsPhotoLibraryPermissions:
 *
 * @brief 定位权限检测
 *
 * @param 权限是否允许回调
 *
 */
- (void)tkIsLocationPermissionsFlag:(TKPermissionsResultBlock)flagBlock;

//弹窗提示
-(void)tkOpenAlertControllerAuthority:(NSString *)message;
@end
