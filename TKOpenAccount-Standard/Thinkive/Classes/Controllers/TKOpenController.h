//
//  TKOpenController
//  TKApp
//
//  Created by 叶璐 on 15/4/27.
//  Copyright (c) 2015年 liubao. All rights reserved.
//

#import <UIKit/UIKit.h>
#import "TKOpenDelegate.h"

@interface TKOpenController:TKBaseWebViewController


@property (nonatomic, weak) id <TKOpenDelegate> oDelegate;
@property (nonatomic, weak) id<TKDeviceInfoDelegate> tkDeviceInfoDelegate;//设备信息代理，设置的话50001取值就是代理实现
@property (nonatomic, assign) BOOL isNoShowLoading;//是否不显示loading层(YES不显示loading层，NO显示loading；默认显示)
@property (nonatomic, assign) BOOL isShowLoadingCloseBtn;//是否显示loading层关闭按钮（YES显示，NO不显示；默认不显示）
@property (nonatomic, strong) NSString *loadingBgColorString;//loading层背景颜色设置（默认白色，16进制字符串）,浏览器颜色也设置成这个
@property (nonatomic, strong) id gifImgName;//loading图，gif图的话必须传名字(不传用TKOpenResource.bundle里面的默认loading图，传的话就用传的图片名作为loading图)
@property (nonatomic, strong) NSString *skinMode;//皮肤模式（0：白色，1：黑色；默认0）
@property (nonatomic, assign) BOOL  isImmersion;//是否沉浸式状态栏（默认NO）
@property (nonatomic, assign) BOOL isNeedTKAuthorIntroduce;//是否在调用插件前展示介绍页面
@property (nonatomic, assign) BOOL isNotDismissAnimated;//当前页面是否不需要关闭动画，YES不需要，FALSE需要；默认需要
@property (nonatomic, strong) NSString *currentTheme;//设置当前主题
@property (nonatomic, assign) BOOL isCloseNavSwipingBack;//是否主动关闭navigationController得侧滑返回；yes：主动关闭；no：不做任何处理

/**
 *
 * @method initWithParams:
 *
 * @brief 初始化开户控制器
 *
 * @param mParams 传入的参数（一般就h5Url）
 *
 */
- (id)initWithParams:(id)mParams;

/**
 *  <AUTHOR> 2018年08月11日09:35:07
 *  初始化控制器（如果平台已登录，就传对应的登陆信息；未登录传空。一般网厅才会需要登录信息）
 *  @param mParams 传入的参数（一般就h5Url）
 *  @param loginInfoParam （默认存账户类型key为：fxcAccountInfo） 登陆信息，传nil就会清理旧的登陆信息（如token、账号、密码等需要多个传多个，例：@{@"token":@"123456",@"phoneNo":@"***********"}）
 */
-(instancetype)initWithParams:(id)mParams loginInfoParam:(NSDictionary *)loginInfoParam;


/**
 *  <AUTHOR> 2018年08月11日09:35:07
 *  初始化控制器（如果平台已登录，就传对应的登陆信息；未登录传空。一般网厅才会需要登录信息）
 *  @param mParams 传入的参数（一般就h5Url）
 *  @param loginInfoParam （默认存账户类型key为：fxcAccountInfo） 登陆信息，传nil就会清理旧的登陆信息（如token、账号、密码等需要多个传多个，例：@{@"token":@"123456",@"phoneNo":@"***********"}）
 *  @param frame 嵌套页面frame，需要在初始化的时候就正确设置
 */
-(instancetype)initWithParams:(id)mParams loginInfoParam:(NSDictionary *)loginInfoParam withFrame:(CGRect)frame;


/**
 *  <AUTHOR> 2019年11月27日18:30:00
 *  登陆信息存储或清空，要是页面存在会通知js
 *  @param loginInfoParam（默认存账户类型key为：fxcAccountInfo） 登陆信息，登陆与退出登陆要传,传nil的话就是清空登陆信息（如token、账号、密码等需要多个传多个，例：@{@"token":@"123456",@"phoneNo":@"***********"}）
 *  return nil
 */
+(void)loginInfoWithParam:(NSDictionary*)loginInfoParam;

/**
 *  <AUTHOR> 2020年01月07日10:08:44
 *  初始化控制器（如果平台已登录，就传对应的登陆信息；未登录传空。一般网厅才会需要登录信息）
 *  @param mParams 传入的参数（一般就h5Url）
 *  @param accountType 不为空就是fxcAccountInfo_xxx;为空就是默认fxcAccountInfo
 *  @param loginInfoParam 登陆信息，传nil就会清理旧的登陆信息，要是loginAccountType也为空就会清理所有带fxcAccountInfo的登陆信息（如token、账号、密码等需要多个传多个，例：@{@"token":@"123456",@"phoneNo":@"***********"}）
 */
-(instancetype)initWithParams:(id)mParams loginAccountType:(NSString *)accountType  loginInfoParam:(NSDictionary *)loginInfoParam;

/**
 *  <AUTHOR> 2019年11月27日18:30:00
 *  登陆信息存储或清空，要是页面存在会通知js
 *  @param accountType 不为空就是fxcAccountInfo_xxx;为空就是默认fxcAccountInfo
 *  @param loginInfoParam（默认存账户类型key为：fxcAccountInfo） 登陆信息，传nil就会清理旧的登陆信息，要是loginAccountType也为空就会清理所有带fxcAccountInfo的登陆信息（如token、账号、密码等需要多个传多个，例：@{@"token":@"123456",@"phoneNo":@"***********"}）
 *  return nil
 */
+(void)loginAccountType:(NSString *)accountType loginInfoWithParam:(NSDictionary*)loginInfoParam;

/**
 *  <AUTHOR> 2018年08月11日11:00:54
 *  登陆信息通知H5登陆
 *  @param loginInfoParam 登陆信息，登陆与退出登陆要传（如token、账号、密码等需要多个传多个，例：@{@"token":@"123456",@"phoneNo":@"***********"}）
 *  return nil
 */
-(void)loginInfoWithParam:(NSDictionary*)loginInfoParam;


/**
 *  <AUTHOR> 2023年05月25日16:33:21
 *  登陆信息存储或清空，要是页面存在会通知js
 *  @param accountType 不为空就是fxcAccountInfo_xxx;为空就是默认fxcAccountInfo
 *  @param loginInfoParam（默认存账户类型key为：fxcAccountInfo） 登陆信息，传nil就会清理旧的登陆信息，要是loginAccountType也为空就会清理所有带fxcAccountInfo的登陆信息（如token、账号、密码等需要多个传多个，例：@{@"token":@"123456",@"phoneNo":@"***********"}）
 *  return nil
 */
-(void)loginAccountType:(NSString *)accountType loginInfoWithParam:(NSDictionary*)loginInfoParam;

/**
 *  <AUTHOR> 2018年08月11日12:01:57
 *  退出登陆并清理账号相关信息（当该控制器实例存在时调用）
 *  return nil
 */
-(void)loginOut;

/**
 *  <AUTHOR> 2019年01月03日18:22:04
 *  存储登录信息，不通知js的60098方法
 *  @param loginInfoParam 登陆信息，登陆与退出登陆要传（如token、账号、密码等需要多个传多个，例：@{@"token":@"123456",@"phoneNo":@"***********"}）
 *  return nil
 */
-(void)loginInfoWithParamNOCallJs:(NSDictionary*)loginInfoParam;

/**
 *  <AUTHOR> 2023年04月17日15:28:43
 *   delegate事件结束后通知h5结果
 *  @param param h5需要获取的事件结果信息
 *  return nil
 */
-(void)openDelegateActionCallback:(NSDictionary*)param;

/**
 *  <AUTHOR> 2019年01月03日18:22:04
 *  打印三方SDK信息日志
 */
+(void)print3libSDKInfoLog;
@end



