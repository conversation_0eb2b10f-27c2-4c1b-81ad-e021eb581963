//
//  TKFxcAccountInfoType.m
//  TKOpenAccount-Standard
//
//  Created by <PERSON>ie on 2020/1/7.
//  Copyright © 2020 thinkive. All rights reserved.
//

#import "TKFxcAccountInfoType.h"

@implementation TKFxcAccountInfoType
/**
 <AUTHOR> 2019年09月04日10:28:49
 @初始化视频面签管理类
 */
+(TKFxcAccountInfoType *)shareInstance
{
    static dispatch_once_t once;
    static id instance;
    dispatch_once(&once, ^{
        instance = [[self alloc]init];
    });
    return instance;
}
@end
