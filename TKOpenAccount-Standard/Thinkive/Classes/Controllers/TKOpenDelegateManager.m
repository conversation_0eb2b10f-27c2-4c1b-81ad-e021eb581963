//
//  TKOpenDelegateManager.m
//  TKOpenAccount-Standard
//
//  Created by 夏博文 on 2024/12/9.
//  Copyright © 2024 thinkive. All rights reserved.
//

#import "TKOpenDelegateManager.h"
#import "TKFxcAccountInfoType.h"
#import "TKOpenAccountService.h"
#define FXC_ACCOUNT_INFO @"fxcAccountInfo"

@interface TKOpenDelegateManager()

@property(nonatomic, strong) NSMutableDictionary *extParam;//存储60099插件传过来的参数

@end

@implementation TKOpenDelegateManager
#pragma mark - Public Selector
// 单例
+ (TKOpenDelegateManager *)shareInstance {
    
    static dispatch_once_t once;
    static id instance;
    dispatch_once(&once, ^{
        instance = [[self alloc]init];
    });
    return instance;
}

/**
 *  <AUTHOR> 2024年12月09日14:37:40
 *  将60099收到的通知，delegate给集成方实现具体功能
 *  @param param  h5调用60099的参数
 *  return nil
 */
-(void)tkWebviewExternalCall:(NSMutableDictionary *)param withCtr:(UIViewController*)hController{
    TKLogInfo(@"SDK收到广播通知：TKOpenDelegateManager处理广播通知,代理为:%@", _tkOpenDelegate);
    self.extParam=param;
    
    if (_tkOpenDelegate && [_tkOpenDelegate respondsToSelector:@selector(interruptHandleExternalCall:withActionType:withActionParams:)]) {
        [_tkOpenDelegate interruptHandleExternalCall:hController withActionType:[self.extParam[@"actionType"] intValue] withActionParams:self.extParam[@"params"]];
        TKLogInfo(@"回调外层interruptHandleExternalCall:withActionType:withActionParams:方法,代理为:%@", _tkOpenDelegate);
        return;
    }else{
        TKLogInfo(@"代理不存在或没有实现方法，无法回调外层interruptHandleExternalCall:withActionType:withActionParams:方法，代理为%@, ", _tkOpenDelegate);
    }
    
    if (_tkOpenDelegate && [_tkOpenDelegate respondsToSelector:@selector(interruptHandleExternalCall:withParams:)]) {
        [_tkOpenDelegate interruptHandleExternalCall:hController withParams:self.extParam];
        TKLogInfo(@"回调外层interruptHandleExternalCall:withParams:方法,代理为:%@", _tkOpenDelegate);
        return;
    }else{
        TKLogInfo(@"代理不存在或没有实现方法，无法回调外层interruptHandleExternalCall:withParams:方法,代理为:%@", _tkOpenDelegate);
    }
}

/**
 *  <AUTHOR> 2024年12月09日14:23:23
 *  登陆信息通知H5登陆
 *  @param param 登陆信息，登陆与退出登陆要传（如token、账号、密码等需要多个传多个，例：@{@"token":@"123456",@"phoneNo":@"***********"}）
 *  @param externalParam interruptHandleExternalCall这个代理方法传过来的参数，需要回传给h5
 *  return nil
 */
-(void)loginInfoWithParam:(NSDictionary*)loginInfoParam{
    if (loginInfoParam) {
        [self handlingAccountInfoWithParam:loginInfoParam loginAccountType:nil];
    }else{
        //清空登陆信息
        [[TKAppEngine shareInstance] callPlugin:@"50040" param:@{@"key":FXC_ACCOUNT_INFO,@"value":@""} moduleName:nil];
    }
    if (self.extParam) {
        NSMutableDictionary *tkReqParam = [NSMutableDictionary dictionary];
        [tkReqParam addEntriesFromDictionary:self.extParam];
        tkReqParam[@"funcNo"]=@"60098";
        tkReqParam[@"accountType"]=FXC_ACCOUNT_INFO;
        tkReqParam[@"actionType"]=@"1";
        self.extParam=tkReqParam;
    }else{
        self.extParam=(NSMutableDictionary *)@{@"funcNo":@"60098",@"accountType":FXC_ACCOUNT_INFO,@"actionType":@"1"};
    }

    //通知h5登陆
    [self iosCallJsWithParam:self.extParam];
}

/**
 *  <AUTHOR> 2024年12月09日14:23:28
 *  登陆信息存储或清空，要是页面存在会通知js
 *  @param loginAccountType 不为空就是fxcAccountInfo_xxx;为空就是默认fxcAccountInfo
 *  @param loginInfoWithParam（默认存账户类型key为：fxcAccountInfo） 登陆信息，传nil就会清理旧的登陆信息，要是loginAccountType也为空就会清理所有带fxcAccountInfo的登陆信息（如token、账号、密码等需要多个传多个，例：@{@"token":@"123456",@"phoneNo":@"***********"}）
 *  return nil
 */
-(void)loginAccountType:(NSString *)accountType loginInfoWithParam:(NSDictionary*)loginInfoParam{
    if (loginInfoParam) {
        [self handlingAccountInfoWithParam:loginInfoParam loginAccountType:[NSString stringWithFormat:@"%@_%@",FXC_ACCOUNT_INFO,accountType]];
    }else{
        if ([TKStringHelper isEmpty:accountType]) {
            //清空所有账号登陆类型
            for (id key in [TKFxcAccountInfoType shareInstance].accountTypeDic) {
                //清空登陆信息
                [[TKAppEngine shareInstance] callPlugin:@"50040" param:@{@"key":key,@"value":@""} moduleName:nil];
            }
            
        }else{
            //清空对应账号类型登陆信息
            [[TKAppEngine shareInstance] callPlugin:@"50040" param:@{@"key":[NSString stringWithFormat:@"%@_%@",FXC_ACCOUNT_INFO,accountType],@"value":@""} moduleName:nil];
        }
    }
    
    NSString *accountTypeKey;
    if ([TKStringHelper isEmpty:accountType]) {
        accountTypeKey=FXC_ACCOUNT_INFO;
    }else{
        accountTypeKey=[NSString stringWithFormat:@"%@_%@",FXC_ACCOUNT_INFO,accountType];
    }
    
    if (self.extParam) {
        NSMutableDictionary *tkReqParam = [NSMutableDictionary dictionary];
        [tkReqParam addEntriesFromDictionary:self.extParam];
        tkReqParam[@"funcNo"]=@"60098";
        tkReqParam[@"accountType"]=accountTypeKey;
        tkReqParam[@"actionType"]=@"1";
        self.extParam=tkReqParam;
    }else{
        self.extParam=(NSMutableDictionary *)@{@"funcNo":@"60098",@"accountType":accountTypeKey,@"actionType":@"1"};
    }
    //通知h5登陆
    [self iosCallJsWithParam:self.extParam];
}

/**
 *  <AUTHOR> 2024年12月09日14:23:35
 *  退出登陆并清理账号相关信息（当该控制器实例存在时调用）
 *  @param param 前面登陆时候传的参数KEY一致，值为NULL（例：@{@"token":@"",@"phoneNo":@""}）
 *  return nil
 */
-(void)loginOut{
    //清空登陆信息
    [[TKAppEngine shareInstance] callPlugin:@"50040" param:@{@"key":FXC_ACCOUNT_INFO,@"value":@""} moduleName:nil];
    //通知h5退出登陆
    if (self.extParam) {
        NSMutableDictionary *tkReqParam = [NSMutableDictionary dictionary];
        [tkReqParam addEntriesFromDictionary:self.extParam];
        tkReqParam[@"funcNo"]=@"60098";
        tkReqParam[@"accountType"]=FXC_ACCOUNT_INFO;
        tkReqParam[@"actionType"]=@"2";
        self.extParam=tkReqParam;
    }else{
        self.extParam=(NSMutableDictionary *)@{@"funcNo":@"60098",@"accountType":FXC_ACCOUNT_INFO,@"actionType":@"2"};
    }
    [self iosCallJsWithParam:self.extParam];
}

/**
 *  <AUTHOR> 2024年12月09日14:23:42
 *  存储登录信息，不通知js的60098方法
 *  @param param 登陆信息，登陆与退出登陆要传（如token、账号、密码等需要多个传多个，例：@{@"token":@"123456",@"phoneNo":@"***********"}）
 *  return nil
 */
-(void)loginInfoWithParamNOCallJs:(NSDictionary*)loginInfoParam{
    if (loginInfoParam) {
        [self handlingAccountInfoWithParam:loginInfoParam loginAccountType:nil];
    }else{
        //清空登陆信息
        [[TKAppEngine shareInstance] callPlugin:@"50040" param:@{@"key":FXC_ACCOUNT_INFO,@"value":@""} moduleName:nil];
    }
}

/**
 *  <AUTHOR> 2024年12月09日14:23:47
 *   delegate事件结束后通知h5结果
 *  @param param h5需要获取的事件结果信息
 *  return nil
 */
-(void)openDelegateActionCallback:(NSDictionary*)param{
    NSMutableDictionary *dic=[[NSMutableDictionary alloc] init];
    dic[@"funcNo"]=@"60098";
    [dic addEntriesFromDictionary:param];
    //通知h5得60098功能号，告知原生60099代理事件结果
    [self iosCallJsWithParam:dic];
}


/**
 *  <AUTHOR> 2024年12月09日14:23:53
 *  将登陆信息Map转换成Json字符串后存储下来
 */
-(void)handlingAccountInfoWithParam:(NSDictionary*)loginInfoParam loginAccountType:(NSString *)accountType{
    if ([TKStringHelper isEmpty:accountType]) {
        accountType=FXC_ACCOUNT_INFO;
        //存字典对象
        [[TKAppEngine shareInstance] callPlugin:@"50040" param:@{@"key":FXC_ACCOUNT_INFO,@"value":loginInfoParam} moduleName:nil];
    }else{
        //存字典对象
        [[TKAppEngine shareInstance] callPlugin:@"50040" param:@{@"key":accountType,@"value":loginInfoParam} moduleName:nil];
    }
    //存储账号类型方便到时候清理
    if (![TKFxcAccountInfoType shareInstance].accountTypeDic) {
        [TKFxcAccountInfoType shareInstance].accountTypeDic=[NSMutableDictionary dictionary];
    }
    [TKFxcAccountInfoType shareInstance].accountTypeDic[accountType]=accountType;
    
}

/**
 *  <AUTHOR> 2019年11月27日18:30:00
 *  登陆信息存储或清空，要是页面存在会通知js
 *  @param param 登陆信息，登陆与退出登陆要传,传nil的话就是清空登陆信息（如token、账号、密码等需要多个传多个，例：@{@"token":@"123456",@"phoneNo":@"***********"}）
 *  return nil
 */
+(void)loginInfoWithParam:(NSDictionary*)loginInfoParam{
    if (loginInfoParam) {
        [TKOpenDelegateManager handlingAccountInfoWithParam:loginInfoParam loginAccountType:nil];
    }else{
        //清空登陆信息
        [[TKAppEngine shareInstance] callPlugin:@"50040" param:@{@"key":FXC_ACCOUNT_INFO,@"value":@""} moduleName:nil];
    }
   
    //群发通知js
    [[TKOpenAccountService new] iosCallJsWithParam:((NSMutableDictionary *)@{@"funcNo":@"60098",@"accountType":FXC_ACCOUNT_INFO,@"actionType":@"1"})];
}

/**
 *  <AUTHOR> 2019年11月27日18:30:00
 *  登陆信息存储或清空，要是页面存在会通知js
 *  @param loginAccountType 不为空就是fxcAccountInfo_xxx;为空就是默认fxcAccountInfo
 *  @param loginInfoWithParam（默认存账户类型key为：fxcAccountInfo） 登陆信息，传nil就会清理旧的登陆信息，要是loginAccountType也为空就会清理所有带fxcAccountInfo的登陆信息（如token、账号、密码等需要多个传多个，例：@{@"token":@"123456",@"phoneNo":@"***********"}）
 *  return nil
 */
+(void)loginAccountType:(NSString *)accountType loginInfoWithParam:(NSDictionary*)loginInfoParam{
    if (loginInfoParam) {
        [TKOpenDelegateManager handlingAccountInfoWithParam:loginInfoParam loginAccountType:[NSString stringWithFormat:@"%@_%@",FXC_ACCOUNT_INFO,accountType]];
    }else{
        if ([TKStringHelper isEmpty:accountType]) {
            //清空所有账号登陆类型
            for (id key in [TKFxcAccountInfoType shareInstance].accountTypeDic) {
                //清空登陆信息
                [[TKAppEngine shareInstance] callPlugin:@"50040" param:@{@"key":key,@"value":@""} moduleName:nil];
            }
            
        }else{
            //清空对应账号类型登陆信息
            [[TKAppEngine shareInstance] callPlugin:@"50040" param:@{@"key":[NSString stringWithFormat:@"%@_%@",FXC_ACCOUNT_INFO,accountType],@"value":@""} moduleName:nil];
        }
    }
    
    if ([TKStringHelper isEmpty:accountType]) {
        //群发通知js
        [[TKOpenAccountService new] iosCallJsWithParam:((NSMutableDictionary *)@{@"funcNo":@"60098",@"accountType":FXC_ACCOUNT_INFO})];
    }else{
        //群发通知js
        [[TKOpenAccountService new] iosCallJsWithParam:((NSMutableDictionary *)@{@"funcNo":@"60098",@"accountType":accountType})];
    }
    
}

#pragma mark - private Selector
/**
 *  <AUTHOR> 2019年08月22日15:58:29
 *  将登陆信息Map转换成Json字符串后存储下来类方法
 */
+(void)handlingAccountInfoWithParam:(NSDictionary*)loginInfoParam loginAccountType:(NSString *)accountType{
   if ([TKStringHelper isEmpty:accountType]) {
       accountType=FXC_ACCOUNT_INFO;
       //存字典对象
       [[TKAppEngine shareInstance] callPlugin:@"50040" param:@{@"key":FXC_ACCOUNT_INFO,@"value":loginInfoParam} moduleName:nil];
   }else{
       //存字典对象
       [[TKAppEngine shareInstance] callPlugin:@"50040" param:@{@"key":accountType,@"value":loginInfoParam} moduleName:nil];
   }
   //存储账号类型方便到时候清理
   if (![TKFxcAccountInfoType shareInstance].accountTypeDic) {
       [TKFxcAccountInfoType shareInstance].accountTypeDic=[NSMutableDictionary dictionary];
   }
   [TKFxcAccountInfoType shareInstance].accountTypeDic[accountType]=accountType;
   
}


//调用js方法
-(void)iosCallJsWithParam:(NSMutableDictionary *)param{
    TKOpenAccountService *service=[[TKOpenAccountService alloc] init];
    if ([TKStringHelper isEmpty:self.extParam[@"tkuuid"]]) {
        //群发通知js
        [service iosCallJsWithParam:self.extParam];
    }else{
        //通知对应h5
        [service iosCallJs:self.extParam[@"tkuuid"] param:self.extParam];
    }
}
@end
