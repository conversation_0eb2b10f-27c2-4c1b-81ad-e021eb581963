//
//  TKOpenDelegate.h
//  TKOpen
//
//  Created by <PERSON><PERSON> on 15/8/25.
//  Copyright (c) 2015年 com.thinkive. All rights reserved.
//

#import <Foundation/Foundation.h>

/**
 *
 * @description 开户回调代理
 */
@protocol TKOpenDelegate<NSObject>

@optional

/**
 *
 * @description 开户完成退出回调
 */
- (void)openAccountExit;

/**
 *
 * @description 开户sdk外部调用回调（和有withActionType方法的二选一实现一个）
 * params[@"actionType"]  取事件类型
 * params[@"params"] 取h5传的事件参数字典
 */
- (void)interruptHandleExternalCall:(UIViewController*)hController withParams:(id)params;
    
/**
 *
 * @description 开户sdk外部调用回调（和没有withActionType方法的二选一实现一个）
 * actionType  事件类型
 * withActionParams h5传的事件参数字典
 */
- (void)interruptHandleExternalCall:(UIViewController*)hController withActionType:(int)actionType withActionParams:(id)actionParams;

/**
 <AUTHOR> 2019年05月23日18:02:00
 @webview给外部回调，可以用于埋点等操作（只需要h5埋点和原生打通情况）
 @return BOOL （YES:埋点业务，那开户后续就不操作该数据；NO:非埋点业务，开户继续处理）
*/
-(BOOL)tkOpenWebView:(id)webView shouldStartLoadWithRequest:(NSURLRequest*)request navigationType:(TKWebViewNavigationType)navigationType;

@end
