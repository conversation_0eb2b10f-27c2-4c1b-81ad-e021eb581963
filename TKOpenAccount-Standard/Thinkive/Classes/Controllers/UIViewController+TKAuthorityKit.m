//
//  UIViewController+TKAuthorityKit.m
//  TKOpenAccount-Standard
//
//  Created by <PERSON>lover on 2017/6/9.
//  Copyright © 2017年 thinkive. All rights reserved.
//

#import "UIViewController+TKAuthorityKit.h"
#import <Photos/PHPhotoLibrary.h>
#import <objc/runtime.h>
#import <CoreTelephony/CTCellularData.h>
#import <CoreLocation/CoreLocation.h>
@interface UIViewController(private)<CLLocationManagerDelegate>


@end

@implementation UIViewController (TKAuthorityKit)
CLLocationManager *tkOpenlManager;
TKPermissionsResultBlock  tkOpenPermissionsLocationBackBlock;

+ (void)load{
//    TKSwizzle([self class], NSSelectorFromString(@"preferredStatusBarStyle"), @selector(tk_preferredStatusBarStyle));
}

- (void)tkIsMicrophonePermissions:(TKPermissionsBlock)pBlock
{
    if ([[[UIDevice currentDevice] systemVersion] floatValue] >= 7.0) {
        
        __block BOOL bCanRecord = NO;
        
        AVAudioSession *audioSession = [AVAudioSession sharedInstance];
        
        if ([audioSession respondsToSelector:@selector(requestRecordPermission:)]) {
            
            [audioSession performSelector:@selector(requestRecordPermission:) withObject:^(BOOL granted) {
                
                if (granted) {
                    
                    bCanRecord = YES;
                    
                    dispatch_async(dispatch_get_main_queue(), ^{
                        pBlock();
                    });
                    
                    
                } else {
                    
                    bCanRecord = NO;
                    
                    
                    dispatch_async(dispatch_get_main_queue(), ^{
                        NSString *msg=@"未开启麦克风权限，请进入'设置'－'隐私'－'麦克风'中开启";
                        NSBundle *bundle = [NSBundle bundleWithURL:[[NSBundle mainBundle] URLForResource:[NSString stringWithFormat:@"%@", TK_OPEN_RESOURCE_NAME] withExtension:@"bundle"]];
                        
                        NSString *plistPath = [bundle pathForResource:@"config" ofType:@"plist"];
                        
                        NSMutableDictionary *cDic;
                        
                        if (plistPath) {
                            
                            cDic = [[NSMutableDictionary alloc] initWithContentsOfFile:plistPath];
                        }
                        if ([TKStringHelper isNotEmpty:cDic[@"MicrophoneTip"]]) {
                            msg=cDic[@"MicrophoneTip"];
                        }
                        
                        
                        [self tkOpenAlertControllerAuthority:msg];
                    });
                }
                
            }];
            
        }
        
    }else{
    
        dispatch_async(dispatch_get_main_queue(), ^{
            pBlock();
        });
    }
    
}

- (void)tkIsCameraPermissions:(TKPermissionsBlock)pBlock
{
    if ([[[UIDevice currentDevice] systemVersion] floatValue] >= 7.0) {
        
        __block BOOL bCanAccessForMedia = NO;
        
        if ([AVCaptureDevice authorizationStatusForMediaType:AVMediaTypeVideo] == AVAuthorizationStatusNotDetermined) {
            
            [AVCaptureDevice requestAccessForMediaType:AVMediaTypeVideo completionHandler:^(BOOL granted) {
                
                if (granted) {
                    
                    bCanAccessForMedia = YES;
                    
                    dispatch_async(dispatch_get_main_queue(), ^{
                        pBlock();
                    });
                    
                }else{
                
                
                    dispatch_async(dispatch_get_main_queue(), ^{
                        NSString *msg=@"未开启相机权限，请进入'设置'－'隐私'－'相机'中开启";
                        NSBundle *bundle = [NSBundle bundleWithURL:[[NSBundle mainBundle] URLForResource:[NSString stringWithFormat:@"%@", TK_OPEN_RESOURCE_NAME] withExtension:@"bundle"]];
                        
                        NSString *plistPath = [bundle pathForResource:@"config" ofType:@"plist"];
                        
                        NSMutableDictionary *cDic;
                        
                        if (plistPath) {
                            
                            cDic = [[NSMutableDictionary alloc] initWithContentsOfFile:plistPath];
                        }
                        if ([TKStringHelper isNotEmpty:cDic[@"CameraTip"]]){
                            msg=cDic[@"CameraTip"];
                        }
                        [self tkOpenAlertControllerAuthority:msg];
                    });
                }
            }];
            
        }else if ([AVCaptureDevice authorizationStatusForMediaType:AVMediaTypeVideo] == AVAuthorizationStatusDenied || [AVCaptureDevice authorizationStatusForMediaType:AVMediaTypeVideo] == AVAuthorizationStatusRestricted) {
            
            
            dispatch_async(dispatch_get_main_queue(), ^{
                NSString *msg=@"未开启相机权限，请进入'设置'－'隐私'－'相机'中开启";
                NSBundle *bundle = [NSBundle bundleWithURL:[[NSBundle mainBundle] URLForResource:[NSString stringWithFormat:@"%@", TK_OPEN_RESOURCE_NAME] withExtension:@"bundle"]];
                
                NSString *plistPath = [bundle pathForResource:@"config" ofType:@"plist"];
                
                NSMutableDictionary *cDic;
                
                if (plistPath) {
                    
                    cDic = [[NSMutableDictionary alloc] initWithContentsOfFile:plistPath];
                }
                if ([TKStringHelper isNotEmpty:cDic[@"CameraTip"]]){
                    msg=cDic[@"CameraTip"];
                }
                [self tkOpenAlertControllerAuthority:msg];
            });
            
        }else{
        
            bCanAccessForMedia = YES;
            
            dispatch_async(dispatch_get_main_queue(), ^{
                pBlock();
            });
        }

    }else{
    
        dispatch_async(dispatch_get_main_queue(), ^{
            pBlock();
        });
    }
    
}

- (void)tkIsPhotoLibraryPermissions:(TKPermissionsBlock)pBlock
{
     if ([[[UIDevice currentDevice] systemVersion] floatValue] >= 8.0) {
        
        __block BOOL bCanPHAuthorization = NO;
        
        if ([PHPhotoLibrary authorizationStatus] == PHAuthorizationStatusNotDetermined) {
            
             [PHPhotoLibrary requestAuthorization:^(PHAuthorizationStatus status) {
                
                if (status == PHAuthorizationStatusAuthorized) {
                    
                    bCanPHAuthorization = YES;
                    
                    dispatch_async(dispatch_get_main_queue(), ^{
                        pBlock();
                    });
                    
                }else{
                    
                    dispatch_async(dispatch_get_main_queue(), ^{
                        
                        NSString *msg=@"未开启相册权限，请进入'设置'－'隐私'-'相册'中开启";
                        NSBundle *bundle = [NSBundle bundleWithURL:[[NSBundle mainBundle] URLForResource:[NSString stringWithFormat:@"%@", TK_OPEN_RESOURCE_NAME] withExtension:@"bundle"]];
                        
                        NSString *plistPath = [bundle pathForResource:@"config" ofType:@"plist"];
                        
                        NSMutableDictionary *cDic;
                        
                        if (plistPath) {
                            
                            cDic = [[NSMutableDictionary alloc] initWithContentsOfFile:plistPath];
                        }
                         if ([TKStringHelper isNotEmpty:cDic[@"PhotoBrowseTip"]]) {
                            msg=cDic[@"PhotoBrowseTip"];
                        }

                        [self tkOpenAlertControllerAuthority:msg];
                    });
                }
            }];
            
        }else if([PHPhotoLibrary authorizationStatus] == PHAuthorizationStatusRestricted || [PHPhotoLibrary authorizationStatus] == PHAuthorizationStatusDenied){
            
            
            dispatch_async(dispatch_get_main_queue(), ^{
                
                NSString *msg=@"未开启相册权限，请进入'设置'－'隐私'-'相册'中开启";
                NSBundle *bundle = [NSBundle bundleWithURL:[[NSBundle mainBundle] URLForResource:[NSString stringWithFormat:@"%@", TK_OPEN_RESOURCE_NAME] withExtension:@"bundle"]];
                
                NSString *plistPath = [bundle pathForResource:@"config" ofType:@"plist"];
                
                NSMutableDictionary *cDic;
                
                if (plistPath) {
                    
                    cDic = [[NSMutableDictionary alloc] initWithContentsOfFile:plistPath];
                }
                if ([TKStringHelper isNotEmpty:cDic[@"PhotoBrowseTip"]]) {
                    msg=cDic[@"PhotoBrowseTip"];
                }
                
                [self tkOpenAlertControllerAuthority:msg];
            });
            
        }else{
            
            bCanPHAuthorization = YES;
            
            dispatch_async(dispatch_get_main_queue(), ^{
                pBlock();
            });
        }
    
     }else{
     
         dispatch_async(dispatch_get_main_queue(), ^{
             pBlock();
         });
     }
    
}

- (void)tkIsNetWorkPermissions:(TKPermissionsBlock)pBlock
{
    if ([[[UIDevice currentDevice] systemVersion] floatValue] >= 9.0) {
    
        CTCellularData *cellularData = [[CTCellularData alloc] init];
//        CTCellularDataRestrictedState state = cellularData.restrictedState;
        cellularData.cellularDataRestrictionDidUpdateNotifier = ^(CTCellularDataRestrictedState state) {
            switch (state) {
                case kCTCellularDataRestricted:
                {
                    TKLogInfo(@"Restricrted");

//                    dispatch_async(dispatch_get_main_queue(), ^{
//
//                        NSDictionary *infoDictionary = [[NSBundle mainBundle] infoDictionary];
//                        CFShow(CFBridgingRetain(infoDictionary));
//                        NSString *app_Name = [infoDictionary objectForKey:@"CFBundleDisplayName"];
//                        UIAlertView *mAlert = [[UIAlertView alloc] initWithTitle:@"" message:[NSString stringWithFormat:@"未开启网络权限，请进入'设置'－'%@'-'无线数据'中开启",app_Name] delegate:self cancelButtonTitle:@"确定" otherButtonTitles:nil, nil];
//
//                        mAlert.tag = 1007;
//
//                        [mAlert show];
//                    });
                }
                    break;
                case kCTCellularDataNotRestricted:{
                    TKLogInfo(@"Not Restricted");
                    dispatch_async(dispatch_get_main_queue(), ^{
                        pBlock();
                    });
                }

                    break;
                case kCTCellularDataRestrictedStateUnknown:
                    TKLogInfo(@"Unknown");
                    break;
                default:
                    break;
            }
        };
    }else{
        
        dispatch_async(dispatch_get_main_queue(), ^{
            pBlock();
        });
    }
    
}


- (void)tkIsLocationPermissions:(TKPermissionsBlock)pBlock
{
    if ([[[UIDevice currentDevice] systemVersion] floatValue] >= 8.0) {
        
        __block BOOL bCanPHAuthorization = NO;
        
        if ([PHPhotoLibrary authorizationStatus] == PHAuthorizationStatusNotDetermined) {
            
            [PHPhotoLibrary requestAuthorization:^(PHAuthorizationStatus status) {
                
                if (status == PHAuthorizationStatusAuthorized) {
                    
                    bCanPHAuthorization = YES;
                    
                    dispatch_async(dispatch_get_main_queue(), ^{
                        pBlock();
                    });
                    
                }else{
                    
                    dispatch_async(dispatch_get_main_queue(), ^{

                        [self tkOpenAlertControllerAuthority:@"未开启定位权限，请进入'设置'－'隐私'-'定位'中开启"];
                    });
                }
            }];
            
        }else if([PHPhotoLibrary authorizationStatus] == PHAuthorizationStatusRestricted || [PHPhotoLibrary authorizationStatus] == PHAuthorizationStatusDenied){
            
            
            dispatch_async(dispatch_get_main_queue(), ^{
                
                [self tkOpenAlertControllerAuthority:@"未开启定位权限，请进入'设置'－'隐私'-'定位'中开启"];
            });
            
        }else{
            
            bCanPHAuthorization = YES;
            
            dispatch_async(dispatch_get_main_queue(), ^{
                pBlock();
            });
        }
        
    }else{
        
        dispatch_async(dispatch_get_main_queue(), ^{
            pBlock();
        });
    }
    
}

#ifdef TK_OPEN_Authority_UIAlertView
- (void)tk_alertView:(UIAlertView *)alertView clickedButtonAtIndex:(NSInteger)buttonIndex
{
    if (alertView.tag == 1007) {
        if(buttonIndex==1){
            #ifdef __IPHONE_8_0
            [TKSystemHelper openAppURLStr:UIApplicationOpenSettingsURLString completionHandler:nil];
            #endif
        }
        
        TKSwizzle([self class], NSSelectorFromString(@"alertView:clickedButtonAtIndex:"), @selector(tk_alertView:clickedButtonAtIndex:));
        
    }else{
        
         [self tk_alertView:alertView clickedButtonAtIndex:buttonIndex];
    }

}
#else
#endif

-(void)tkOpenAlertControllerAuthority:(NSString *)message{
#ifdef TK_OPEN_Authority_UIAlertView
    TKSwizzle([self class], NSSelectorFromString(@"alertView:clickedButtonAtIndex:"), @selector(tk_alertView:clickedButtonAtIndex:));
    UIAlertView *mAlert = [[UIAlertView alloc] initWithTitle:@"温馨提示" message:message delegate:self cancelButtonTitle:@"暂不授权" otherButtonTitles:@"去授权", nil];
    
    mAlert.tag = 1007;
    
    [mAlert show];
#else
    UIAlertController *alertController = [UIAlertController alertControllerWithTitle:@"温馨提示" message:message preferredStyle:UIAlertControllerStyleAlert];
    
    // 2.创建并添加按钮
    UIAlertAction *cancelAction = [UIAlertAction actionWithTitle:@"暂不授权" style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {

    }];
    [alertController addAction:cancelAction];
    
    // 2.创建并添加按钮
    UIAlertAction *okAction = [UIAlertAction actionWithTitle:@"去授权" style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
            #ifdef __IPHONE_8_0
            [TKSystemHelper openAppURLStr:UIApplicationOpenSettingsURLString completionHandler:nil];
            #endif
    }];
    [alertController addAction:okAction];
    [self presentViewController:alertController animated:YES completion:nil];
#endif

}

- (UIStatusBarStyle)tk_preferredStatusBarStyle
{
    SEL styleSel = NSSelectorFromString(@"statusBarStyle");
    
    TKLogInfo(@"%@%s",NSStringFromClass([self class]), __FUNCTION__);
    
    if (styleSel && [self respondsToSelector:styleSel]) {
        
        IMP imp = [self methodForSelector:styleSel];
        
        UIStatusBarStyle (*func)(id, SEL) = (void *)imp;
        
        return func(self, styleSel);
        
    }else{
    
        return [self tk_preferredStatusBarStyle];
    }

}

//方法交换
void TKSwizzle(Class c, SEL org, SEL new)
{
    // 交换方法
    Method orgMet = class_getInstanceMethod(c, org);
    
    if (orgMet == nil) {
        
        class_addMethod(c, org, method_getImplementation(orgMet), method_getTypeEncoding(orgMet));
        
        orgMet = class_getInstanceMethod(c, org);
    }
    
    Method newMet = class_getInstanceMethod(c, new);

    method_exchangeImplementations(orgMet, newMet);

}



- (void)tkIsMicrophonePermissionsFlag:(TKPermissionsResultBlock)flagBlock{
    if ([[[UIDevice currentDevice] systemVersion] floatValue] >= 7.0) {
        
        __block BOOL bCanRecord = NO;
        
        AVAudioSession *audioSession = [AVAudioSession sharedInstance];
        
        if ([audioSession respondsToSelector:@selector(requestRecordPermission:)]) {
            
            [audioSession performSelector:@selector(requestRecordPermission:) withObject:^(BOOL granted) {
                
                if (granted) {
                    
                    bCanRecord = YES;
                    dispatch_async(dispatch_get_main_queue(), ^{
                        flagBlock(bCanRecord);
                    });
                    
                    
                } else {
                    
                    bCanRecord = NO;
                   dispatch_async(dispatch_get_main_queue(), ^{
                       flagBlock(bCanRecord);
                   });
                }
                
            }];
            
        }
        
    }else{
        dispatch_async(dispatch_get_main_queue(), ^{
            flagBlock(YES);
        });
    }
}

- (void)tkIsCameraPermissionsFlag:(TKPermissionsResultBlock)flagBlock{
    if ([[[UIDevice currentDevice] systemVersion] floatValue] >= 7.0) {
        
        __block BOOL bCanAccessForMedia = NO;
        
        if ([AVCaptureDevice authorizationStatusForMediaType:AVMediaTypeVideo] == AVAuthorizationStatusNotDetermined) {
            
            [AVCaptureDevice requestAccessForMediaType:AVMediaTypeVideo completionHandler:^(BOOL granted) {
                
                if (granted) {
                    
                    bCanAccessForMedia = YES;
                    
            
                    dispatch_async(dispatch_get_main_queue(), ^{
                        flagBlock(bCanAccessForMedia);
                    });
                }else{
                    
                   dispatch_async(dispatch_get_main_queue(), ^{
                       flagBlock(bCanAccessForMedia);
                   });
                }
            }];
            
        }else if ([AVCaptureDevice authorizationStatusForMediaType:AVMediaTypeVideo] == AVAuthorizationStatusDenied || [AVCaptureDevice authorizationStatusForMediaType:AVMediaTypeVideo] == AVAuthorizationStatusRestricted) {
            
            dispatch_async(dispatch_get_main_queue(), ^{
                flagBlock(bCanAccessForMedia);
            });
            
        }else{
            
            bCanAccessForMedia = YES;
            
            dispatch_async(dispatch_get_main_queue(), ^{
                flagBlock(bCanAccessForMedia);
            });
        }
        
    }else{
        
       dispatch_async(dispatch_get_main_queue(), ^{
           flagBlock(YES);
       });
    }
}

- (void)tkIsPhotoLibraryPermissionsFlag:(TKPermissionsResultBlock)flagBlock{
    if ([[[UIDevice currentDevice] systemVersion] floatValue] >= 8.0) {
        
        __block BOOL bCanPHAuthorization = NO;
        
        if ([PHPhotoLibrary authorizationStatus] == PHAuthorizationStatusNotDetermined) {
            
            [PHPhotoLibrary requestAuthorization:^(PHAuthorizationStatus status) {
                
                if (status == PHAuthorizationStatusAuthorized) {
                    
                    bCanPHAuthorization = YES;
                    
                    
                    dispatch_async(dispatch_get_main_queue(), ^{
                        flagBlock(bCanPHAuthorization);
                    });
                    
                }else{
                    
                    dispatch_async(dispatch_get_main_queue(), ^{
                        flagBlock(bCanPHAuthorization);
                    });
                }
            }];
            
        }else if([PHPhotoLibrary authorizationStatus] == PHAuthorizationStatusRestricted || [PHPhotoLibrary authorizationStatus] == PHAuthorizationStatusDenied){
            
            dispatch_async(dispatch_get_main_queue(), ^{
                flagBlock(bCanPHAuthorization);
            });
            
        }else{
            
            bCanPHAuthorization = YES;
            
            dispatch_async(dispatch_get_main_queue(), ^{
                flagBlock(bCanPHAuthorization);
            });
        }
        
    }else{
        
        dispatch_async(dispatch_get_main_queue(), ^{
            flagBlock(YES);
        });
    }
    
}
- (void)tkIsLocationPermissionsFlag:(TKPermissionsResultBlock)flagBlock{
    tkOpenPermissionsLocationBackBlock=flagBlock;
    tkOpenlManager = [[CLLocationManager alloc] init];
    
    if (![CLLocationManager locationServicesEnabled]) {
        
        dispatch_async(dispatch_get_main_queue(), ^{
            flagBlock(NO);
        });
        
    }
    
    if ([CLLocationManager authorizationStatus] == kCLAuthorizationStatusNotDetermined) {
        
        if([[[UIDevice currentDevice] systemVersion] floatValue] >= 8.0){
            
            [tkOpenlManager requestWhenInUseAuthorization];
            
        }
        
    }
//    else if([CLLocationManager authorizationStatus] == kCLAuthorizationStatusDenied || [CLLocationManager authorizationStatus] == kCLAuthorizationStatusRestricted){
//
//        flagBlock(NO);
//    }
    
    tkOpenlManager.delegate = self;
    
    tkOpenlManager.desiredAccuracy = kCLLocationAccuracyBest;
    
    [tkOpenlManager startUpdatingLocation];
}

#pragma mark -implement CLLocationManagerDelegate
//用户更改定位权限
-(void)locationManager:(CLLocationManager *)manager didChangeAuthorizationStatus:(CLAuthorizationStatus)status{
    if([CLLocationManager authorizationStatus] == kCLAuthorizationStatusDenied || [CLLocationManager authorizationStatus] == kCLAuthorizationStatusRestricted){
        
        
        dispatch_async(dispatch_get_main_queue(), ^{
            [manager stopUpdatingLocation];
            tkOpenPermissionsLocationBackBlock(NO);
        });
    }else if([CLLocationManager authorizationStatus] == kCLAuthorizationStatusAuthorizedWhenInUse||[CLLocationManager authorizationStatus] == kCLAuthorizationStatusAuthorizedAlways){
        dispatch_async(dispatch_get_main_queue(), ^{
            [manager stopUpdatingLocation];
            tkOpenPermissionsLocationBackBlock(YES);
        });
    }
}

- (void)locationManager:(CLLocationManager *)manager didUpdateLocations:(NSArray *)locations
{

    
}

@end
