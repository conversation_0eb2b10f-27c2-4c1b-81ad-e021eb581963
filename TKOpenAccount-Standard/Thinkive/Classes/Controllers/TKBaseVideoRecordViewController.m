//
//  TKBaseVideoRecordViewController.m
//  TKOpenAccount-Standard
//
//  Created by <PERSON> on 2022/3/23.
//  Copyright © 2022 thinkive. All rights reserved.
//

#import "TKBaseVideoRecordViewController.h"

#import <AVKit/AVKit.h>
#import <MediaPlayer/MediaPlayer.h>

#import "TKFaceDetectManager.h"
#import "TKPlayer.h"
#import "TKZFPortraitViewController.h"

#import "TKVideoFragmentModel.h"

@interface TKBaseVideoRecordViewController ()<TKOneWayVideoAlertTipViewDelegate, TKPlayerDelegate>

// UI
@property (nonatomic, strong) TKOneWayVideoAlertTipView *alertTipView;//弹窗提示

// 视频录制控制参数
@property (nonatomic, assign) CGFloat videoWidth;//视频宽度
@property (nonatomic, assign) CGFloat videoHeight;//视频高度
//@property (nonatomic, assign) int longestTime; //最长录制时间

// 流程控制flag

@property(nonatomic, assign) BOOL isSpeechRecognitionThrough; //语音识别是否通过
@property(nonatomic, assign) BOOL forcedEndOneWayVideo; // 是否已强制结束单向
@property (nonatomic, assign) TKOneWayVideoEndType oneWayVideoEndType;  // 单向视频结束type。主要处理错误type
@property (nonatomic, readwrite, assign) BOOL isPlayingSound;   // 是否正在播放固定文件

// 流程数组
@property (nonatomic, assign) int   beforeVideoIndex;
@property (nonatomic, assign) int   questionIndex;//记录当前是第几个问题
@property (nonatomic, assign) int   afterVideoIndex;

@property (nonatomic, readwrite, assign) int faceDetectFailureCount; // 最大人脸检测失败次数
@property (nonatomic, readwrite, assign) int faceCompareFailureCount; // 最大人脸比对失败次数
@property (nonatomic, readwrite, assign) int recordFailureCount; // 录制失败次数
@property (nonatomic, readwrite, assign) int totalFaceDetectFailureCount; // 最大人脸检测失败次数
@property (nonatomic, readwrite, assign) int totalFaceCompareFailureCount; // 最大人脸比对失败次数
@property (nonatomic, readwrite, assign) int totalFailureCount; // 最大录制失败次数（所有录制错误，包括但不限于人脸不在框、人脸比对不通过等）

@property (nonatomic, readwrite, assign) int failureCountPerAsrNoVoice; // 每轮语音识别没有回答的无关回答次数
@property (nonatomic, readwrite, assign) int maxFailureCountPerAsrNoVoice; // 每轮语音识别没有回答的最大无关回答次数
@property (nonatomic, readwrite, assign) int noVoiceFailureCount; // 语音识别没有声音失败次数
@property (nonatomic, readwrite, assign) int totalNoVoiceFailureCount; // 最大语音识别没有声音失败次数
@property (nonatomic, readwrite, assign) int wrongAnswerFailureCount; // 错误回答失败次数
@property (nonatomic, readwrite, assign) int totalWrongAnswerFailureCount; // 最大语音识别错误回答失败次数
@property (nonatomic, readwrite, assign) int totalVideoTimeOutFailureCount; // 最大语音识别错误回答失败次数
@property (nonatomic, strong) NSString *answeredErrorInfo;//问题回答失败原因


// 录制工具
//@property (nonatomic, strong) TKPlayer *player; //播放器



@property (nonatomic, readwrite, strong) AVAudioSessionCategory originCategory;
@property (nonatomic, readwrite, assign) AVAudioSessionCategoryOptions originOption;

@property (nonatomic, readwrite, strong) NSTimer *startRecordTimeoutTimer; // 开始录制超时定时器，防止不做录制占用时间过长


/// 所有的片段模型
@property (nonatomic, readwrite, strong) NSMutableArray *fragmentModelList;
@property (nonatomic, readwrite, strong) TKVideoFragmentModel *currentBusinessFragmentModel;  // tts、asr、暂停模型，这三个模型之间同一时间只会有1个
@property (nonatomic, readwrite, strong) TKVideoFragmentModel *currentFaceErrorFragmentModel;
@property (nonatomic, readwrite, assign) int currentAsrFragmentIndex;   // 客户回答index
@property (nonatomic, readwrite, assign) int currentPauseFragmentIndex; // 展示资料index
@property (nonatomic, readwrite, assign) int currentFragmentGroupId; // 关键帧类型分组ID

@property (nonatomic, readwrite, assign) NSTimeInterval statisticEventStartTime;    // 总事件初始时间
@property (nonatomic, readwrite, strong) TKLayerView *layerView;
@property (nonatomic, readwrite, assign) BOOL isExitProgress;   // 是否已经退出任务
@property (nonatomic, readwrite, assign) BOOL isEndRecord;   // 是否已到预览页面或录制错误页面
@property (nonatomic, readwrite, assign) BOOL isInitAutoRecordTimer;   // 是否已初始化自动录制定时器

@property (nonatomic, strong) AVPlayer *voicePlayer;//按钮响应语音播放
@end


@implementation TKBaseVideoRecordViewController

#pragma mark - Init && Dealloc
- (void)dealloc {
    [[NSNotificationCenter defaultCenter] removeObserver:self];
}

- (instancetype)initWithParam:(NSMutableDictionary *)param{
    self = [super init];
    if (self) {
        self.requestParam = param;
    }
    return self;
}

#pragma mark - Orientations
- (UIInterfaceOrientationMask)supportedInterfaceOrientations
{
    return UIInterfaceOrientationMaskPortrait;
}

- (UIInterfaceOrientation)preferredInterfaceOrientationForPresentation
{
    return UIInterfaceOrientationPortrait;
}

- (BOOL)shouldAutorotate
{
    return NO;
}

#pragma mark - View life cycle
- (void)viewDidLoad {
    [super viewDidLoad];
    self.isResetParentStatusBarStyle=YES;
    self.statusBarStyle = UIStatusBarStyleLightContent;
    
    // 埋点-单向_开始
    self.statisticEventStartTime = [[NSDate date] timeIntervalSince1970];
    NSMutableDictionary *eventDic = [NSMutableDictionary dictionaryWithDictionary:self.requestParam];
    TKPrivateOneWayVideoType oneWayVideoType = (TKPrivateOneWayVideoType)[self.pluginCallBackfuncNoToTKPrivateOneWayVideoType[self.pluginCallBackfuncNo] intValue];
    [TKStatisticEventHelper sendEvent:TKPrivateEventOneWayVideo
                         subEventName:TKPrivateSubEventStart
                             progress:TKPrivateEventProgressNone
                               result:TKPrivateEventResultNone
                          orientation:self.isLandscape ? TKPrivateVideoOrientationLandscape : TKPrivateVideoOrientationPortrait
                      oneWayVideoType:oneWayVideoType
                 prepareVideoProgress:TKPrivatePrepareVideoProgressNone
                             eventDic:eventDic];
    
    // 删除原有的视频。这是防止异常退出没有清除视频
    [self deleteLocalVideoFile:[self getLocalOneWayVideoPath]];
    
    // 检测网络
    [self checkNetwork];
    
    // 处理token过期通知。要在发网络请求前注册
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(handleInvalidTokenNoti:) name:TK_OPEN_TOKEN_INVALID_NOTIFICATION  object:nil];
    
    // 屏幕常亮
    [UIApplication sharedApplication].idleTimerDisabled = YES;
    
    _originCategory = [AVAudioSession sharedInstance].category;
    _originOption = [AVAudioSession sharedInstance].categoryOptions;
    
    // 设置失败统计次数
    self.maxFailureCountPerAsrNoVoice = [TKStringHelper isEmpty:self.requestParam[@"maxFailureCountPerAsrNoVoice"]] ? 3 : [self.requestParam[@"maxFailureCountPerAsrNoVoice"] intValue];
    self.totalFaceDetectFailureCount = [TKStringHelper isEmpty:self.requestParam[@"totalFaceDetectFailureCount"]] ? 3 : [self.requestParam[@"totalFaceDetectFailureCount"] intValue];
    self.totalFaceCompareFailureCount = [TKStringHelper isEmpty:self.requestParam[@"totalFaceCompareFailureCount"]] ? 3 : [self.requestParam[@"totalFaceCompareFailureCount"] intValue];
    self.totalNoVoiceFailureCount = [TKStringHelper isEmpty:self.requestParam[@"totalNoVoiceFailureCount"]] ? 3 : [self.requestParam[@"totalNoVoiceFailureCount"] intValue];
    self.totalWrongAnswerFailureCount = [TKStringHelper isEmpty:self.requestParam[@"totalWrongAnswerFailureCount"]] ? 3 : [self.requestParam[@"totalWrongAnswerFailureCount"] intValue];
    self.totalVideoTimeOutFailureCount = [TKStringHelper isEmpty:self.requestParam[@"totalVideoTimeOutFailureCount"]] ? 3 : [self.requestParam[@"totalVideoTimeOutFailureCount"] intValue];
    self.continueOnAnswerTimeout = [[self.requestParam getStringWithKey:@"continueOnAnswerTimeout"] isEqualToString:@"1"] ? YES : NO;
    self.totalFailureCount = [TKStringHelper isEmpty:self.requestParam[@"totalFailureCount"]] ? 9999 : [self.requestParam[@"totalFailureCount"] intValue];
    
    
    
    //商汤活体摄像头是横屏，腾讯活体是竖屏
    self.videoWidth = [self.requestParam[@"videoWidth"] intValue]?[self.requestParam[@"videoWidth"] intValue] : 240;
    self.videoHeight = [self.requestParam[@"videoHeight"] intValue]?[self.requestParam[@"videoHeight"] intValue] : 320;
    
    //朗读场景
    self.shortestTime = [TKStringHelper isEmpty:self.requestParam[@"shortestTime"]] ? 10 : [self.requestParam[@"shortestTime"] intValue];
    self.longestTime = [TKStringHelper isEmpty:self.requestParam[@"longestTime"]] ? 20 : [self.requestParam[@"longestTime"] intValue];
    

    
    // 最小音量调整支持h5控制
    [self volumeChange];
    
    // 初始化数据
    [self initData];
    
    // 添加展示图层
    [self.view addSubview:self.tkOneView];
    self.tkOneView.avPreviewView = self.avPreviewView;
    
    // 启动音视频采集设备
    [self bootDevice:YES];
//    [self testData];
    
    // 横屏时旋转录制页面
    if (self.isLandscape) {
        
        //        TKLogInfo(@"111 isLandscape, self.view.frame = %@", NSStringFromCGRect(self.view.frame));
        
        CGAffineTransform transform = CGAffineTransformMakeRotation((90.0f * M_PI) / 180.0f);
        self.view.transform = transform;
        self.view.frame = CGRectMake(0, 0, self.view.TKWidth, self.view.TKHeight);
        
        //        TKLogInfo(@"111 isLandscape++++, self.view.frame = %@", NSStringFromCGRect(self.view.frame));
        
        //        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.01 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        //            CGAffineTransform transform2 = CGAffineTransformMakeRotation((-90.0f * M_PI) / 180.0f);
        //            self.avPreviewView.transform = transform2;
        //        });
    }
    
    self.recordManager.isFrontCamera=YES;//当前使用前置摄像头
    
    if (self.isLandscape) {
        if([self.requestParam getIntWithKey:@"cameraType"]==2){
            //横屏默认打开后置摄像头
            [self.recordManager tkSwitchVideoCamera:self.recordManager.isFrontCamera];
            self.recordManager.isFrontCamera=!self.recordManager.isFrontCamera;
        }
    }
}

- (void)testData
{
    self.recordStartTime = CFAbsoluteTimeGetCurrent();
    
    CFAbsoluteTime currentTime = CFAbsoluteTimeGetCurrent();
    NSInteger milliseconds = (NSInteger)(currentTime * 1000) - self.recordStartTime * 1000;
    NSTimeInterval unixTimestamp = currentTime + 978307200; // 978307200是2001年1月1日到1970年1月1日的秒数差
    
    TKVideoFragmentModel *model1 = [[TKVideoFragmentModel alloc] init];
//    model1.fragmentType = TKFragmentTypeFaceDetectErrorStart;
//    model1.beginTime = milliseconds + 3000;
//    model1.endTime = milliseconds + 6000;
//    model1.fragmentRemark = @"视频中出现2人";
//    model1.videoStartTime = (self.recordStartTime + 978307200) * 1000; // 978307200是2001年1月1日到1970年1月1日的秒数差
//    [self.fragmentModelList addObject:model1];
    
//    TKVideoFragmentModel *model2 = [[TKVideoFragmentModel alloc] init];
//    model2.fragmentType = TKFragmentTypeFaceDetectErrorStart;
//    model2.beginTime = milliseconds + 9000;
//    model2.endTime = milliseconds + 13000;
//    model2.fragmentRemark = @"视频中出现2人";
//    model2.videoStartTime = (self.recordStartTime + 978307200) * 1000; // 978307200是2001年1月1日到1970年1月1日的秒数差
//    [self.fragmentModelList addObject:model2];
    
    TKVideoFragmentModel *model3 = [[TKVideoFragmentModel alloc] init];
    model3.fragmentType = TKFragmentTypeStartAsr;
    model3.beginTime = milliseconds + 13000;
    model3.endTime = milliseconds + 15000;
    model3.fragmentRemark = @"客户回答 1";
    model3.text = @"客户回答 1";
    model3.videoStartTime = (self.recordStartTime + 978307200) * 1000; // 978307200是2001年1月1日到1970年1月1日的秒数差
    model3.asrStatus = TKAsrStatusSuccess;
    [self.fragmentModelList addObject:model3];
    
    TKVideoFragmentModel *model4 = [[TKVideoFragmentModel alloc] init];
    model4.fragmentType = TKFragmentTypeStartAsr;
    model4.beginTime = milliseconds + 15000;
    model4.endTime = milliseconds + 17000;
    model4.fragmentRemark = @"客户回答 2";
    model4.text = @"客户回答 2";
    model4.videoStartTime = (self.recordStartTime + 978307200) * 1000; // 978307200是2001年1月1日到1970年1月1日的秒数差
    [self.fragmentModelList addObject:model4];
    
//    TKVideoFragmentModel *model5 = [[TKVideoFragmentModel alloc] init];
//    model5.fragmentType = TKFragmentTypePause;
//    model5.beginTime = milliseconds + 18000;
//    model5.endTime = milliseconds + 30000;
//    model5.fragmentRemark = @"展示资料 1";
//    model5.videoStartTime = (self.recordStartTime + 978307200) * 1000; // 978307200是2001年1月1日到1970年1月1日的秒数差
//    [self.fragmentModelList addObject:model5];
//
//    TKVideoFragmentModel *model6 = [[TKVideoFragmentModel alloc] init];
//    model6.fragmentType = TKFragmentTypePause;
//    model6.beginTime = milliseconds + 33000;
//    model6.endTime = milliseconds + 40000;
//    model6.fragmentRemark = @"展示资料 2";
//    model6.videoStartTime = (self.recordStartTime + 978307200) * 1000; // 978307200是2001年1月1日到1970年1月1日的秒数差
//    [self.fragmentModelList addObject:model6];
//
//    TKVideoFragmentModel *model7 = [[TKVideoFragmentModel alloc] init];
//    model7.fragmentType = TKFragmentTypePause;
//    model7.beginTime = milliseconds + 41000;
//    model7.endTime = milliseconds + 44000;
//    model7.fragmentRemark = @"展示资料 3";
//    model7.videoStartTime = (self.recordStartTime + 978307200) * 1000; // 978307200是2001年1月1日到1970年1月1日的秒数差
//    [self.fragmentModelList addObject:model7];
    
//    NSString *fragmentModelListStr = [self getFragmentModelListStr];
//    TKLogInfo(@"fragmentModelListStr = %@", fragmentModelListStr);
    
//    [self recordStopCallBack:@"" fullFilePath:@"https://opt-dev.thinkive.com:15149/auth-common-server/servlet/VideoPlaytAction?function=play&video_path=0/0100010/202304/26/123123/57bb2c8b8ce0e90f1cc7e4c2544ef017-227.mp4&tk-jwt-authorization=Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJzdWIiOiIwMTAwMDEwIiwiaXNzIjoidGhpbmtpdmUiLCJmbG93Tm8iOiIxMjMxMjMiLCJleHAiOjE2OTEwNTU4NDksInVzZXJOYW1lIjoiMTIzMTIzIn0.0IAPdfXuHbV6obWefFQ6U1fqpA_3yrlTJSe_4PppZMA" videoLenth:29 * 1000 catonLength:20 * 1000];
//    [self recordStopCallBack:@"" fullFilePath:@"https://sample-videos.com/video123/mp4/480/big_buck_bunny_480p_2mb.mp4" videoLenth:29 * 1000 catonLength:20 * 1000];
//    [self recordStopCallBack:@"" fullFilePath:[self getLocalOneWayVideoPath] videoLenth:29 * 1000 catonLength:20 * 1000];
    [self recordStopCallBack:@"" fullFilePath:[NSHomeDirectory() stringByAppendingFormat:@"/Documents/thinkive/c_-1_-1_1695110543_0.mp4"] videoLenth:29 * 1000 catonLength:20 * 1000];
    self.isSpeechRecognitionThrough = YES;
    [self jumpToRecordResultPage:YES errorType:TKOneWayVideoEndTypeFaceDetectError errorMsg:@"测试信息"];
    self.oneWayProcess = TKOneWayProcessOneWayDone;
    [self nextOneWayProcess];
//    [self.tkOneEndView updateTipViewWithTipArr:[self.faceDetectManager getFaceDetectTipArray]];

}

- (NSString *)getFragmentModelListStr
{
    NSMutableString *fragmentModelListStr = [NSMutableString string];
    [fragmentModelListStr appendString:@"["];
    
    for (int i = 1; i < self.fragmentModelList.count; i++) {
        TKVideoFragmentModel *model = self.fragmentModelList[i];
        if (i == 1) {
            
            [fragmentModelListStr appendFormat:@"%@,%@", model.beginFrameDescription, model.endFrameDescription];
        } else {
            [fragmentModelListStr appendFormat:@",%@,%@", model.beginFrameDescription, model.endFrameDescription];
        }
    }
    [fragmentModelListStr appendString:@"]"];
    
    return fragmentModelListStr;
}

- (void)updatePlayAndRecordCategory
{
    AVAudioSession *audioSession = [AVAudioSession sharedInstance];
    [[AVAudioSession sharedInstance] setCategory:AVAudioSessionCategoryPlayAndRecord mode:AVAudioSessionModeVideoRecording options:audioSession.categoryOptions | AVAudioSessionCategoryOptionDefaultToSpeaker error:nil];
}

- (BOOL)checkBluetoothHeadphoneCanUse
{
    if (self.isUseTChat == NO && [TKCommonUtil isHeadsetPluggedIn] &&
        ![[self.requestParam getStringWithKey:@"enableHeadphone"] isEqualToString:@"1"]) {
        if (self.oneWayProcess == TKOneWayProcessOneWayDone) { // 预览页面
            TKLogInfo(@"思迪录制日志:预览页面插拔耳机忽略");
        } else {
            
            [self showAlertView:@"提示" message:@"您当前在使用耳机，请移除耳机后重试" tag:7001];
        }
        return NO;
    }
    
    return YES;
}

- (void)viewWillAppear:(BOOL)animated{
    [super viewWillAppear:animated];
    
    // 蓝牙耳机检测
    BOOL canUse = [self checkBluetoothHeadphoneCanUse];
    if (canUse) {
        // 录制
        [self activeDevice];
        
        // 必须在激活设备之后设置
        // 设置录制模式
        [self updatePlayAndRecordCategory];
        
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.2 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            // 支持耳机输出
            if ([[self.requestParam getStringWithKey:@"enableHeadphone"] isEqualToString:@"1"] && self.isUseTChat == NO) {
                
                [TKCommonUtil autoHeadsetState];
            }
        });
    }
}

- (void)viewDidAppear:(BOOL)animated{
    [super viewDidAppear:animated];

    
    [[UIApplication sharedApplication] beginReceivingRemoteControlEvents];
    //监听系统音量
    [[AVAudioSession sharedInstance] addObserver:self forKeyPath:@"outputVolume" options:NSKeyValueObservingOptionNew | NSKeyValueObservingOptionOld context:(void *)[AVAudioSession sharedInstance]];

    //App失去焦点监听
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(enterBackground:) name:UIApplicationWillResignActiveNotification object:nil];
    //耳机状态获取的通知
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(audioRouteChangeListenerCallback:)  name:AVAudioSessionRouteChangeNotification object:nil];

    [self recordLongTime:0 startRecord:NO]; // 显示录制时间
    
    // 在框通过允许录制
//    [self.tkOneView enableTakeRecord:YES];
}


- (void)viewWillDisappear:(BOOL)animated{
    [super viewWillDisappear:animated];
    
    [[AVAudioSession sharedInstance] removeObserver:self forKeyPath:@"outputVolume" context:(void *)[AVAudioSession sharedInstance]];
    [[NSNotificationCenter defaultCenter] removeObserver:self name:AVAudioSessionRouteChangeNotification object:nil];
    [[NSNotificationCenter defaultCenter] removeObserver:self name:UIApplicationWillResignActiveNotification object:nil];
    
    // 停止录制
    [self deactiveDevice];
}

#pragma mark - 框架通知监听
- (NSArray *)listNotificationInter
{
    NSMutableArray *notes = [[super listNotificationInter] mutableCopy];
    [notes addObject:NOTE_NETWORK_CHANGE];//监听网络变换
    return notes;
}

- (void)handleNotification:(NSNotification *)notification
{
    if ([notification.name isEqualToString:NOTE_NETWORK_CHANGE])
    {
        [self checkNetwork];
    }
    else
    {
        [super handleNotification:notification];
    }
}

- (void)checkNetwork {
    
    Network_Type networkType=[TKNetHelper getNetworkType];
    //断网或者2g退出，将错误信息给h5
    if (![TKNetHelper isConnectionAvailable] || networkType==Network_2G) {
        
        // 需要延迟，防止刚进来时，页面还没有present
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.3 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            [self failActionCallJsWith:@"-9" errorMsg:@"网络不稳定，请切换网络再试"];
        });
    }
}

#pragma mark - 事件方法

- (void)initData
{
    // 转换字典->模型
    if ([self.requestParam[@"beforeVideoArray"] isKindOfClass:NSString.class]) {
        self.beforeVideoArray = [TKSmartQuestionModel convertDicArrToModels:[TKDataHelper jsonToArray:self.requestParam[@"beforeVideoArray"]]];
    } else {
        self.beforeVideoArray = [TKSmartQuestionModel convertDicArrToModels:self.requestParam[@"beforeVideoArray"]];
    }
    
    if ([self.requestParam[@"questionArray"] isKindOfClass:NSString.class]) {
        self.questionArray = [TKSmartQuestionModel convertDicArrToModels:[TKDataHelper jsonToArray:self.requestParam[@"questionArray"]]];
    } else {
        self.questionArray = [TKSmartQuestionModel convertDicArrToModels:self.requestParam[@"questionArray"]];
    }
    
    if ([self.requestParam[@"afterVideoArray"] isKindOfClass:NSString.class]) {
        self.afterVideoArray = [TKSmartQuestionModel convertDicArrToModels:[TKDataHelper jsonToArray:self.requestParam[@"afterVideoArray"]]];
    } else {
        self.afterVideoArray = [TKSmartQuestionModel convertDicArrToModels:self.requestParam[@"afterVideoArray"]];
    }
    
    if(self.isReadVideo){
        [self getReadVideoVideoArray];
    }
    
//    self.beforeVideoArray = [TKSmartQuestionModel convertDicArrToModels:self.requestParam[@"beforeVideoArray"]];
//    self.questionArray = [TKSmartQuestionModel convertDicArrToModels:self.requestParam[@"questionArray"]];
//    self.afterVideoArray = [TKSmartQuestionModel convertDicArrToModels:self.requestParam[@"afterVideoArray"]];
}

//朗读模式创建beforeVideoArray数据只要fileFlag和tipContent
-(void)getReadVideoVideoArray{
    //  提示语音
    NSMutableArray *beforeVideoArray = [[NSMutableArray alloc] init];
    NSMutableDictionary *beforeVideoDic1 = [[NSMutableDictionary alloc] init];
    //准备话术语音
    beforeVideoDic1[@"fileFlag"]=@"2";
    beforeVideoDic1[@"tipContent"]=@"请您保持全脸在人相框内";
    [beforeVideoArray addObject:beforeVideoDic1];
    self.beforeVideoArray=[TKSmartQuestionModel convertDicArrToModels:beforeVideoArray];

    NSMutableArray *questionArray = [[NSMutableArray alloc] init];
    NSMutableDictionary *questionDic1 = [[NSMutableDictionary alloc] init];
    //开始录制问题
    questionDic1[@"tipContent"]=self.requestParam[@"readString"];
    if ([TKStringHelper isNotEmpty:self.requestParam[@"readHtmlString"]]) {
        questionDic1[@"tipContent"]=self.requestParam[@"readHtmlString"];
    }
    if ([TKStringHelper isNotEmpty:self.requestParam[@"longReadString"]]) {
        questionDic1[@"tipContent"]=self.requestParam[@"longReadString"];
    }
    [questionArray addObject:questionDic1];
    self.questionArray = [TKSmartQuestionModel convertDicArrToModels:questionArray];
}

/**
 *  <AUTHOR> 2018-06-09 15:13:50
 *  监听耳机是否插入
 *  @param notif
 */
- (void)audioRouteChangeListenerCallback:(NSNotification *)notif{
    
//    TKLogDebug(@"111 蓝牙耳机是否可用 %i", [self isBluetoothAudioRouteAvailable]);
    
    // 蓝牙耳机检测
    [self checkBluetoothHeadphoneCanUse];
    
    if (self.isUseTChat == NO &&
        [[self.requestParam getStringWithKey:@"enableHeadphone"] isEqualToString:@"1"]) {
        NSDictionary *userInfo = notif.userInfo;
        NSInteger routeChangeReason   = [[userInfo
                 valueForKey:AVAudioSessionRouteChangeReasonKey] integerValue];
        AVAudioSessionRouteDescription *previousRoute = userInfo[AVAudioSessionRouteChangePreviousRouteKey];
        if (routeChangeReason == AVAudioSessionRouteChangeReasonNewDeviceAvailable) {

    //        TKLogDebug(@"111 AVAudioSessionRouteChangeReasonNewDeviceAvailable");
            if ([self isBluetoothAudioRouteAvailable] && self.isUseTChat == NO) {
    //            TKLogDebug(@"111 插入蓝牙耳机");

                [TKCommonUtil autoHeadsetState];
            }

            // 耳机断开连接或切换
            [self checkBluetoothWasUsedWhenRecording:previousRoute];
        } else if (routeChangeReason == AVAudioSessionRouteChangeReasonOldDeviceUnavailable) {
    //        TKLogDebug(@"111 AVAudioSessionRouteChangeReasonOldDeviceUnavailable");
            
            // 耳机断开连接或切换
            [self checkBluetoothWasUsedWhenRecording:previousRoute];
        } else if (routeChangeReason == AVAudioSessionRouteChangeReasonCategoryChange) {
    //        TKLogDebug(@"111 AVAudioSessionRouteChangeReasonCategoryChange");
            
            // 耳机断开连接或切换
            [self checkBluetoothWasUsedWhenRecording:previousRoute];
        } else if (routeChangeReason == AVAudioSessionRouteChangeReasonOverride) {
    //        TKLogDebug(@"111 AVAudioSessionRouteChangeReasonOverride");
            
            // 耳机断开连接或切换
            [self checkBluetoothWasUsedWhenRecording:previousRoute];
        }
    //    else if (routeChangeReason == AVAudioSessionRouteChangeReasonWakeFromSleep) {
    //        TKLogDebug(@"111 AVAudioSessionRouteChangeReasonWakeFromSleep");
    //
    //    } else if (routeChangeReason == AVAudioSessionRouteChangeReasonNoSuitableRouteForCategory) {
    //        TKLogDebug(@"111 AVAudioSessionRouteChangeReasonNoSuitableRouteForCategory");
    //
    //    } else if (routeChangeReason == AVAudioSessionRouteChangeReasonRouteConfigurationChange) {
    //        TKLogDebug(@"111 AVAudioSessionRouteChangeReasonRouteConfigurationChange");
    //
    //    }
    }
    
    if (_player && _player.isPlaying) {
        //视频回放中插拔耳机行为都暂停播放
        dispatch_async(dispatch_get_main_queue(), ^{
            
            [self.player pause];
        });
    }
}


- (BOOL)isBluetoothAudioRouteAvailable {
    AVAudioSession *audioSession = [AVAudioSession sharedInstance];
    AVAudioSessionRouteDescription *currentRoute = audioSession.currentRoute;

    for (AVAudioSessionPortDescription *outputPort in currentRoute.outputs) {
        if ([outputPort.portType isEqualToString:AVAudioSessionPortBluetoothA2DP] ||
            [outputPort.portType isEqualToString:AVAudioSessionPortBluetoothLE] ||
            [outputPort.portType isEqualToString:AVAudioSessionPortBluetoothHFP]) {
            return YES; // 蓝牙耳机接入
        }
    }

    return NO; // 没有蓝牙耳机接入
}


- (void)checkBluetoothWasUsedWhenRecording:(AVAudioSessionRouteDescription *)previousRoute
{
    // 耳机断开连接或切换
    AVAudioSessionPortDescription *previousOutput = previousRoute.outputs.firstObject;

    if ([previousOutput.portType isEqualToString:AVAudioSessionPortBluetoothA2DP] ||
        [previousOutput.portType isEqualToString:AVAudioSessionPortBluetoothLE] ||
        [previousOutput.portType isEqualToString:AVAudioSessionPortBluetoothHFP]) {
        
//        TKLogDebug(@"111 拔掉蓝牙耳机");
        
//        AVAudioSession *session = [AVAudioSession sharedInstance];
//        AVAudioSessionCategoryOptions options = session.categoryOptions;
//        options = options ^ AVAudioSessionCategoryOptionAllowBluetoothA2DP ^ AVAudioSessionCategoryOptionAllowBluetooth;
//        [[AVAudioSession sharedInstance] setCategory:session.category  withOptions:options error:nil];
//        [session setActive:YES error:nil];
        
        if (self.isRecording) {
            [self showAlertView:@"提示" message:@"视频过程中请勿切换蓝牙耳机" tag:7001];
        }
    } else {
        if ([self isBluetoothAudioRouteAvailable] && self.isRecording) {
//            TKLogDebug(@"111 插入蓝牙耳机");

            [self showAlertView:@"提示" message:@"视频过程中请勿切换蓝牙耳机" tag:7001];
        }
    }
}

     
/**
 *  <AUTHOR> 2018-06-09 15:13:50
 *  标记App进入到后台
 *  @param notif
 */
-(void)enterBackground:(NSNotification *)notif{
    //    [self showAlertView:@"提示" message:@"视频过程中请勿切换App或锁屏" tag:7001];
    if (self.isRecording) {
        [self failActionCallJsWith:@"-3" errorMsg:@"视频过程中请勿切换App或锁屏"];
    }
    
    if(self.player.isPlaying){
        [self.player pause];
    }
}

-(void)handleInvalidTokenNoti:(NSNotification *)notif{
    
    [self failActionCallJsWith:@"-10" errorMsg:@"token已失效，请重新登录"];
}

- (void)volumeChange{
    AVAudioSession *audioSession = [AVAudioSession sharedInstance];
    
    CGFloat volume = audioSession.outputVolume;
    
    //最小音量调整支持h5控制
    float deviceMinVolume=TKSmartOpenVolume;
    if (self.requestParam[@"deviceMinVolume"]) {
        deviceMinVolume=[self.requestParam[@"deviceMinVolume"] intValue]/100.00f;
    }
    if ((volume+0.05)<deviceMinVolume) {
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.3 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            
            MPMusicPlayerController *mp = [MPMusicPlayerController applicationMusicPlayer];
            mp.volume = deviceMinVolume;//0为最小1为最大
        });
        
        
        //        [self showAlertView:@"提示" message:[NSString stringWithFormat:@"请调大多媒体音量至少至%.0f%@后重试",100*TKSmartOpenVolume,@"%"] tag:7001];
    }
    
}


/**
 <AUTHOR> 2019年05月26日12:41:59
 @失败次数超过指定次数就退出给h5处理页面
 */
- (void)failActionCallJsWith:(NSString *)errorNo errorMsg:(NSString *)errorMsg
{
    [self exitProcess];
    
    // 埋点-单向_结果_失败 | 埋点-单向_结果_取消 | 埋点-单向_结果_异常
    NSMutableDictionary *eventDic = [NSMutableDictionary dictionaryWithDictionary:self.requestParam];
    eventDic[@"errorNo"] = errorNo;
    eventDic[@"event_err"] = errorMsg;
    eventDic[@"costTime"] = [NSString stringWithFormat:@"%.0f", ([[NSDate date] timeIntervalSince1970] - self.statisticEventStartTime) * 1000] ; // 单位毫秒
    eventDic[@"isEndRecord"] = [NSString stringWithFormat:@"%i", self.isEndRecord];
    eventDic[@"recordSuccess"] = [NSString stringWithFormat:@"%i", self.isSpeechRecognitionThrough];
    TKPrivateEventResult result = TKPrivateEventResultFail;
    result = [errorNo isEqualToString:@"-1"] ? TKPrivateEventResultCancel : result;
    result = [errorNo isEqualToString:@"-2"] ? TKPrivateEventResultError : result;
    TKPrivateOneWayVideoType oneWayVideoType = (TKPrivateOneWayVideoType)[self.pluginCallBackfuncNoToTKPrivateOneWayVideoType[self.pluginCallBackfuncNo] intValue];
    [TKStatisticEventHelper sendEvent:TKPrivateEventOneWayVideo
                         subEventName:TKPrivateSubEventNone
                             progress:TKPrivateEventProgressEnd
                               result:result
                          orientation:self.isLandscape ? TKPrivateVideoOrientationLandscape : TKPrivateVideoOrientationPortrait
                      oneWayVideoType:oneWayVideoType
                 prepareVideoProgress:TKPrivatePrepareVideoProgressNone
                             eventDic:eventDic];
    
    NSMutableDictionary *callJsParam=[NSMutableDictionary dictionary];
    callJsParam[@"funcNo"] = [self pluginCallBackfuncNo];
    callJsParam[@"error_no"] = errorNo;
    callJsParam[@"error_info"] = errorMsg;
    [callJsParam setObject:[TKStringHelper isNotEmpty:self.requestParam[@"tkuuid"]] ? self.requestParam[@"tkuuid"] : @"" forKey:@"tkuuid"];
    
    [self sendCallBack:callJsParam];
}

- (void)sendCallBack:(NSMutableDictionary *)callJsParam
{
    [self.openAccountService iosCallJsWithDic:callJsParam callBackFunc:nil];
}



/**
 <AUTHOR> 2019年04月08日19:31:25
 @重新开始单向视频流程
 */
- (void)restartOneVideo {
    self.statusBarStyle = UIStatusBarStyleLightContent;
    TKLogInfo(@"思迪录制日志:重新开始单向视频流程");
    [self.recordManager tkSwitchVideoCamera:NO];
    self.recordManager.isFrontCamera=YES;
    if (self.isLandscape) {
        if([self.requestParam getIntWithKey:@"cameraType"]==2){
            //横屏默认打开后置摄像头
            [self.recordManager tkSwitchVideoCamera:self.recordManager.isFrontCamera];
            self.recordManager.isFrontCamera=!self.recordManager.isFrontCamera;
        }
    }
    
    // 停止播放
    [self.player stop];
    
    // 删除原有的视频。这是防止异常退出没有清除视频
    [self deleteLocalVideoFile:[self getLocalOneWayVideoPath]];
    
    // 录制闲置
    self.oneWayProcess = TKOneWayProcessIdle;
    [self nextOneWayProcess];
    
    // 最小音量调整支持h5控制
    [self volumeChange];
    
    // 重置录制信息
    self.videoLength = nil;
    self.forcedEndOneWayVideo = NO;
	self.isEndRecord = NO;
    self.fragmentModelList = nil;
    self.currentBusinessFragmentModel = nil;
    self.currentFaceErrorFragmentModel = nil;
    self.currentAsrFragmentIndex = 0;
    self.currentPauseFragmentIndex = 0;
    self.isClickTakeRecordBtn = NO;
    self.isInitAutoRecordTimer = NO;

    // 初始化数据
    [self initData];
    
    [self handleWaitTip];
    
    // 埋点-单向_开始
    self.statisticEventStartTime = [[NSDate date] timeIntervalSince1970];
    NSMutableDictionary *eventDic = [NSMutableDictionary dictionaryWithDictionary:self.requestParam];
    TKPrivateOneWayVideoType oneWayVideoType = (TKPrivateOneWayVideoType)[self.pluginCallBackfuncNoToTKPrivateOneWayVideoType[self.pluginCallBackfuncNo] intValue];
    [TKStatisticEventHelper sendEvent:TKPrivateEventOneWayVideo
                         subEventName:TKPrivateSubEventStart
                             progress:TKPrivateEventProgressNone
                               result:TKPrivateEventResultNone
                          orientation:self.isLandscape ? TKPrivateVideoOrientationLandscape : TKPrivateVideoOrientationPortrait
                      oneWayVideoType:oneWayVideoType
                 prepareVideoProgress:TKPrivatePrepareVideoProgressNone
                             eventDic:eventDic];
    
    // 重置UI
    [self.view addSubview:self.tkOneView];
    self.tkOneView.avPreviewView = self.avPreviewView;
    [self.tkOneEndView removeFromSuperview];
    self.tkOneEndView = nil;
    [self.playerControlView removeFromSuperview];
    self.playerControlView = nil;
    self.player = nil;
    self.playerController = nil;
    
    // 蓝牙耳机检测
    BOOL canUse = [self checkBluetoothHeadphoneCanUse];
    if (canUse) {
        
        [self hideTestRoomIDLabel];
        [self recordLongTime:0 startRecord:NO]; // 显示录制时间
        // 不可点击
        [self.tkOneView enableTakeRecord:NO];
        
        // 重新连接视频服务器
        [self bootDevice:NO];
        
        //    // 在框通过允许录制
        //    [self.tkOneView enableTakeRecord:YES];
        
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.2 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            // 支持耳机输出
            if ([[self.requestParam getStringWithKey:@"enableHeadphone"] isEqualToString:@"1"] && self.isUseTChat == NO) {
                
                [TKCommonUtil autoHeadsetState];
            }
        });
    }
}

- (void)jumpToRecordResultPage:(BOOL)isSuccess errorType:(TKOneWayVideoEndType)errorType errorMsg:(NSString *)errorMsg
{
    self.isEndRecord = YES;
    
    // 埋点-单向_视频预览
    NSMutableDictionary *eventDic = [NSMutableDictionary dictionaryWithDictionary:self.requestParam];
    eventDic[@"recordSuccess"] = [NSString stringWithFormat:@"%i", isSuccess];
    switch (errorType) {
        case TKOneWayVideoEndTypeNormal:
            eventDic[@"errorNo"] = @"0";
            break;
        case TKOneWayVideoEndTypeFaceDetectError:
            eventDic[@"errorNo"] = @"-4";
            break;
        case TKOneWayVideoEndTypeFaceCompareError:
            eventDic[@"errorNo"] = @"-5";
            break;
        case TKOneWayVideoEndTypeNoVoiceError:
            eventDic[@"errorNo"] = @"-6";
            break;
        case TKOneWayVideoEndTypeWrongAnswerError:
            eventDic[@"errorNo"] = @"-7";
            break;
            
        default:
            break;
    }
    eventDic[@"isEndRecord"] = [NSString stringWithFormat:@"%i", self.isEndRecord];
    eventDic[@"event_err"] = [TKStringHelper isNotEmpty:errorMsg] ? errorMsg : @"";
    eventDic[@"costTime"] = [NSString stringWithFormat:@"%.0f", ([[NSDate date] timeIntervalSince1970] - self.statisticEventStartTime) * 1000] ; // 单位毫秒
    TKPrivateOneWayVideoType oneWayVideoType = (TKPrivateOneWayVideoType)[self.pluginCallBackfuncNoToTKPrivateOneWayVideoType[self.pluginCallBackfuncNo] intValue];
    [TKStatisticEventHelper sendEvent:TKPrivateEventOneWayVideo
                         subEventName:TKPrivateSubEventOneWayVideoPreview
                             progress:TKPrivateEventProgressNone
                               result:TKPrivateEventResultNone
                          orientation:self.isLandscape ? TKPrivateVideoOrientationLandscape : TKPrivateVideoOrientationPortrait
                      oneWayVideoType:oneWayVideoType
                 prepareVideoProgress:TKPrivatePrepareVideoProgressNone
                             eventDic:eventDic];
    
    if (self.isSpeechRecognitionThrough) {
        
        [self.tkOneEndView updateTipViewWithTipArr:[self.faceDetectManager getFaceDetectTipArray]];
        
        // 播放音频
        [self previewVideo];
        
        self.tkOneEndView.videoShowImg = self.videoFirstImage;
        [self.playerControlView showTitle:@"" coverImage:self.videoFirstImage fullScreenMode:TKZFFullScreenModePortrait];
        [self.playerControlView.portraitControlView playBtnSelectedState:NO];   // 默认播放，这里要修改为暂停状态
        [self.playerControlView.landScapeControlView playBtnSelectedState:NO];// 默认播放，这里要修改为暂停状态
        
        [self animateToResultPage];
    }else{
        
        [self animateToResultPage];
    }
}

- (void)previewVideo
{
    // 这里有个坑，需要先updateTipViewWithTipArr再加载url。因为结果页播放图层提前加载，updateTipViewWithTipArr会移除掉播放图层
    // 视频录制结束才抓取url图片
//        self.player.contentView = self.tkOneEndView.videoShowImgView; // 每次重新录制都是新创建view.因此要重新设置
    
    if (self.isLocalRecord) {
//            self.player.assetURL = [NSURL fileURLWithPath:[self fullVideoFileURLString]];
        self.playerController.assetURL = [NSURL fileURLWithPath:[self fullVideoFileURLString]];
    } else {
//            self.player.assetURL = [NSURL URLWithString:[self fullVideoFileURLString]];
        self.playerController.assetURL = [NSURL URLWithString:[self fullVideoFileURLString]];
    }

    // 过滤掉播报模型
    NSMutableArray *tempArray = [NSMutableArray array];
    for (TKVideoFragmentModel *model in self.fragmentModelList) {
       if (model.fragmentType != TKFragmentTypeTTSStart) {
           [tempArray addObject:model];
       }
    }
    self.playerControlView.portraitControlView.fragmentModelList = self.fragmentModelList;
    self.playerControlView.landScapeControlView.fragmentModelList = self.fragmentModelList; // 设置分段播放列表
}

- (void)animateToResultPage
{
    [self.tkOneEndView setFrameX:self.view.frame.size.width];
    [self.view addSubview:self.tkOneEndView];
    
    [UIView animateWithDuration:0.3f animations:^{
        [self.tkOneEndView setFrameX:self.view.frame.origin.x];
    } completion:^(BOOL finished) {
        self.statusBarStyle = TKUIStatusBarStyleDefault;
    }];
    
    //    if (self.isLandscape) {
    //        CGAffineTransform transform = CGAffineTransformMakeRotation((90.0f * M_PI) / 180.0f);
    //
    //        self.tkOneEndView.transform = transform;
    //        self.tkOneEndView.frame = CGRectMake(0, 0, self.tkOneEndView.TKWidth, self.tkOneEndView.TKHeight);
    //
    ////        self.tkOneEndView.frame = CGRectMake(self.view.TKLeft, self.view.TKTop, MAX(UISCREEN_WIDTH, UISCREEN_HEIGHT), MIN(UISCREEN_WIDTH, UISCREEN_HEIGHT));
    //    }
}

- (void)hideTestRoomIDLabel
{
    // 测试代码
    NSString *sessionUrl = [self.requestParam getStringWithKey:@"_sessionUrl"];
    if ([TKStringHelper isNotEmpty:sessionUrl]) {
        
        UILabel *roomIDLabel = (UILabel *)[[UIApplication sharedApplication].keyWindow viewWithTag:888888];
        if (roomIDLabel != nil) {
            roomIDLabel.hidden = YES;
            //            roomIDLabel.textColor = [TKUIHelper colorWithHexString:isDark ? @"#333333" : @"#ffffff"];
            [roomIDLabel.superview bringSubviewToFront:roomIDLabel];
        }
    }
}

/**
 <AUTHOR> 2019年04月15日15:59:58
 @退出界面流程
 */
- (void)exitProcess {
    TKLogInfo(@"思迪录制日志:退出界面流程");
    self.isExitProgress = YES;
    
    // 停止视频预览
    if (_player) {
        [self.player stop];
    }
    
    // 非本地录制，删除原有的视频;本地录制不能删除，返回地址供外层下载的逻辑，删除了文件，外层没法下载
    if (!([self.requestParam getIntWithKey:@"resultType"] == 2))
        [self deleteLocalVideoFile:[self getLocalOneWayVideoPath]];
    
    [self stopTTSAndAsr];
    [self stopRecordingAction];
    [self.tkOneView playEndVoiceView];
    
    if (self.isAlertViewShow) {
        self.isAlertViewShow = NO;
        [self.videoAlertView removeFromSuperview];  // 防止退出录制的时候弹窗没有收起
    }
    
    // 主动取消或者异常退出都要重置该标志，否则会一直调用时间更新方法
    // 结束录制的回调也会设置该值，只是回调时间会比较慢。关闭TChat并释放资源的时候，有可能停止录制的回调没有返回，导致该值没有设置
    self.isRecording = NO;
    
    //关闭屏幕常亮
    [UIApplication sharedApplication].idleTimerDisabled = NO;
    
    // 关闭人脸检测
    [self.faceDetectManager stopFaceDetect];
    
    [self stopDevice:YES];
    
    // 重置播放模式
    [[AVAudioSession sharedInstance] setCategory:_originCategory withOptions:_originOption error:nil];
    
    if ([self.requestParam[@"isPush"] intValue]==1) {
        [self.navigationController popViewControllerAnimated:YES];
    }else{
        [self dismissViewControllerAnimated:YES completion:nil];
    }
    
}


/**
 <AUTHOR> 2019年05月24日13:15:37
 @弹出提示窗
 */
- (void)showAlertView:(NSString *)title message:(NSString *)message tag:(NSInteger)tag{
    dispatch_async(dispatch_get_main_queue(), ^{
        TKLogInfo(@"思迪录制日志:isShow:%d, 弹窗内容 = %@",self.isTKShow, message);
        if (self.isTKShow) {
            if (!self.isAlertViewShow) {
                
                [self stopTTSAndAsr];
                [self stopRecordingAction];
                [self.faceDetectManager stopFaceDetect];
                [self stopDevice:NO];
                
                self.isAlertViewShow = YES;
                
                // 不直接添加到window上是因为要支持竖屏模式下的横屏展示（实际上是将subview做了选择展示）
                if (self.oneWayProcess == TKOneWayProcessOneWayDone) { // 预览页面
                    [self.tkOneEndView addSubview:self.videoAlertView];
                } else {    // 录制页面
                    [self.tkOneView addSubview:self.videoAlertView];
                }
                self.videoAlertView.describeLabel.text=message;
                self.videoAlertView.titleLabel.text=title;
                [self.videoAlertView.cancelBtn setTitle:@"取消" forState:UIControlStateNormal];
                [self.videoAlertView.takeBtn setTitle:@"重试" forState:UIControlStateNormal];
                
                //                if (self.isLandscape) {
                //                    CGAffineTransform transform = CGAffineTransformMakeRotation((0.0f * M_PI) / 180.0f);
                //                    self.videoAlertView.transform = transform;
                //                    self.videoAlertView.frame = CGRectMake(0, 0, self.videoAlertView.TKWidth, self.videoAlertView.TKHeight);
                //                }
            }
        }
    });
}


/// 失败次数超限
/// @param oneWayVideoEndType 错误类型
/// @param errorMsg 错误信息
- (void)videoFailureDidExceedLimit:(TKOneWayVideoEndType)oneWayVideoEndType {
    
    TKSmartQuestionModel *model = nil;
    
    NSDictionary *errorTipJson = nil;
    if ([self.requestParam[@"errorTipJson"] isKindOfClass:NSString.class]) {
        errorTipJson = [TKDataHelper jsonToDictionary:self.requestParam[@"errorTipJson"]];
    } else {
        errorTipJson = self.requestParam[@"errorTipJson"];
    }
    if ([errorTipJson isKindOfClass:NSDictionary.class] && errorTipJson.allKeys.count > 0) {
        // 处理报错逻辑
        if (oneWayVideoEndType == TKOneWayVideoEndTypeFaceDetectError) {
            
            model = [[TKSmartQuestionModel alloc] initWithDic:errorTipJson[@"overScreenCountTip"]];
            
        } else if (oneWayVideoEndType == TKOneWayVideoEndTypeFaceCompareError) {
            
            model = [[TKSmartQuestionModel alloc] initWithDic:errorTipJson[@"overCompareCountTip"]];
            //        model = [[TKSmartQuestionModel alloc] initWithDic:errorTipJson[@"overPresonCountTip"]];
        } else if (oneWayVideoEndType == TKOneWayVideoEndTypeNoVoiceError) {
            // 多次没有回答或识别不通过
            model = [[TKSmartQuestionModel alloc] initWithDic:errorTipJson[@"overNoVoiceCountTip"]];
            
        }  else if (oneWayVideoEndType == TKOneWayVideoEndTypeFaceNumberError) {
            // 多人脸
            model = [[TKSmartQuestionModel alloc] initWithDic:errorTipJson[@"overPresonCountTip"]];
            
        } else if (oneWayVideoEndType == TKOneWayVideoEndTypeWrongAnswerError) {
            
             model = self.currentModel.errorTipModel;
        }
    } else {
        TKSmartErrorPageTipModel *errorPageTipModel=[self getCurrentTKSmartErrorPageTipModel:oneWayVideoEndType];
        // 无须展示和播报错误结果，直接跳转到失败页面
        [self stopAndJumpToFailurePage:oneWayVideoEndType errorMsg:errorPageTipModel.errorMsg errorTitle:errorPageTipModel.errorTitle];
        return;
    }
    
    TKLogInfo(@"思迪录制日志：录制出错中断流程，即将展示的文案 = %@， 上一个播放的文案 = %@", model.displayTip, self.currentModel.displayTip);
    
    // model != nil 这是针对错误回答的一个取巧判断。正常流程的Model都不会是这种错误的model。displayTip会不一样。此时要切换播放的Model。不用这个判断，就要记录错误model已经播放过，再退出录制
    // TKOneWayVideoEndTypeWrongAnswerError的时候不需要播放model。此时model为nil。此时直接退出录制
    @synchronized (self) {
        if (![model.displayTip isEqualToString:self.currentModel.displayTip] && model != nil) {
            
            self.currentModel = model;
            self.playQuessionProcess = TKPlayQuessionProcessStart;
            [self nextPlayQuestionProcess];
            
            TKLogInfo(@"思迪录制日志：准备错误语音播报 self.playQuessionProcess = %i", self.playQuessionProcess);
        } else {
            
            // 不用model.displayTip而是用model.tipContent，是因为结果页面不支持富文本和多音字的展示
            // self.playQuessionProcess == TKPlayQuessionProcessIdle判断的原因是因为人脸超限做错误语音播报的同时，正好正常流程的语音播报结束，重复调用了nextOneWayProcess，导致这个方法进来了两次，刚准备播放错误语音的时候就马上跳转到失败页面了，因此要加一个拦截判断
            TKLogInfo(@"思迪录制日志：准备跳转错误页面 self.playQuessionProcess = %i", self.playQuessionProcess);
            
            
            TKSmartErrorPageTipModel *errorPageTipModel=[self getCurrentTKSmartErrorPageTipModel:oneWayVideoEndType];
            
            
            if (self.playQuessionProcess == TKPlayQuessionProcessIdle) {
                TKLogInfo(@"思迪录制日志：跳转错误页面 self.playQuessionProcess = %i", self.playQuessionProcess);
                [self stopAndJumpToFailurePage:oneWayVideoEndType errorMsg:[TKStringHelper isEmpty:errorPageTipModel.errorMsg]?self.currentModel.tipContent:errorPageTipModel.errorMsg errorTitle:errorPageTipModel.errorTitle];
            }
        }
    }
}

//获取跳转错误页面数据
-(TKSmartErrorPageTipModel *)getCurrentTKSmartErrorPageTipModel:(TKOneWayVideoEndType)oneWayVideoEndType{
    NSDictionary *errorPageTipJson = nil;
    if ([self.requestParam[@"errorPageTipJson"] isKindOfClass:NSString.class]) {
        errorPageTipJson = [TKDataHelper jsonToDictionary:self.requestParam[@"errorPageTipJson"]];
    } else {
        errorPageTipJson = self.requestParam[@"errorPageTipJson"];
    }
    
    TKSmartErrorPageTipModel *errorPageTipModel=nil;
    if ([errorPageTipJson isKindOfClass:NSDictionary.class] && errorPageTipJson.allKeys.count > 0) {
        // 处理报错逻辑
        if (oneWayVideoEndType == TKOneWayVideoEndTypeFaceDetectError) {
            errorPageTipModel = [[TKSmartErrorPageTipModel alloc] initWithDic:errorPageTipJson[@"faceDetectFailure"]];
        } else if (oneWayVideoEndType == TKOneWayVideoEndTypeFaceCompareError) {
            errorPageTipModel = [[TKSmartErrorPageTipModel alloc] initWithDic:errorPageTipJson[@"faceCompareFailure"]];
        } else if (oneWayVideoEndType == TKOneWayVideoEndTypeNoVoiceError) {
            errorPageTipModel = [[TKSmartErrorPageTipModel alloc] initWithDic:errorPageTipJson[@"noVoiceFailure"]];
        }  else if (oneWayVideoEndType == TKOneWayVideoEndTypeFaceNumberError) {
            errorPageTipModel = [[TKSmartErrorPageTipModel alloc] initWithDic:errorPageTipJson[@"faceNumberFailure"]];
        } else if (oneWayVideoEndType == TKOneWayVideoEndTypeWrongAnswerError) {
            errorPageTipModel = [[TKSmartErrorPageTipModel alloc] initWithDic:errorPageTipJson[@"wrongAnswerFailure"]];
        }
    }
    return errorPageTipModel;
}

- (void)stopAndJumpToFailurePage:(TKOneWayVideoEndType)oneWayVideoEndType errorMsg:(NSString *)errorMsg errorTitle:(NSString *)errorTitle
{
    if (self.forcedEndOneWayVideo == YES) {
        // 拦截重复跳转
        return;
    }
    self.forcedEndOneWayVideo = YES;
    
    //错误标题
    
    if ([TKStringHelper isEmpty:errorTitle]) {
        if (oneWayVideoEndType == TKOneWayVideoEndTypeFaceDetectError) {
            self.tkOneEndView.errorTitle = @"未检测到面部在框";
        } else if (oneWayVideoEndType == TKOneWayVideoEndTypeFaceCompareError) {
            self.tkOneEndView.errorTitle = @"人脸识别不通过";
        } else if (oneWayVideoEndType == TKOneWayVideoEndTypeNoVoiceError) {
            self.tkOneEndView.errorTitle = @"回答不通过";
        } else if (oneWayVideoEndType == TKOneWayVideoEndTypeWrongAnswerError) {
            self.tkOneEndView.errorTitle = @"回答不通过";
        }else if (oneWayVideoEndType == TKOneWayVideoEndTypeFaceNumberError) {
            self.tkOneEndView.errorTitle = @"视频出现其他人";
        }
    } else {
        self.tkOneEndView.errorTitle = errorTitle;
    }
    
    //错误内容
    NSString *faceDetectErrorMsg = [TKStringHelper isNotEmpty:errorMsg] ? errorMsg : @"由于长时间未检测到面部在框，本次视频录制失败，请重新录制。";
    NSString *faceCompareErrorMsg = [TKStringHelper isNotEmpty:errorMsg] ? errorMsg :@"人脸比对不通过，请确保为本人录制，本次视频录制失败，请重新录制。";
    NSString *noVoiceErrorMsg = [TKStringHelper isNotEmpty:errorMsg] ? errorMsg :@"回答不通过，本次视频见证失败，您可重新发起录制。";
    NSString *wrongAnswerErrorMsg = [TKStringHelper isNotEmpty:errorMsg] ? errorMsg :@"回答不通过，本次视频见证失败，您可重新发起录制。";
    NSString *faceNumberErrorMsg = [TKStringHelper isNotEmpty:errorMsg] ? errorMsg : @"由于视频中出现其他人，本次视频录制失败，请重新录制。";
    
    if ([TKStringHelper isEmpty:errorMsg]) {
        if (oneWayVideoEndType == TKOneWayVideoEndTypeFaceDetectError) {
            self.tkOneEndView.failureString = faceDetectErrorMsg;
        } else if (oneWayVideoEndType == TKOneWayVideoEndTypeFaceCompareError) {
            self.tkOneEndView.failureString = faceCompareErrorMsg;
        } else if (oneWayVideoEndType == TKOneWayVideoEndTypeNoVoiceError) {
            self.tkOneEndView.failureString = noVoiceErrorMsg;
        } else if (oneWayVideoEndType == TKOneWayVideoEndTypeWrongAnswerError) {
            self.tkOneEndView.failureString = wrongAnswerErrorMsg;
        }else if (oneWayVideoEndType == TKOneWayVideoEndTypeFaceNumberError) {
            self.tkOneEndView.failureString = faceNumberErrorMsg;
        }
    } else {
        self.tkOneEndView.failureString = errorMsg;
    }
    
    self.tkOneEndView.endType = oneWayVideoEndType;
    
    NSString *isRejectToH5=[NSString stringWithFormat:@"%@",self.requestParam[@"isRejectToH5"]];
    if ([isRejectToH5 isEqualToString:@"1"]) {
        
        // h5处理报错逻辑
        if (oneWayVideoEndType == TKOneWayVideoEndTypeFaceDetectError) {
            [self failActionCallJsWith:@"-4" errorMsg:faceDetectErrorMsg];
        } else if (oneWayVideoEndType == TKOneWayVideoEndTypeFaceCompareError) {
            [self failActionCallJsWith:@"-5" errorMsg:faceCompareErrorMsg];
        } else if (oneWayVideoEndType == TKOneWayVideoEndTypeNoVoiceError) {
            [self failActionCallJsWith:@"-6" errorMsg:noVoiceErrorMsg];
        } else if (oneWayVideoEndType == TKOneWayVideoEndTypeWrongAnswerError) {
            [self failActionCallJsWith:@"-7" errorMsg:wrongAnswerErrorMsg];
        } else if (oneWayVideoEndType == TKOneWayVideoEndTypeFaceNumberError) {
            [self failActionCallJsWith:@"-4" errorMsg:faceDetectErrorMsg];
        }
        
    } else {
        // 上报错误次数
        if ([self reportRecordFailAndCheckCanRecord:oneWayVideoEndType errorMsg:self.tkOneEndView.failureString]) { // 超过最大次数
            return;
        }
        
        // 原生处理错误次数超限逻辑
        if (oneWayVideoEndType == TKOneWayVideoEndTypeFaceDetectError||oneWayVideoEndType == TKOneWayVideoEndTypeFaceNumberError) {
            
            self.faceDetectFailureCount++;
            if (self.faceDetectFailureCount >= self.totalFaceDetectFailureCount) { // 超过最大次数
                // 次数超限，返回给h5
                //                [self failActionCallJsWith:@"-4" errorMsg:@"由于长时间未完成视频录制，我们为您切换到一对一人工见证服务。"];
                [self failActionCallJsWith:@"-4" errorMsg:faceDetectErrorMsg];
            } else {
                
                // 跳到失败页面
                [self endOneWayVideo:NO errorType:TKOneWayVideoEndTypeFaceDetectError errorMsg:faceDetectErrorMsg];
            }
        } else if (oneWayVideoEndType == TKOneWayVideoEndTypeFaceCompareError) {
            
            self.faceCompareFailureCount++;
            if (self.faceCompareFailureCount >= self.totalFaceCompareFailureCount) { // 超过最大次数
                // 次数超限，返回给h5
                //                [self failActionCallJsWith:@"-5" errorMsg:@"由于长时间未完成视频录制，我们为您切换到一对一人工见证服务。"];
                [self failActionCallJsWith:@"-5" errorMsg:faceCompareErrorMsg];
            } else {
                // 跳到失败页面
                [self endOneWayVideo:NO errorType:TKOneWayVideoEndTypeFaceCompareError errorMsg:faceCompareErrorMsg];
            }
        } else if (oneWayVideoEndType == TKOneWayVideoEndTypeNoVoiceError) {
            
            self.noVoiceFailureCount++;
            if (self.noVoiceFailureCount >= self.totalNoVoiceFailureCount) { // 超过最大次数
                // 次数超限，返回给h5
                //                [self failActionCallJsWith:@"-6" errorMsg:@"由于长时间未完成视频录制，我们为您切换到一对一人工见证服务。"];
                [self failActionCallJsWith:@"-6" errorMsg:noVoiceErrorMsg];
            } else {
                // 跳到失败页面
                [self endOneWayVideo:NO errorType:TKOneWayVideoEndTypeNoVoiceError errorMsg:noVoiceErrorMsg];
            }
        } else if (oneWayVideoEndType == TKOneWayVideoEndTypeWrongAnswerError) {
            
            self.wrongAnswerFailureCount++;
            if (self.wrongAnswerFailureCount >= self.totalWrongAnswerFailureCount) { // 超过最大次数
                // 次数超限，返回给h5
                //                [self failActionCallJsWith:@"-7" errorMsg:@"由于长时间未完成视频录制，我们为您切换到一对一人工见证服务。"];
                [self failActionCallJsWith:@"-7" errorMsg:wrongAnswerErrorMsg];
            } else {
                // 跳到失败页面
                [self endOneWayVideo:NO errorType:TKOneWayVideoEndTypeWrongAnswerError errorMsg:wrongAnswerErrorMsg];
            }
        }
    }
}


- (BOOL)reportRecordFailAndCheckCanRecord:(TKOneWayVideoEndType)oneWayVideoEndType errorMsg:(NSString *)errorMsg {
    // 上报错误次数
    [self reportRecordFail:oneWayVideoEndType errorMsg:errorMsg];
    
    // 通用错误次数判断
    self.recordFailureCount++;
    if (self.recordFailureCount >= self.totalFailureCount) { // 超过最大次数
        // 次数超限，返回给h5
        //                [self failActionCallJsWith:@"-4" errorMsg:@"由于长时间未完成视频录制，我们为您切换到一对一人工见证服务。"];
        [self failActionCallJsWith:@"-16" errorMsg:@"由于录制失败次数已达当日上限,请您通过柜台办理。"];
        return YES;
    }
    
    return NO;
}

- (void)reportRecordFail:(TKOneWayVideoEndType)oneWayVideoEndType errorMsg:(NSString *)errorMsg
{
    // 检查h5参数，是否需要上传
    NSDictionary *extParams = (NSDictionary *)[self.requestParam getObjectWithKey:@"extParams"];
    if ([extParams isKindOfClass:NSDictionary.class] && extParams.allKeys.count > 0) {
        NSString *url = [extParams getStringWithKey:@"failReportUrl"];
        if ([TKStringHelper isNotEmpty:url]) {
            // 发送请求
            NSMutableDictionary *params = [NSMutableDictionary dictionaryWithDictionary:extParams];
            params[@"requestHeaders"] = (NSDictionary *)[self.requestParam getObjectWithKey:@"requestHeaders"];

            NSString *eventNo = @"";
            if (oneWayVideoEndType == TKOneWayVideoEndTypeFaceDetectError) {
                eventNo = @"00007";
            } else if (oneWayVideoEndType == TKOneWayVideoEndTypeFaceCompareError) {
                eventNo = @"00009";
            } else if (oneWayVideoEndType == TKOneWayVideoEndTypeNoVoiceError) {
                eventNo = @"00005";
            } else if (oneWayVideoEndType == TKOneWayVideoEndTypeWrongAnswerError) {
                eventNo = @"00006";
            }
            if ([TKStringHelper isEmpty:eventNo]) return;
            params[@"eventNo"] = eventNo;
            params[@"eventMsg"] = errorMsg;
            params[@"isRestFull"] = @"1";   // 微服务版本
            [[TKOpenAccountService new] recordFailureReportWithURL:url param:params callBackFunc:^(ResultVo *resultVo) {
                
                // 处理回调结果
//                int errorNo = resultVo.errorNo;
                NSString *errorInfo = resultVo.errorInfo;
                TKLogInfo(@"上报错误完毕，%@", errorInfo);
            }];
        }
    }
}

- (void)deleteLocalVideoFile:(NSString *)localFilePath
{
    NSFileManager *fileManager = [NSFileManager defaultManager];
    BOOL fileExists = [fileManager fileExistsAtPath:localFilePath];

    if (fileExists == NO) {
        TKLogInfo(@"预览视频文件不存在，无需删除");
        return;
    }

    // 删除原有的视频
    NSError *error = nil;
    [fileManager removeItemAtPath:localFilePath error:&error];
    if (error) {
        TKLogInfo(@"删除预览视频文件出错%@", error.description);
        //                return;
    } else {
        fileExists = NO;
        TKLogInfo(@"预览视频文件删除成功");
    }
}

- (NSString *)getLocalOneWayVideoPath
{
    NSString *document = NSSearchPathForDirectoriesInDomains(NSDocumentDirectory, NSUserDomainMask, true)[0];
    NSString *filePath = [document stringByAppendingString:@"/tempOneWayFile.mp4"];
    return filePath;
}

- (void)createStartRecordTimeoutTimer:(NSTimeInterval)timeoutInterval
{
    if (timeoutInterval <= 0) timeoutInterval = 600;
    // 设置开始录制超时定时器
    self.startRecordTimeoutTimer = [NSTimer timerWithTimeInterval:timeoutInterval target:self selector:@selector(startRecordTimeOutBreakLink:) userInfo:nil repeats:NO];
    [[NSRunLoop mainRunLoop] addTimer:self.startRecordTimeoutTimer forMode:NSRunLoopCommonModes];
}

- (void)startRecordTimeOutBreakLink:(NSTimer *)timer
{
    TKLogInfo(@"思迪录制日志：就绪后一直没有开始录制，达到时间，断开视频");
        
    [self.startRecordTimeoutTimer invalidate];
    self.startRecordTimeoutTimer = nil;
        
    [self failActionCallJsWith:@"-12" errorMsg:@"由于您长时间未进行操作，请重新发起录制"];
}

- (void)detectFace:(UIImage *)image pixelBuffer:(CVPixelBufferRef)pixelBuffer {
    if (image != nil) {
        [self.faceDetectManager detectFace:image];
    } else {
        [self.faceDetectManager detectFaceWithPixelBuffer:pixelBuffer];
    }
}

- (void)nextOneWayProcess
{
    if (!self.isTKShow) {return;}
    
    if (self.oneWayProcess == TKOneWayProcessPlayPrompt){
        TKLogInfo(@"思迪录制日志：录制流程-播放录制前提示语音");
        if (self.beforeVideoIndex < self.beforeVideoArray.count) {

                // 播放提示语音
                self.currentModel = self.beforeVideoArray[self.beforeVideoIndex];
                self.playQuessionProcess = TKPlayQuessionProcessStart;
                [self nextPlayQuestionProcess];
                
                // 提前修改序号。音频播放完会重新进到该流程
                self.beforeVideoIndex++;
            
        } else {
            if ([self.tkOneView respondsToSelector:@selector(showTakeRecordBtn:)]) {
                [self.tkOneView showTakeRecordBtn:YES];
            }
            
            // 播完最后一段提示音
            // 创建定时器抓取图片做人脸检测
            [self.faceDetectManager startFaceDetect];
            
            if (!self.isLocalRecord) {
                // create start record time out timer
                [self createStartRecordTimeoutTimer:[self.requestParam getDoubleWithKey:@"startRecordTimeOut"]];
            }
            
//            [self startRecordingAction];
//
//            self.afterVideoIndex = 100;
//            [self recordStopCallBack:@"" fullFilePath:@"https://khh5test.csc108.com:8084/auth-common-server/servlet/VideoPlaytAction?function=play&video_path=0/0100010/202112/29/18229709218/7d4955194adbd10c071b5b368aa48ed3-180.mp4&tk-jwt-authorization=Bearer%20eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJzdWIiOiIwMTAwMDEwIiwiaXNzIjoidGhpbmtpdmUiLCJmbG93Tm8iOiIxODIyOTcwOTIxOCIsImV4cCI6MTY0MDc3MDk5NiwidXNlck5hbWUiOiIxODIyOTcwOTIxOCIsImlhdCI6MTY0MDc2OTE5Nn0.-U6Jq4VP5B4Dfkc4a7WwbFeEXPXWFJmNxjOZQROv_64" videoLenth:100];
        }
        
    } else if(self.oneWayProcess == TKOneWayProcessStartRecord) {
        TKLogInfo(@"思迪录制日志：录制流程-开始录制");
        
        // 按钮等待人脸检测通过后可用
        // 用户点击按钮后，开始播放问题
        // 开始服务器录制
        [self startRecordingAction];
        // 开始录制回调执行TKOneWayProcessPlayQuestion流程
        
    } else if(self.oneWayProcess == TKOneWayProcessPlayQuestion) {
        
        if (self.questionIndex < self.questionArray.count) {
            TKLogInfo(@"思迪录制日志：录制流程-播放问题");
            // 播放问题语音
            self.currentModel = self.questionArray[self.questionIndex];
            self.playQuessionProcess = TKPlayQuessionProcessStart;
            [self nextPlayQuestionProcess]; // 走视频播放子流程
            
            self.questionIndex++;
        } else {
            // 录制完毕
            self.oneWayProcess = TKOneWayProcessRecordDone;
            [self nextOneWayProcess];
        }
        
    } else if(self.oneWayProcess == TKOneWayProcessRecordDone) {
        TKLogInfo(@"思迪录制日志：录制流程-录制结束");
        [self stopRecordingAction];
        [self.tkOneView playEndVoiceView];
        // 结束录制回调执行TKOneWayProcessRecordDonePrompt流程
        
    } else if (self.oneWayProcess == TKOneWayProcessRecordCancel) {
        TKLogInfo(@"思迪录制日志：录制流程-录制取消");
        [self goBack];
    } else if (self.oneWayProcess == TKOneWayProcessRecordError) {
        TKLogInfo(@"思迪录制日志：录制流程-录制出错");
        // 重置人脸识别相关flag
        [self.faceDetectManager stopFaceDetect];
        [self.tkOneView liveWarning:nil];
        // 停止录制
        [self stopRecordingAction];
        
        // 要提前处理识别时的定时器
        if (self.transcriberStarted) {
            
            [self.tkOneView answered:NO displayTime:0];
        }
        // 走错误流程
        [self videoFailureDidExceedLimit:self.oneWayVideoEndType];
        
    } else if (self.oneWayProcess == TKOneWayProcessRecordDonePrompt) { // 录制完毕后的提示
        TKLogInfo(@"思迪录制日志：录制流程-录制结束播放结束提示音");
        // 重置人脸识别相关flag
        [self.faceDetectManager stopFaceDetect];
        
        if(self.isReadVideo)return;//目前朗读不走这里，播放结束语音还在单独处理，等会统一处理在调整
        
        if (self.afterVideoIndex < self.afterVideoArray.count) {
            // 播放提示语音
            self.currentModel = self.afterVideoArray[self.afterVideoIndex];
            self.playQuessionProcess = TKPlayQuessionProcessStart;
            [self nextPlayQuestionProcess];
            
            self.afterVideoIndex++;
        } else {
            // 单向完毕
            self.oneWayProcess = TKOneWayProcessOneWayDone;
            [self nextOneWayProcess];
            
        }
    }  else if (self.oneWayProcess == TKOneWayProcessOneWayDone) { // 录制完毕后的提示
        
        TKLogInfo(@"思迪录制日志：录制流程-单向完成");
        [self endOneWayVideo:YES errorType:TKOneWayVideoEndTypeNormal errorMsg:@""];
        
    } else if (self.oneWayProcess == TKOneWayProcessIdle) { // 录制完毕后的提示
        TKLogInfo(@"思迪录制日志：录制流程-录制闲置");
        self.isRecording = NO;
        self.beforeVideoIndex = 0;
        self.questionIndex = 0;
        self.afterVideoIndex = 0;
        self.oneWayVideoEndType = TKOneWayVideoEndTypeNormal;
        
        [self.tkOneView removeFromSuperview];
        self.tkOneView = nil;
        
        self.isSpeechRecognitionThrough = NO;
    }
}


- (void)nextPlayQuestionProcess
{
    TKSmartQuestionModel *model = self.currentModel;
    TKPlayQuessionProcess playQuessionProcess = self.playQuessionProcess;
    
    if (!self.isTKShow) {return;}
    
    if (playQuessionProcess == TKPlayQuessionProcessIdle){
        TKLogInfo(@"思迪录制日志：音频流程-闲置");
        self.failureCountPerAsrNoVoice = 0;
        self.tkOneEndView.failureString = @"";
        
    } else if (playQuessionProcess == TKPlayQuessionProcessStart){
        TKLogInfo(@"思迪录制日志：音频流程-开始");
        if(self.isReadVideo){
            //滴一声没有fileFlag，只播放开始前准备话术
            if([TKStringHelper isNotEmpty:model.fileFlag]){
                // 播放语音提示
                [self startPlaySound:model.fileFlag];
                [self recordTime:0 longestTime:self.longestTime startRecord:NO];
            }

        }else{
            // 播放新音频的时候，正在识别，停止识别
            // 这是因为人脸不在框等异常情况插入正常流程时，需要处理的例外情况
            if (self.transcriberStarted) [self stopAsr];
            self.transcriberStarted = NO;
            self.answeredErrorInfo = @""; //回答结果清空
            self.isPlayingSound = NO;
            
            // 提前设置流程
            self.playQuessionProcess = TKPlayQuessionProcessPrompt;
            
            // 播放语音
            [self startTTS:model];
        }
    
    } else if(playQuessionProcess == TKPlayQuessionProcessPrompt) {
        
        if (self.currentModel.type == TKRecordTypeTTSAndAsr) {
            if (self.currentModel.isNeedPause) {    // 强制暂停
                
                if ([self.recordManager respondsToSelector:@selector(enableMute:)]) {
                    // 收音
                    [self.recordManager enableMute:0];
                }
                [self addFragmentModel:TKFragmentTypeStartAsr fragmentRemark:@"展示资料" isDone:NO];

                if([TKStringHelper isNotEmpty:self.currentModel.displayPrompt]){
                    [self.tkOneView liveContinue:self.currentModel.displayPrompt
                                   isOneLineShow:NO
                                    isHtmlString:self.currentModel.isHtmlPrompt
                            questionOneWordSpeed:[self getQuestionOneWordSpeed:model]];
                }
                [self.tkOneView showNextBtn:YES btnTitle:self.currentModel.longPauseBtn];
                // 提前指定流程
                self.playQuessionProcess = TKPlayQuessionProcessDone;
                return;
            }
            
            // 提前设置流程
            if (model.isNeedAsr == YES) {
                if (self.shouldPlayBeepSound) {
                    TKLogInfo(@"思迪录制日志：音频流程-识别提示（滴一声）");
                    // 先播放“滴”语音，后做asr
                    [self startPlaySound:@"1"];
                    self.playQuessionProcess = TKPlayQuessionProcessAsrStart;
                } else {
                    // 不播放“滴”一声
                    self.playQuessionProcess = TKPlayQuessionProcessAsrStart;
                    [self nextPlayQuestionProcess];
                }
                
            } else if (model.isNeedDelay == YES) {  // 播放需要等待一段时间的文案
                
                if ([self.recordManager respondsToSelector:@selector(enableMute:)]) {
                    // 收音
                    [self.recordManager enableMute:0];
                }
                [self addFragmentModel:TKFragmentTypeStartAsr fragmentRemark:@"展示资料" isDone:NO];
                // 不需要播放语音，直接展示文案并开始倒计时
                if([TKStringHelper isNotEmpty:self.currentModel.displayPrompt]){
                    // 不需要播放语音，直接展示文案并开始倒计时
                    [self.tkOneView showUserActionPrompt:self.currentModel.displayPrompt
                                            isHtmlString:self.currentModel.isHtmlPrompt
                                                waitTime:self.currentModel.pauseTime
                                    questionOneWordSpeed:[self getQuestionOneWordSpeed:model]];
                }
                // 提前指定流程
                self.playQuessionProcess = TKPlayQuessionProcessDone;
                //                [self nextPlayQuestionProcess];
            } else {
                self.playQuessionProcess = TKPlayQuessionProcessDone;
                [self nextPlayQuestionProcess];
            }
            
        } else if (self.currentModel.type == TKRecordTypeReadAloud ||
                 self.currentModel.type == TKRecordTypeRead) {
            
            // 显示继续按钮
            NSString *btnTitle = self.currentModel.readPromptBtnTitle;
            if ([TKStringHelper isEmpty:btnTitle]) {
                btnTitle = self.currentModel.type == TKRecordTypeRead ? @"开始阅读" : @"开始朗读";
            }
            [self.tkOneView showNextBtn:YES btnTitle:btnTitle];
            self.playQuessionProcess = TKPlayQuessionProcessRead;
            
        } else {
            self.playQuessionProcess = TKPlayQuessionProcessDone;
            [self nextPlayQuestionProcess];
        }
        
    } else if (playQuessionProcess == TKPlayQuessionProcessAsrStart) {
        TKLogInfo(@"思迪录制日志：音频流程-识别开始");
        // 滴一声完毕，开始语音识别。由于启动识别需要时间，展示放到识别开始回调时处理
        [self startAsr];
        
    } else if (playQuessionProcess == TKPlayQuessionProcessAsrEnd) {
        TKLogInfo(@"思迪录制日志：音频流程-识别结束");
        if (self.currentModel.asrResult == TKAsrResultFail) self.oneWayVideoEndType = TKOneWayVideoEndTypeWrongAnswerError;
        
        //            if ([TKStringHelper isNotEmpty:self.answeredErrorInfo]) {
        // 展示识别结果
        [self.tkOneView answerPromptType:(self.currentModel.asrResult == TKAsrResultSuccess) identifyString:self.answeredErrorInfo];
        //            }
        
        // 为了展示错误回答内容，停留1s，但是识别要马上停止，否则会有问题
        //停止继续识别，避免后面回调多次结果影响流程
        self.transcriberStarted = false;
        [self stopAsr];
        
        TKSmartQuestionModel *asrRsultModel = self.currentModel.asrResultModel;
        int delayTime = 1;
        
        //            if (self.currentModel.asrResult == TKAsrResultSuccess) {
        //                // 播放识别通过提示音
        //                delayTime = 0;
        //
        //            }
        //            else if (self.currentModel.asrResult == TKAsrResultFail) {
        //                // 播放识别不通过提示音
        //                delayTime = 10;
        //            } else if (self.currentModel.asrResult == TKAsrResultNoIdentify) {
        //                // 播放没有结果提示音
        //                delayTime = 0;
        //            } else if (self.currentModel.asrResult == TKAsrResultNo) {
        //                // 不应该出现No的情况
        //            }
        
        [self.tkOneView answered:(self.currentModel.asrResult == TKAsrResultSuccess) displayTime:delayTime];
        // 提前指定流程。播放结束后会跳到该流程
        self.playQuessionProcess = TKPlayQuessionProcessDone;
        
        // 根据情况是否延迟
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(delayTime * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            
            if (self.currentModel.isNeedPlayAsrResultPrompt) {
                
                if (asrRsultModel) [self startTTS:asrRsultModel];
            } else {
                // 无需播放，直接下一个流程
                [self nextPlayQuestionProcess];
            }
        });
    } else if (playQuessionProcess == TKPlayQuessionProcessRead) {
        
        TKLogInfo(@"思迪录制日志：音频流程-朗读/阅读");
        
        // 收起tts/asr文案
        [self.tkOneView liveContinue:@"" isOneLineShow:YES isHtmlString:@"" questionOneWordSpeed:@""];
        
        self.playQuessionProcess = TKPlayQuessionProcessDone;
        
        // 添加字幕
        if ([self.recordManager respondsToSelector:@selector(createSubtitles:showTime:questionOneWordSpeed:)]) {
            [self.recordManager createSubtitles:self.currentModel.readContent showTime:self.currentModel.readTime questionOneWordSpeed:@""];
        }
        
        // 展示朗读/阅读文案
        if ([self.tkOneView respondsToSelector:@selector(showWithContent:countdownTime:type:readTitle:readConfirmBtnTitle:oneWordSpeed:)]) {
            [self.tkOneView showWithContent:self.currentModel.readContent
                              countdownTime:self.currentModel.readTime.intValue
                                       type:self.currentModel.type == TKRecordTypeRead ? TKReadingTypeRead : TKReadingTypeReadAloud
                                  readTitle:self.currentModel.readTitle
                        readConfirmBtnTitle:self.currentModel.readConfirmBtnTitle
                               oneWordSpeed:@""];
            
            // 朗读时，录制用户声音
            if (self.currentModel.type == TKRecordTypeReadAloud) {
                
                if ([self.recordManager respondsToSelector:@selector(enableMute:)]) {
                    // 收音
                    [self.recordManager enableMute:0];
                }
            }
            //        [self nextPlayQuestionProcess];
        } else {
            // 不支持该能力，直接下一个流程
            [self nextPlayQuestionProcess];
        }

    } else if (playQuessionProcess == TKPlayQuessionProcessDone) {
        
        TKLogInfo(@"思迪录制日志：音频流程-播放结束");
        if (self.currentModel.asrResult == TKAsrResultSuccess || self.currentModel.asrResult == TKAsrResultNo) {
            [self nextPlayQuessionProcessDoneProgress];
            
        } else if (self.currentModel.asrResult == TKAsrResultFail) {
            
            // 重置状态
            self.playQuessionProcess = TKPlayQuessionProcessIdle;
            [self nextPlayQuestionProcess];
            
            self.tkOneEndView.failureString = @"";
            self.oneWayProcess = TKOneWayProcessRecordError;
            [self nextOneWayProcess];
        } else if (self.currentModel.asrResult == TKAsrResultNoIdentify) {
            // 没有结果次数加1
            self.failureCountPerAsrNoVoice++;
            
            if (self.failureCountPerAsrNoVoice >= self.maxFailureCountPerAsrNoVoice) {
                
                if (self.continueOnAnswerTimeout) {
                    // 无关回答继续单向
                    [self nextPlayQuessionProcessDoneProgress];
                } else {
                    // 无关回答关闭单向
                    // 重置状态
                    self.playQuessionProcess = TKPlayQuessionProcessIdle;
                    [self nextPlayQuestionProcess];
                    
                    // TKLogInfo(@"333 TKOneWayVideoEndTypeNoVoiceError");
                    
                    // 录制出错.跳出播放问题音频流程。执行下一个主流程
                    self.oneWayVideoEndType = TKOneWayVideoEndTypeNoVoiceError;
                    self.oneWayProcess = TKOneWayProcessRecordError;
                    [self nextOneWayProcess];
                }
            } else {
                
                // 播放没有回答提示音
                [self startTTS:self.currentModel.noVoiceTipModel];
                
                // 重新走播放语音流程。提前设置流程
                self.playQuessionProcess = TKPlayQuessionProcessAsrStart;   // 重新识别
            }
        }
    }
}

- (void)nextPlayQuessionProcessDoneProgress
{
    // 重置状态
    self.playQuessionProcess = TKPlayQuessionProcessIdle;
    [self nextPlayQuestionProcess];
    
    // 更新进度条
    [self updateProcessStatus];
    
    [self nextOneWayProcess];
}

// 更新进度条
-(void)updateProcessStatus{
    // 更新进度条
    if (self.oneWayProcess == TKOneWayProcessPlayQuestion) {
        NSInteger index = self.questionIndex;   // 注意这里的questionIndex已经是下一个问题的Index
        index = index < 0 ? 0 : index;
        index = index > self.questionArray.count ? self.questionArray.count : index;
        if ([self.tkOneView respondsToSelector:@selector(currentNum:allNum:)]) {
            [self.tkOneView currentNum:(int)index allNum:(float)(self.questionArray.count)];
        }
    }
}

- (void)endOneWayVideo:(BOOL)isSuccess errorType:(TKOneWayVideoEndType)errorType errorMsg:(NSString *)errorMsg
{
    [self stopTTSAndAsr];
    //完成语音播报结束
    self.isSpeechRecognitionThrough = isSuccess;
    
    if ([self skipPreview] && self.isLocalRecord && isSuccess) {
        //不展示成功结果页，直接把结果给h5
        [self uploadBtnClick];
    } else {
        [self jumpToRecordResultPage:isSuccess errorType:errorType errorMsg:errorMsg];
    }
    
    [self stopDevice:NO];
}

- (void)recordLongTime:(int)recordTime startRecord:(BOOL)startRecord
{
    if ([self.tkOneView respondsToSelector:@selector(recordLongTime:startRecord:)]) {
        [self.tkOneView recordLongTime:recordTime startRecord:startRecord];
    }
}


- (void)addFragmentModel:(TKFragmentType)fragmentType fragmentRemark:(NSString *)fragmentRemark isDone:(BOOL)isDone
{
    if (self.isRecording == NO) return;
    
    // 添加片段标记
    if (!isDone) {
        if (fragmentType == TKFragmentTypeFaceDetectErrorStart) {
            
            if (!self.currentFaceErrorFragmentModel) {
                self.currentFaceErrorFragmentModel = [[TKVideoFragmentModel alloc] init];
                self.currentFaceErrorFragmentModel.fragmentType = TKFragmentTypeFaceDetectErrorStart;
                CFAbsoluteTime currentTime = CFAbsoluteTimeGetCurrent();
                NSInteger milliseconds = (NSInteger)(currentTime * 1000) - self.recordStartTime * 1000;
                self.currentFaceErrorFragmentModel.videoStartTime = (self.recordStartTime + 978307200) * 1000; // 978307200是2001年1月1日到1970年1月1日的秒数差
                self.currentFaceErrorFragmentModel.beginTime = milliseconds;
                self.currentFaceErrorFragmentModel.fragmentRemark = fragmentRemark;
                self.currentFaceErrorFragmentModel.frameGroupId = self.currentFragmentGroupId;
                self.currentFragmentGroupId++;
            }
        } else if (
                   fragmentType == TKFragmentTypeTTSStart ||
                   fragmentType == TKFragmentTypeStartAsr ||
                   fragmentType == TKFragmentTypePause) {
            
            if (!self.currentBusinessFragmentModel) {
                
                
                TKVideoFragmentModel *fragmentModel = [[TKVideoFragmentModel alloc] init];
                fragmentModel.fragmentType = fragmentType;
                CFAbsoluteTime currentTime = CFAbsoluteTimeGetCurrent();
                NSInteger milliseconds = (NSInteger)(currentTime * 1000) - self.recordStartTime * 1000;
                fragmentModel.videoStartTime = (self.recordStartTime + 978307200) * 1000; // 978307200是2001年1月1日到1970年1月1日的秒数差
                fragmentModel.beginTime = milliseconds;
                
                if (fragmentType == TKFragmentTypeStartAsr ||
                    fragmentType == TKFragmentTypeAsrReslut) {
                    self.currentAsrFragmentIndex++;
                    fragmentModel.fragmentRemark = [NSString stringWithFormat:@"%@ %i", fragmentRemark, self.currentAsrFragmentIndex];
                } else {
                    self.currentPauseFragmentIndex++;
                    fragmentModel.fragmentRemark = [NSString stringWithFormat:@"%@ %i", fragmentRemark, self.currentPauseFragmentIndex];
                }
                fragmentModel.frameGroupId = self.currentFragmentGroupId;
                self.currentFragmentGroupId++;
                self.currentBusinessFragmentModel = fragmentModel;
            }
        }
        
        return;
    }
    
    // 记录打点结束时间
    if (fragmentType == TKFragmentTypeFaceDetectErrorStart) {
        
        if (self.currentFaceErrorFragmentModel) {
            
            CFAbsoluteTime currentTime = CFAbsoluteTimeGetCurrent();
            NSInteger milliseconds = (NSInteger)(currentTime * 1000) - self.recordStartTime * 1000;
            self.currentFaceErrorFragmentModel.endTime = milliseconds;
            [self.fragmentModelList addObject:self.currentFaceErrorFragmentModel];
            self.currentFaceErrorFragmentModel = nil;
        }
    } else if (
            fragmentType == TKFragmentTypeTTSStart ||
            fragmentType == TKFragmentTypeStartAsr ||
            fragmentType == TKFragmentTypePause) {
       if (self.currentBusinessFragmentModel) {
           
           CFAbsoluteTime currentTime = CFAbsoluteTimeGetCurrent();
           NSInteger milliseconds = (NSInteger)(currentTime * 1000) - self.recordStartTime * 1000;
           self.currentBusinessFragmentModel.endTime = milliseconds;
           [self.fragmentModelList addObject:self.currentBusinessFragmentModel];
           self.currentBusinessFragmentModel = nil;
       }
    }
}

- (NSString *)getQuestionOneWordSpeed:(TKSmartQuestionModel *)model
{
    NSString *wordSpeed = @"0.19";
    if ([TKStringHelper isNotEmpty:model.wordSpeed]) {
        
        wordSpeed = model.wordSpeed;
    } else if ([TKStringHelper isNotEmpty:model.tipSpeed]) {
        
        wordSpeed = [NSString stringWithFormat:@"%.2f", (2 - model.tipSpeed.floatValue) * 0.19f];
    } else if ([TKStringHelper isNotEmpty:[self.requestParam getStringWithKey:@"questionOneWordSpeed"]]) {
        
        wordSpeed = [self.requestParam getStringWithKey:@"questionOneWordSpeed"];
    }

    return wordSpeed;
}

#pragma mark - KVO
- (void)observeValueForKeyPath:(NSString *)keyPath ofObject:(id)object change:(NSDictionary<NSKeyValueChangeKey,id> *)change context:(void *)context {
    
    if(context == (__bridge void *)[AVAudioSession sharedInstance] && [keyPath isEqualToString:@"outputVolume"]) {
        float newVolume = [[change objectForKey:@"new"] floatValue];
        
        //        TKLogDebug(@"volumeChangeNotification volumn = %.2f, oldValue = %.2f", newVolume, oldVolume);
        
        //最小音量调整支持h5控制
        float deviceMinVolume=TKSmartOpenVolume;
        if (self.requestParam[@"deviceMinVolume"]) {
            deviceMinVolume=[self.requestParam[@"deviceMinVolume"] intValue]/100.00f;
        }
        if ((newVolume+0.05)  < deviceMinVolume) {
            
            //直接调整音量api还能使用，先改音量不提示用户
            dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.3 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                
                MPMusicPlayerController *mp = [MPMusicPlayerController applicationMusicPlayer];
                mp.volume = deviceMinVolume;//0为最小1为最大
            });
            
            
            //            TKLogDebug(@"volumeChangeNotification MPMusicPlayerController");
            
        }
    }
    
}

//#pragma mark 播放本地mp3处理
//
//- (void)handleAudioPlay:(NSString*)voiceName withType:(NSString*)type
//{
//    [self handleAudioPlay:voiceName withType:type sourceDirectory:@"Resources/TKOpenPlugin60006"];
//}
//
///**
// <AUTHOR> 2020年03月07日17:54:19
// @播放本地语音文件
// */
//- (void)handleAudioPlay:(NSString*)voiceName withType:(NSString*)type sourceDirectory:(NSString *)sourceDirectory {
//    NSBundle *bundle = [NSBundle bundleWithURL:[[NSBundle mainBundle] URLForResource:[NSString stringWithFormat:@"%@", TK_OPEN_RESOURCE_NAME] withExtension:@"bundle"]];
//    NSURL *aUrl = nil;
//    NSString* fPath = [bundle pathForResource:voiceName ofType:type inDirectory:sourceDirectory];
//    
//    NSFileManager *fm = [[NSFileManager alloc] init];
//    
//    if ([fm fileExistsAtPath:fPath]) {
//        aUrl = [NSURL fileURLWithPath:fPath];
//    }
//    AVPlayerItem * songItem = [[AVPlayerItem alloc]initWithURL:aUrl];
//    self.voicePlayer = [[AVPlayer alloc]initWithPlayerItem:songItem];
//    [self.voicePlayer play];
//    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(moviePlayDidEnd:) name:AVPlayerItemDidPlayToEndTimeNotification object:self.voicePlayer.currentItem];
//}
//
////音频播放完毕
//- (void)moviePlayDidEnd:(NSNotification*)notification {
//    dispatch_async(dispatch_get_main_queue(), ^{
//        
//        if (self.isClickTakeRecordBtn == NO) {
//            
//            // 创建定时器抓取图片做人脸检测
//            [self.faceDetectManager startFaceDetect];
//        }
//    });
//}

#pragma mark - TKFaceDetectManagerDelegate
- (void)faceDetectDidComplete:(TKFacePassStatus)facePassStatus faceCompareStatus:(TKFaceCompareStatus)faceCompareStatus facNumberPassStatus:(TKSingleFaceStatus)facNumberPassStatus errorMsg:(NSString *)errorMsg {
    
    [self handleFaceDetectResult:facePassStatus faceCompareStatus:faceCompareStatus facNumberPassStatus:facNumberPassStatus errorMsg:errorMsg];
}



/// 人脸检测失败回调
- (void)faceDetectDidFail:(NSString *)errorMsg {
    [self.tkOneView liveWarning:errorMsg forceDisplay:NO];
}

/// 失败次数超限
- (void)faceDetectDidExceedLimit:(TKFaceErrorType)faceErrorType errorMsg:(NSString *)errorMsg {
    //    [self videoFailureDidExceedLimit:faceErrorType == TKFaceErrorTypeFaceDetect ? TKOneWayVideoEndTypeFaceDetectError : TKOneWayVideoEndTypeFaceCompareError];
    
    if (self.isExitProgress) return;    // 直接抛弃回调结果
    
    // 只需要处理一次报错，其他的报错全部拦截
    if (self.oneWayVideoEndType == TKOneWayVideoEndTypeNormal) {
        // 跳转到录制出错流程
        if (faceErrorType==TKFaceErrorTypeFaceDetect) {
            self.oneWayVideoEndType=TKOneWayVideoEndTypeFaceDetectError;
        }else if(faceErrorType==TKFaceErrorTypeFaceNumber){
            self.oneWayVideoEndType=TKOneWayVideoEndTypeFaceNumberError;
        }else{
            self.oneWayVideoEndType=TKOneWayVideoEndTypeFaceCompareError;
        }
        self.oneWayProcess = TKOneWayProcessRecordError;
        [self nextOneWayProcess];
    }
}

#pragma mark - TKVideoAlertViewDelegate

//取消按钮事件
-(void)cancelVideoBtnAction{
    [self.videoAlertView removeFromSuperview];
    self.isAlertViewShow=NO;
    
    //    [self failActionCallJsWith:@"-1" errorMsg:@"用户主动返回"];
    
    if (self.oneWayProcess == TKOneWayProcessOneWayDone) { // 预览页面报错
        
    } else {    // 录制页面报错返回
        
        [self failActionCallJsWith:@"-8" errorMsg:self.videoAlertView.describeLabel.text];
    }
}

//继续按钮事件
-(void)takeVideoBtnAction{
    [self.videoAlertView removeFromSuperview];
    self.isAlertViewShow=NO;
    
    if (self.oneWayProcess == TKOneWayProcessOneWayDone) { // 预览页面报错
        [self.player reloadPlayer];
    } else {
        [self restartOneVideo];
    }
    
}

//独立按钮事件
-(void)onlyVideoBtnAction{
    [self.videoAlertView removeFromSuperview];
    self.isAlertViewShow=NO;
    
    self.videoAlertView = nil; // 设置为1个按钮后暂时无法恢复成2个按钮
}


#pragma mark - TKSmartVirtualManVideoEndViewDelegate
/**
 <AUTHOR> 2019年04月13日14:18:27
 @单向视频结果页返回
 */
- (void)endGoBack {
    
    [self failActionCallJsWith:@"-1" errorMsg:@"用户主动返回"];
}


/**
 <AUTHOR> 2019年04月13日14:20:28
 @单向视频结果页重试
 */
-(void)endReste{
    // 埋点-单向_重录
    NSMutableDictionary *eventDic = [NSMutableDictionary dictionaryWithDictionary:self.requestParam];
    eventDic[@"recordSuccess"] = [NSString stringWithFormat:@"%i", self.isSpeechRecognitionThrough];
    eventDic[@"isEndRecord"] = [NSString stringWithFormat:@"%i", self.isEndRecord];
    TKPrivateOneWayVideoType oneWayVideoType = (TKPrivateOneWayVideoType)[self.pluginCallBackfuncNoToTKPrivateOneWayVideoType[self.pluginCallBackfuncNo] intValue];
    [TKStatisticEventHelper sendEvent:TKPrivateEventOneWayVideo
                         subEventName:TKPrivateSubEventOneWayVideoRerecord
                             progress:TKPrivateEventProgressNone
                               result:TKPrivateEventResultNone
                          orientation:self.isLandscape ? TKPrivateVideoOrientationLandscape : TKPrivateVideoOrientationPortrait
                      oneWayVideoType:oneWayVideoType
                 prepareVideoProgress:TKPrivatePrepareVideoProgressNone
                             eventDic:eventDic];
    
    [self restartOneVideo];
}

/**
 <AUTHOR> 2019年04月13日14:21:40
 @单向视频结果页提交
 */
- (void)endSubmit{
    
    //先停止预览视频
    [self.player pause];

    //是否需要提示上传确认
    if ([TKStringHelper isEmpty:self.requestParam[@"uploadTipString"]]) {
        [self uploadBtnClick];
    }else{
        [self.tkOneEndView addSubview:self.alertTipView];
    }
}

#pragma mark - TKOneWayVideoAlertTipViewDelegate
- (void)cancelBtnClick {

    // 提交视频弹窗并确认的场景
    // 埋点-单向_提交确认_取消
    NSMutableDictionary *eventDic = [NSMutableDictionary dictionaryWithDictionary:self.requestParam];
    TKPrivateOneWayVideoType oneWayVideoType = (TKPrivateOneWayVideoType)[self.pluginCallBackfuncNoToTKPrivateOneWayVideoType[self.pluginCallBackfuncNo] intValue];
    [TKStatisticEventHelper sendEvent:TKPrivateEventOneWayVideo
                         subEventName:TKPrivateSubEventOneWayVideoSubmitConfirm
                             progress:TKPrivateEventProgressEnd
                               result:TKPrivateEventResultCancel
                          orientation:self.isLandscape ? TKPrivateVideoOrientationLandscape : TKPrivateVideoOrientationPortrait
                      oneWayVideoType:oneWayVideoType
                 prepareVideoProgress:TKPrivatePrepareVideoProgressNone
                             eventDic:eventDic];
}

//继续视频客服排队（针对视频接通时候单独处理退出视频再重新打开排队）
- (void)uploadBtnClick {
    
    if (![self skipPreview]
        && ![TKStringHelper isEmpty:self.requestParam[@"uploadTipString"]]) {
        // 提交视频弹窗并确认的场景
        // 埋点-单向_提交确认_确认
        NSMutableDictionary *eventDic = [NSMutableDictionary dictionaryWithDictionary:self.requestParam];
        TKPrivateOneWayVideoType oneWayVideoType = (TKPrivateOneWayVideoType)[self.pluginCallBackfuncNoToTKPrivateOneWayVideoType[self.pluginCallBackfuncNo] intValue];
        [TKStatisticEventHelper sendEvent:TKPrivateEventOneWayVideo
                             subEventName:TKPrivateSubEventOneWayVideoSubmitConfirm
                                 progress:TKPrivateEventProgressEnd
                                   result:TKPrivateEventResultSuccess
                              orientation:self.isLandscape ? TKPrivateVideoOrientationLandscape : TKPrivateVideoOrientationPortrait
                          oneWayVideoType:oneWayVideoType
                     prepareVideoProgress:TKPrivatePrepareVideoProgressNone
                                 eventDic:eventDic];
    }
    
    // 埋点-单向_提交
    NSMutableDictionary *eventDic = [NSMutableDictionary dictionaryWithDictionary:self.requestParam];
    NSTimeInterval startTime = [[NSDate date] timeIntervalSince1970];
    TKPrivateOneWayVideoType oneWayVideoType = (TKPrivateOneWayVideoType)[self.pluginCallBackfuncNoToTKPrivateOneWayVideoType[self.pluginCallBackfuncNo] intValue];
    [TKStatisticEventHelper sendEvent:TKPrivateEventOneWayVideo
                         subEventName:TKPrivateSubEventOneWayVideoSubmit
                             progress:TKPrivateEventProgressStart
                               result:TKPrivateEventResultNone
                          orientation:self.isLandscape ? TKPrivateVideoOrientationLandscape : TKPrivateVideoOrientationPortrait
                      oneWayVideoType:oneWayVideoType
                 prepareVideoProgress:TKPrivatePrepareVideoProgressNone
                             eventDic:eventDic];
    
    if (self.isLocalRecord) {
        
        [self handleLocalVideoUploadBtnCick:startTime];
        
    } else {
        [self handleServerVideoUploadBtnCick:startTime];
    }
}

- (void)handleLocalVideoUploadBtnCick:(NSTimeInterval)startTime {
    // 埋点-单向_结果_成功
    NSMutableDictionary *eventDic = [NSMutableDictionary dictionaryWithDictionary:self.requestParam];
    eventDic = [NSMutableDictionary dictionaryWithDictionary:self.requestParam];
    eventDic[@"errorNo"] = [NSString stringWithFormat:@"0"];;
    eventDic[@"event_err"] = @"";
    eventDic[@"costTime"] = [NSString stringWithFormat:@"%.0f", ([[NSDate date] timeIntervalSince1970] - self.statisticEventStartTime) * 1000] ; // 单位毫秒
    TKPrivateEventResult result = TKPrivateEventResultSuccess;
    TKPrivateOneWayVideoType oneWayVideoType = (TKPrivateOneWayVideoType)[self.pluginCallBackfuncNoToTKPrivateOneWayVideoType[self.pluginCallBackfuncNo] intValue];
    [TKStatisticEventHelper sendEvent:TKPrivateEventOneWayVideo
                         subEventName:TKPrivateSubEventNone
                             progress:TKPrivateEventProgressEnd
                               result:result
                          orientation:self.isLandscape ? TKPrivateVideoOrientationLandscape : TKPrivateVideoOrientationPortrait
                      oneWayVideoType:oneWayVideoType
                 prepareVideoProgress:TKPrivatePrepareVideoProgressNone
                             eventDic:eventDic];
    
    NSMutableDictionary *callJsParam = [NSMutableDictionary dictionary];
    callJsParam[@"funcNo"] = [self pluginCallBackfuncNo];
    callJsParam[@"start_time"] = self.recordStartDateString;
    
    // ----------设置你想要的格式,hh与HH的区别:分别表示12小时制,24小时制
    //现在时间,你可以输出来看下是什么格式
    //----------将nsdate按formatter格式转成nsstring
    callJsParam[@"end_time"]=[TKDateHelper formatDate:[NSDate date] format:@"yyyy-MM-dd HH:mm:ss"];
    callJsParam[@"video_length"] = self.videoLength;
    if ([self.requestParam[@"resultType"] isEqualToString:@"2"]) {
        //给文件路径
        //        callJsParam[@"filePath"] = [self getLocalOneWayVideoPath];
        callJsParam[@"filePath"] = self.fullVideoFileURLString;
    }else{
        //        NSString *tempBase64=[TKBase64Helper stringWithEncodeBase64Data:[NSData dataWithContentsOfFile:[self getLocalOneWayVideoPath]]];
        NSString *tempBase64=[TKBase64Helper stringWithEncodeBase64Data:[NSData dataWithContentsOfFile:self.fullVideoFileURLString]];
        
        callJsParam[@"videoBase64"]=[NSString stringWithFormat:@"data:video/mp4;base64,%@", tempBase64];
    }
    
    // 增加签名
    callJsParam[@"videoSign"] = [TKCommonUtil signOneWayVideo:[self getLocalOneWayVideoPath]];
    
    NSString *tmpImgBase64 = [TKBase64Helper stringWithEncodeBase64Data:UIImageJPEGRepresentation(self.videoFirstImage, 0.7)];
    callJsParam[@"headBase64"] = [NSString stringWithFormat:@"data:image/jpg;base64,%@", tmpImgBase64];
    callJsParam[@"error_no"] = @"0";
    callJsParam[@"asr_result"] = [self getFragmentModelListStr];
    
    if ([self skipPreview]) {
        //isSuccessShow = 0 不显示结果页面时返回；给h5多返回参数
        callJsParam[@"detectFailureCount"]=[NSString stringWithFormat:@"%d",self.faceDetectManager.faceDetectFailureCount];//人脸不在框次数
        callJsParam[@"compareFailureCount"]=[NSString stringWithFormat:@"%d",self.faceDetectManager.faceCompareFailureCount];//人脸比对失败次数
        callJsParam[@"multipleFaceCount"]=[NSString stringWithFormat:@"%d",self.faceDetectManager.faceNumberFailureCount];//多人脸次数
    }
    
    [self sendCallBack:callJsParam];
    
    // 本地录制要先返回结果给h5，否则会把视频删掉，导致返回给h5的视频为空
    [self exitProcess];
}

//继续视频客服排队（针对视频接通时候单独处理退出视频再重新打开排队）
- (void)handleServerVideoUploadBtnCick:(NSTimeInterval)startTime {
    
    __weak typeof(self) weakSelf = self;
    [self uploadFileWithCallBack:^(BOOL success, NSString *errorMsg) {
       
        // 单向_保存_结果
        NSMutableDictionary *eventDic = [NSMutableDictionary dictionaryWithDictionary:self.requestParam];
        eventDic[@"errorNo"] = [NSString stringWithFormat:@"%i", success ? 0 : -1];
        eventDic[@"event_err"] = errorMsg;
        eventDic[@"costTime"] = [NSString stringWithFormat:@"%.0f", ([[NSDate date] timeIntervalSince1970] - startTime) * 1000];
        TKPrivateEventResult result = success ? TKPrivateEventResultSuccess : TKPrivateEventResultFail;
        TKPrivateOneWayVideoType oneWayVideoType = (TKPrivateOneWayVideoType)[weakSelf.pluginCallBackfuncNoToTKPrivateOneWayVideoType[weakSelf.pluginCallBackfuncNo] intValue];
        [TKStatisticEventHelper sendEvent:TKPrivateEventOneWayVideo
                             subEventName:TKPrivateSubEventOneWayVideoSubmit
                                 progress:TKPrivateEventProgressEnd
                                   result:result
                              orientation:weakSelf.isLandscape ? TKPrivateVideoOrientationLandscape : TKPrivateVideoOrientationPortrait
                          oneWayVideoType:oneWayVideoType
                     prepareVideoProgress:TKPrivatePrepareVideoProgressNone
                                 eventDic:eventDic];
        
        if (success) {
            // 埋点-单向_结果_成功
            eventDic = [NSMutableDictionary dictionaryWithDictionary:self.requestParam];
            eventDic[@"errorNo"] = [NSString stringWithFormat:@"0"];
            eventDic[@"event_err"] = @"";
            eventDic[@"costTime"] = [NSString stringWithFormat:@"%.0f", ([[NSDate date] timeIntervalSince1970] - weakSelf.statisticEventStartTime) * 1000];
            TKPrivateEventResult result = TKPrivateEventResultSuccess;
    //        TKPrivateOneWayVideoType oneWayVideoType = (TKPrivateOneWayVideoType)[self.pluginCallBackfuncNoToTKPrivateOneWayVideoType[self.pluginCallBackfuncNo] intValue];
            [TKStatisticEventHelper sendEvent:TKPrivateEventOneWayVideo
                                 subEventName:TKPrivateSubEventNone
                                     progress:TKPrivateEventProgressEnd
                                       result:result
                                  orientation:weakSelf.isLandscape ? TKPrivateVideoOrientationLandscape : TKPrivateVideoOrientationPortrait
                              oneWayVideoType:oneWayVideoType
                         prepareVideoProgress:TKPrivatePrepareVideoProgressNone
                                     eventDic:eventDic];
            
            [weakSelf exitProcess];
            
            NSMutableDictionary *callJsParam = [NSMutableDictionary dictionary];
            callJsParam[@"funcNo"] = [weakSelf pluginCallBackfuncNo];
            callJsParam[@"start_time"] = weakSelf.recordStartDateString;

            //----------将nsdate按formatter格式转成nsstring
            callJsParam[@"end_time"] = weakSelf.recordEndDateString;
            callJsParam[@"video_length"] = weakSelf.videoLength;
            callJsParam[@"filePath"] = weakSelf.fullVideoFileURLString;

            callJsParam[@"error_no"] = @"0";
            [weakSelf sendCallBack:callJsParam];
        } else {
            //            [TKAlertHelper showAlert:@"上传失败，请重试" title:@"温馨提示"  okBtnText:@"确定" btnHandler:nil parentViewController:weakSelf];
            weakSelf.isAlertViewShow=YES;
            [weakSelf.tkOneEndView addSubview:weakSelf.videoAlertView];
            weakSelf.videoAlertView.describeLabel.text = @"上传失败，请重试";
            weakSelf.videoAlertView.titleLabel.text = @"温馨提示";
            [weakSelf.videoAlertView setOnlyBtnTitle:@"确定"];
        }
    }];
}


- (void)uploadFileWithCallBack:(void((^)(BOOL success, NSString *errorMsg)))callback
{
    NSString *url = [NSString stringWithFormat:@"%@", self.requestParam[@"url"]];
    // 请求参数
    NSMutableDictionary *param = [TKOpenAccountService filterRequestParam:self.requestParam];
    
    param[@"origin"] = @(2);
    param[@"video_path"] = [TKStringHelper isNotEmpty:self.relateVideoFilePath] ? self.relateVideoFilePath : @"";
    param[@"video_length"] = self.videoLength;
    param[@"funcNo"] = @"********";
    param[@"asr_result"] = [self getFragmentModelListStr];
    
    [self.layerView showLoading:@"数据提交中..."];
    
    __weak typeof(self) weakSelf = self;
    [self.openAccountService handleNetworkWithURL:url param:param callBackFunc:^(ResultVo *resultVo) {
        dispatch_async(dispatch_get_main_queue(), ^{
            
            [weakSelf.layerView hideLoading];
            
            NSString *msg = @"网络异常，请重试";
            msg = resultVo.errorInfo;
            
            if (resultVo.errorNo == 0) {

                // 成功回调
                if (callback != nil) {
                    callback(YES, @"");
                }
                
                return;
            }
            
            // 失败回调
            if (callback != nil) {
                callback(NO, msg);
            }
        });
    }];
    
}


#pragma mark - TKSmartVirtualManVideoViewDelegate
//返回代理
- (void)goBack{
    
    [self failActionCallJsWith:@"-1" errorMsg:@"客户主动返回"];
}

/**
 <AUTHOR> 2020年08月26日11:10:59
 @开始录制视频
 */
- (void)takeRecord {
    if(self.isReadVideo){
       //朗读情况单向
        self.isClickTakeRecordBtn = YES;
        
        [self.tkOneView showWaitTip:@"开始"];
        [self startPlaySound:@"1"];
        self.recordStartTime=0;
        //延迟，让开始语音播放完再录制
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.2 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            
            [self startRecordingAction];
        });
    }else{
        // cancel timer
        if (self.startRecordTimeoutTimer) {
            [self.startRecordTimeoutTimer invalidate];
            self.startRecordTimeoutTimer = nil;
        }

        // 开始录制视频
        // 有可能在点击录制之后，还会做倒计时动画等，因此增加了该方法。目前的流程不需要
        [self countDownEnd];
    }

}
/**
 <AUTHOR> 2023年02月17日17:05:20
 @切换摄像头
 */
- (void)switchVideoCamera{
    if (self.isLandscape) {
        //横屏单向支持切换摄像头
        [self.recordManager tkSwitchVideoCamera:self.recordManager.isFrontCamera];
        self.recordManager.isFrontCamera=!self.recordManager.isFrontCamera;
    }
}
/**
 <AUTHOR> 2020年02月28日10:47:29
 @结束录制代理
 */
-(void)endRecord{
    
    CFAbsoluteTime currentTime = CFAbsoluteTimeGetCurrent();
    int seconds=currentTime-self.recordStartTime;
    if (seconds<self.shortestTime||self.recordStartTime==0) {
        
        TKLayerView *layerView=[[TKLayerView alloc] initContentView:self.rootWindow withBtnTextColor:nil];
        [layerView showTip:[NSString stringWithFormat:@"录制时间不得少于%d秒",self.shortestTime] position:TKLayerPosition_Center];
        
    }else{
        
        // 如果在收到回调前已经完成录制，不做处理。 否则可能存在完成录制时间和人脸检测不通过时间先后到达导致UI异常的问题
//        if (self.isEndOneWayVideo == YES) return;
//
//        // 标记已经完成录制
//        self.isEndOneWayVideo = YES;
        if (self.oneWayVideoEndType == TKOneWayVideoEndTypeNormal && self.isAlertViewShow == NO){
//            self.oneWayProcess = TKOneWayProcessRecordDonePrompt;
    //        [self nextOneWayProcess];
            [self stopDevice:NO];
            [self stopRecordingAction];
            [self.tkOneView showWaitTip:@"结束"];

            [self startPlaySound:@"3"];
            //延迟，让开始语音播放完跳转页面
            dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.7 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                [self.tkOneView hideWaitTip];
                // 重置人脸识别相关flag
                [self.faceDetectManager stopFaceDetect];
                // 单向完毕
                self.oneWayProcess = TKOneWayProcessOneWayDone;
                [self nextOneWayProcess];

            });
        }

        
    }
}



/**
 <AUTHOR> 2020年02月28日13:23:19
 @录制计时
 */
- (void)recordStatus{
    if (self.isRecording) {
        CFAbsoluteTime currentTime = CFAbsoluteTimeGetCurrent();
        int seconds=currentTime-self.recordStartTime;
        if(self.isReadVideo){
            // 显示录制时间,显示开始录制
            [self recordTime:seconds longestTime:self.longestTime startRecord:YES];
            //最长录制时长为-1时，不限制录制时长
            if (seconds<self.longestTime||self.longestTime==-1) {
                [self performSelector:@selector(recordStatus) withObject:nil afterDelay:1.0f];
            }else{
                [self endRecord];
            }
        }else{
            [self recordLongTime:seconds startRecord:YES]; // 显示录制时间

            [self performSelector:@selector(recordStatus) withObject:nil afterDelay:1.0f];
        }
    }
}

//最长录制时长为-1时，不限制录制时长
- (void)recordTime:(int)recordTime longestTime:(int)longestTime startRecord:(BOOL)startRecord
{
    if (longestTime!=-1) {
        
        if ([self.tkOneView respondsToSelector:@selector(recordTime:longestTime:)]) {
            
            [self.tkOneView recordTime:recordTime longestTime:longestTime]; // 录制倒计时
        }
    } else {
        if ([self.tkOneView respondsToSelector:@selector(recordLongTime:startRecord:)]) {
            
            [self.tkOneView recordLongTime:recordTime startRecord:startRecord];//录制正计时
        }
    }
}

// 录制准备时间结束代理
- (void)countDownEnd{
    
    if (self.isRecording) return;
    
    // 设置流程节点
    self.oneWayProcess = TKOneWayProcessStartRecord;
    [self nextOneWayProcess];
}

//回答问题倒计时结束
- (void)answerCountDownEnd {
    
    if (!self.isAlertViewShow && !self.forcedEndOneWayVideo) {
        // 到时间停止识别
        [self stopAsr];
        
        // 标记没有结果
        self.currentModel.asrResult = TKAsrResultNoIdentify;
        
        // 设置流程节点
        self.playQuessionProcess = TKPlayQuessionProcessAsrEnd;
        [self nextPlayQuestionProcess]; // 按流程处理
        
        return;
    }
    
    // 已经弹窗，不再继续流程
    TKLogInfo(@"思迪录制日志：已弹出弹窗或已强制结束，不再处理其他任务");
}

/**
 <AUTHOR> 2021年03月12日18:29:43
 @录制倒计时结束
 */
- (void)recordCountDownEnd{
//    [self failActionCallJsWith:@"-8" errorMsg:@"超过最大录制时间"];
}

/// 点击继续按钮，执行下一步
- (void)nextAction {
    // 结束打点
    [self addFragmentModel:TKFragmentTypeStartAsr fragmentRemark:self.currentBusinessFragmentModel.fragmentRemark isDone:YES];
    
//    self.playQuessionProcess = TKPlayQuessionProcessDone;
    [self nextPlayQuestionProcess];
}

/**
 <AUTHOR> 2019年04月26日19:07:28
 @等待用户动作倒计时结束
 */
- (void)userActionCountDownEnd {
    // 结束打点
    [self addFragmentModel:TKFragmentTypeStartAsr fragmentRemark:self.currentBusinessFragmentModel.fragmentRemark isDone:YES];
    
    [self nextPlayQuestionProcess];
}

#pragma mark - 以下方法交给子类重写-必须重写
- (NSString *)pluginCallBackfuncNo {
    NSAssert(NO, @"请重写该方法指定回调函数号");
    return @"";
}


#pragma mark - 以下方法交给子类重写-可选
- (void)bootDevice:(BOOL)isFirst {
    
    [self handleBootDeviceDidStart];
    
    [self.recordManager bootDevcie:isFirst];
}

- (void)stopDevice:(BOOL)isRelease{
    [self.recordManager stopDevice:isRelease];
}

- (void)startRecordingAction {
    // 埋点-单向_录制_开始
    NSMutableDictionary *eventDic = [NSMutableDictionary dictionaryWithDictionary:self.requestParam];
    TKPrivateOneWayVideoType oneWayVideoType = (TKPrivateOneWayVideoType)[self.pluginCallBackfuncNoToTKPrivateOneWayVideoType[self.pluginCallBackfuncNo] intValue];
    [TKStatisticEventHelper sendEvent:TKPrivateEventOneWayVideo
                         subEventName:TKPrivateSubEventOneWayVideoRecord
                             progress:TKPrivateEventProgressStart
                               result:TKPrivateEventResultNone
                          orientation:self.isLandscape ? TKPrivateVideoOrientationLandscape : TKPrivateVideoOrientationPortrait
                      oneWayVideoType:oneWayVideoType
                 prepareVideoProgress:TKPrivatePrepareVideoProgressNone
                             eventDic:eventDic];
    
    [self.recordManager startRecord];
}

- (void)stopRecordingAction {
    if (!self.isRecording) return;
    
    // 打点
    [self addFragmentModel:TKFragmentTypeFaceDetectErrorStart fragmentRemark:self.currentFaceErrorFragmentModel.fragmentRemark isDone:YES];  // 录制结束，结束质检打点（如果之前已经开始打点）
    
    // 埋点-单向_录制_结束
    NSMutableDictionary *eventDic = [NSMutableDictionary dictionaryWithDictionary:self.requestParam];
    TKPrivateOneWayVideoType oneWayVideoType = (TKPrivateOneWayVideoType)[self.pluginCallBackfuncNoToTKPrivateOneWayVideoType[self.pluginCallBackfuncNo] intValue];
    [TKStatisticEventHelper sendEvent:TKPrivateEventOneWayVideo
                         subEventName:TKPrivateSubEventOneWayVideoRecord
                             progress:TKPrivateEventProgressEnd
                               result:TKPrivateEventResultNone
                          orientation:self.isLandscape ? TKPrivateVideoOrientationLandscape : TKPrivateVideoOrientationPortrait
                      oneWayVideoType:oneWayVideoType
                 prepareVideoProgress:TKPrivatePrepareVideoProgressNone
                             eventDic:eventDic];
    
    [self.recordManager stopRecord];
    
    // cancel timer
    if (self.startRecordTimeoutTimer) {
        [self.startRecordTimeoutTimer invalidate];
        self.startRecordTimeoutTimer = nil;
    }

}

- (void)stopTTSAndAsr {
    [self stopTTS];
    [self stopAsr];
    
    if ([self.recordManager respondsToSelector:@selector(stopPlayVideo)]) {
        [self.recordManager stopPlayVideo];
    }
}

- (void)startTTS:(TKSmartQuestionModel *)model {
    if (self.isAlertViewShow || self.forcedEndOneWayVideo) {
        TKLogInfo(@"思迪录制日志：已弹出弹窗或已强制单向，不再处理其他任务.self.isAlertViewShow = %i, self.forcedEndOneWayVideo = %i", self.isAlertViewShow, self.forcedEndOneWayVideo);
        return;
    }
    
    //防止偶现到原生结果页面，语音还在播放问题
    if (self.oneWayProcess == TKOneWayProcessOneWayDone) {
        TKLogInfo(@"思迪录制日志：已经到结果页了，不需要再处理语音播放了");
        // 停止语音合成和识别
        [self stopTTSAndAsr];
        return;
    }
    
    if (model.tipContentList.count) {
        
        [self.recordManager syntheticAndPlayContents:model.tipContentList tipSpeed:model.tipSpeed];
    } else {
        
        if ([TKStringHelper isNotEmpty:model.voiceUrl]) {

            // 播放缓存TTS语音文件
            if ([self.recordManager respondsToSelector:@selector(playRemoteUrl:tipSpeed:needRequestToken:)]) {
                [self.recordManager playRemoteUrl:model.voiceUrl tipSpeed:model.tipSpeed needRequestToken:model.isNeedVoiceToken];
            }
            if ([self.recordManager respondsToSelector:@selector(createSubtitles:showTime:questionOneWordSpeed:)]) {

                [self.recordManager createSubtitles:model.displayTip showTime:@"" questionOneWordSpeed:[self getQuestionOneWordSpeed:model]];
            }

        } else {
            if ([TKStringHelper isNotEmpty:model.tipContent]) {
                // 实时合成语音
                [self.recordManager syntheticAndPlay:model.tipContent tipSpeed:model.tipSpeed];
                if ([self.recordManager respondsToSelector:@selector(createSubtitles:showTime:questionOneWordSpeed:)]) {

                    [self.recordManager createSubtitles:model.displayTip showTime:@"" questionOneWordSpeed:[self getQuestionOneWordSpeed:model]];
                }
            } else {
                [self nextPlayQuestionProcess];
            }
        }
    }
}

- (void)startPlaySound:(NSString *)flag {
    if (self.isAlertViewShow || self.forcedEndOneWayVideo) {
        TKLogInfo(@"思迪录制日志：已弹出弹窗或已强制单向，不再处理其他任务");
        return;
    }
    
    self.isPlayingSound = YES;
    [self.recordManager playVideo:flag];
}

- (void)stopTTS {
    [self.recordManager stopSyntheticAndPlay];
}

- (void)startAsr {
    if (self.isAlertViewShow || self.forcedEndOneWayVideo) {
        TKLogInfo(@"思迪录制日志：已弹出弹窗或已强制单向，不再处理其他任务");
        return;
    }
    
    [self.recordManager startRecognize];
}

- (void)stopAsr {
    [self.recordManager stopRecognize];
}

- (UIImage *_Nullable)videoFirstImage {
    // 先获取本地的
    UIImage *image = [self.recordManager getLocalVideoPreViewImage:[self getLocalOneWayVideoPath]];
    
    if (image == nil && [self.recordManager respondsToSelector:@selector(getVideoPreViewImage:)]) {
        image = [self.recordManager getVideoPreViewImage:[NSURL URLWithString:self.fullVideoFileURLString]];
    }
    
    return image;
}

- (BOOL)shouldPlayBeepSound {
    if (self.requestParam[@"needPlayBeep"] && [self.requestParam[@"needPlayBeep"] intValue] == 1) {
        return YES;
    } else {
        return NO;
    }
}

- (BOOL)skipPreview {
    return (self.requestParam[@"isSuccessShow"] && [self.requestParam[@"isSuccessShow"] intValue] == 0);
}

- (void)activeDevice {
    if ([self.recordManager respondsToSelector:@selector(activeDevice)]) {
        [self.recordManager activeDevice];
    }
}

- (void)deactiveDevice {
    if ([self.recordManager respondsToSelector:@selector(deactiveDevice)]) {
        [self.recordManager deactiveDevice];
    }
}

- (BOOL)isLocalRecord {
    return YES;
}

- (BOOL)isUseTChat {
    return NO;
}

-(void)handleWaitTip{
    if(self.isReadVideo){
        if ([self.tkOneView respondsToSelector:@selector(liveEndWait)]) {
           [self.tkOneView liveEndWait];
       }
    }else{
        if (self.beforeVideoArray.count) {
            TKSmartQuestionModel *model = self.beforeVideoArray.firstObject;
            
            // 针对旧版本处理
            // 旧版录制前的播报逻辑和录制中、录制后的逻辑不一样。主要是UI不一样
            NSString *oldVersion = [self.requestParam getStringWithKey:@"oldVersion"];
            if ([oldVersion isEqualToString:@"4.0.0"]) {
            
                if ([self.tkOneView respondsToSelector:@selector(liveEndWait:)]) {
                    [self.tkOneView liveEndWait:model.displayTip];
                } else if ([self.tkOneView respondsToSelector:@selector(liveEndWait)]) {
                    [self.tkOneView liveEndWait];
                }
            } else {
                self.tkOneView.currentDisplayPrompt=@"";
                [self.tkOneView startRecorderVideoPlay:model.displayTip isOneLineShow:model.isNeedAsr isHtmlString:model.isHtmlTip questionOneWordSpeed:[self getQuestionOneWordSpeed:model]];
            }
        }
    }

}

#pragma mark - 以下方法供子类调用
- (void)handleBootDeviceDidStart
{
    if ([self.tkOneView respondsToSelector:@selector(tkShowLoading)]) {
        [self.tkOneView tkShowLoading];
    }
}

- (void)handleBootDeviceDidSuccess
{
	// 埋点-单向_准备视频_成功
    NSMutableDictionary *eventDic = [NSMutableDictionary dictionaryWithDictionary:self.requestParam];
    eventDic[@"errorNo"] = [NSString stringWithFormat:@"0"];;
    eventDic[@"event_err"] = @"";
    TKPrivateOneWayVideoType oneWayVideoType = (TKPrivateOneWayVideoType)[self.pluginCallBackfuncNoToTKPrivateOneWayVideoType[self.pluginCallBackfuncNo] intValue];
    [TKStatisticEventHelper sendEvent:TKPrivateEventOneWayVideo
                         subEventName:TKPrivateSubEventOneWayVideoPrepareVideo
                             progress:TKPrivateEventProgressNone
                               result:TKPrivateEventResultSuccess
                          orientation:self.isLandscape ? TKPrivateVideoOrientationLandscape : TKPrivateVideoOrientationPortrait
                      oneWayVideoType:oneWayVideoType
                 prepareVideoProgress:TKPrivatePrepareVideoProgressNone
                             eventDic:eventDic];

    [self handleWaitTip];
    
    // 刚开始建立连接，网络通道占用较大，此时马上播放会有卡顿
    CGFloat tts_delay = 0.0f;
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(tts_delay * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{

        self.oneWayProcess = TKOneWayProcessPlayPrompt;
        [self nextOneWayProcess]; // 走下一个主流程
    });
    

    // 之前的操作耗时，在发送语音合成请求之后才收起加载动画
    if ([self.tkOneView respondsToSelector:@selector(tkHideLoading)]) {
        [self.tkOneView tkHideLoading];
    }


}

- (void)handleBootDeviceDidFail:(NSString *)errorMsg
{
    // 埋点-单向_准备视频_失败
    NSMutableDictionary *eventDic = [NSMutableDictionary dictionaryWithDictionary:self.requestParam];
    eventDic[@"errorNo"] = @"-2";
    eventDic[@"event_err"] = errorMsg;
    TKPrivateOneWayVideoType oneWayVideoType = (TKPrivateOneWayVideoType)[self.pluginCallBackfuncNoToTKPrivateOneWayVideoType[self.pluginCallBackfuncNo] intValue];
    [TKStatisticEventHelper sendEvent:TKPrivateEventOneWayVideo
                         subEventName:TKPrivateSubEventOneWayVideoPrepareVideo
                             progress:TKPrivateEventProgressNone
                               result:TKPrivateEventResultError
                          orientation:self.isLandscape ? TKPrivateVideoOrientationLandscape : TKPrivateVideoOrientationPortrait
                      oneWayVideoType:oneWayVideoType
                 prepareVideoProgress:TKPrivatePrepareVideoProgressNone
                             eventDic:eventDic];
    
    if ([self.tkOneView respondsToSelector:@selector(tkHideLoading)]) {
        [self.tkOneView tkHideLoading];
    }
    [self failActionCallJsWith:@"-2" errorMsg:errorMsg];
}

- (void)handleDeviceRunFail:(NSString *)errorMsg
{
//    [self failActionCallJsWith:@"-8" errorMsg:errorMsg];
    [self showAlertView:@"提示" message:errorMsg tag:7001];
}

- (void)handleSpeechSynthesisDidStart
{
    NSString *tipContent = self.currentModel.asrResultModel ? self.currentModel.asrResultModel.tipContent : self.currentModel.tipContent;
    [self handleSpeechSynthesisDidStartWithIndex:0 synthesisArray:@[tipContent]];
}

- (void)handleSpeechSynthesisDidStartWithIndex:(int)index synthesisArray:(NSArray *)synthesisArray
{
    NSString *tipContent = synthesisArray[index];
    
    // 埋点-单向_tts_开始
    NSMutableDictionary *eventDic = [NSMutableDictionary dictionaryWithDictionary:self.requestParam];
    eventDic[@"tipContent"] = tipContent;
    TKPrivateOneWayVideoType oneWayVideoType = (TKPrivateOneWayVideoType)[self.pluginCallBackfuncNoToTKPrivateOneWayVideoType[self.pluginCallBackfuncNo] intValue];
    [TKStatisticEventHelper sendEvent:TKPrivateEventOneWayVideo
                         subEventName:TKPrivateSubEventOneWayVideoTTS
                             progress:TKPrivateEventProgressStart
                               result:TKPrivateEventResultNone
                          orientation:self.isLandscape ? TKPrivateVideoOrientationLandscape : TKPrivateVideoOrientationPortrait
                      oneWayVideoType:oneWayVideoType
                 prepareVideoProgress:TKPrivatePrepareVideoProgressNone
                             eventDic:eventDic];
    
    // 正在播放固定视频，不需要展示文案，比如“滴”一声
    if (self.isPlayingSound == YES) {
        
        self.isPlayingSound = NO;
        return;
    }
    
    if(self.isReadVideo){
        if ([self.tkOneView respondsToSelector:@selector(liveEndWait)]) {
           [self.tkOneView liveEndWait];
       }
    }else{
        // 针对旧版本处理
        // 旧版录制前的播报逻辑和录制中、录制后的逻辑不一样。主要是UI不一样
        if (self.oneWayProcess == TKOneWayProcessPlayPrompt) {
            NSString *oldVersion = [self.requestParam getStringWithKey:@"oldVersion"];
            if ([oldVersion isEqualToString:@"4.0.0"]) {
            
                if ([self.tkOneView respondsToSelector:@selector(liveEndWait:)]) {
                    [self.tkOneView liveEndWait:self.currentModel.displayTip];
                } else if ([self.tkOneView respondsToSelector:@selector(liveEndWait)]) {
                    [self.tkOneView liveEndWait];
                }
                
                return;
            }
        }
    }

    
    // 若asrResultModel有值，则目前正在播放识别结果
    if (self.currentModel.asrResultModel) {
        self.tkOneView.currentDisplayPrompt=@"";
        [self.tkOneView startRecorderVideoPlay:self.currentModel.asrResultModel.displayTip isOneLineShow:self.currentModel.asrResultModel.isNeedAsr isHtmlString:self.currentModel.isHtmlTip questionOneWordSpeed:[self getQuestionOneWordSpeed:self.currentModel]];
    } else {
        
        if (index == 0) {
            self.tkOneView.currentDisplayPrompt=self.currentModel.displayPrompt;
            // 单段语音 | 多段语音的第一段
            [self.tkOneView startRecorderVideoPlay:self.currentModel.displayTip isOneLineShow:self.currentModel.isNeedAsr isHtmlString:self.currentModel.isHtmlTip questionOneWordSpeed:[self getQuestionOneWordSpeed:self.currentModel]];
        } else {
            // 多段语音。展示的文案都是同一段长文案。分拆的原因是阿里语音只支持实时转换300字以内的语音
            // 第二段语音设置文案开始滚动.不需要重新设置语音
            // TODO
        }
    }
    
    // 打点
    [self addFragmentModel:TKFragmentTypeTTSStart fragmentRemark:@"" isDone:NO];
    self.currentBusinessFragmentModel.text = tipContent;
}



- (void)handleSpeechSynthesisDidFail:(NSString *)errorMsg {
    // 埋点-单向_tts_异常
    NSMutableDictionary *eventDic = [NSMutableDictionary dictionaryWithDictionary:self.requestParam];
    eventDic[@"tipContent"] = self.currentModel.asrResultModel ? self.currentModel.asrResultModel.tipContent : self.currentModel.tipContent;
    eventDic[@"event_err"] = errorMsg;
    TKPrivateOneWayVideoType oneWayVideoType = (TKPrivateOneWayVideoType)[self.pluginCallBackfuncNoToTKPrivateOneWayVideoType[self.pluginCallBackfuncNo] intValue];
    [TKStatisticEventHelper sendEvent:TKPrivateEventOneWayVideo
                         subEventName:TKPrivateSubEventOneWayVideoTTS
                             progress:TKPrivateEventProgressNone
                               result:TKPrivateEventResultError
                          orientation:self.isLandscape ? TKPrivateVideoOrientationLandscape : TKPrivateVideoOrientationPortrait
                      oneWayVideoType:oneWayVideoType
                 prepareVideoProgress:TKPrivatePrepareVideoProgressNone
                             eventDic:eventDic];
    
    [self showAlertView:@"提示" message:errorMsg tag:7001];
}

- (void)handleSpeechSynthesisDidPlayDone
{
    if (self.isReadVideo) {
//        if (self.isClickTakeRecordBtn == NO) {
//            // 创建定时器抓取图片做人脸检测
//            [self.faceDetectManager startFaceDetect];
//        }
        // 重置标志位
        self.isPlayingSound = NO;
        [self nextOneWayProcess];
    } else {
        
        if ([TKStringHelper isNotEmpty:self.currentModel.tipContent]) {
            
            [self handleSpeechSynthesisDidPlayDoneWithIndex:0 synthesisArray:@[self.currentModel.tipContent]];
        } else {
            [self nextPlayQuestionProcess];
        }
    }
}

- (void)handleSpeechSynthesisDidPlayDoneWithIndex:(int)index synthesisArray:(NSArray *)synthesisArray
{
    if (self.isAlertViewShow || self.forcedEndOneWayVideo) {
        TKLogInfo(@"思迪录制日志：已弹出弹窗或已强制单向，不再处理其他任务");
        return;
    }
    
//    TKLogDebug(@"handleSpeechSynthesisDidPlayDoneWithIndex index = %i, synthesisArray.count = %i",index, synthesisArray.count);
    if (index == (synthesisArray.count - 1)) {
        if (self.isPlayingSound == YES) {
            
            // 重置标志位
            self.isPlayingSound = NO;
        } else {
        
        	// 埋点-单向_tts_结束
        	NSMutableDictionary *eventDic = [NSMutableDictionary dictionaryWithDictionary:self.requestParam];
        	eventDic[@"tipContent"] = self.currentModel.asrResultModel ? self.currentModel.asrResultModel.tipContent : self.currentModel.tipContent;
        	TKPrivateOneWayVideoType oneWayVideoType = (TKPrivateOneWayVideoType)[self.pluginCallBackfuncNoToTKPrivateOneWayVideoType[self.pluginCallBackfuncNo] intValue];
        	[TKStatisticEventHelper sendEvent:TKPrivateEventOneWayVideo
                             subEventName:TKPrivateSubEventOneWayVideoTTS
                                 progress:TKPrivateEventProgressEnd
                                   result:TKPrivateEventResultNone
                              orientation:self.isLandscape ? TKPrivateVideoOrientationLandscape : TKPrivateVideoOrientationPortrait
                          oneWayVideoType:oneWayVideoType
                     prepareVideoProgress:TKPrivatePrepareVideoProgressNone
                                 eventDic:eventDic];
    	}
        
        TKLogInfo(@"思迪录制日志：音频播放结束，执行下一个播放流程");
        
        [self nextPlayQuestionProcess];
    } else {
        // TODO
        // 停止文案滚动
    }
    
    // 打点
    [self addFragmentModel:TKFragmentTypeTTSStart fragmentRemark:@"" isDone:YES];
}


- (void)hanldeSpeechRecognizeDidStart {
    
    [self addFragmentModel:TKFragmentTypeStartAsr fragmentRemark:@"客户回答" isDone:NO];

    // 埋点-单向_asr_开始
    NSMutableDictionary *eventDic = [NSMutableDictionary dictionaryWithDictionary:self.requestParam];
    TKPrivateOneWayVideoType oneWayVideoType = (TKPrivateOneWayVideoType)[self.pluginCallBackfuncNoToTKPrivateOneWayVideoType[self.pluginCallBackfuncNo] intValue];
    [TKStatisticEventHelper sendEvent:TKPrivateEventOneWayVideo
                         subEventName:TKPrivateSubEventOneWayVideoASR
                             progress:TKPrivateEventProgressStart
                               result:TKPrivateEventResultNone
                          orientation:self.isLandscape ? TKPrivateVideoOrientationLandscape : TKPrivateVideoOrientationPortrait
                      oneWayVideoType:oneWayVideoType
                 prepareVideoProgress:TKPrivatePrepareVideoProgressNone
                             eventDic:eventDic];
    
    //允许去做语音识别了
    self.transcriberStarted = true;

    if ([self.tkOneView respondsToSelector:@selector(liveContinue:isOneLineShow:isHtmlString:questionOneWordSpeed:)]) {
        
        [self.tkOneView liveContinue:self.currentModel.displayPrompt isOneLineShow:YES isHtmlString:self.currentModel.isHtmlPrompt questionOneWordSpeed:[self getQuestionOneWordSpeed:self.currentModel]];
    } else {
        if ([self.tkOneView respondsToSelector:@selector(liveContinue:)]) {
            [self.tkOneView liveContinue:self.currentModel.displayPrompt];
        }
    }
    
    [self.tkOneView playEndView:[self.currentModel.waitTime intValue] prompt:nil noAnswerPromptTime:[self.currentModel.noAnswerPromptTime intValue]];
}

- (void)handleSpeechRecognizeDidFail:(NSString *)errorMsg
{
    // 埋点-单向_asr_异常
    NSMutableDictionary *eventDic = [NSMutableDictionary dictionaryWithDictionary:self.requestParam];
    eventDic[@"event_err"] = errorMsg;
    TKPrivateOneWayVideoType oneWayVideoType = (TKPrivateOneWayVideoType)[self.pluginCallBackfuncNoToTKPrivateOneWayVideoType[self.pluginCallBackfuncNo] intValue];
    [TKStatisticEventHelper sendEvent:TKPrivateEventOneWayVideo
                         subEventName:TKPrivateSubEventOneWayVideoASR
                             progress:TKPrivateEventProgressNone
                               result:TKPrivateEventResultError
                          orientation:self.isLandscape ? TKPrivateVideoOrientationLandscape : TKPrivateVideoOrientationPortrait
                      oneWayVideoType:oneWayVideoType
                 prepareVideoProgress:TKPrivatePrepareVideoProgressNone
                             eventDic:eventDic];
    
    [self showAlertView:@"提示" message:errorMsg tag:7001];
}

//语音实时识别结果处理
- (void)handleSpeechRecognizeResult:(NSString *)identifyString {
    
    // 埋点-单向_asr_结束
    NSMutableDictionary *eventDic = [NSMutableDictionary dictionaryWithDictionary:self.requestParam];
    eventDic[@"asrResult"] = [TKStringHelper isEmpty:identifyString] ? @"" : identifyString;
    TKPrivateOneWayVideoType oneWayVideoType = (TKPrivateOneWayVideoType)[self.pluginCallBackfuncNoToTKPrivateOneWayVideoType[self.pluginCallBackfuncNo] intValue];
    [TKStatisticEventHelper sendEvent:TKPrivateEventOneWayVideo
                         subEventName:TKPrivateSubEventOneWayVideoASR
                             progress:TKPrivateEventProgressEnd
                               result:TKPrivateEventResultNone
                          orientation:self.isLandscape ? TKPrivateVideoOrientationLandscape : TKPrivateVideoOrientationPortrait
                      oneWayVideoType:oneWayVideoType
                 prepareVideoProgress:TKPrivatePrepareVideoProgressNone
                             eventDic:eventDic];
    
    // 防止一次识别结果中，返回多个有效结果，重复处理
    if (self.transcriberStarted != YES) {
        TKLogInfo(@"思迪录制日志:上一笔已经处理完毕，又进来新的一笔识别结果=%@。重复了", identifyString);
        return;
    };
    
   if ([TKStringHelper isNotEmpty:identifyString]) {
       //是否需要同音字转换
       if (self.requestParam[@"convertWords"] && [self.requestParam[@"convertWords"] isKindOfClass:[NSDictionary class]]) {
           identifyString = [TKCommonUtil convertWords:self.requestParam[@"convertWords"] withWord:identifyString];
       }
       
       self.answeredErrorInfo = identifyString;
       TKLogInfo(@"思迪录制日志:识别到-%@",identifyString);
       TKSmartQuestionModel *model = self.currentModel;
                              
       //错误答案正则
       NSRegularExpression *regularExpressionFailans = [NSRegularExpression regularExpressionWithPattern:model.failans options:0 error:nil];
       NSArray *arrayOfAllMatchesFailans = [regularExpressionFailans matchesInString:identifyString options:0 range:NSMakeRange(0, [identifyString length])];
       if (arrayOfAllMatchesFailans.count > 0) {
           
           self.currentModel.asrResult = TKAsrResultFail;
           
           // 继续下一个播放流程
           self.playQuessionProcess = TKPlayQuessionProcessAsrEnd;
           [self nextPlayQuestionProcess];

           return ;
       }

                              
       //正确答案正则
       NSRegularExpression *regularExpression = [NSRegularExpression regularExpressionWithPattern:model.standardans options:0 error:nil];
       NSArray *arrayOfAllMatches = [regularExpression matchesInString:identifyString options:0 range:NSMakeRange(0, [identifyString length])];
       if (arrayOfAllMatches.count>0) {
           
           TKLogInfo(@"思迪录制日志:语音识别到了是，或者好");
           //正确回答
           self.currentModel.asrResult = TKAsrResultSuccess;
           
           // 结束打点
           self.currentBusinessFragmentModel.text = identifyString;
           self.currentBusinessFragmentModel.asrStatus = TKAsrStatusSuccess;
           [self addFragmentModel:TKFragmentTypeStartAsr fragmentRemark:self.currentBusinessFragmentModel.fragmentRemark isDone:YES];
           
           // 继续下一个播放流程
           self.playQuessionProcess = TKPlayQuessionProcessAsrEnd;
           [self nextPlayQuestionProcess];
           return ;
       }
       
       // 无关回答
       [self.tkOneView answerPromptType:NO identifyString:identifyString];
       // 持续识别，直到超时
       self.currentModel.asrResult = TKAsrResultNoIdentify;
    }
}

- (void)handleRecordDidStart
{
    // 展示录制时间信息
    [self.tkOneView hideWaitTip];
    
    self.isRecording = YES;
    
    self.recordStartTime = CFAbsoluteTimeGetCurrent();
    //----------将nsdate按formatter格式转成nsstring
    self.recordStartDateString = [self.formatter stringFromDate:[NSDate date]];
    
    // 录制过程中持续继续人脸检测失败次数
    [self.faceDetectManager startRecordFailCount];
    
    [self recordLongTime:0 startRecord:YES]; // 显示录制时间,显示开始录制
    [self performSelector:@selector(recordStatus) withObject:nil afterDelay:0.0f];
    
    // 提前添加字幕
    if ([self.recordManager respondsToSelector:@selector(createSubtitles:showTime:questionOneWordSpeed:)]) {
        if (self.questionIndex < self.questionArray.count) {
            TKLogInfo(@"思迪录制日志：录制流程-播放问题");
            // 播放问题语音
            self.currentModel = self.questionArray[self.questionIndex];
        }
        
        [self.recordManager createSubtitles:self.currentModel.displayTip showTime:@"" questionOneWordSpeed:@""];
    }
    
    self.oneWayProcess = TKOneWayProcessPlayQuestion;
    
    if(self.isReadVideo){
        [self.tkOneView readingGuideTip];
    }else{
        // 更新进度条
        [self updateProcessStatus];
        
        [self nextOneWayProcess];
    }
    

}

- (void)handleRecordDidSuccess:(NSString *)filePath fullFilePath:(NSString *)fullFilePath videoLenth:(int)videoLenth
{
    if (self.isAlertViewShow || self.forcedEndOneWayVideo) {
        TKLogInfo(@"思迪录制日志：已弹出弹窗或已强制单向，不再处理其他任务");
        return;
    }
    
    self.isRecording = NO;
    
    self.fullVideoFileURLString = [TKStringHelper encodeURL:fullFilePath];
    self.relateVideoFilePath = filePath;
    self.videoLength = [NSString stringWithFormat:@"%i", videoLenth / 1000];
    self.recordEndDateString = [self.formatter stringFromDate:[NSDate date]];
    
    
    TKLogInfo(@"思迪录制日志:视频时长%@", self.videoLength);
    TKLogInfo(@"预览日志----视频地址%@", [self fullVideoFileURLString]);
    
    // 播放结束提示音
    if (self.oneWayVideoEndType == TKOneWayVideoEndTypeNormal && self.isAlertViewShow == NO) {
        
        self.oneWayProcess = TKOneWayProcessRecordDonePrompt;
        [self nextOneWayProcess];
    }
}

- (void)handleRecordDidFail:(NSString *)errorMsg
{
//    [self failActionCallJsWith:@"-8" errorMsg:errorMsg];
    [self showAlertView:@"提示" message:errorMsg tag:7001];
}

- (void)handleNetworkError:(NSString *_Nullable)errorMsg {
    // @"网络不稳定，请切换网络再试"
    [self failActionCallJsWith:@"-9" errorMsg:errorMsg];
}


- (void)handleFaceDetectResult:(TKFacePassStatus)facePassStatus faceCompareStatus:(TKFaceCompareStatus)faceCompareStatus facNumberPassStatus:(TKSingleFaceStatus)facNumberPassStatus errorMsg:(NSString *)errorMsg
{
    // 未开始录制前，默认不可录制
//    if (self.isRecording == NO) {
//        [self.tkOneView enableTakeRecord:NO];
//    }
    
    if (facNumberPassStatus == TKSingleFaceStatusNotSingle
        || facePassStatus == TKFacePassStatusNotPass
        || faceCompareStatus == TKFaceCompareStatusNotPass) {
        [self addFragmentModel:TKFragmentTypeFaceDetectErrorStart fragmentRemark:errorMsg isDone:NO];
    }
    
    // 检查人脸数量情况
    if (facNumberPassStatus == TKSingleFaceStatusNotSingle) {
//        errorMsg = [NSString stringWithFormat:@"%@(%i)", errorMsg, self.faceDetectManager.faceNumberFailureCount];
        [self.tkOneView liveWarning:errorMsg];
        return;
    }
    
    // 检验人脸在框情况
    if (facePassStatus == TKFacePassStatusNotPass) {
//        errorMsg = [NSString stringWithFormat:@"%@(%i)", errorMsg, self.faceDetectManager.faceDetectFailureCount];
        [self.tkOneView liveWarning:errorMsg];
        return;
    } else {
        // 在框通过允许录制
        [self.tkOneView enableTakeRecord:YES];
        
        if (self.isInitAutoRecordTimer == NO) {
            self.isInitAutoRecordTimer = YES;
            // 自动开始倒计时
            [self.tkOneView atuoStartTakeRecord:[self.requestParam getDoubleWithKey:@"startAutoRecordTime"]];
        }
    }
    
    // 检查人脸比对情况
    if (faceCompareStatus == TKFaceCompareStatusNotPass) {
//        errorMsg = [NSString stringWithFormat:@"%@(%i)", errorMsg, self.faceDetectManager.faceCompareFailureCount];
        [self.tkOneView liveWarning:errorMsg forceDisplay:YES];
        return;
    }
    
    [self addFragmentModel:TKFragmentTypeFaceDetectErrorStart fragmentRemark:self.currentFaceErrorFragmentModel.fragmentRemark isDone:YES];
    
    // 通过情况隐藏提示
    [self.tkOneView liveWarning:nil];
}

#pragma mark - TKVideoRecordManagerDelegate
// 语音合成开始回调
- (void)speechSynthesisDidStart {
    
    [self handleSpeechSynthesisDidStart];
}

/// 多段语音合成开始回调
/// - Parameter index: 当前正在合成的语音索引
- (void)speechSynthesisDidStartWithIndex:(int)index synthesisArray:(NSArray *)synthesisArray {
    [self handleSpeechSynthesisDidStartWithIndex:index synthesisArray:synthesisArray];
}

// 语音合成失败
- (void)speechSynthesisDidFail:(NSString *)errorMsg{
    [self handleSpeechSynthesisDidFail:errorMsg];
}

// 语音合成播放结束回调
- (void)speechSynthesisDidPlayDone {
    
    [self handleSpeechSynthesisDidPlayDone];
}

- (void)speechSynthesisDidPlayDoneWithIndex:(int)index synthesisArray:(NSArray *)synthesisArray {
    [self handleSpeechSynthesisDidPlayDoneWithIndex:index synthesisArray:synthesisArray];
}

// 每个语音包分片识别结果
// @param result 一段语音每次识别结果
- (void)speechRecognizeOnSliceRecognize:(NSString *)result {
     [self handleSpeechRecognizeResult:result];
}

/// 语音识别开始回调
- (void)speechRecognizeDidStart {

    [self hanldeSpeechRecognizeDidStart];
}


// 语音识别失败回调
- (void)speechRecognizeDidFail:(NSString *)errorMsg {
    [self handleSpeechRecognizeDidFail:errorMsg];
}

/// 连接视频服务器回调
- (void)connectServerRoomDidComplete:(BOOL)success errorMsg:(NSString *)errorMsg {
    
    if (success) {
        
        TKLogInfo(@"思迪录制日志：设备初始化成功，开始录制");
        [self handleBootDeviceDidSuccess];

    } else {
        
        [self handleBootDeviceDidFail:errorMsg];
    }
}

/// 断开视频服务器回调
- (void)disconnectServerRoomDidComplete:(BOOL)success errorMsg:(NSString *)errorMsg {
    TKLogInfo(@"思迪录制日志：视频录制异常退出 errorMsg=%@", errorMsg);
    [self handleDeviceRunFail:errorMsg];
}

/// 开始录制回调
- (void)recordStartCallBack {
    
    [self handleRecordDidStart];
}

/// 结束录制回调
- (void)recordStopCallBack:(NSString *)filePath fullFilePath:(NSString *)fullFilePath videoLenth:(int)videoLenth catonLength:(int)catonLength{
    
    if (self.isExitProgress) return;    // 直接抛弃回调结果
    
    if (catonLength>0) {
        //视频默认卡顿最大占比
        float maxCatonRatio=0.2f;

        if ([TKStringHelper isNotEmpty:self.requestParam[@"maxCatonRatio"]]) {
            //h5传入卡顿比
            maxCatonRatio=[self.requestParam[@"maxCatonRatio"] floatValue];
        }
        //当前视频卡顿占比
        float currentCatonRatio=(float)catonLength/videoLenth;
        if (currentCatonRatio>=maxCatonRatio) {
            self.requestParam[@"isMoreMaxCaton"]=@"1";//超过最大卡顿比
        }
    }
    [self handleRecordDidSuccess:filePath fullFilePath:fullFilePath videoLenth:videoLenth];
}

/// 结束录制出错回调
- (void)recordStopErrocCallBack:(NSString *)errorMsg {
    TKLogInfo(@"思迪录制日志：视频录制异常退出 errorMsg=%@", errorMsg);
    [self handleRecordDidFail:errorMsg];
}

/// 视频流回调
- (void)OnVideoDataCallBack:(UIImage *)image {

    [self detectFace:image pixelBuffer:nil];
}

/// 音频流回调
//- (void)OnAudioDataCallBack:(NSData*)buf {
//    [self isQuite:buf];
//}

/// 返回TChat Rtc视频流数据
/// @param buffRef CVPixelBufferRef
- (void)onVideoDataCallBackWithPixelBuffer:(CVPixelBufferRef)pixelBuffer {

    [self detectFace:nil pixelBuffer:pixelBuffer];
}

- (NSDictionary *)pluginCallBackfuncNoToTKPrivateOneWayVideoType
{
    // 由于没有记录插件号，这里用插件回调号曲线救国
    return @{
        @"60031" : @(TKPrivateOneWayVideoTypeNormal),
        @"60027" : @(TKPrivateOneWayVideoTypeLocalSmart),
        @"60058" : @(TKPrivateOneWayVideoTypeTChatSmart),
        @"60073" : @(TKPrivateOneWayVideoTypeTChatDigitalMan),
        @"60092" : @(TKPrivateOneWayVideoTypeNormal),
        @"60090" : @(TKPrivateOneWayVideoTypeLocalSmart),
        @"60078" : @(TKPrivateOneWayVideoTypeTChatSmart),
    };
}

#pragma mark - lazyloading
/**
 <AUTHOR> 2019年04月13日17:30:09
 @初始化懒加载视频播放器
 @return 视频播放器
 */
- (TKPlayer *)player{
    if (!_player) {
        _player =  [TKPlayer playerWithURL:nil contentView:nil];
        _player.delegate = self;
//        _player.speakerPlayback = YES;
        _player.isInvertLayer = NO;
        _player.shouldAutoPlay = NO;
        _player.timeRefreshInterval=0.1;
        if (!([self.requestParam getIntWithKey:@"resultType"] == 2))
            _player.downLoadFileFullPath = [self getLocalOneWayVideoPath]; // TKPlayer最后会把文件删掉，本地录制返回地址供外层下载的需求会冲突
        
        NSString *loadRemoteVideoMaxTime = [self.requestParam getStringWithKey:@"loadRemoteVideoMaxTime"];
        if ([TKStringHelper isNotEmpty:loadRemoteVideoMaxTime]) {
            _player.loadRemoteVideoMaxTime = loadRemoteVideoMaxTime.intValue;
        }
        
        NSString *disableDownload = [self.requestParam getStringWithKey:@"disableDownload"];
        _player.disableDownload = [disableDownload isEqualToString:@"1"];
//        _player.disableDownload = YES;

    }
    return _player;
}


/// 人脸在框检测工具类
- (TKFaceDetectManager *)faceDetectManager {
    if (!_faceDetectManager) {
//        NSMutableDictionary *tempDic = self.requestParam.mutableCopy;
//        tempDic[@"url"] = @"";
//        _faceDetectManager = [[TKFaceDetectManager alloc] initWithConfig:tempDic];
        _faceDetectManager = [[TKFaceDetectManager alloc] initWithConfig:self.requestParam];
        _faceDetectManager.delegate = self;
        _faceDetectManager.targetRect = self.tkOneView.boxRect;
        if (self.requestParam[@"isShowHeadRect"]&&[self.requestParam[@"isShowHeadRect"] integerValue] == 0) {
            //不需要头像框的UI场景，做全屏检测
            _faceDetectManager.targetRect=self.tkOneView.frame;
            _faceDetectManager.avPreviewSize=self.tkOneView.frame.size;
        }
        
        if([[self pluginCallBackfuncNo] isEqualToString:@"60073"]||[[self pluginCallBackfuncNo] isEqualToString:@"60031"]){
            //虚拟人和朗读普通单向不支持隐藏头像框做全屏
            _faceDetectManager.targetRect=self.tkOneView.boxRect;
            _faceDetectManager.avPreviewSize=CGSizeMake(UISCREEN_WIDTH, UISCREEN_HEIGHT);
        }
//        _faceDetectManager.detectUseLocalService = YES;
//        TKLogInfo(@"TKFaceDetectManager = %@", _faceDetectManager);
    }
    return _faceDetectManager;
}

/**
 <AUTHOR> 2019年10月23日13:10:48
 @初始化懒加载alertTipView
 @return alertTipView
 */
-(TKOneWayVideoAlertTipView *)alertTipView{
    if (!_alertTipView) {
        CGRect rect = CGRectMake(self.view.TKTop, self.view.TKLeft, MIN(UISCREEN_WIDTH, UISCREEN_HEIGHT), MAX(UISCREEN_WIDTH, UISCREEN_HEIGHT));
        if (self.isLandscape) rect= CGRectMake(self.view.TKTop, self.view.TKLeft, MAX(UISCREEN_WIDTH, UISCREEN_HEIGHT), MIN(UISCREEN_WIDTH, UISCREEN_HEIGHT));
        _alertTipView=[[TKOneWayVideoAlertTipView alloc] initWithFrame:rect withTipString:self.requestParam[@"uploadTipString"] withParam:self.requestParam];
        _alertTipView.delegate=self;
    }
    return _alertTipView;
}


//iOS13默认全屏适配
- (UIModalPresentationStyle)modalPresentationStyle {
    return UIModalPresentationFullScreen;
}

- (TKOpenAccountService *)openAccountService {
    if (_openAccountService == nil) {
        _openAccountService = [TKOpenAccountService new];
    }
    return _openAccountService;
}

/**
 <AUTHOR> 2019年03月02日10:39:21
 @懒加载初始化视频预览视图
 @return UIView
 */
- (UIView *)avPreviewView{
    if (!_avPreviewView) {
        _avPreviewView = [[UIView alloc] initWithFrame:[UIScreen mainScreen].bounds];
        [_avPreviewView setBackgroundColor:[UIColor grayColor]];
//        [_avPreviewView.layer addSublayer:self.avPreviewLayer];
        
    }
    return _avPreviewView;
}

/**
 <AUTHOR> 2019年04月03日10:09:41
 @初始化懒加载单向视频界面
 @return 单向视频界面
 */
- (UIView<TKBaseVideoRecordViewProtocol> *)tkOneView {
    
//    BOOL isBaseClass = [self isMemberOfClass:NSClassFromString(@"TKBaseVideoRecordViewController")];
//    NSAssert(isBaseClass, @"%@没有实现具体的tkOneView", self);
    return _tkOneView;
}

/**
 <AUTHOR>
 @初始化单向视频结果页面
 */
- (UIView<TKBaseVideoRecordEndViewProtocol> *)tkOneEndView{
    
//    BOOL isBaseClass = [self isMemberOfClass:NSClassFromString(@"TKBaseVideoRecordViewController")];
//    NSAssert(isBaseClass, @"%@没有实现具体的tkOneEndView", self);
    return _tkOneEndView;
}

- (NSObject<TKRecordManagerProtocol> *)recordManager {
    
//    BOOL isBaseClass = [self isMemberOfClass:NSClassFromString(@"TKBaseVideoRecordViewController")];
//    NSAssert(isBaseClass, @"%@没有实现具体的recordManager", self);
    return _recordManager;
}

- (NSDateFormatter *)formatter {
    if (_formatter == nil) {
        _formatter = [[NSDateFormatter alloc] init];
        _formatter.locale = [[NSLocale alloc] initWithLocaleIdentifier:@"zh_CN"];
        // ----------设置你想要的格式,hh与HH的区别:分别表示12小时制,24小时制
        [_formatter setDateFormat:@"yyyy-MM-dd HH:mm:ss"];
    }
    return _formatter;
}

- (TKLayerView *)layerView{
    if (!_layerView) {
        _layerView = [[TKLayerView alloc] initContentView:self.avPreviewView withBtnTextColor:@"#ffffff"];
    }
    return _layerView;
}

/**
 <AUTHOR> 2022年05月25日09:43:41
 @初始化懒加载videoAlertView
 @return videoAlertView
 */
-(TKVideoAlertView *)videoAlertView{
    if (!_videoAlertView) {
        CGRect rect = CGRectMake(self.view.TKTop, self.view.TKLeft, MIN(UISCREEN_WIDTH, UISCREEN_HEIGHT), MAX(UISCREEN_WIDTH, UISCREEN_HEIGHT));
        if (self.isLandscape) rect= CGRectMake(self.view.TKTop, self.view.TKLeft, MAX(UISCREEN_WIDTH, UISCREEN_HEIGHT), MIN(UISCREEN_WIDTH, UISCREEN_HEIGHT));
        _videoAlertView=[[TKVideoAlertView alloc] initWithFrame:rect requestParams:self.requestParam];
        _videoAlertView.delegate=self;
    }
    return _videoAlertView;
}

/// 播放视频工具视图
- (TKPlayerControlView *)playerControlView{
    if (!_playerControlView) {

        _playerControlView = [[TKPlayerControlView alloc] initWithFrame:self.tkOneEndView.videoShowImgView.frame withParam:self.requestParam];
        _playerControlView.autoHiddenTimeInterval = 5; // 3
        _playerControlView.autoFadeTimeInterval = 0.5;
        _playerControlView.prepareShowLoading = YES;
        _playerControlView.prepareShowControlView = YES;
        
//        _playerControlView.delegate = self;
        if (self.isLandscape) _playerControlView.landScapeControlViewRect = CGRectMake(0, 0, MAX(self.view.TKWidth, self.view.TKHeight), MIN(self.view.TKWidth, self.view.TKHeight));
        _playerControlView.needShowForwardView = self.isLandscape;
        _playerControlView.portraitControlView.isLandscapeRecordPreview = self.isLandscape;
        _playerControlView.landScapeControlView.isLandscapeRecordPreview = self.isLandscape;
        _playerControlView.fastViewAnimated = NO;
        _playerControlView.autoHiddenWhenPlay = NO;
    }
    return _playerControlView;
}

- (TKZFPlayerController *)playerController {
    if (!_playerController) {

        _playerController = [TKZFPlayerController playerWithPlayerManager:self.player containerView:self.tkOneEndView.videoShowImgView];
        _playerController.customAudioSession = YES;
        _playerController.controlView = self.playerControlView;
        
        /// 播放完成
        @tkzf_weakify(self)
        self.playerController.playerDidToEnd = ^(id  _Nonnull asset) {
            @tkzf_strongify(self)
                            
            [self.player seekToTime:0 completionHandler:nil];
            // 重新显示controllviwe
//            [self.playerControlView showTitle:@"" coverImage:self.tkOneEndView.videoShowImg fullScreenMode:TKZFFullScreenModePortrait];
//            [self.playerControlView.portraitControlView playBtnSelectedState:NO];   // 默认播放，这里要修改为暂停状态
//            [self.playerControlView.landScapeControlView playBtnSelectedState:NO];  // 默认播放，这里要修改为暂停状态
        };
        
        self.playerController.playerPrepareToPlay = ^(id<TKZFPlayerMediaPlayback>  _Nonnull asset, NSURL * _Nonnull assetURL) {
            @tkzf_strongify(self)
            self.playerController.disableGestureTypes = TKZFPlayerDisableGestureTypesNone;
        };
        
        self.playerController.playerPlayFailed = ^(id<TKZFPlayerMediaPlayback>  _Nonnull asset, id  _Nonnull error) {
            @tkzf_strongify(self)
            self.playerController.disableGestureTypes = TKZFPlayerDisableGestureTypesAll;
        };
        
        // 横屏时旋转预览全屏页面
        if (self.isLandscape) {
//            self.playerController.orientationObserver.portraitViewController.disablePortraitGestureTypes = TKZFDisablePortraitGestureTypesTap; // 禁用单击收起全屏手势
            self.playerController.orientationObserver.portraitViewController.fullScreenSize = CGSizeMake(UISCREEN_HEIGHT, UISCREEN_WIDTH);
            self.playerController.orientationObserver.portraitFullScreenMode = TKZFPortraitFullScreenModeScaleToFill;
            self.playerController.orientationObserver.presentationSize = CGSizeMake(UISCREEN_HEIGHT, UISCREEN_WIDTH);
            self.playerController.orientationObserver.portraitViewController.fullScreenAnimation = NO;
            self.playerController.orientationWillChange = ^(TKZFPlayerController * _Nonnull player, BOOL isFullScreen) {
                @tkzf_strongify(self)

                if (isFullScreen) {
                    CGFloat angle = (90.0f * M_PI) / 180.0f; // 旋转90度，顺时针方向
                    self.playerController.orientationObserver.portraitViewController.view.transform = CGAffineTransformIdentity; // 先重置偏移
                    CGAffineTransform transform = CGAffineTransformMakeRotation(angle);
                    self.playerController.orientationObserver.portraitViewController.view.transform = transform;
                    self.playerController.orientationObserver.portraitViewController.view.frame = CGRectMake(0, 0, MIN(self.view.TKWidth, self.view.TKHeight), MAX(self.view.TKWidth, self.view.TKHeight));
                }
            };
        } else {
            self.playerController.orientationWillChange = ^(TKZFPlayerController * _Nonnull player, BOOL isFullScreen) {
                @tkzf_strongify(self)
                
                if (isFullScreen) {
                    self.statusBarStyle = UIStatusBarStyleLightContent;
                } else {
                    self.statusBarStyle = TKUIStatusBarStyleDefault;
                }
            };
        };;
        
        
    }
    return _playerController;
}

- (NSMutableArray *)fragmentModelList {
    if (!_fragmentModelList) {
        _fragmentModelList = [NSMutableArray array];
        
        TKVideoFragmentModel *model0 = [[TKVideoFragmentModel alloc] init];
        model0.fragmentType = TKFragmentTypeNone;
        model0.fragmentRemark = @"观看完整视频";
        [_fragmentModelList addObject:model0];
    }
    return _fragmentModelList;
}

@end
