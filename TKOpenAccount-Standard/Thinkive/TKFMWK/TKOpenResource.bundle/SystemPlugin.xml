<!--H5调用原生基础公共插件-->
<?xml version="1.0" encoding="UTF-8"?>
<plugins>
  <item name="50000" value="TKPlugin50000" description="获取应用配置"></item>
  <item name="50001" value="TKPlugin50001" description="获取应用设备的详细信息"></item>
  <item name="50002" value="TKPlugin50002" description="修改应用配置"></item>
  <item name="50010" value="TKPlugin50010" description="获取应用版本号"></item>
  <item name="50011" value="TKPlugin50011" description="获取应用名称"></item>
  <item name="50020" value="TKPlugin50020" description="获取设备型号"></item>
  <item name="50021" value="TKPlugin50021" description="获取设备SDK版本号"></item>
  <item name="50022" value="TKPlugin50022" description="获取设备唯一码"></item>
  <item name="50023" value="TKPlugin50023" description="获取设备IP地址"></item>
  <item name="50024" value="TKPlugin50024" description="获取设备MAC地址"></item>
  <item name="50025" value="TKPlugin50025" description="获取设备地理位置"></item>
  <item name="50030" value="TKPlugin50030" description="获取设备网络环境"></item>
  <item name="50031" value="TKPlugin50031" description="获取设备网络运营商"></item>
  <item name="50040" value="TKPlugin50040" description="保存数据到内存缓存"></item>
  <item name="50041" value="TKPlugin50041" description="从内存缓存获取数据"></item>
  <item name="50042" value="TKPlugin50042" description="保存数据到用户Cache缓存目录"></item>
  <item name="50043" value="TKPlugin50043" description="从用户Cache缓存目录获取数据"></item>
  <item name="50100" value="TKPlugin50100" description="通知原生H5加载完毕"></item>
  <item name="50101" value="TKPlugin50101" description="通知原生打开模块"></item>
  <item name="50102" value="TKPlugin50102" description="通知原生打开/关闭左右菜单"></item>
  <item name="50103" value="TKPlugin50103" description="设置左右抽屉不生效的区域范围(此范围内滑动不触发)"></item>
  <item name="50104" value="TKPlugin50104" description="设置APP主题"></item>
  <item name="50105" value="TKPlugin50105" description="退出APP应用"></item>
  <item name="50106" value="TKPlugin50106" description="显示Toast提示"></item>
  <item name="50108" value="TKPlugin50108" description="隐藏/显示系统底部TabBar"></item>
  <item name="50109" value="TKPlugin50109" description="通知原生打开系统浏览器的网页"></item>
  <item name="50110" value="TKPlugin50110" description="实现打开原生的信息提示框接口"></item>
  <item name="50112" value="TKPlugin50112" description="获取备用站点中最优站点"></item>
  <item name="50114" value="TKPlugin50114" description="通知原生关闭模块"></item>
  <item name="50115" value="TKPlugin50115" description="通知原生打开一个WebView加载url"></item>
  <item name="50116" value="TKPlugin50116" description="通知原生设备震动"></item>
  <item name="50118" value="TKPlugin50118" description="代理发送http/https相关的网络请求"></item>
  <item name="50119" value="TKPlugin50119" description="设置webView状态栏的颜色"></item>
  <item name="50120" value="TKPlugin50120" description="H5插件的检测"></item>
  <item name="50122" value="TKPlugin50122" description="滑动返回，通知原生H5页面发生跳转，方便原生记录H5历史堆栈"></item>
  <item name="50123" value="TKPlugin50123" description="H5通知原生WebView加载失败"></item>
  <item name="50124" value="TKPlugin50124" description="获取APP进入后台的状态"></item>
  <item name="50200" value="TKPlugin50200" description="获取软件是否安装"></item>
  <item name="50201" value="TKPlugin50201" description="进行软件的版本升级或者安装"></item>
  <item name="50202" value="TKPlugin50202" description="打开软件"></item>
  <item name="50203" value="TKPlugin50203" description="检测软件版本并实现自动更新下载"></item>
  <item name="50210" value="TKPlugin50210" description="打开原生键盘"></item>
  <item name="50211" value="TKPlugin50211" description="关闭原生键盘"></item>
  <item name="50220" value="TKPlugin50220" description="拨打电话"></item>
  <item name="50221" value="TKPlugin50221" description="发送短信"></item>
  <item name="50222" value="TKPlugin50222" description="弹出手机通讯录"></item>
  <item name="50224" value="TKPlugin50224" description="获取手机号码"></item>
  <item name="50225" value="TKPlugin50225" description="获取手机通讯录数据"></item>
  <item name="50228" value="TKPlugin50228" description="保存用户数据到通信录"></item>
  <item name="50230" value="TKPlugin50230" description="弹出原生分享菜单"></item>
  <item name="50231" value="TKPlugin50231" description="发布分享内容"></item>
  <item name="50240" value="TKPlugin50240" description="查看pdf文件"></item>
  <item name="50250" value="TKPlugin50250" description="弹出日期控件"></item>
  <item name="50252" value="TKPlugin50252" description="弹出滚动选择控件"></item>
  <item name="50260" value="TKPlugin50260" description="设置手势密码"></item>
  <item name="50261" value="TKPlugin50261" description="验证手势密码"></item>
  <item name="50263" value="TKPlugin50263" description="获取手势密码的设置状态"></item>
  <item name="50264" value="TKPlugin50264" description="设置手势密码的设置状态"></item>
  <item name="50266" value="TKPlugin50266" description="关闭掉弹出的手势密码界面"></item>
  <item name="50270" value="TKPlugin50270" description="弹出生成图片二维码组件"></item>
  <item name="50271" value="TKPlugin50271" description="弹出扫描图片二维码组件"></item>
  <item name="50273" value="TKPlugin50273" description="选择照片或者拍照，经过裁剪实现图片上传"></item>
  <item name="50275" value="TKPlugin50275" description="进行视频文件的播放"></item>
  <item name="50276" value="TKPlugin50276" description="自动识别二维码图片(一般用于长按图片触发)"></item>
  <item name="50277" value="TKPlugin50277" description="批量上传图片"></item>
  <item name="50300" value="TKPlugin50300" description="老版本统计页面访问次数，停留时长等（此接口只用在页面进入和退出的时候调用，APP会自动进行页面访问统计）"></item>
  <item name="50301" value="TKPlugin50301" description="老版本统计自定义事件次数"></item>
  <item name="50302" value="TKPlugin50302" description="老版本统计页面错误事件"></item>
  <item name="50303" value="TKPlugin50303" description="老版本统计页面访问次数，停留时长等（H5自己统计页面的进入和退出的时间，通过此接口保存到服务器）"></item>
  <item name="50400" value="TKPlugin50400" description="大数据版本获取H5可视化埋点的页面配置信息"></item>
  <item name="50401" value="TKPlugin50401" description="大数据版本提交H5可视化埋点的页面配置信息"></item>
  <item name="50404" value="TKPlugin50404" description="大数据版本统计页面可视化埋点事件"></item>
  <item name="50405" value="TKPlugin50405" description="大数据版本统计页面固定埋点事件"></item>
  <item name="50406" value="TKPlugin50406" description="大数据版本统计页面错误事件"></item>
  <item name="50407" value="TKPlugin50407" description="大数据版本统计H5设置SID"></item>
  <item name="50408" value="TKPlugin50408" description="大数据版本统计H5页面访问开始事件"></item>
  <item name="50409" value="TKPlugin50409" description="大数据版本统计H5切换设置账户"></item>
  <item name="50410" value="TKPlugin50410" description="大数据版本统计页面固定埋点结束事件"></item>
  <item name="50411" value="TKPlugin50411" description="大数据版本统计H5页面访问结束事件"></item>
  <item name="50500" value="TKPlugin50500" description="统一模块交互消息定义"></item>
  <item name="50501" value="TKPlugin50501" description="H5日志统计"></item>
</plugins>
