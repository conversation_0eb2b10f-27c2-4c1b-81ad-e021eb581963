<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>Headers/QCPlayerError.h</key>
		<data>
		GcBglYzaVwjgHfWYqQ+ZUSG3Uao=
		</data>
		<key>Headers/QCloudMediaPlayer.h</key>
		<data>
		olErFINwSKeBXrmWwn1OgGKJoec=
		</data>
		<key>Headers/QCloudMediaPlayer2.h</key>
		<data>
		WJT6QPmSe5jTGllxIhKljhOzgds=
		</data>
		<key>Headers/QCloudOfflineAuthInfo.h</key>
		<data>
		tolrRjjKQqWWjKFyUooedkLsIpc=
		</data>
		<key>Headers/QCloudTTSEngine.h</key>
		<data>
		UQB6vhAlGndL5YeDl1ZEw3vyc2M=
		</data>
		<key>Headers/TtsError.h</key>
		<data>
		325otzQ/Cu8q0kGDUVU4SMibWZU=
		</data>
		<key>Info.plist</key>
		<data>
		5GE0ifEmtfdwYjPeObosS3YNe9I=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>Headers/QCPlayerError.h</key>
		<dict>
			<key>hash</key>
			<data>
			GcBglYzaVwjgHfWYqQ+ZUSG3Uao=
			</data>
			<key>hash2</key>
			<data>
			B5vdIZRO7860RHiBmzU42oztYKV5/+TSI8nZImlfEHI=
			</data>
		</dict>
		<key>Headers/QCloudMediaPlayer.h</key>
		<dict>
			<key>hash</key>
			<data>
			olErFINwSKeBXrmWwn1OgGKJoec=
			</data>
			<key>hash2</key>
			<data>
			gpKrrZN48eY8o7QbUConF+wlyLu5Rhc6/27H42TVj98=
			</data>
		</dict>
		<key>Headers/QCloudMediaPlayer2.h</key>
		<dict>
			<key>hash</key>
			<data>
			WJT6QPmSe5jTGllxIhKljhOzgds=
			</data>
			<key>hash2</key>
			<data>
			aWSR35tX7sKARPhOHbJmpq4FYVNmlR7ST5aqDc4aLn4=
			</data>
		</dict>
		<key>Headers/QCloudOfflineAuthInfo.h</key>
		<dict>
			<key>hash</key>
			<data>
			tolrRjjKQqWWjKFyUooedkLsIpc=
			</data>
			<key>hash2</key>
			<data>
			KLQptLzwtaJYFeFD49+ue23NLWqaZAyjWljo3VimNwg=
			</data>
		</dict>
		<key>Headers/QCloudTTSEngine.h</key>
		<dict>
			<key>hash</key>
			<data>
			UQB6vhAlGndL5YeDl1ZEw3vyc2M=
			</data>
			<key>hash2</key>
			<data>
			hsHHpz1PLxxiMLlxsy6ZqRbS7NlmoYybdFTUHQnrsXA=
			</data>
		</dict>
		<key>Headers/TtsError.h</key>
		<dict>
			<key>hash</key>
			<data>
			325otzQ/Cu8q0kGDUVU4SMibWZU=
			</data>
			<key>hash2</key>
			<data>
			Jtc052fx72HaSNcpnKz0cL2f8VNTIYa1+DhDUcQX2v8=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
