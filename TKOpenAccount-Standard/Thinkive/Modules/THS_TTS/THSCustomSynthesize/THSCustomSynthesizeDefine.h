//
//  THSCustomSynthesizeDefine.h
//  THSCustomSynthesize
//
//  Created by wuguangqing on 2017/11/28.
//  Copyright © 2017年 wuguangqing. All rights reserved.
//

#ifndef THSCustomSynthesizeDefine_h
#define THSCustomSynthesizeDefine_h

#import <Foundation/Foundation.h>

#pragma mark - SDK版本名称

static NSString * const TTSCustomVersionName = @"1.3.0";

#pragma mark - 调试日志级别
typedef enum THSCustomSynthesizeDebugLogLevel
{
    TTSCustomDebugLogLevelOff = 0,
    TTSCustomDebugLogLevelFatal = 1,
    TTSCustomDebugLogLevelError = 2,
    TTSCustomDebugLogLevelWarning = 3,
    TTSCustomDebugLogLevelInformation = 4,
    TTSCustomDebugLogLevelDebug = 5,
    TTSCustomDebugLogLevelTrace = 6
} THSCustomSynthesizeDebugLogLevel;

#pragma mark - 合成播放状态
typedef enum TTSCustomPlayerState {          //合成播放状态
    THS_CustomSYNTHESIZER_PLAYER_PLAYING,    //播放中
    THS_CustomSYNTHESIZER_PLAYER_PAUSE,      //暂停
    THS_CustomSYNTHESIZER_PLAYER_QUIT,       //停止
} TTSCustomPlayerState;

#pragma mark - 合成错误码
typedef enum: NSInteger {
    TTS_CustomERROR_NONETWORK = -2001,                  //网络连接不可用
    TTS_CustomERROR_INITFAILED = -2101,                 //权限验证未通过
    TTS_CustomERROR_CLAIMTOKENFAILED = -2102,           //申请令牌失败
    TTS_CustomERROR_DATAEXCEPTION = -2103,              //数据解析失败，通常是服务器返回的应答数据格式不对
    TTS_CustomERROR_AbnormalNoDecode = -2104,           //未申请令牌的非法操作
    TTS_CustomERROR_Frequently_Access = -2105,          //服务器忙,通常是解码服务器异常
    TTS_CustomERROR_NETWORKABNORMAL = -2106,            //网络请求超时
    TTS_CustomERROR_EmptyModel = -2107,                 //模型为空
    TTS_CustomERROR_NoModel = -2108,                    //模型不存在
    TTS_CustomERROR_AbnormalOther = -2199,              //其他异常
    TTS_CustomERROR_PLAY_BAD_STREAM = -2202,            //音频流错误
    TTS_CustomERROR_TEXT_EMPTY = -2302,                 //文本为空
    TTS_CustomERROR_TEXT_TOO_LONG = -2303,              //文本过长
    TTS_CustomERROR_AbnormalTTSResult = -2305,          //语音合成失败
    TTS_CustomERROR_APPIDLimit = -2308,                 //该AppID可用引擎数量已达到上限
    TTS_CustomERROR_TokenTimeOut = -2310,               //令牌被回收
} TTSCustomErrorCode;

#endif /* THSCustomSynthesizeDefine_h */
