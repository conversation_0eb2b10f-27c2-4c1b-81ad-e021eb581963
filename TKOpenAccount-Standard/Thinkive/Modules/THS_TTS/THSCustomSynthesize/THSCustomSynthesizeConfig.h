//
//  THSCustomSynthesizeConfig.h
//  THSCustomSynthesize
//
//  Created by wuguangqing on 2017/6/22.
//  Copyright © 2017年 wuguangqing. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "THSCustomSynthesizeDefine.h"

@interface THSCustomSynthesizeConfig : NSObject

/**
 获取单例
 @return 单例对象
 */
+ (THSCustomSynthesizeConfig *)sharedInstance;

/**
 *音量 0-200，默认100
 */
@property (nonatomic, assign) NSInteger vol;

/**
 *语速 50-200，默认100
 */
@property (nonatomic, assign) NSInteger speed;

/**
 *语调 -100-100，默认0
 */
@property (nonatomic, assign) NSInteger pitch;

/**
模型编号
 */
@property (nonatomic, copy) NSString *modelNumber;


/**
 采样率  默认16k   0=16k  1=8k
 */
@property (nonatomic, assign) int samplingRate;

/**
 音频格式  默认wav   0=wav  1=mp3
 */
@property (nonatomic, assign) int audioType;

/**
 * 排队时间  默认13s 阈值 0-20
 * 发送合成文本，进入排队等待阶段。
 * 若排队时间到了第一段文本语音数据还没回来，报错，网络超时
 */
@property (nonatomic, assign) unsigned int synthesizeTokenTimeout;

@property (nonatomic, assign) THSCustomSynthesizeDebugLogLevel debugLogLevel;

/**
 * 是否自动播放合成结果,默认YES
 * 设置为NO将直接返回音频数据,应用层自己存储与控制播放
 */
@property (nonatomic, assign) BOOL isAutoPlay;

/**
 句子停顿时间，可设置值0-20，数字越大，停顿越久，默认值8
 */
@property (nonatomic, assign) NSInteger endSil;

/**
  用户编号
 */
@property (nonatomic, copy) NSString *userId;

/**
 播放进度回调间隔,单位:秒
 范围0.5 -   max   ,默认 0.5s
 */
@property (nonatomic, assign) float progressCallBackPeriod;

/**
 倍速  默认1.0 正常速度 , 范围 0.5 - 2.0
 仅在 isAutoPlay 为YES时生效
 */
@property (nonatomic, assign) float playRate;

/**
 * 服务器ip地址
 */
@property (nonatomic, copy) NSString * ip;

/**
 * 服务器端口号
 */
@property (nonatomic, assign) int port;
@end
