//
//  THSCustomAudioInfo.h
//  THSCustomSynthesize
//
//  Created by liming on 2019/4/18.
//  Copyright © 2019年 liming. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface Timestamp : NSObject
@property (nonatomic, copy) NSString * txt; // 当前时间戳对应文本
@property (nonatomic, copy) NSString * startTime; // 当前时间戳对应开始时间
@property (nonatomic, copy) NSString * endTime; // 当前时间戳对应开始时间
@end

@interface THSCustomAudioInfo : NSObject
@property (nonatomic, copy) NSString *data;//音频数据的base64字符串
@property (nonatomic, assign) BOOL  isFinish;//是否合成完毕
@property (nonatomic, assign) int order;//当前的段数
@property (nonatomic, assign) int sum;//总段数(只是记录用,判断是否合成完毕请用isFinish字段)
@property (nonatomic, copy) NSArray<Timestamp *> * time; //时间戳信息,精确到 字
@end

NS_ASSUME_NONNULL_END
