//
//  THSCustomSynthesize.h
//  THSCustomSynthesize
//
//  Created by liming on 2018/11/30.
//  Copyright © 2018年 liming. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "THSCustomAudioInfo.h"
@protocol THSCustomSynthesizeDelegate <NSObject>

@optional

/**
 合成发生错误
 @param error 错误信息
 */
- (void)customSpeechSynthesizeOccurError:(NSError *)error;

//开始播放
- (void)customSpeechSynthesizePlayBegin;

//播放完毕
- (void)customSpeechSynthesizePlayEnd;

/**
 不自动播放模式 ---合成音频的数据信息
 @param audioInfo 音频数据
*/
- (void)customSpeechSynthesizeResult:(THSCustomAudioInfo *)audioInfo;

/** 播放进度
 text: 播放的文本
 start: 播放开始的文本位置
 end:  播放结束的文本位置
 */
- (void)customSpeechSynthesizePlayOnSpeakChangeProgress:(float)progress Start:(NSInteger)start End:(NSInteger)end Text:(NSString *)text;
@end


@interface THSCustomSynthesize : NSObject
@property (nonatomic, weak) id<THSCustomSynthesizeDelegate> delegate;

/**
 *  获取THSSpeechSynthesize 单例
 *
 *  @return 单例对象
 */
+ (THSCustomSynthesize *)sharedInstance;

/**
 *  初始化语音合成SDK
 
 *  @param appid       应用ID
 *  @param appKey      应用密钥
 */
- (void)registerSpeechSynthesizeWithAppId: (NSString *)appid appKey: (NSString *)appKey;

/**
 *  SDK解析完文本后直接由SDK调用播放器播放
 *  @param text       文本
 *  @param delegate     回调接口
 */
- (void)speakingText:(NSString *)text WithDelegate:(id)delegate;

/**
 *  停止播放，停止合成
 */
- (void)stopSpeaking;

/**
 *  暂停播放 (合成不会暂停) 返回当前播放状态
 */
- (void)pauseSpeaking;

/**
 *  恢复播放，返回当前播放状态
 */
- (void)resumeSpeaking;
@end
