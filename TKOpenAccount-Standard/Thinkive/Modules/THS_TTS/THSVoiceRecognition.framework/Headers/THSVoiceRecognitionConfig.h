//
//  THSVoiceRecognitionConfig.h
//  THSVoiceRecognition
//
//  Created by wuguangqing on 2017/6/22.
//  Copyright © 2017年 wuguangqing. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "THSVoiceRecognitionDefine.h"
#import "THSRecoginModel.h"
@interface THSVoiceRecognitionConfig : NSObject

/**
 获取单例
 
 @return 单例对象
 */
+ (THSVoiceRecognitionConfig *)sharedInstance;


/**
 *  是否开启超时功能, 默认为YES(开启)
 *  开启后，不需要用户调用SDK的stopRecord方法，sdk会根据vadBos，vadEos，KEY_SPEECH_TIMEOUT来自主断开本次语音识别.
 */
@property (nonatomic, assign) BOOL vadEnable;

/**
 *  设置前端点  (在开启vadEnable后生效)
 *  从调用录音开始,  vadBosBOS 秒没有发出声音即结束本次语音识别
 *  范围限定 (1s - 10s) 默认为3s
 */
@property (nonatomic, assign) float vadBos;

/**
 *  设置后端点 (默认为4s, 在开启vadEnable后生效)
 *  调用录音开始说话后, vadEos 秒没有发出声音即结束本次语音识别
 *  范围限定 (0.1s - 10s) 默认为3s
 */
@property (nonatomic, assign) float vadEos;

/**
 *  语音输入超时 (默认为60s, 无论是否开启vadEnable 都会生效)
 *  总的说话时长，超过这个阈值就自动结束本次语音识别
 *  范围限定 (1s - 60s)
 */
@property (nonatomic, assign) float keySpeechTimeout;

/**
 *识别语言引擎,默认2101 16k中文 通用
 */
@property (nonatomic, copy) NSString * engineType;

/**
 * 排队时间  默认13s 阈值 0-20
 * 结束录音等待服务器返回数据。
 * 若时间到了还没识别结果回来，报错，网络超时
 */
@property (nonatomic, assign) unsigned int recognizeTokenTimeout;

/**
 * 实时查询语音识别结果的时间间隔(以秒为时间单位)   default = 1
 * 如果设置为小于等于0，则不会启动实时语音识别功能
 */
@property (nonatomic, assign) float recogTimeDuration;

/**
 * 调试日志等级，默认关闭调试日志
 */
@property (nonatomic, assign) THSVoiceRecognitionDebugLogLevel debugLogLevel;

/**
 *是否转阿拉伯数字
 */
@property (nonatomic, assign) Byte reprocess;

/**
 *是否进行性别识别  默认不接入   0代表否 1代表是
 */
@property (nonatomic, assign) int sex;

/**
 *是否过降噪 默认否   0代表否 1代表是
 */
@property (nonatomic, assign) int denoise;

/**
 * 是否删除此次识别SDK的内部产生的录音数据文件，默认YES，删除
 */
@property (nonatomic, assign) BOOL deleteFile;

@property (nonatomic, copy) NSString *userId;

/**
 *是否开启情绪多模态,默认关闭   0代表否 1代表是
 */
@property (nonatomic, assign) int emoMul;

/**
 *是否开启情绪.默认开启   0代表否 1代表是
 */
@property (nonatomic, assign) int emotion;

/**
 * 服务器ip地址
 */
@property (nonatomic, copy) NSString * ip;

/**
 * 服务器端口号
 */
@property (nonatomic, assign) int port;

/**
 * 是否SDK内部录音获取音频数据,默认YES
 */
@property (nonatomic, assign) BOOL  isUserRecord;

/**
*采样率 默认16k
*/
@property (nonatomic, assign) NSInteger samplerate;

@end
