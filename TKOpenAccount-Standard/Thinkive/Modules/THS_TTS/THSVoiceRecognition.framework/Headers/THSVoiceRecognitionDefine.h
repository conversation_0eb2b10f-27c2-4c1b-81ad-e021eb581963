//
//  THSVoiceRecognitionDefine.h
//  THSVoiceRecognition
//
//  Created by wuguangqing on 2017/11/29.
//  Copyright © 2017年 wuguangqing. All rights reserved.
//
#ifndef THSVoiceRecognitionDefine_h
#define THSVoiceRecognitionDefine_h

#import <Foundation/Foundation.h>

static NSString * const ShortASRVersionName = @"3.3.0";

#pragma mark - 录音超时类型
typedef enum THSVoiceRecognitionTimeoutState
{
    ASRVoiceRecognitionBosTimeout = 0,      //前端超时
    ASRVoiceRecognitionEosTimeout = 1,      //后端超时
    ASRVoiceRecognitionSpeechTimeout = 2,   //录音超时
} THSVoiceRecognitionTimeoutState;

#pragma mark - 调试日志级别
typedef enum THSVoiceRecognitionDebugLogLevel
{
    ASRDebugLogLevelOff = 0,
    ASRDebugLogLevelFatal = 1,
    ASRDebugLogLevelError = 2,
    ASRDebugLogLevelWarning = 3,
    ASRDebugLogLevelInformation = 4,
    ASRDebugLogLevelDebug = 5,
    ASRDebugLogLevelTrace = 6
} THSVoiceRecognitionDebugLogLevel;

#pragma mark - 错误编码
typedef enum THSVoiceRecognitionErrorCode
{
    ASR_ERROR_NoNetwork = -2001,                  //网络连接不可用
    ASR_ERROR_MicrophoneBusy = -2003,       //麦克风正忙
    ASR_ERROR_MicrophoneNotAuthorized = -2004,       //麦克风没权限
    ASR_ERROR_InitFailed = -2101,             //权限验证未通过
    ASR_ERROR_ClaimTokenFailed = -2102,       //申请令牌失败
    ASR_ERROR_DataException = -2103,          //数据解析失败，通常是服务器返回的应答数据格式不对
    ASR_ERROR_AbnormalNoDecode = -2104,           //未申请令牌的非法操作
    ASR_ERROR_AbnormalFrequentlyAccess = -2105,    //服务器忙，客户端访问过于频繁，appid访问达到上限
    ASR_ERROR_NetworkAbnormal = -2106,        //网络请求超时
    ASR_ERROR_AbnormalOther = -2199,          //其他异常
    ASR_ERROR_AbnormalSend = -2203,           //语音数据包异常
    ASR_ERROR_AbnormalRecorderEnd = -2204,    //语音数据结束包异常
    ASR_ERROR_AbnormalRecogResult = -2205,   //语音识别结果异常
    ASR_ERROR_AbnormalASR = -2206,          //语音识别失败,通常是解码服务器异常

} THSVoiceRecognitionErrorCode;

#endif

