//
//  THSVoiceRecognitionDefine.h
//  THSVoiceRecognition
//
//  Created by wuguangqing on 2017/11/29.
//  Copyright © 2017年 wuguangqing. All rights reserved.
//
#ifndef THSLongVoiceDefine_h
#define THSLongVoiceDefine_h

#import <Foundation/Foundation.h>

static NSString * const LongVersionName = @"2.2.2";

#pragma mark - 录音超时类型
typedef enum THSLongVoiceTimeoutState
{
    ASRLongVoiceBosTimeout = 0,         //前端超时
    ASRLongVoiceEosTimeout = 1,         //后端超时
    ASRLongVoiceSpeechTimeout = 2,   //录音超时
} THSLongVoiceTimeoutState;

#pragma mark - 调试日志级别
typedef enum THSLongVoiceDebugLogLevel
{
    ASRDebugLogLevelOff_Long = 0,
    ASRDebugLogLevelFatal_Long = 1,
    ASRDebugLogLevelError_Long= 2,
    ASRDebugLogLevelWarning_Long = 3,
    ASRDebugLogLevelInformation_Long = 4,
    ASRDebugLogLevelDebug_Long = 5,
    ASRDebugLogLevelTrace_Long = 6
} THSLongVoiceDebugLogLevel;

#pragma mark - 错误编码
typedef enum THSLongVoiceErrorCode
{
    ASR_ERROR_StratNoNetwork_Long = -2001,                      //网络连接不可用
    ASR_ERROR_MicrophoneBusy_Long = -2003,                    //麦克风正忙
    ASR_ERROR_MicrophoneNotAuthorized_Long = -2004,       //麦克风无权限
    ASR_ERROR_NoNetwork_Long = -2007,                            //网络异常，无法继续识别
    ASR_ERROR_InitFailed_Long = -2101,                                //权限验证未通过
    ASR_ERROR_ClaimTokenFailed_Long = -2102,                   //申请令牌失败
    ASR_ERROR_DataException_Long = -2103,                        //数据解析失败，通常是服务器返回的应答数据格式不对
    ASR_ERROR_AbnormalNoDecode_Long = -2104,                //未申请令牌的非法操作
    ASR_ERROR_AbnormalFrequentlyAccess_Long = -2105,      //服务器忙，客户端访问过于频繁，appid访问达到上限
    ASR_ERROR_NetworkAbnormal_Long = -2106,                   //网络请求超时
    ASR_ERROR_AbnormalNoTokenResponse = -2107,             //转写服务异常，请稍后再试
    ASR_ERROR_AbnormalOther_Long = -2199,                       //其他异常
    ASR_ERROR_AbnormalSend_Long = -2203,                       //语音数据包异常
    ASR_ERROR_AbnormalRecorderEnd_Long = -2204,            //语音数据结束包异常
    ASR_ERROR_AbnormalRecogResult_Long = -2205,             //语音识别结果异常
    ASR_ERROR_AbnormalASR_Long = -2206,                        //语音识别失败,通常是解码服务器异常
    ASR_ERROR_AbnormalFile = -2207,                                 //录音文件无效
} THSLongVoiceErrorCode;

#endif

