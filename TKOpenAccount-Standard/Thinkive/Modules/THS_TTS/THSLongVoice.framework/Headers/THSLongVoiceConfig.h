//
//  THSVoiceRecognitionConfig.h
//  THSVoiceRecognition
//
//  Created by wuguangqing on 2017/6/22.
//  Copyright © 2017年 wuguangqing. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "THSLongVoiceDefine.h"

@interface THSLongVoiceConfig : NSObject

/**
 获取单例
 
 @return 单例对象
 */
+ (THSLongVoiceConfig *)sharedInstance;


/**
 *  是否开启超时功能, 默认为YES(开启)
 *  开启后，不需要用户调用SDK的stopRecord方法，sdk会根据vadBos，vadEos，KEY_SPEECH_TIMEOUT来自主断开本次语音识别.
 */
@property (nonatomic, assign) BOOL vadEnable;

/**
 *  设置前端点  (在开启vadEnable后生效)
 *  从调用录音开始,  vadBosBOS 秒没有发出声音即结束本次语音识别
 *  范围限定 (1s - 10s) 默认为3s
 */
@property (nonatomic, assign) float vadBos;

/**
 *  设置后端点 (默认为4s, 在开启vadEnable后生效)
 *  调用录音开始说话后, vadEos 秒没有发出声音即结束本次语音识别
 *  范围限定 (0.1s - 10s) 默认为3s
 */
@property (nonatomic, assign) float vadEos;

/**
 * 排队时间  默认13s 阈值 0-20
 * 结束录音等待服务器返回数据。
 * 若时间到了还没识别结果回来，报错，网络超时
 */
@property (nonatomic, assign) unsigned int recognizeTokenTimeout;


/**
 * 调试日志等级，默认关闭调试日志
 */
@property (nonatomic, assign) THSLongVoiceDebugLogLevel debugLogLevel;

/**
 *是否接入后处理  默认不接入   0代表不接入  1代表接入
 */
@property (nonatomic, assign) int reprocess;

/**
 标识是否正在录音
 */
@property (nonatomic, assign) BOOL isRecording;

/**
 *是否进行性别识别  默认不接入   0代表否 1代表是
 */
@property (nonatomic, assign) int sex;


//识别引擎类型 ,默认2105
@property (nonatomic, copy) NSString * engineType;

//用户id
@property (nonatomic, copy) NSString * userId;

/**
 *性别识别类型     1代表8k  2代表16k（默认） 3代表重叠音识别
 */
@property (nonatomic, assign) int sexType;

/**
 *是否过降噪 默认否   0代表否 1代表是
 */
@property (nonatomic, assign) int denoise;

/**
 *识别模式,0.会议模式(默认)    1.翻译模式     2 .降采样率模式
 */
@property (nonatomic, assign) NSInteger recognitionWay;

/**
 * 0:中翻英(默认)    1: 英翻中 ,当且仅当 recognitionWay 为1时有效
 */
@property (nonatomic, assign) NSInteger transWay;


@property (nonatomic, assign) NSInteger sampRate;

@end
