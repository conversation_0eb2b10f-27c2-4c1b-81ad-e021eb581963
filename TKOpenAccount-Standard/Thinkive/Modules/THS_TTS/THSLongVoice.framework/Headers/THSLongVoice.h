//
//  THSVoiceRecognition.h
//  THSVoiceRecognition
//
//  Created by wuguangqing on 2017/6/21.
//  Copyright © 2017年 wuguangqing. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "THSLongVoiceConfig.h"

@protocol THSLongVoiceDelegate <NSObject>

@optional
/**
 *  全部文本解析完后的回调
 *  @param info 文本内容
 */
- (void)longVoiceRecognitionResult:(NSDictionary *)info;

/**
 *  因为超时而结束本次录音的回调，包括开启VAD功能后超时和整个录音时长超时,无需再调用stop
 */
- (void)longVoiceRecognitionEndOfSpeechTimeout:(THSLongVoiceTimeoutState)state;
/**
 * 录音设备已启动，开始录音。
 * @param fileName 此次录音完整版的路径
 */
- (void)longVoiceRecognitionOnStartOfSpeech:(NSString *)fileName;

/**
 *  实时文本内容解析
 
 @param info 文本内容
 */
- (void)longVoiceRecognitionOntimeResult:(NSDictionary *)info;

/**
 *  语音识别错误回调
 
 @param error 错误信息
 */
- (void)longVoiceRecognitionOccurError:(NSError *)error;


/**
 *  获取热词数组
 @param addressBookArr 热词数组
 @param keyArr 首字母数组
 */
- (void)longVoiceRecognitionGetHotWords:(NSDictionary<NSString *,NSArray *>*)addressBookArr AndKey:(NSArray *)keyArr;
/**
 *  获取热词数组
 @param updateResult 更新热词结果
 @param code 199:无数据。 200：成功且有数据。201：请求失败 202：请求异常
 */
- (void)longVoiceRecognitionUpdateHotWords:(NSString *)updateResult code:(NSInteger)code;

@end

@interface THSLongVoice : NSObject

@property (nonatomic, weak) id<THSLongVoiceDelegate> delegate;

/**
 *  获取THSVoiceRecognition 单例
 *
 *  @return 单例对象
 */
+ (THSLongVoice *)sharedInstance;

/**
 *  初始化语音识别SDK
 
 *  @param appid       应用ID
 *  @param appKey      应用密钥
 */
- (void)registerVoiceRecognizeWithAppId: (NSString *)appid appKey: (NSString *)appKey;

/**
 *  录音并发送数据识别
 *  @param delegate 回调接口
 */
- (void)startRecordWithDelegate:(id)delegate;

/**
 *  读取录音文件并发送数据识别
 *  @param delegate 回调接口
 *  @param filePath 录音文件路径
 */
- (void)readFileWithfilePath:(NSString *)filePath delegate:(id)delegate;

/**
 *  结束录音
 */
- (void)stopRecord;

/**
 * 暂停录音
 */
- (void)pauseRecord;
/**
 * 恢复录音
 */
- (void)resumRecord;

/**
 *  放弃本次录音及语音识别
 */
- (void)cancleRecordAndVoiceRecognize;

/**
 获取当前最大音量 范围:0 ~ 1
 */
- (float)getPeekPower;

/**
 获取当前平均音量 范围:0 ~ 1
 */
- (float)getAveragePower;

/**
 获取片段录音文件存储录音
 */
- (NSString *)getRecordDataPath;

/**
 获取整段录音文件存储录音
 */
- (NSString *)getCompleteRecordDataPath;

/**
 获取热词 查
 *  @param appid  应用ID
 */
- (void)getHotWordsWithAppId: (NSString *)appid ;

/**
 更新热词 增删改
 *  @param appid        应用ID
 *  @param hotWordsArr  NSString类型
 */
- (void)updateHotWordsWithAppId:(NSString *)appid andHotWords:(NSArray *)hotWordsArr;


@end
