//
//  IFlyMSC.h
//  msc
//
//  Created by 张剑 on 15/1/14.
//  Copyright (c) 2015年 iflytek. All rights reserved.
//

#ifndef MSC_IFlyMSC_h
#define MSC_IFlyMSC_h

#import "IFlyAudioSession.h"
#import "IFlyDataUploader.h"
#import "IFlyDebugLog.h"
#import "IFlyIdentityVerifier.h"
#import "IFlyIdentityResult.h"
#import "IFlyIdentityVerifierDelegate.h"
#import "IFlyISVDelegate.h"
#import "IFlyISVRecognizer.h"
#import "IFlyRecognizerView.h"
#import "IFlyRecognizerViewDelegate.h"
#import "IFlyResourceUtil.h"
#import "IFlySetting.h"
#import "IFlySpeechConstant.h"
#import "IFlySpeechError.h"
#import "IFlySpeechEvaluator.h"
#import "IFlySpeechEvaluatorDelegate.h"
#import "IFlySpeechEvent.h"
#import "IFlySpeechRecognizer.h"
#import "IFlySpeechRecognizerDelegate.h"
#import "IFlySpeechSynthesizer.h"
#import "IFlySpeechSynthesizerDelegate.h"
#import "IFlySpeechUtility.h"
#import "IFlyUserWords.h"
#import "IFlyPcmRecorder.h"
#import "IFlyVerifierUtil.h"
#import "IFlyVoiceWakeuper.h"
#import "IFlyVoiceWakeuperDelegate.h"

#endif
