{"device_type": "phone", "assets_version": "1.1.20190902", "nui_config": {"service_mode": "kModeFullCloud", "log_level": "kLogLevelVerbose", "enable_recorder_by_user": true, "enable_dialog": false}, "nls_config": {"debug_level": 4, "sr_format": "opus", "sample_rate": 16000, "dns_timeout": 5000, "vocab_default_weight": 2}, "audio_config": {"debug": "none", "16k_audio": {"name": "16kmono16bit", "id": 0, "mic": {"name": "16kmono16bit", "debug_heap_pollution": false, "read_cnt": 0, "sample_rate": 16000, "bits_per_sample": 16, "channels": 1, "recording_interval": 10, "cei_frame_time_len": 20, "channel_mask": "kAMChannalMaskNone", "format_type": "kAMDataFormatPcmInterleaved", "endianness": "kAMByteOrderLittleEndian"}}, "8k_audio": {"name": "8kmono16bit", "id": 0, "mic": {"name": "8kmono16bit", "debug_heap_pollution": false, "read_cnt": 0, "sample_rate": 8000, "bits_per_sample": 16, "channels": 1, "recording_interval": 10, "cei_frame_time_len": 20, "channel_mask": "kAMChannalMaskNone", "format_type": "kAMDataFormatPcmInterleaved", "endianness": "kAMByteOrderLittleEndian"}}}}