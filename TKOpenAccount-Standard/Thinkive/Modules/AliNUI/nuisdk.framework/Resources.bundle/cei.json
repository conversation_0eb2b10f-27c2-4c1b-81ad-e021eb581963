{"cei": {"cei_param_version": "2.2.0", "cei_param_device_type": "phone", "cei_param_reco_mode": 0, "cei_param_log_level": 2, "cei_param_debug_path": "asr_debug", "cei_param_is_debug_enable": false, "cei_param_resource_path": "", "cei_param_resource_is_multi_language": false, "cei_param_audio_format_str": "16k16bitmono", "cei_param_mcs_mode": 0, "cei_param_work_mode": 0, "cei_param_max_cache_frames": 1000, "cei_param_is_aec_bf_active": false, "cei_param_is_agc_active": false, "cei_param_is_vad_active": true, "cei_param_is_kws_active": true, "cei_param_is_sr_active": true}, "asp": {"asp_param_is_process_parallel": false, "asp_param_is_input_debug_enable": false, "asp_param_is_output_debug_enable": false, "asp_param_debug_path": "asr_debug", "asp_param_is_callback_enable": false, "asp_param_callback_period_frames": 5}, "vad": {"vad_param_is_input_debug_enable": false, "vad_param_is_output_debug_enable": false, "vad_param_debug_path": "asr_debug", "vad_param_asleep_speech_noise_thres": -0.6, "vad_param_awake_speech_noise_thres": -0.5, "vad_param_asleep_max_speech_segment_time": 300000, "vad_param_awake_max_speech_segment_time": 10000, "vad_param_asleep_block_size": 3, "vad_param_awake_block_size": 3, "vad_param_front_timeout_interval": 8000, "vad_param_tail_timeout_interval": 800, "vad_param_is_detect_start": true, "vad_param_is_detect_end": true}, "kws": {"kws_param_is_input_debug_enable": false, "kws_param_is_output_debug_enable": false, "kws_param_debug_path": "asr_debug", "kws_param_is_process_parallel": false, "kws_param_front_extend_frames": 10, "kws_param_tail_extend_frames": 5, "kws_param_encoder_type_str": "feat", "kws_param_encoder_bitrate": 16000, "kws_param_encoder_complexity": 2, "kws_param_callback_period_ms": 100, "kws_param_max_frames_per_callback": 25, "kws_param_max_bytes_per_callback": 16000}, "sr": {"sr_param_is_input_debug_enable": false, "sr_param_is_output_debug_enable": false, "sr_param_debug_path": "asr_debug", "sr_param_is_itn_enable": true, "sr_param_is_do_conf_filter": false, "sr_param_is_process_parallel": true, "sr_param_is_need_result": false, "sr_param_is_need_voice": true, "sr_param_ngram_conf_thres": 65.0, "sr_param_jsgf_conf_thres": 65.0, "sr_param_encoder_type_str": "opus", "sr_param_encoder_bitrate": 16000, "sr_param_encoder_complexity": 2, "sr_param_callback_period_ms": 100, "sr_param_max_frames_per_callback": 25, "sr_param_max_bytes_per_callback": 16000}, "ou": {"oss_upload_param_is_enable": false, "oss_upload_param_asp_in": false, "oss_upload_param_asp_out": false, "oss_upload_param_vad_in": false, "oss_upload_param_vad_out": false, "oss_upload_param_kws_in": false, "oss_upload_param_kws_susp_in": false, "oss_upload_param_kws_out": false, "oss_upload_param_kws_susp_out": false, "oss_upload_param_sr_in": false}}