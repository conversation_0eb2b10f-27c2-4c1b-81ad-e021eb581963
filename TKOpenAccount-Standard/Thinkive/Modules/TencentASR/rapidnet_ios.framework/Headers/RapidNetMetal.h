//
//  RapidNetMetal.h
//  RapidNetMetal
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2017/2/7.
//  Copyright © 2017年 tencent. All rights reserved.
//

#import <Foundation/Foundation.h>

#if TARGET_IPHONE_SIMULATOR or TARGET_OS_IPHONE

#import "RapidNetCPU.h"
#import "YTRapidNetError.h"
#import <Metal/Metal.h>
#import <MetalKit/MetalKit.h>
#import "CNNImage.h"
#import "CNNBufferCache.h"
#import "CNNPipeLine.h"

#if YT_USE_MPSCNN && !YT_IS_SIMULATOR
#import <MetalPerformanceShaders/MetalPerformanceShaders.h>
#else
#endif

#import "CNNImage.h"


@interface RapidNetMetal : NSObject

@property (nonatomic, strong, readonly) RapidNetCPU *netCpu;


@property (nonatomic, strong, readonly) id<MTLDevice> device;
@property (nonatomic, strong, readonly) id<MTLLibrary> library;
@property (nonatomic, strong, readonly) CNNBufferCache *bufferCache;
@property (nonatomic, strong, readonly) CNNPipeLineManager *pipeLineManager;

@property (nonatomic, strong, readonly) NSString *protoFile;
@property (nonatomic, strong, readonly) NSString *modelFile;

- (instancetype)initWithDevice:(id<MTLDevice>) device
                       library:(id<MTLLibrary>) library;

//构建网络
- (NSError *)constructNetworkWith:(RapidNetCPU *)cpuNetwork;
//前向计算
- (CNNImage *)forwardWithTexture:(id<MTLTexture>) texture
                   commandBuffer:(id<MTLCommandBuffer>)commandBuffer;

- (CNNImage *)forwardWithCNNImage:(CNNImage *) image
                    commandBuffer:(id<MTLCommandBuffer>)commandBuffer;
//销毁网络
- (BOOL)destructNetwork;
//获取网络输入维度，必须在constructNetworkWith后调用
- (CNNDimension)getNetDataDimension;
- (CNNDimension)getOutputDataDimension;

//对于总体输入大小dimension，预先分配显存
- (void)preloadForDataDimension:(CNNDimension)dimension;

//调整输入图片大小dataDimension，网络输入大小netdimension，预先分配显存/内存
- (void)preloadForDataDimension:(YoutuMetalCNN::CNNDimension)dataDimension
              netInputDimension:(YoutuMetalCNN::CNNDimension)netdimension;

#if YT_USE_MPSCNN && !YT_IS_SIMULATOR
- (void)forwardWithImage:(MPSImage *) texture
           commandBuffer:(id<MTLCommandBuffer>)commandBuffer;
#endif
@end
#endif
