//
//  Metal+Utility.h
//  RapidNet
//
//  Created by da<PERSON><PERSON><PERSON> on 2017/2/8.
//  Copyright © 2017年 tencent. All rights reserved.
//
#import <Foundation/Foundation.h>
#import <Metal/Metal.h>
#import "CNNTypes.h"

@interface MetalUtility : NSObject 
+ (id<MTLComputePipelineState>)buildPipelineWithFuncName:(NSString *)funcName
                                                  device:(id<MTLDevice>)device
                                                 library:(id<MTLLibrary>)library
                                                   error:(NSError **)error;

@end
