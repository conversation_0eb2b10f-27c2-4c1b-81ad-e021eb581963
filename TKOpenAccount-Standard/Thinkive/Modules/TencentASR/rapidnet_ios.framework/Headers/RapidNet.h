//
//  RapidNet.h
//  RapidNet
//
//  Created by darre<PERSON><PERSON> on 2017/2/7.
//  Copyright © 2017年 tencent. All rights reserved.
//

#import <Foundation/Foundation.h>

#if TARGET_IPHONE_SIMULATOR or TARGET_OS_IPHONE

#import <Metal/Metal.h>
#include <opencv2/core/core.hpp>
#import "CNNImage.h"
#import "YTRapidNetError.h"

@class RapidNetCPU;
@class RapidNetMetal;

@interface RapidNet : NSObject
@property (nonatomic, strong, readonly) RapidNetCPU *netCpu;
@property (nonatomic, strong, readonly) RapidNetMetal *netMetal;

/**
 * 检测GPU设备是否可用
 */
+ (BOOL)isGPUDeviceAvailable;

/**
 init function
 
 @param protoFile rapidnet proto model
 @param modelFile model
 @return self for init success and nil for init fail
 */
- (instancetype)initWithProto:(NSString *)protoFile
                        model:(NSString *)modelFile;

- (NSError *)loadGPU;
- (NSError *)loadGPUWithDevice:(id<MTLDevice>) device
                       library:(id<MTLLibrary>) library;

//调整输入图片大小dataDimension，网络输入大小netdimension，预先分配显存/内存
- (void)preloadForDataDimension:(YoutuMetalCNN::CNNDimension)dataDimension
              netInputDimension:(YoutuMetalCNN::CNNDimension)netdimension;

//CPU前向计算
- (cv::Mat)forwardWithCvMat:(const cv::Mat&)image
                      error:(NSError *__autoreleasing *)error;

//GPU前向计算
- (CNNImage *)forwardWithCNNImage:(CNNImage *) image
                    commandBuffer:(id<MTLCommandBuffer>)commandBuffer
                            error:(NSError *__autoreleasing *)error;

//gpu metallib path
+ (NSString *)gpuLibraryPath;

/**
 *在loadGPU函数之前调用，app可以打印feature map
 */
+ (void)enableImageDebug;
@end

//Subclass must overide
@interface RapidNet (Overide)
- (Class)rapidNetCPUClass;
- (Class)rapidNetMetalClass;
@end

#endif
