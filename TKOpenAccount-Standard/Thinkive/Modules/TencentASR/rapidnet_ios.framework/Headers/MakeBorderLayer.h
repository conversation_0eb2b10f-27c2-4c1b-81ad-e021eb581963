//
//  MakeBorderLayer.h
//  SuperResolution
//
//  Created by da<PERSON><PERSON><PERSON> on 2017/4/5.
//  Copyright © 2017年 darrenyao. All rights reserved.
//

#import "CNNKernel.h"

struct MakeBorderPara {
    int left;
    int top;
    int right;
    int bottom;
};

@interface MakeBorderLayer : CNNKernel
- (instancetype)initWithPara:(MakeBorderPara)para
              inputDimension:(CNNDimension)inputDimension
                      device:(id<MTLDevice>) device
                     library:(id<MTLLibrary>) library;
@end
