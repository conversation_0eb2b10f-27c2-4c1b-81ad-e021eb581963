//
//  ColorConvertLayer.h
//  SuperResolution
//
//  Created by da<PERSON><PERSON><PERSON> on 2017/4/4.
//  Copyright © 2017年 darrenyao. All rights reserved.
//

#import "CNNKernel.h"

typedef enum : int {
    ColorConvertTypeInvalid     = 0,
    ColorConvertTypeRGBA2YCrCb  = 1,
    ColorConvertTypeYCrCb2RGBA  = 2,
} ColorConvertType;



@interface ColorConvertLayer : CNNKernel
- (instancetype)initWithPara:(ColorConvertPara)para
              inputDimension:(CNNDimension)inputDimension
                      device:(id<MTLDevice>) device
                     library:(id<MTLLibrary>) library;
@end
