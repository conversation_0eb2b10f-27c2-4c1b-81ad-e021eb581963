//
//  DeconvolutionLayer.h
//  RapidNet
//
//  Created by da<PERSON><PERSON><PERSON> on 2017/2/7.
//  Copyright © 2017年 tencent. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "CNNKernel.h"
//#import "rpdnet_cfg.h"
#import "CNNTypes.h"


#if YT_USE_MPSCNN && !YT_IS_SIMULATOR
#import <MetalPerformanceShaders/MetalPerformanceShaders.h>
@interface DeconvolutionLayer : MPSCNNConvolution
#else
@interface DeconvolutionLayer : CNNKernel {
//    rpd_layer_info *_para;
}
@property(nonatomic, assign, readonly) CNNConvolutionPara convolutionPara;


- (instancetype)initWithPara:(CNNConvolutionPara)para
              inputDimension:(CNNDimension)inputDimension
                      device:(id<MTLDevice>) device
                     library:(id<MTLLibrary>) library;

//srcData: cvMat中的r0g0b0r1g1b1
//- (void)constructWithSrcdata:(float *)srcData
//                     weights:(float *)weight
//                        bias:(float *)bias;

- (NSError *)constructWithConvolutionData:(const layer_res *)convData
                            batchNormPara:(const layer_param *)batchNormPara
                            batchNormData:(const layer_res *)batchNormData
                                preluPara:(const layer_param *)preluPara
                                preluData:(const layer_res *)preluData;
- (void)printInfo;
@end
#endif
