//
//  YTRapidNetError.h
//  SuperResolution
//
//  Created by darrenya<PERSON> on 2017/3/31.
//  Copyright © 2017年 darrenyao. All rights reserved.
//

#ifndef YTRapidNetError_h
#define YTRapidNetError_h
#import <Foundation/Foundation.h>

static NSString *YTRapidNetErrorDomain = @"yt.rapidnet.error.domain";

typedef enum : int {
    YTRapidNetErrorOK                    = 0,
    YTRapidNetErrorInvalidData           = 1,
    YTRapidNetErrorInvalidModel          = 2,
    YTRapidNetErrorInternalError         = 3,
    
    YTRapidNetErrorInvalidLayerParam     = 100,
    YTRapidNetErrorInvalidLayerData      = 100 + 1,
    
    
    /**系统版本过低，低于8.0*/
    YTRapidNetErrorLowSystemVersion   = 1000,
    /**没有支持的GPU设备*/
    YTRapidNetErrorNoGPUDevice        = 1000 + 1,
    /**没有支持的GPU库*/
    YTRapidNetErrorNoGPULibrary       = 1000 + 2,
} YTRapidNetError;


#endif /* YTRapidNetError_h */
