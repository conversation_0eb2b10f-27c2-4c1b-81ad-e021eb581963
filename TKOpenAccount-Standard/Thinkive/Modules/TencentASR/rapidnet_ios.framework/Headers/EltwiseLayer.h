//
//  EltwiseLayer.h
//  RapidNet
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2017/2/8.
//  Copyright © 2017年 tencent. All rights reserved.
//

#import <Foundation/Foundation.h>
#import <Metal/Metal.h>
#import "CNNKernel.h"
#import "CNNTypes.h"

//#import "rpdnet_cfg.h"
//using namespace RPNNameSpace;
using namespace YoutuMetalCNN;

@interface EltwiseLayer : CNNKernel {
//    rpd_layer_info *_para;
}

- (instancetype)initWithPara:(CNNConvolutionPara)para
              inputDimension:(CNNDimension)inputDimension
                      device:(id<MTLDevice>) device
                     library:(id<MTLLibrary>) library;

- (NSError *)construct;
@end
