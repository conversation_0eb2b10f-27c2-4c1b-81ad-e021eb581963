//
//  RapidNetCPU.h
//  RapidNet
//
//  Created by da<PERSON><PERSON><PERSON> on 2017/3/30.
//  Copyright © 2017年 tencent. All rights reserved.
//

#import <Foundation/Foundation.h>
#if TARGET_IPHONE_SIMULATOR or TARGET_OS_IPHONE

#import <opencv2/opencv.hpp>
#import "rpdnet_api.h"
#import "YTRapidNetError.h"
using namespace RPNNameSpace;

@interface RapidNetCPU : NSObject
- (instancetype)initWithProto:(NSString *)protoFil
                    modelFile:(NSString *)modelFile;

/**
 *子类必须重载，输入Mat转网络输入Blob
 */
- (void)convertInputMat:(const cv::Mat &)inputMat
            toInputBlob:(rpd_blob<float> &)inputBlob;

/**
 *子类必须重载，网络输出Blob转输出Mat
 */
- (void)convertOutputBlob:(const rpd_blob<float> &)outputBlob
              toOutputMat:(cv::Mat &)outputMat;

/**
 *子类必须重载，dongleMap
 */
- (unsigned int *)dongleMap;

- (NSError *)forwordWithInputMat:(const cv::Mat &)inputMat
                       outputMat:(cv::Mat &)outputMat;

- (NSError *)forwordWithInputBlob:(rpd_blob<float> &)inputBlob
                       outputBlob:(rpd_blob<float> &)outputBlob;
@end

#endif
