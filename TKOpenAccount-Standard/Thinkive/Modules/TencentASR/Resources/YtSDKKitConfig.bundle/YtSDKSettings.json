{"version": "1.0.0", "sdk_settings": {"ocr_settings": {"resource_online": false, "app_id": "10198923", "ocrtype": "idcard", "cardtype": 0, "mode_type": 2, "auto_timeout_ms": 10000, "user_id": "360566935", "secret_id": "AKIDfEbnkRTNXkkVt0pjsoXyCE1Yu63lqQQC", "secret_key": "e3HH3Zq01mHcicJkywgATWFjxzqYzIPG", "video_quality": 2, "idcard": {"result_api_url": "https://api.youtu.qq.com/youtu/ocrapi/idcardocr", "request_options": {"border_check_flag": true, "enable_reshoot": true, "enable_detect_copy": true, "enable_quality_value": true, "ret_portrait_flag": true, "enable_detect_ps": true}}, "creditcard": {"result_api_url": "https://api.youtu.qq.com/youtu/ocrapi/creditcardocr", "request_options": {"enable_border_check": true, "enable_reshoot_check": true, "enable_copy_check": true, "enable_quality_value": true}}}, "silent_settings": {"predetect_countdown_ms": 10000, "force_pose_check": true, "resource_online": false, "app_id": "youtu_ios_0823", "timeout_countdown_ms": 10000, "check_eye_open": false, "same_tips_filter": false, "manual_trigger": true, "net_request_timeout_ms": 10000, "resourceDownloadPath": "targetpath", "action_security_level": 1, "action_default_seq": [5], "result_api_url": "https://meetingtest.youtu.qq.com/actionliveapi/silencelive"}, "action_settings": {"resource_online": false, "timeout_countdown_ms": 10000, "app_id": "youtu_android_demo", "action_security_level": 0, "result_api_url": "https://meetingtest.youtu.qq.com/actionliveapi/actionlive", "action_default_seq": [1, 2]}, "reflect_settings": {"resource_online": false, "timeout_countdown_ms": 10000, "color_data": "1 120 3 3 4 5 1 1 ", "app_id": "youtu_android_demo", "reflect_security_level": 2, "config_api_url": "https://meetingtest.youtu.qq.com/reflectliveapi/getlivetype", "result_api_url": "https://meetingtest.youtu.qq.com/reflectliveapi/reflectlive"}, "action+reflect_settings": {"resource_online": false, "timeout_countdown_ms": 10000, "app_id": "youtu_android_demo", "action_security_level": 0, "reflect_security_level": 2, "action_default_seq": [0], "config_api_url": "https://meetingtest.youtu.qq.com/actrefliveapi/getlivetype", "result_api_url": "https://meetingtest.youtu.qq.com/actrefliveapi/actionreflectlive"}, "lipread_settings": {"app_id": "youtu_ios_0823", "timeout_countdown_ms": 10000, "backend_proto_type": 2, "result_api_url": "https://meetingtest.youtu.qq.com/youtu/openliveapi/person_live4", "action_default_seq": [6, 0, 9, 8], "manual_trigger": true, "request_options": {"business_id": "wx_default", "person_id": "wx_default1", "person_type": "youtu", "req_type": "live"}}, "need_network": true}}