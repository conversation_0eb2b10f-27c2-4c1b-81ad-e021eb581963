/* 
  YtSDKInfoLocalization.strings
  yt-ios-verification

  Created by <PERSON><PERSON><PERSON><PERSON>(王小松) on 2019/11/21.
  Copyright © 2019 Tecnet.Youtu. All rights reserved.
*/
/* 错误类型 */
"yt_auth_failed"="授权失败";
"yt_verify_failed"="识别失败";
"yt_param_error"="参数异常";
"yt_network_error"="网络异常";
"yt_camera_permission_error"="摄像机权限异常";
"yt_user_cancel"="用户取消操作";

/* 识别提示 */
/** 亮度 **/
"yt_light_dark" = "光线强度：暗";
"yt_light_normal" = "光线强度：适中";
"yt_light_bright" = "光线强度：亮";
/** 基础提示信息 **/
"yt_verify_succeed"="识别通过";
"yt_net_wait"="请等待结果";
"yt_verify_step_timeout"="识别超时";
"yt_cam_refocus"="摄像头重新对焦";
/** 人脸相关 **/
"yt_face_keep_pose"="请保持姿态";
"yt_face_closer"="请靠近一点";
"yt_face_farer"="请离远一点";
"yt_face_not_in_rect"="请保持脸在检测框内";
"yt_face_no_face"="没有检测到人脸";
"yt_face_incorrect_pose"="请保持正脸对准检测框";
"yt_face_no_left_face"="请勿遮挡左脸";
"yt_face_no_right_face"="请勿遮挡右脸";
"yt_face_no_chin"="请勿遮挡下巴";
"yt_face_no_mouth"="请勿遮挡嘴巴";
"yt_face_no_nose"="请勿遮挡鼻子";
"yt_face_no_left_eye"="请勿遮挡左眼";
"yt_face_no_right_eye"="请勿遮挡右眼";
"yt_face_open_eye"="请睁眼";
"yt_face_too_many_face"="请保持一个人";
/** 动作相关 **/
"yt_face_act_blink"="请眨眨眼";
"yt_face_act_open_mouth"="请张张嘴";
"yt_face_act_shake_head"="请摇摇头";
"yt_face_act_nod_head"="请点点头";
"yt_face_act_silence"="请保持静止不动";
"yt_face_act_screen_shaking"="请不要晃动";
"yt_face_act_light_not_right"="光线不合适";

/** OCR相关 **/
"yt_ocr_auto_timeout"="自动捕获超时";
"yt_ocr_manual_on"="手动捕获模式，请点击拍照按钮";
"yt_ocr_auto_succeed"="自动捕获成功，请等待验证结果";
"yt_ocr_manual_succeed"="手动捕获成功，请等待验证结果";
"yt_ocr_keep_card"="请保持证件在框内";
"yt_ocr_no_card"="请放入证件";
