//
//  StateNameList.h
//  yt-ios-face-recognition-demo
//
//  Created by <PERSON> on 2019/9/6.
//  Copyright © 2019 Tencent.Youtu. All rights reserved.
//

#pragma once
#import <Foundation/Foundation.h>
#include "YtSDKCommonDefines.h"
typedef enum  : int
{
    YT_UNKNOWN_STATE = 0,
    YT_IDLE_STATE = 1,
    YT_SILENT_STATE,
    YT_ACTION_STATE,
    YT_REFLECT_STATE,
    YT_OCR_AUTO_STATE,
    YT_OCR_MANUAL_STATE,
    YT_NET_FETCH_STATE,
    YT_NET_REQ_RESULT_STATE,
    YT_NET_REQ_REFLECT_RESULT_STATE,
    YT_NET_OCR_REQ_RESULT_STATE,
    YT_LIP_READ_STATE,
    YT_DETECT_ONLY_STATE,
    YT_STATE_COUNT
}StateName;

YT_SDK_EXPORT @interface StateNameHelper : NSObject
+ (NSArray *)names;
+ (NSString *)nameForType:(StateName)type;
+ (StateName)typeFromName:(NSString*)name;
@end

