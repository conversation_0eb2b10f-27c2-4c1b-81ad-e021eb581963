//
//  YtSDKLogger.h
//  yt-ios-verification-sdk
//
//  Created by <PERSON> on 2019/9/25.
//  Copyright © 2019 Tencent.Youtu. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

#define YTLOG_DEBUG(format_string, ...) \
[YtSDKLogger logDebug:[NSString stringWithFormat:format_string,##__VA_ARGS__]]

#define YTLOG_INFO(format_string, ...) \
[YtSDKLogger logInfo:[NSString stringWithFormat:format_string,##__VA_ARGS__]]

#define YTLOG_WARN(format_string, ...) \
[YtSDKLogger logWarnning:[NSString stringWithFormat:format_string,##__VA_ARGS__]]

#define YTLOG_ERROR(format_string, ...) \
[YtSDKLogger logError:[NSString stringWithFormat:format_string,##__VA_ARGS__]]

/// YtSDKLoggerLevel
typedef NS_ENUM(NSInteger, YtSDKLoggerLevel)
{
    /// ERROR 级别
    YT_SDK_ERROR_LEVEL = 0,
    /// WARN 级别
    YT_SDK_WARN_LEVEL,
    /// INFO 级别
    YT_SDK_INFO_LEVEL,
    /// DEBUG 基本
    YT_SDK_DEBUG_LEVEL
};

typedef void (^OnLoggerEventBlock)(YtSDKLoggerLevel loggerLevel, NSString * _Nonnull logInfo);
@interface YtSDKLogger : NSObject
+ (void)registerLoggerListener:(OnLoggerEventBlock _Nullable)listener withNativeLog:(BOOL)needNative;
+ (BOOL)needNativeLog;
+ (void)logDebug:(NSString* _Nonnull)message;
+ (void)logInfo:(NSString* _Nonnull)message;
+ (void)logWarnning:(NSString* _Nonnull)message;
+ (void)logError:(NSString* _Nonnull)message;
@end

NS_ASSUME_NONNULL_END
