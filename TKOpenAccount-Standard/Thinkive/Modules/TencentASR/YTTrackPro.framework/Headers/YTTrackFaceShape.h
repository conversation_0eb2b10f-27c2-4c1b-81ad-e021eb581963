//
//  YTTrackFaceShape.h
//  rpd_tracker
//
//  Created by PanCheng on 05/09/2017.
//  Copyright © 2017 <PERSON>. All rights reserved.
//

#ifndef YTTrackFaceShape_h
#define YTTrackFaceShape_h

#include <opencv2/core/core.hpp>
#include <vector>
#include <string>
#include "YTTrackCommonDefine.h"

#if (defined __ANDROID__) || (defined __APPLE__) 
#define FACEALIGNMENT __attribute__((visibility("default")))
#else
#define FACEALIGNMENT
#endif

struct FACEALIGNMENT FaceAlignParam
{
    FaceAlignParam(){
        refine_config= yttrackpro::REFINE_CONFIG_OFF;
        net_fix_config = FIX_EYE_MOUTH;
        movement_model_config = yttrackpro::MOVEMENT_MODEL_CONFIG_OFF;
    };
    int refine_config;
    int net_fix_config;
    int movement_model_config;
   
};


/** The face profile landmarks ordering
 *
 *     0                            20
 *     1                            19
 *      2                          18
 *      3                          17
 *       4                        16
 *        5                     15
 *         6                   14
 *          7                 13
 *            8              12
 *              9         11
 *                   10
 */
struct TC_FaceProfile
{
    std::vector<cv::Point2f> points;
};

/** The left eye and eyebrow landmarks ordering
 *
 *         7   6   5
 *    0                 4
 *         1   2   3
 */
struct TC_LeftEye
{
    std::vector<cv::Point2f> points;
};

struct TC_LeftEyebrow
{
    std::vector<cv::Point2f> points;
};


/** The right eye and eyebrow landmarks ordering
 *
 *         5   6   7
 *    4                 0
 *         3   2   1
 */
struct TC_RightEye
{
    std::vector<cv::Point2f> points;
};

struct TC_RightEyebrow
{
    std::vector<cv::Point2f> points;
};

/** The nose landmarks ordering
 *
 *           1
 *        2    12
 *      3        11
 *     4     0    10
 *    5  6  7  8   9
 */
struct TC_Nose
{
    std::vector<cv::Point2f> points;
};

/** The mouth landmarks ordering
 *
 *                 10   9   8
 *          11                      7
 *            21  20  19 18 17
 *    0                                    6
 *            12  13  14 15 16
 *           1                      5
 *                  2   3   4
 */

struct TC_Mouth
{
    std::vector<cv::Point2f> points;
};

/** The pupils ordering
 *
 *       0                          1
 */
struct TC_Pupil
{
    std::vector<cv::Point2f> points;
    
};

/************************
 * @brief 人脸配准关键点记录结构体, 具体结构参考以上的结构体定义
 * faceProfile: 人脸轮廓
 * leftEyebrow: 左眉毛
 * rightEyebrow: 右眉毛
 * leftEye: 左眼
 * rightEye: 右眼
 * nose: 鼻子
 * mouth: 嘴巴
 * pupil: 眼珠
 ************************/
struct TC_FaceShape
{
    TC_FaceProfile faceProfile;
    TC_LeftEyebrow leftEyebrow;
    TC_RightEyebrow rightEyebrow;
    TC_LeftEye leftEye;
    TC_RightEye rightEye;
    TC_Nose nose;
    TC_Mouth mouth;
    TC_Pupil pupil;
};


namespace yttrackpro{
    typedef struct
    {
        // get result points by TC_FaceShape
        TC_FaceShape faceShape;
        // get the visibility of each point, the order of points aligns to face shape order
        std::vector<float> facePointsVis;
        // get the confidence level by float : 0-1(best)
        float cls;
    } YTTrackResultInfo;
    
}


#endif /* YTTrackFaceShape_h */
