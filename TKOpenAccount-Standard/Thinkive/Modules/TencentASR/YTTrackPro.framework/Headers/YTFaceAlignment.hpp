//
//  YTFaceAlignment.hpp
//  YTFaceAlignment
//
//  Created by PanCheng on 2018-04-16 11:10:05 GMT+8.
//  Copyright © 2017 PanCheng. All rights reserved.
//

#ifndef YTFaceAlignment_hpp
#define YTFaceAlignment_hpp

#include <opencv2/core/core.hpp>
#include "YTTrackFaceShape.h"
#include "YTTrackCommonDefine.h"

#if (defined __ANDROID__) || (defined __APPLE__) 
#define FACEDETECT_EXPORT __attribute__((visibility("default")))
#endif
#ifdef FACETRACKPRO_API
#define FACETRACKPRO_EXPORT //__declspec(dllexport)
#else
#define FACETRACKPRO_EXPORT //__declspec(dllimport)
#endif

class YtFaceAlignmentSdk;
class PtsPostProc;
class YTFaceRefiner;
class PupilPtsPostProc;

//早先安卓跟iOS的接口类名不一样
#ifdef __ANDROID__
#define YTFACEALIGNMENT_CLASS TC_FaceAlignmentRunner
#else
#define YTFACEALIGNMENT_CLASS YTFaceAlignment
#endif

class FACEDETECT_EXPORT YTFaceWrapper {
public:
    PupilPtsPostProc *m_pppp;
    bool m_need_firstframe_refine;
    int lastLeftEyeClose;
    int lastRightEyeClose;
};


class FACEDETECT_EXPORT YTFACEALIGNMENT_CLASS{

public :
    
    YTFACEALIGNMENT_CLASS();
    ~YTFACEALIGNMENT_CLASS();
    
    /************************
     * @brief GlobalInit: 全局初始化函数，进程初始化一次即可，多线程不安全  
     * @param modelDir: 模型所在目录  
     * @return: 0 => 成功  
     *          非0 => 失败，常见值如下：  
                 -1025  重复调用GlobalInit。调用成功GlobalInit之后如果再次调用，需要先调用GlobalRelease  
                 -1     ufa.bundle中的align.stb加载失败  
                 8193   ufa.bundle中的模型读取失败  
                 8194   ufa.bundle中的模型加载失败  
                 其他值    rapidnet前向框架初始化失败，通常是模型文件损坏造成  
     ************************/
	static int GlobalInit(const std::string& modelPath);
    
    /************************
     * @brief GlobalRelease: 全局析构函数，进程结束析构一次即可，多线程不安全  
     * @return: 0 => 成功  
     *          非0 => 失败  
     ************************/
	static int GlobalRelease();
    
    //每次执行GlobalInit且接口返回0，计数+1；执行GlobalRelease，计数-1，小于0归0  
	static int GlobalInitSuccessCount();
    
    
    /************************
     * @brief Version: sdk版本  
     * @return: 版本相关信息  
     ************************/
    static const char* Version();
    static const char* VersionAlignment();
    static const char* VersionStable();
    static const char* VersionRefine();
    static const char* VersionPupilStable();

#ifdef __ANDROID__
    static void setFaceAlignConfig(FaceAlignParam parm);
    static FaceAlignParam getFaceAlignConfig();
#else
    void setFaceAlignConfig(FaceAlignParam parm);
    FaceAlignParam getFaceAlignConfig();
#endif

    /************************
     * @brief sdk模型文件的md5, 多个模型文件则以空格隔开  
     * @return: 模型文件的md5字符串, 如果有多个模型文件则以空格隔开  
     ************************/
#ifdef __ANDROID__
    static const char* ModelCheckSum();
#else
	static const std::string ModelCheckSum();
#endif

    /**
     calculate 1st frame face points position & human face confidence
     
     @param (input)_image: MUST input a GRAY image
     @param (input)faceRect: face rectangle
     @param (output)trackResInfo: face info
     @param (input)ptsTag 点位信息，默认90，输入94会输出94点信息（包括左右瞳孔边界）  
     @return error code: 0 成功  
                         非0 => 失败，常见值如下：  
                         -2 输入的人脸框体宽高为0  
                         -10 尚未调用GlobalInit或者GlobalInit返回值不为0  
                         -4096 授权失败，请检查授权文件  
                         -8195 入参图片不正确，可能不是灰度图，或者是一张空的灰度图  
     */
    int doFaceAlignmentWithClsGray(const cv::Mat &gray, const cv::Rect& faceRect, yttrackpro::YTTrackResultInfo &trackResInfo, const int ptsTag = 90);
    //__deprecated. replaced by "doFaceAlignmentWithClsGray"
    int doFaceAlignmentWithCls(const cv::Mat &rgb, const cv::Rect& faceRect, yttrackpro::YTTrackResultInfo &trackResInfo, const int ptsTag = 90);

    /*
     ptsTag 点位信息，默认90，输入94会输出94点信息（包括左右瞳孔边界）  
    @return error code: 0 成功  
    非0 => 失败，常见值如下：  
    -2 输入的人脸框体宽高为0  
    -10 尚未调用GlobalInit或者GlobalInit返回值不为0  
    -4096 授权失败，请检查授权文件  
    -8195 入参图片不正确，可能不是灰度图，或者是一张空的灰度图  
     */
    // picture alignment
#if (defined __ANDROID__)
    int doFaceAlignmentWithCls(const cv::Mat& gray, const cv::Point2f& leftPupil, const cv::Point2f& rightPupil, yttrackpro::YTTrackResultInfo &trackResInfo, const int ptsTag = 90);
#endif
    int doFaceAlignmentTrackWithClsGray(const cv::Mat& gray, const cv::Point2f& leftPupil, const cv::Point2f& rightPupil, yttrackpro::YTTrackResultInfo &trackResInfo, const int ptsTag = 90);

    /**
     calculate consequtial frame face points position & human face confidence
     
     @param (input)image: MUST input a GRAY image
     @param (input)lastShape: last frame face points position
     @param (output)trackResInfo: face info
     @param (input)ptsTag 点位信息，默认90，输入94会输出94点信息（包括左右瞳孔边界）
     @return error code: 0 成功
                         非0 => 失败，常见值如下：  
                         -2 输入的人脸框体宽高为0  
                         -10 尚未调用GlobalInit或者GlobalInit返回值不为0  
                         -4096 授权失败，请检查授权文件  
                         -8195 入参图片不正确，可能不是灰度图，或者是一张空的灰度图  
     */
    int doFaceAlignmentTrackWithClsGray(const cv::Mat& gray,const TC_FaceShape& lastShape, yttrackpro::YTTrackResultInfo &trackResInfo, const int ptsTag = 90);
    //__deprecated. replaced by "doFaceAlignmentTrackWithClsGray"
    int doFaceAlignmentTrackWithCls(const cv::Mat& rgb,const TC_FaceShape& lastShape, yttrackpro::YTTrackResultInfo &trackResInfo, const int ptsTag = 90);
    
    //function methods
    //bool is90 :true for 90(used for poseesitimate) ; false for normal
    static void convertFaceShapeToPtsVec(const TC_FaceShape& faceShape, std::vector<cv::Point2f>& facePoints);

    
private :
    YtFaceAlignmentSdk *m_YtFaceAlignmentSdk;
    PtsPostProc *m_stable;
    YTFaceRefiner *m_refiner;
    YTFaceWrapper *m_wrapper;
};



#endif /* YTFaceAlignment_hpp */
