//
//  YTFaceDetection.hpp
//  YTTrackPro
//
//  Created by PanCheng on 10/11/2017.
//  Copyright © 2017 <PERSON>. All rights reserved.
//

#ifndef YTFaceDetection_hpp
#define YTFaceDetection_hpp

#include <opencv2/opencv.hpp>
#include "YTTrackCommonDefine.h"

#if (defined __ANDROID__) || (defined __APPLE__) 
#define FACEDETECT_EXPORT __attribute__((visibility("default")))
#else
#define FACEDETECT_EXPORT
#endif
class YtFaceDetection;

class FACEDETECT_EXPORT YTFaceDetection
{
public:
    YTFaceDetection();
    ~YTFaceDetection();
    // global init
    /*
     0 模型加载成功
     -1 模型数量不正确
     -2 new pnet失败，可能是内存空间不足
     -3 pnet初始化失败，请检查模型文件是否正确
     -4 new rnet失败，可能是内存空间不足
     -5 rnet初始化失败，请检查模型文件是否正确
     -6 new onet失败，可能是内存空间不足
     -7 onet初始化失败，请检查模型文件是否正确
     -101 net_1_bin.rpnproto读取失败
     -102 net_2_bin.rpnproto读取失败
     -103 net_3_bin.rpnproto读取失败
     */
    static int GlobalInit(const std::string& bundlePath);
    // global exit
    static int GlobalRelease();
    //每次执行GlobalInit且接口返回0，计数+1；执行GlobalRelease，计数-1，小于0归0
    static int GlobalInitSuccessCount();
    // model inforamtion
    static const char* Version();
    static const char* Md5CheckSum();
    //获取全场景的md5模型值，只要其中一组符合当前正在使用的MD5即可
    static const std::vector<std::string> Md5CheckSumAll();
    static bool CheckModel(std::string modelDir);
    /**
     * @brief do detection, pass your parameter or use defalut
     *
     * @param[in] input_rgb: 8bit depth, 3 channels;
     * @param[out] ret_face_rects: all detected faces(sort by confidence, decending order)
     *
     * @return   0 成功
                 >0 当前检测到的人脸数量
                 -1 入参的FaceDetectionParam取值范围不正确
                 -2 pnet计算失败
                 -3 rnet计算失败
                 -4 onet计算失败
                 -5 输入人脸过小
                 -6 图片过大。目前支持最大图片宽或者高不能超过4096
                 -7 内部图片操作时候cv库爆异常
                 -1024 授权无效，请检查授权文件
                 -101 成员变量申请内存返回空
     *
     */
    int Detection(const cv::Mat & input_rgb, std::vector<cv::Rect>& ret_face_rects);
    int Detection(const cv::Mat & input_rgb, const YtFaceDetParam& param, std::vector<cv::Rect>& ret_face_rects);
    
    //get the corresponding detection confidence if necessary
    std::vector<float> GetFacesConfidence();
    
private:
    YtFaceDetection * m_ytFaceDetection;
    
};

#endif /* YTFaceDetection_hpp */
