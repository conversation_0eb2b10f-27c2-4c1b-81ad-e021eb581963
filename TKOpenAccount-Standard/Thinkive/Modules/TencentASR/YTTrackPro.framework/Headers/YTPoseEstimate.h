#ifndef YTFACEPOSEESITMATE_H
#define YTFACEPOSEESITMATE_H

#include <vector>
#include <string>

//外发版本使用的动作库使用该宏
// #define USEFOR_OUTPUT

#include <opencv2/core/core.hpp>

#define FACEDETECT_EXPORT __attribute__((visibility("default")))

#if defined(YTFACETRACK_NAMESPACE) && ((defined __ANDROID__) || (defined __APPLE__))
#include "YTTrackCommonDefine.h"
#ifdef YTFACETRACK_NAMESPACE
namespace YTFACETRACK_NAMESPACE
{
#endif //YTFACETRACK_NAMESPACE
#endif
    
#ifdef USEFOR_OUTPUT
    namespace YtPoseEstimate {
#endif
        class YtPoseEstimateSdk;
#ifdef USEFOR_OUTPUT
    }
#endif

	struct FACEDETECT_EXPORT YTPOSE
	{
		float pitch;//Up<0, Down>0, 范围在-60~60内会更加准确
		float yaw;	//Left>0, Right<0, 范围在-60~60内会更加准确
		float roll;	//Anti-clockwise<0, Clockwise>0
	};

	/************************
	* @brief The PoseEstimateSdk Class: 人脸姿态估计sdk
	*************************/
	class FACEDETECT_EXPORT YTPoseEstimate
	{
	public:
        YTPoseEstimate();
        ~YTPoseEstimate();

		/************************
		* @brief GlobalInit: 全局初始化函数
		* @param modelDir: 模型所在目录
		* @return: 0 => 成功
		*          非0 => 失败
        *          -10000 => 模型读取失败
		************************/
		static int GlobalInit(const char* modelDir);
		static int GlobalInit(const std::string &modelDir);

		/************************
		* @brief GlobalRelease: 全局析构函数
		************************/
		static int GlobalRelease();
        
        //每次执行GlobalInit且接口返回0，计数+1；执行GlobalRelease，计数-1，小于0归0
        static int GlobalInitSuccessCount();

		/************************
		* @brief Version: sdk版本
		* @return: 版本相关信息
		************************/
		static std::string Version();

		/************************
		* @brief ModelCheckSum: 模型版本
		* @return: 模型MD5
		************************/
		static std::string Md5CheckSum();
        //检查指定路径的模型是否正确
        static bool CheckModel(std::string modelDir);

		/************************
		* @brief Esitmate: 人脸姿态估计接口函数，输出三个角度
		* @param_in faceshape： 人脸配准点EstimateExEstimateExEstimateEx
		* @param_in Deg	:	true(default)	:	以角度形式输出,
		*					false			:	以弧度形式输出
		* @param_out	:	pose(pitch, yaw, roll)
		************************/
		int Estimate(const std::vector<cv::Point2f> faceshape, YTPOSE &pose, bool Deg = true);

		/************************
		* @brief YtPoseEsitmatePerspective: 人脸透视投影姿态估计接口函数,输出三个角度及3D平移(需要已知摄像头参数)
		* @param_in faceshape： 人脸配准点
		* @param_in focal	:	摄像头像素焦距
		* @param_in center_x:	图像中点x
		* @param_in center_y:	图像中点y
		* @param_in meshout:true			:	输出物体坐标系下的三维人脸模型
		*					false(default)	:	不输出三维模型（只做姿态估计）
		* @param_in Deg:	true(default)	:	以角度形式输出,
		*					false			:	以弧度形式输出
		* @output	: Param_Perspective(pitch, yaw, roll, transform, denseFaceModel)
        * @return : 0 检测成功；
                    -10001 输入的faceshape点位不正确
		************************/
		int EstimateEx(const std::vector<cv::Point2f> faceshape, YTPOSE &pose, float center_x, float center_y);
		int EstimateEx(const std::vector<cv::Point2f> faceshape, YTPOSE &pose, float focal, float center_x, float center_y, bool meshout = false, bool Deg = true);
        
    private:
#ifdef USEFOR_OUTPUT
        YtPoseEstimate::YtPoseEstimateSdk * m_ytPoseEst;
#else
        YtPoseEstimateSdk * m_ytPoseEst;
#endif
	};
    


#if defined(YTFACETRACK_NAMESPACE) && ((defined __ANDROID__) || (defined __APPLE__))
#ifdef YTFACETRACK_NAMESPACE
}   //namespace YTFACETRACK_NAMESPACE
#endif //YTFACETRACK_NAMESPACE
#endif

#endif
