//
//  YTTrackCommonDefine.h
//  rpd_tracker
//
//  Created by PanCheng on 04/07/2017.
//  Copyright © 2017 <PERSON>. All rights reserved.
//

#ifndef YTTrackCommonDefine_h
#define YTTrackCommonDefine_h

#define YT_FACETRACKPRO_VERSION "2.3.3.4"

#if (defined __ANDROID__) || (defined __APPLE__) 
#define FACEDETECT_EXPORT __attribute__((visibility("default")))
#else
#define FACEDETECT_EXPORT
#endif

struct FACEDETECT_EXPORT YtFaceDetParam
{
	YtFaceDetParam();
	int min_face_size; //default=70; (18~max)
	int max_face_size; //default=99999999; (18~max)
	float scale_factor; //default 2.0736 (1.1~3.0)

	float net1_threshold;//default=0.1f; (0.1~1.0)
	float net2_threshold;//default=0.9f; (0.8~1.0)
	float final_threshold;//default=0.98f; (0.9~1.0)

	int net1_size;//default=18 (12 or 18)
	float nms_threshold;//default=0.3
	bool bigger_face_mode;//default=false; (false/true)(attention: not biggest)
	bool non_square_rect;//default=true
};


enum NETFIXCONFIG {
	FIX_NONE = 0,
	FIX_EYE = 1,
	FIX_EYEBROW = 2,
	FIX_EYE_EYEBROW = 3,
	FIX_MOUTH = 4,
	FIX_EYE_MOUTH = 5,
	FIX_EYEBROW_MOUTH = 6,
	FIX_EYE_EYEBROW_MOUTH = 7,
};


namespace yttrackpro
{
	static const int TRACKPRO_OK = 0x0;

	//auth
	static const int AUTH_LICENCE_ERROR = 0x1000;

	//detect
	static const int DETECT_MODEL_PATH_ERROR = 0x1001;
	static const int DETECT_MODEL_CONTENT_ERROR = 0x1002;
	static const int DETECT_NOT_INIT = 0x1003;
	static const int DETECT_INPUT_MAT_EMPTY = 0x1004;
	static const int DETECT_NET_NULL = 0x1005;

	//track
	static const int TRACK_MODEL_OPEN_ERROR = 0x2001;
	static const int TRACK_RPDNET_INIT_ERROR = 0x2002;
	static const int TRACK_INPUT_MAT_FORMAT_ERROR = 0x2003;
	static const int TRACK_INPUT_RECT_ERROR = 0x2004;
	static const int TRACK_INPUT_SHAPE_ERROR = 0x2005;

	static const int REFINE_CONFIG_OFF = 0x2005;
	static const int REFINE_CONFIG_PTS86 = 0x2006;
	//    static const int REFINE_CONFIG_PTS40 = 0x2007;

	static const int TRACK_NET_NULL = 0x2010;
	static const int TRACK_GLOBAL_RELEASED = 0x2011;

	//    static const int PUPIL_RADIUS_CONFIG_OFF = 0x201a;
	//    static const int PUPIL_RADIUS_CONFIG_ON = 0x201b;

	static const int MOVEMENT_MODEL_CONFIG_ON = 0x201c;
	static const int MOVEMENT_MODEL_CONFIG_OFF = 0x201d;

	//pose
	static const int POSE_MODEL_PATH_ERROR = 0x3001;
	static const int POSE_MODEL_CONTENT_ERROR = 0x3002;

	static const int POSE_2D_OPEN = 0x3003;
	static const int POSE_3D_OPEN = 0x3004;
	static const int POSE_OFF = 0x3005;

}

#endif /* YTTrackCommonDefine_h */
