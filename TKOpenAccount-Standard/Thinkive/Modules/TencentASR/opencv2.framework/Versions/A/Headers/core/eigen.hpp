// /*M///////////////////////////////////////////////////////////////////////////////////////
// //
// //  IMPORTANT: READ BEFORE DOWNLOADING, COPYING, INSTALLING OR USING.
// //
// //  By downloading, copying, installing or using the software you agree to this license.
// //  If you do not agree to this license, do not download, install,
// //  copy or use the software.
// //
// //
// //                          License Agreement
// //                For Open Source Computer Vision Library
// //
// // Copyright (C) 2000-2008, Intel Corporation, all rights reserved.
// // Copyright (C) 2009, Willow Garage Inc., all rights reserved.
// // Third party copyrights are property of their respective owners.
// //
// // Redistribution and use in source and binary forms, with or without modification,
// // are permitted provided that the following conditions are met:
// //
// //   * Redistribution's of source code must retain the above copyright notice,
// //     this list of conditions and the following disclaimer.
// //
// //   * Redistribution's in binary form must reproduce the above copyright notice,
// //     this list of conditions and the following disclaimer in the documentation
// //     and/or other materials provided with the distribution.
// //
// //   * The name of the copyright holders may not be used to endorse or promote products
// //     derived from this software without specific prior written permission.
// //
// // This software is provided by the copyright holders and contributors "as is" and
// // any express or implied warranties, including, but not limited to, the implied
// // warranties of merchantability and fitness for a particular purpose are disclaimed.
// // In no event shall the Intel Corporation or contributors be liable for any direct,
// // indirect, incidental, special, exemplary, or consequential damages
// // (including, but not limited to, procurement of substitute goods or services;
// // loss of use, data, or profits; or business interruption) however caused
// // and on any theory of liability, whether in contract, strict liability,
// // or tort (including negligence or otherwise) arising in any way out of
// // the use of this software, even if advised of the possibility of such damage.
// //
// //M*/

// #ifndef __OPENCV_CORE_EIGEN_HPP__
// #define __OPENCV_CORE_EIGEN_HPP__

// #ifdef __cplusplus

// #include "opencv2/core/core_c.h"
// #include "opencv2/core/core.hpp"

// #if defined _MSC_VER && _MSC_VER >= 1200
// #pragma warning( disable: 4714 ) //__forceinline is not inlined
// #pragma warning( disable: 4127 ) //conditional expression is constant
// #pragma warning( disable: 4244 ) //conversion from '__int64' to 'int', possible loss of data
// #endif

// namespace cv
// {

// template<typename _Tp, int _rows, int _cols, int _options, int _maxRows, int _maxCols>
// void eigen2cv( const Eigen::Matrix<_Tp, _rows, _cols, _options, _maxRows, _maxCols>& src, Mat& dst )
// {
//     if( !(src.Flags & Eigen::RowMajorBit) )
//     {
//         Mat _src(src.cols(), src.rows(), DataType<_Tp>::type,
//               (void*)src.data(), src.stride()*sizeof(_Tp));
//         transpose(_src, dst);
//     }
//     else
//     {
//         Mat _src(src.rows(), src.cols(), DataType<_Tp>::type,
//                  (void*)src.data(), src.stride()*sizeof(_Tp));
//         _src.copyTo(dst);
//     }
// }

// template<typename _Tp, int _rows, int _cols, int _options, int _maxRows, int _maxCols>
// void cv2eigen( const Mat& src,
//                Eigen::Matrix<_Tp, _rows, _cols, _options, _maxRows, _maxCols>& dst )
// {
//     CV_DbgAssert(src.rows == _rows && src.cols == _cols);
//     if( !(dst.Flags & Eigen::RowMajorBit) )
//     {
//         Mat _dst(src.cols, src.rows, DataType<_Tp>::type,
//                  dst.data(), (size_t)(dst.stride()*sizeof(_Tp)));
//         if( src.type() == _dst.type() )
//             transpose(src, _dst);
//         else if( src.cols == src.rows )
//         {
//             src.convertTo(_dst, _dst.type());
//             transpose(_dst, _dst);
//         }
//         else
//             Mat(src.t()).convertTo(_dst, _dst.type());
//         CV_DbgAssert(_dst.data == (uchar*)dst.data());
//     }
//     else
//     {
//         Mat _dst(src.rows, src.cols, DataType<_Tp>::type,
//                  dst.data(), (size_t)(dst.stride()*sizeof(_Tp)));
//         src.convertTo(_dst, _dst.type());
//         CV_DbgAssert(_dst.data == (uchar*)dst.data());
//     }
// }

// // Matx case
// template<typename _Tp, int _rows, int _cols, int _options, int _maxRows, int _maxCols>
// void cv2eigen( const Matx<_Tp, _rows, _cols>& src,
//                Eigen::Matrix<_Tp, _rows, _cols, _options, _maxRows, _maxCols>& dst )
// {
//     if( !(dst.Flags & Eigen::RowMajorBit) )
//     {
//         Mat _dst(_cols, _rows, DataType<_Tp>::type,
//                  dst.data(), (size_t)(dst.stride()*sizeof(_Tp)));
//         transpose(src, _dst);
//         CV_DbgAssert(_dst.data == (uchar*)dst.data());
//     }
//     else
//     {
//         Mat _dst(_rows, _cols, DataType<_Tp>::type,
//                  dst.data(), (size_t)(dst.stride()*sizeof(_Tp)));
//         Mat(src).copyTo(_dst);
//         CV_DbgAssert(_dst.data == (uchar*)dst.data());
//     }
// }

// template<typename _Tp>
// void cv2eigen( const Mat& src,
//                Eigen::Matrix<_Tp, Eigen::Dynamic, Eigen::Dynamic>& dst )
// {
//     dst.resize(src.rows, src.cols);
//     if( !(dst.Flags & Eigen::RowMajorBit) )
//     {
//         Mat _dst(src.cols, src.rows, DataType<_Tp>::type,
//              dst.data(), (size_t)(dst.stride()*sizeof(_Tp)));
//         if( src.type() == _dst.type() )
//             transpose(src, _dst);
//         else if( src.cols == src.rows )
//         {
//             src.convertTo(_dst, _dst.type());
//             transpose(_dst, _dst);
//         }
//         else
//             Mat(src.t()).convertTo(_dst, _dst.type());
//         CV_DbgAssert(_dst.data == (uchar*)dst.data());
//     }
//     else
//     {
//         Mat _dst(src.rows, src.cols, DataType<_Tp>::type,
//                  dst.data(), (size_t)(dst.stride()*sizeof(_Tp)));
//         src.convertTo(_dst, _dst.type());
//         CV_DbgAssert(_dst.data == (uchar*)dst.data());
//     }
// }

// // Matx case
// template<typename _Tp, int _rows, int _cols>
// void cv2eigen( const Matx<_Tp, _rows, _cols>& src,
//                Eigen::Matrix<_Tp, Eigen::Dynamic, Eigen::Dynamic>& dst )
// {
//     dst.resize(_rows, _cols);
//     if( !(dst.Flags & Eigen::RowMajorBit) )
//     {
//         Mat _dst(_cols, _rows, DataType<_Tp>::type,
//              dst.data(), (size_t)(dst.stride()*sizeof(_Tp)));
//         transpose(src, _dst);
//         CV_DbgAssert(_dst.data == (uchar*)dst.data());
//     }
//     else
//     {
//         Mat _dst(_rows, _cols, DataType<_Tp>::type,
//                  dst.data(), (size_t)(dst.stride()*sizeof(_Tp)));
//         Mat(src).copyTo(_dst);
//         CV_DbgAssert(_dst.data == (uchar*)dst.data());
//     }
// }

// template<typename _Tp>
// void cv2eigen( const Mat& src,
//                Eigen::Matrix<_Tp, Eigen::Dynamic, 1>& dst )
// {
//     CV_Assert(src.cols == 1);
//     dst.resize(src.rows);

//     if( !(dst.Flags & Eigen::RowMajorBit) )
//     {
//         Mat _dst(src.cols, src.rows, DataType<_Tp>::type,
//                  dst.data(), (size_t)(dst.stride()*sizeof(_Tp)));
//         if( src.type() == _dst.type() )
//             transpose(src, _dst);
//         else
//             Mat(src.t()).convertTo(_dst, _dst.type());
//         CV_DbgAssert(_dst.data == (uchar*)dst.data());
//     }
//     else
//     {
//         Mat _dst(src.rows, src.cols, DataType<_Tp>::type,
//                  dst.data(), (size_t)(dst.stride()*sizeof(_Tp)));
//         src.convertTo(_dst, _dst.type());
//         CV_DbgAssert(_dst.data == (uchar*)dst.data());
//     }
// }

// // Matx case
// template<typename _Tp, int _rows>
// void cv2eigen( const Matx<_Tp, _rows, 1>& src,
//                Eigen::Matrix<_Tp, Eigen::Dynamic, 1>& dst )
// {
//     dst.resize(_rows);

//     if( !(dst.Flags & Eigen::RowMajorBit) )
//     {
//         Mat _dst(1, _rows, DataType<_Tp>::type,
//                  dst.data(), (size_t)(dst.stride()*sizeof(_Tp)));
//         transpose(src, _dst);
//         CV_DbgAssert(_dst.data == (uchar*)dst.data());
//     }
//     else
//     {
//         Mat _dst(_rows, 1, DataType<_Tp>::type,
//                  dst.data(), (size_t)(dst.stride()*sizeof(_Tp)));
//         src.copyTo(_dst);
//         CV_DbgAssert(_dst.data == (uchar*)dst.data());
//     }
// }


// template<typename _Tp>
// void cv2eigen( const Mat& src,
//                Eigen::Matrix<_Tp, 1, Eigen::Dynamic>& dst )
// {
//     CV_Assert(src.rows == 1);
//     dst.resize(src.cols);
//     if( !(dst.Flags & Eigen::RowMajorBit) )
//     {
//         Mat _dst(src.cols, src.rows, DataType<_Tp>::type,
//                  dst.data(), (size_t)(dst.stride()*sizeof(_Tp)));
//         if( src.type() == _dst.type() )
//             transpose(src, _dst);
//         else
//             Mat(src.t()).convertTo(_dst, _dst.type());
//         CV_DbgAssert(_dst.data == (uchar*)dst.data());
//     }
//     else
//     {
//         Mat _dst(src.rows, src.cols, DataType<_Tp>::type,
//                  dst.data(), (size_t)(dst.stride()*sizeof(_Tp)));
//         src.convertTo(_dst, _dst.type());
//         CV_DbgAssert(_dst.data == (uchar*)dst.data());
//     }
// }

// //Matx
// template<typename _Tp, int _cols>
// void cv2eigen( const Matx<_Tp, 1, _cols>& src,
//                Eigen::Matrix<_Tp, 1, Eigen::Dynamic>& dst )
// {
//     dst.resize(_cols);
//     if( !(dst.Flags & Eigen::RowMajorBit) )
//     {
//         Mat _dst(_cols, 1, DataType<_Tp>::type,
//                  dst.data(), (size_t)(dst.stride()*sizeof(_Tp)));
//         transpose(src, _dst);
//         CV_DbgAssert(_dst.data == (uchar*)dst.data());
//     }
//     else
//     {
//         Mat _dst(1, _cols, DataType<_Tp>::type,
//                  dst.data(), (size_t)(dst.stride()*sizeof(_Tp)));
//         Mat(src).copyTo(_dst);
//         CV_DbgAssert(_dst.data == (uchar*)dst.data());
//     }
// }


// }

// #endif

// #endif
