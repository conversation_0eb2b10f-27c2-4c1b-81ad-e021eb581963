<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>Headers/QCloudAudioDataSource.h</key>
		<data>
		YEhxG6dlVGIs0SeEBCun6xSDO5o=
		</data>
		<key>Headers/QCloudAudioRecorderDataSource.h</key>
		<data>
		PhVtAf0iA+4zHOjCIq4BZvCp8NM=
		</data>
		<key>Headers/QCloudCommonParams.h</key>
		<data>
		2tIDEEBS3rW6kK7cQIJbV4P8eWU=
		</data>
		<key>Headers/QCloudConfig.h</key>
		<data>
		pWY8JV5VO4k8NcpCH2HkXjgYuIU=
		</data>
		<key>Headers/QCloudRealTimeRecognizer.h</key>
		<data>
		u84+iuMJPo88WXC0sQtgOSLclT8=
		</data>
		<key>Headers/QCloudRealTimeResult.h</key>
		<data>
		D082CoZUc/7O07H/S/6B0wvgc84=
		</data>
		<key>Headers/sdk_version.h</key>
		<data>
		XAWZ/k3+NKTKTaafGO53em5x+iA=
		</data>
		<key>Info.plist</key>
		<data>
		duBKk0BrmEyb7Y7t+Zo/XjVD9WE=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>Headers/QCloudAudioDataSource.h</key>
		<dict>
			<key>hash</key>
			<data>
			YEhxG6dlVGIs0SeEBCun6xSDO5o=
			</data>
			<key>hash2</key>
			<data>
			F0tAF+1hAr+SB42WkpusPPwUznOPjVW0U9XIRDnqJGA=
			</data>
		</dict>
		<key>Headers/QCloudAudioRecorderDataSource.h</key>
		<dict>
			<key>hash</key>
			<data>
			PhVtAf0iA+4zHOjCIq4BZvCp8NM=
			</data>
			<key>hash2</key>
			<data>
			NMo9VeUheGEL3grX75fB8fEArA+s+/5a7RHBJZHNbnE=
			</data>
		</dict>
		<key>Headers/QCloudCommonParams.h</key>
		<dict>
			<key>hash</key>
			<data>
			2tIDEEBS3rW6kK7cQIJbV4P8eWU=
			</data>
			<key>hash2</key>
			<data>
			Xaz5P9oss2qS4NAwmmRUhNSgAvpxWjA1KDWMfZvhlP4=
			</data>
		</dict>
		<key>Headers/QCloudConfig.h</key>
		<dict>
			<key>hash</key>
			<data>
			pWY8JV5VO4k8NcpCH2HkXjgYuIU=
			</data>
			<key>hash2</key>
			<data>
			KnX1Z29wnJCPYIa7kAtM6zjLxyKMBpCQcg9F7Hhi9TM=
			</data>
		</dict>
		<key>Headers/QCloudRealTimeRecognizer.h</key>
		<dict>
			<key>hash</key>
			<data>
			u84+iuMJPo88WXC0sQtgOSLclT8=
			</data>
			<key>hash2</key>
			<data>
			TKhOTVNMT8EO2J9hclw804rpZ0o8p2oKvZTwdMPa+PU=
			</data>
		</dict>
		<key>Headers/QCloudRealTimeResult.h</key>
		<dict>
			<key>hash</key>
			<data>
			D082CoZUc/7O07H/S/6B0wvgc84=
			</data>
			<key>hash2</key>
			<data>
			4rsQ/XnV0doKytr8lF1jU3HRdhdEEz/3rAggjcI+ynw=
			</data>
		</dict>
		<key>Headers/sdk_version.h</key>
		<dict>
			<key>hash</key>
			<data>
			XAWZ/k3+NKTKTaafGO53em5x+iA=
			</data>
			<key>hash2</key>
			<data>
			yko/aq9KjYltqHy+doXda3LQncnOWZ+LkcYw510g3m0=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
