//
//  FinAppletManager.m
//  FinoAppletDemo
//
//  Created by 刘宝 on 2020/10/19.
//  Copyright © 2020 刘宝. All rights reserved.
//

#import "TKAppletPluginManager.h"
#import <FinApplet/FinApplet.h>

@interface TKAppletPluginManager ()<TKIOSCallJSFilterDelegate>

@property(nonatomic,assign) BOOL isCheckPassed;

@end

@implementation TKAppletPluginManager

/**
 单例对象
 */
+(instancetype)shareInstance;
{
    static dispatch_once_t once;
    static id instance;
    dispatch_once(&once, ^{
        instance = [[self alloc]init];
    });
    return instance;
}


- (BOOL)initCTX
{
    [[TKAppEngine shareInstance]start];
    TKLogInfo(@"initCTX:");
    //url非空判断和鉴权校验
    NSString *authLicPath = [[NSBundle mainBundle] pathForResource:@"TKSDKAuth" ofType:@"lic"];
    NSString *licString = [[NSString alloc] initWithContentsOfFile:authLicPath encoding:NSUTF8StringEncoding error:nil];
    licString=[TKStringHelper stringWithTrim:licString];//去空格
    licString=[TKStringHelper stringWithReplace:licString oldStr:@"\n" newStr:@""];//去换行符
    self.isCheckPassed = [self authorChecking:licString url:@""];
    if(self.isCheckPassed)
    {
        [self initTKCTX];
        [self initFinJSBridgeCTX];
    }
    
    return self.isCheckPassed;
//    return YES;
}

-(void)initTKCTX
{
    
    [TKCommonService setIOSCallJSGlobalFilter:self];
}


-(void)initFinJSBridgeCTX
{
    TKLogInfo(@"initFinJSBridgeCTX:");
    [[FATClient sharedClient] fat_registerWebApi:@"tkjs-invoke-external" handle:^(id param, FATExtensionApiCallback callback) {
       NSMutableDictionary *paramMap = [(NSDictionary *)param mutableCopy];
        TKLogInfo(@"jsonParams:%@",[TKDataHelper dictionaryToJson:param]);
       NSString *funcNo = [paramMap getStringWithKey:@"funcNo"];
       NSString *moduleName = [paramMap getStringWithKey:@"moduleName"];
       if([funcNo isEqualToString:@"50114"])
       {
           [[FATClient sharedClient]closeCurrentApplet:YES];
       }
       else
       {
           [[TKPluginInvokeCenter shareInstance]callPlugin:funcNo param:paramMap moduleName:moduleName isH5:NO callBackFunc:^(NSMutableDictionary *result) {
               callback(FATExtensionCodeSuccess, result);
           }];
       }
   }];
}


/**
 * 原生调用JS拦截
 */
-(BOOL)onFilterIOSCallJS:(NSString *)webViewName function:(NSString *)function param:(NSObject *)param
{
    //这里是HTML中传过来的pageRoute
    NSString *pageRoute = nil;
    //这里是HTML中传过来的pageId
    NSNumber *pageId = nil;
    NSDictionary *webviewInfo = [TKDataHelper stringToDictionay:webViewName firstSplit:@"|" secondSplit:@":"];
    if(webviewInfo && webviewInfo.count > 0)
    {
        NSString *pageRouteParam = [webviewInfo getStringWithKey:@"@@PageRoute"];
        if([TKStringHelper isNotEmpty:pageRouteParam])
        {
            pageRoute = pageRouteParam;
        }
        NSString *pageIdParam = [webviewInfo getStringWithKey:@"@@PageId"];
        if([TKStringHelper isNotEmpty:pageIdParam])
        {
            pageId = @(pageIdParam.integerValue);
        }
    }
    //这里应该是参数字典转换成的json字符串
    NSMutableDictionary *jsonParam = [NSMutableDictionary dictionary];
    if([TKStringHelper isNotEmpty:function])
    {
        [jsonParam setString:function withKey:@"func"];
    }
    if(param)
    {
        [jsonParam setObject:param withKey:@"param"];
    }
    NSString *jsonParams = [TKDataHelper dictionaryToJson:jsonParam];
    
    [[FATClient sharedClient] fat_callWebApi:@"callMessageTKH5" paramString:jsonParams pageId:pageId handler:^(id result, NSError *error) {
    }];
    return NO;
}


#pragma mark - 鉴权模块

/**
<AUTHOR> 2020年04月05日10:13:03
@授权检查
*/
-(BOOL)authorChecking:(NSString *)authorString url:(NSString *)urlString{
    if (![authorString isEqualToString:@"7X9FVxYHFo1/0hGuh7mHE+1/RVcWBxaNf9IRroe5hxMeT9CVhloh0e0kmq2PagBU"]) {
        if ([TKStringHelper isEmpty:authorString]) {
            //没有授权
            TKLogInfo(@"鉴权-----授权字符串为空");
            return NO;
        }else{
            NSString *authorInfo=[TKAesHelper stringWithAesDecryptString:authorString withKey:[TKPasswordGenerator generatorPassword]];
            if ([TKStringHelper isEmpty:authorInfo]) {
                TKLogInfo(@"鉴权-----授权信息解密为空");
                return NO;
            }else{
                NSArray *arr = [authorInfo componentsSeparatedByString:@"#"];
                NSString *appKey;
                NSString *bundleId;
                if (arr.count>=2) {
                    appKey=arr[0];
                    bundleId=arr[1];//包名,多个用|分开
                    NSString *currentBundleId=[TKSystemHelper getAppIdentifier];
                    if ([bundleId rangeOfString:currentBundleId].location !=NSNotFound) {
                        if (arr.count>=3&&[arr[2] isEqualToString:@"ignoreURL"]) {
                            //鉴权忽略url校验,只管bundleid
                            return YES;
                        }else{
                            NSDictionary *params = [self paramsWithWebviewUrl:urlString];
                            NSString *tkid=params[@"tkid"];
                            if ([TKStringHelper isEmpty:tkid]) {
                                TKLogInfo(@"鉴权-----url没有tkid授权");
                                return NO;
                            }else{
                                NSURL *url=[NSURL URLWithString:urlString];
                                NSString *ipPort=[NSString stringWithFormat:@"%@:%@",url.host,url.port];
                                //生产md5截取16位，与url中tkid对比
                                NSString *md516String=[[TKMd5Helper md5Encrypt:[NSString stringWithFormat:@"%@%@",appKey,ipPort]] substringToIndex:16];
                                if ([tkid isEqualToString:md516String]) {
                                    return YES;
                                }else{
                                    TKLogInfo(@"鉴权-----tkid不匹配");
                                    return NO;
                                }
                            }
                        }
                        

                        
                    }else{
                        TKLogInfo(@"鉴权-----授权bundleId不正确");
                        return NO;
                    }
                }else{
                    TKLogInfo(@"鉴权-----授权信息格式不正确");
                    return NO;
                }
            }
        }
    }else{
        return YES;
    }
    
}

#pragma mark-解析url参数

- (NSDictionary *)paramsWithWebviewUrl:(NSString *)url{
    NSDictionary *params;
    NSArray *temp = [TKStringHelper string:url splitWith:@"?"];
    
    if (temp.count > 1)
    {
        NSString *paramStr = temp[1];
        paramStr=[TKStringHelper string:paramStr splitWith:@"#"][0];
        params=[TKDataHelper stringToDictionay:paramStr firstSplit:@"&" secondSplit:@"="];
    }
    return params;
}


@end
