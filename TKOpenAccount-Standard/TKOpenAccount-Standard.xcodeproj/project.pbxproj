// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 60;
	objects = {

/* Begin PBXAggregateTarget section */
		B3D794591B8EA14F00768134 /* build_TKOpenResource */ = {
			isa = PBXAggregateTarget;
			buildConfigurationList = B3D7945A1B8EA14F00768134 /* Build configuration list for PBXAggregateTarget "build_TKOpenResource" */;
			buildPhases = (
				B3D7945D1B8EA15400768134 /* ShellScript */,
			);
			dependencies = (
			);
			name = build_TKOpenResource;
			productName = build_TKOpenResource;
		};
/* End PBXAggregateTarget section */

/* Begin PBXBuildFile section */
		042CE7052408DD7B00B9AC15 /* TKOrdinaryOneVideoEndView.m in Sources */ = {isa = PBXBuildFile; fileRef = 042CE7042408DD7B00B9AC15 /* TKOrdinaryOneVideoEndView.m */; };
		043015EC23FCC811004F0C17 /* TKOpenPlugin60028.m in Sources */ = {isa = PBXBuildFile; fileRef = 043015EB23FCC811004F0C17 /* TKOpenPlugin60028.m */; };
		043015EF23FCC936004F0C17 /* TKFaceImageViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 043015EE23FCC936004F0C17 /* TKFaceImageViewController.m */; };
		043015F323FCCDBF004F0C17 /* TKFaceImageView.m in Sources */ = {isa = PBXBuildFile; fileRef = 043015F123FCCDBE004F0C17 /* TKFaceImageView.m */; };
		043884B923B05A240009F14D /* KeyBoard.xml in Resources */ = {isa = PBXBuildFile; fileRef = 043884B823B05A240009F14D /* KeyBoard.xml */; };
		0441008C23C43BD1005B9D05 /* TKFxcAccountInfoType.m in Sources */ = {isa = PBXBuildFile; fileRef = 0441008B23C43BD1005B9D05 /* TKFxcAccountInfoType.m */; };
		04441AA0221691F300CFDC2D /* VideoToolbox.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 040B88412148BC19008DBE05 /* VideoToolbox.framework */; };
		045ACEA622A4FC30004D8557 /* TKWebViewApp.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 045ACEA522A4FC30004D8557 /* TKWebViewApp.framework */; };
		045EF862241BA81000032B22 /* TKOpenPlugin60032.m in Sources */ = {isa = PBXBuildFile; fileRef = 045EF861241BA81000032B22 /* TKOpenPlugin60032.m */; };
		045EF866241BAF0100032B22 /* TKOpenPlugin60033.m in Sources */ = {isa = PBXBuildFile; fileRef = 045EF865241BAF0100032B22 /* TKOpenPlugin60033.m */; };
		04652354212E84C100F8C0D0 /* TKSignatureController.m in Sources */ = {isa = PBXBuildFile; fileRef = 0465234E212E84C000F8C0D0 /* TKSignatureController.m */; };
		04652355212E84C100F8C0D0 /* TKOpenPlugin60022.m in Sources */ = {isa = PBXBuildFile; fileRef = 04652350212E84C000F8C0D0 /* TKOpenPlugin60022.m */; };
		04652356212E84C100F8C0D0 /* TKTouchView.m in Sources */ = {isa = PBXBuildFile; fileRef = 04652353212E84C000F8C0D0 /* TKTouchView.m */; };
		0472AC9E23580798008FC27E /* TKOpenQueueView.m in Sources */ = {isa = PBXBuildFile; fileRef = 0472AC9823580798008FC27E /* TKOpenQueueView.m */; };
		0472AC9F23580798008FC27E /* TKOpenVideoChatView.m in Sources */ = {isa = PBXBuildFile; fileRef = 0472AC9A23580798008FC27E /* TKOpenVideoChatView.m */; };
		0472CA8623FB9DD4000B5EB7 /* TKOpenPlugin60007.m in Sources */ = {isa = PBXBuildFile; fileRef = 0472CA8123FB9DD4000B5EB7 /* TKOpenPlugin60007.m */; };
		0472CA8B23FB9EC4000B5EB7 /* TKLiveFaceViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 0472CA8A23FB9EC4000B5EB7 /* TKLiveFaceViewController.m */; };
		0472CA8E23FBA05F000B5EB7 /* TKLiveFaceView.m in Sources */ = {isa = PBXBuildFile; fileRef = 0472CA8C23FBA05F000B5EB7 /* TKLiveFaceView.m */; };
		047AF9682137BF5E003B1366 /* version.txt in Resources */ = {isa = PBXBuildFile; fileRef = 047AF9672137BF5D003B1366 /* version.txt */; };
		048DC9952407E66400A6F3EC /* TKOpenPlugin60030.m in Sources */ = {isa = PBXBuildFile; fileRef = 048DC9912407E66400A6F3EC /* TKOpenPlugin60030.m */; };
		048DC9962407E66400A6F3EC /* TKOrdinaryOneVideoView.m in Sources */ = {isa = PBXBuildFile; fileRef = 048DC9942407E66400A6F3EC /* TKOrdinaryOneVideoView.m */; };
		048DC9992407E6EF00A6F3EC /* TKOrdinaryOneVideoViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 048DC9982407E6EF00A6F3EC /* TKOrdinaryOneVideoViewController.m */; };
		049A8350211ECD6B00AA2048 /* TKAsset.bundle in Resources */ = {isa = PBXBuildFile; fileRef = 049A82E6211ECD6A00AA2048 /* TKAsset.bundle */; };
		049A8359211ECD6B00AA2048 /* libcrypto.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 049A834D211ECD6B00AA2048 /* libcrypto.a */; };
		049A835A211ECD6B00AA2048 /* libssl.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 049A834E211ECD6B00AA2048 /* libssl.a */; };
		04D9604922C3366E0010E4C6 /* Configuration_Open_BuriedPoint.xml in Resources */ = {isa = PBXBuildFile; fileRef = 04D9604822C3366E0010E4C6 /* Configuration_Open_BuriedPoint.xml */; };
		04E5B28A241CA7CD0069BCEE /* TKOpenPlugin60034.m in Sources */ = {isa = PBXBuildFile; fileRef = 04E5B289241CA7CD0069BCEE /* TKOpenPlugin60034.m */; };
		04E5B28D241CA9E20069BCEE /* TKDirectVideoViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 04E5B28C241CA9E20069BCEE /* TKDirectVideoViewController.m */; };
		04E5B290241CABE20069BCEE /* TKDirectVideoChatView.m in Sources */ = {isa = PBXBuildFile; fileRef = 04E5B28F241CABE20069BCEE /* TKDirectVideoChatView.m */; };
		04E5B29C241CB4030069BCEE /* TKDirectVideoModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 04E5B29B241CB4030069BCEE /* TKDirectVideoModel.m */; };
		18034324291CD426002C8E2C /* TKPlayerToolView.m in Sources */ = {isa = PBXBuildFile; fileRef = 18034323291CD426002C8E2C /* TKPlayerToolView.m */; };
		18138943244B269A007F96FA /* TKOpenPlugin60037.m in Sources */ = {isa = PBXBuildFile; fileRef = 1813893E244B269A007F96FA /* TKOpenPlugin60037.m */; };
		18138944244B269A007F96FA /* TKTakeBankPhotoViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 18138941244B269A007F96FA /* TKTakeBankPhotoViewController.m */; };
		181D4CA1286E94F600679EB3 /* TKVideoAlertView.m in Sources */ = {isa = PBXBuildFile; fileRef = 181D4CA0286E94F600679EB3 /* TKVideoAlertView.m */; };
		182CA7DE25BA6B820084C9F8 /* TKOpenPlugin60044.m in Sources */ = {isa = PBXBuildFile; fileRef = 182CA7DD25BA6B820084C9F8 /* TKOpenPlugin60044.m */; };
		182F804725DFCDC900505CDE /* TKOpenPlugin60049.m in Sources */ = {isa = PBXBuildFile; fileRef = 182F804625DFCDC900505CDE /* TKOpenPlugin60049.m */; };
		18339699245C27C000ECE057 /* TKOpenPlugin60039.m in Sources */ = {isa = PBXBuildFile; fileRef = 18339698245C27C000ECE057 /* TKOpenPlugin60039.m */; };
		183396A3245C298B00ECE057 /* READMEZXJT.txt in Resources */ = {isa = PBXBuildFile; fileRef = 1833969F245C298B00ECE057 /* READMEZXJT.txt */; };
		183D05B929384A8000346A76 /* TKOrganizationDoubleVideoViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 183D059E29384A8000346A76 /* TKOrganizationDoubleVideoViewController.m */; };
		183D05BA29384A8000346A76 /* TKOrganizationDoubleVideoBaseViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 183D059F29384A8000346A76 /* TKOrganizationDoubleVideoBaseViewController.m */; };
		183D05BB29384A8000346A76 /* TKOrganizationDoubleVideoNoVoiceViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 183D05A329384A8000346A76 /* TKOrganizationDoubleVideoNoVoiceViewController.m */; };
		183D05BC29384A8000346A76 /* TKOrganizationDoubleVideoAVManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 183D05A529384A8000346A76 /* TKOrganizationDoubleVideoAVManager.m */; };
		183D05BD29384A8000346A76 /* TKOpenPlugin61003.m in Sources */ = {isa = PBXBuildFile; fileRef = 183D05A729384A8000346A76 /* TKOpenPlugin61003.m */; };
		183D05BE29384A8000346A76 /* TKOrganizationDoubleVideoBottomTipView.m in Sources */ = {isa = PBXBuildFile; fileRef = 183D05AC29384A8000346A76 /* TKOrganizationDoubleVideoBottomTipView.m */; };
		183D05BF29384A8000346A76 /* TKOrganizationDoubleVideoTimeView.m in Sources */ = {isa = PBXBuildFile; fileRef = 183D05AD29384A8000346A76 /* TKOrganizationDoubleVideoTimeView.m */; };
		183D05C029384A8000346A76 /* TKOrganizationDoubleVideoBottomRightView.m in Sources */ = {isa = PBXBuildFile; fileRef = 183D05AE29384A8000346A76 /* TKOrganizationDoubleVideoBottomRightView.m */; };
		183D05C129384A8000346A76 /* TKOpenPlugin61001.m in Sources */ = {isa = PBXBuildFile; fileRef = 183D05B129384A8000346A76 /* TKOpenPlugin61001.m */; };
		183D05C229384A8000346A76 /* TKOrganizationSinglePhotoViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 183D05B529384A8000346A76 /* TKOrganizationSinglePhotoViewController.m */; };
		183D05C329384A8000346A76 /* TKOrginizationDoublePhotoViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 183D05B629384A8000346A76 /* TKOrginizationDoublePhotoViewController.m */; };
		1854638229669CBA00553822 /* TKChatService.m in Sources */ = {isa = PBXBuildFile; fileRef = 1854637F29669CB900553822 /* TKChatService.m */; };
		18714D8824454DE7002D809B /* TKVideoMsgTableViewCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 18714D8624454DE7002D809B /* TKVideoMsgTableViewCell.m */; };
		1876A14E2949CF57007F0500 /* TKTakeIDPhotoViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 1876A14D2949CF57007F0500 /* TKTakeIDPhotoViewController.m */; };
		187942C8255CD0AC0067C701 /* AdSupport.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 187942C7255CD0AC0067C701 /* AdSupport.framework */; };
		187CC37228F15C5E003442D6 /* TKOpenViewStyleHelper.m in Sources */ = {isa = PBXBuildFile; fileRef = 187CC37128F15C5E003442D6 /* TKOpenViewStyleHelper.m */; };
		187F35FB26AAB44C0046E9E3 /* TKFaceImageLandscapeViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 187F35FA26AAB44C0046E9E3 /* TKFaceImageLandscapeViewController.m */; };
		187F360026AAB4530046E9E3 /* TKFaceImageLandscapeView.m in Sources */ = {isa = PBXBuildFile; fileRef = 187F35FF26AAB4530046E9E3 /* TKFaceImageLandscapeView.m */; };
		187F361826AAB4680046E9E3 /* TKOneWayLandscapeVideoViewController.mm in Sources */ = {isa = PBXBuildFile; fileRef = 187F360726AAB4680046E9E3 /* TKOneWayLandscapeVideoViewController.mm */; };
		187F361926AAB4680046E9E3 /* TKOpenPlugin60062.m in Sources */ = {isa = PBXBuildFile; fileRef = 187F360826AAB4680046E9E3 /* TKOpenPlugin60062.m */; };
		187F361A26AAB4680046E9E3 /* TKOneWayLandscapeVideoEndView.m in Sources */ = {isa = PBXBuildFile; fileRef = 187F360A26AAB4680046E9E3 /* TKOneWayLandscapeVideoEndView.m */; };
		187F361B26AAB4680046E9E3 /* TKOneWayLandscapeVideoView.m in Sources */ = {isa = PBXBuildFile; fileRef = 187F360B26AAB4680046E9E3 /* TKOneWayLandscapeVideoView.m */; };
		187F361C26AAB4680046E9E3 /* TKOpenPlugin60064.m in Sources */ = {isa = PBXBuildFile; fileRef = 187F360F26AAB4680046E9E3 /* TKOpenPlugin60064.m */; };
		187F361D26AAB4680046E9E3 /* TKOpenBusinessLicenseViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 187F361126AAB4680046E9E3 /* TKOpenBusinessLicenseViewController.m */; };
		187F361E26AAB4680046E9E3 /* TKOpenPlugin61000.m in Sources */ = {isa = PBXBuildFile; fileRef = 187F361726AAB4680046E9E3 /* TKOpenPlugin61000.m */; };
		188DB0BE26245B4A00F6732D /* TKAppletPluginManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 188DB0B926245B4A00F6732D /* TKAppletPluginManager.m */; };
		188DB0BF26245B4A00F6732D /* README.txt in Resources */ = {isa = PBXBuildFile; fileRef = 188DB0BB26245B4A00F6732D /* README.txt */; };
		188DB0C026245E9700F6732D /* FinApplet.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 188DB0B826245B4A00F6732D /* FinApplet.framework */; };
		188DB0C126245E9700F6732D /* FinApplet.framework in Embed Frameworks */ = {isa = PBXBuildFile; fileRef = 188DB0B826245B4A00F6732D /* FinApplet.framework */; settings = {ATTRIBUTES = (CodeSignOnCopy, RemoveHeadersOnCopy, ); }; };
		188FC5B72468F43300F7D2A3 /* ZXJTBaseFramework.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 188FC5B52468F43300F7D2A3 /* ZXJTBaseFramework.framework */; };
		188FC5B82468F43300F7D2A3 /* JT_BasicControlImages.bundle in Resources */ = {isa = PBXBuildFile; fileRef = 188FC5B62468F43300F7D2A3 /* JT_BasicControlImages.bundle */; };
		1891C4812486148300C54EFF /* TKVideoReadAgreeView.m in Sources */ = {isa = PBXBuildFile; fileRef = 1891C47F2486148300C54EFF /* TKVideoReadAgreeView.m */; };
		18935039296547610052E77B /* TKOpenLoginViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 18935003296547610052E77B /* TKOpenLoginViewController.m */; };
		1893503A296547610052E77B /* DemoPageViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 18935004296547610052E77B /* DemoPageViewController.m */; };
		1893503B296547610052E77B /* DemoTableViewCell.xib in Resources */ = {isa = PBXBuildFile; fileRef = 18935007296547610052E77B /* DemoTableViewCell.xib */; };
		1893503C296547610052E77B /* DemoTableViewCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 18935008296547610052E77B /* DemoTableViewCell.m */; };
		1893503D296547610052E77B /* DemoViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 18935009296547610052E77B /* DemoViewController.m */; };
		1893503E296547610052E77B /* TestTextFieldView.m in Sources */ = {isa = PBXBuildFile; fileRef = 1893500D296547610052E77B /* TestTextFieldView.m */; };
		1893503F296547610052E77B /* TestGuidePageVC.m in Sources */ = {isa = PBXBuildFile; fileRef = 1893500F296547610052E77B /* TestGuidePageVC.m */; };
		1893504D296547610052E77B /* TChatRtcOneWayVideoTestVC.m in Sources */ = {isa = PBXBuildFile; fileRef = 18935034296547610052E77B /* TChatRtcOneWayVideoTestVC.m */; };
		1893504E296547610052E77B /* TKTakeIDCardTestVC.m in Sources */ = {isa = PBXBuildFile; fileRef = 18935036296547610052E77B /* TKTakeIDCardTestVC.m */; };
		1893504F296547610052E77B /* TChatRtcArtificialWitnessTestVC.m in Sources */ = {isa = PBXBuildFile; fileRef = 18935038296547610052E77B /* TChatRtcArtificialWitnessTestVC.m */; };
		189350712965498F0052E77B /* IQTextView.m in Sources */ = {isa = PBXBuildFile; fileRef = 189350522965498F0052E77B /* IQTextView.m */; };
		189350722965498F0052E77B /* IQToolbar.m in Sources */ = {isa = PBXBuildFile; fileRef = 189350562965498F0052E77B /* IQToolbar.m */; };
		189350732965498F0052E77B /* IQTitleBarButtonItem.m in Sources */ = {isa = PBXBuildFile; fileRef = 189350582965498F0052E77B /* IQTitleBarButtonItem.m */; };
		189350742965498F0052E77B /* IQBarButtonItem.m in Sources */ = {isa = PBXBuildFile; fileRef = 189350592965498F0052E77B /* IQBarButtonItem.m */; };
		189350752965498F0052E77B /* IQUIView+IQKeyboardToolbar.m in Sources */ = {isa = PBXBuildFile; fileRef = 1893505A2965498F0052E77B /* IQUIView+IQKeyboardToolbar.m */; };
		189350762965498F0052E77B /* IQPreviousNextView.m in Sources */ = {isa = PBXBuildFile; fileRef = 1893505B2965498F0052E77B /* IQPreviousNextView.m */; };
		189350772965498F0052E77B /* IQKeyboardManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 189350642965498F0052E77B /* IQKeyboardManager.m */; };
		189350782965498F0052E77B /* IQNSArray+Sort.m in Sources */ = {isa = PBXBuildFile; fileRef = 189350662965498F0052E77B /* IQNSArray+Sort.m */; };
		189350792965498F0052E77B /* IQUITextFieldView+Additions.m in Sources */ = {isa = PBXBuildFile; fileRef = 189350682965498F0052E77B /* IQUITextFieldView+Additions.m */; };
		1893507A2965498F0052E77B /* IQUIScrollView+Additions.m in Sources */ = {isa = PBXBuildFile; fileRef = 189350692965498F0052E77B /* IQUIScrollView+Additions.m */; };
		1893507B2965498F0052E77B /* IQUIView+Hierarchy.m in Sources */ = {isa = PBXBuildFile; fileRef = 1893506A2965498F0052E77B /* IQUIView+Hierarchy.m */; };
		1893507C2965498F0052E77B /* IQUIViewController+Additions.m in Sources */ = {isa = PBXBuildFile; fileRef = 1893506E2965498F0052E77B /* IQUIViewController+Additions.m */; };
		1893507D2965498F0052E77B /* IQKeyboardReturnKeyHandler.m in Sources */ = {isa = PBXBuildFile; fileRef = 189350702965498F0052E77B /* IQKeyboardReturnKeyHandler.m */; };
		1895FCB326959A9B00513E5F /* TKOpenPrivacyAgreementView.m in Sources */ = {isa = PBXBuildFile; fileRef = 1895FCB126959A9B00513E5F /* TKOpenPrivacyAgreementView.m */; };
		189F778926D8E6AE00F4089E /* TTTAttributedLabel.m in Sources */ = {isa = PBXBuildFile; fileRef = 189F778826D8E6AE00F4089E /* TTTAttributedLabel.m */; };
		18AF8645250771BA0094450D /* TKOpenPlugin60025.m in Sources */ = {isa = PBXBuildFile; fileRef = 18AF8644250771BA0094450D /* TKOpenPlugin60025.m */; };
		18AF8649250778530094450D /* TKOpenPlugin60043.m in Sources */ = {isa = PBXBuildFile; fileRef = 18AF8648250778530094450D /* TKOpenPlugin60043.m */; };
		18BBEF4F28A24AC100A26825 /* WebKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 18BBEF4E28A24AC100A26825 /* WebKit.framework */; };
		18C46C37291A54A10076BAC0 /* TKChatTokenHelper.m in Sources */ = {isa = PBXBuildFile; fileRef = 18C46C36291A54A10076BAC0 /* TKChatTokenHelper.m */; };
		18F129EB25FEF96000F26F5C /* TKFaceDetectManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 18F129E925FEF96000F26F5C /* TKFaceDetectManager.m */; };
		18F129F825FF231600F26F5C /* TKOpenTipView.m in Sources */ = {isa = PBXBuildFile; fileRef = 18F129F725FF231600F26F5C /* TKOpenTipView.m */; };
		18F12A1425FF24E000F26F5C /* TKOneWayVideoAlertTipView.m in Sources */ = {isa = PBXBuildFile; fileRef = 18F12A1125FF24E000F26F5C /* TKOneWayVideoAlertTipView.m */; };
		18FC9C84295D89320081518D /* TKOpenPlugin60093.m in Sources */ = {isa = PBXBuildFile; fileRef = 18FC9C82295D89320081518D /* TKOpenPlugin60093.m */; };
		18FF460E28E1B7AE0026440D /* SenseID_Liveness_Silent.lic in Resources */ = {isa = PBXBuildFile; fileRef = 18FF460B28E1B7AE0026440D /* SenseID_Liveness_Silent.lic */; };
		18FF460F28E1B7AE0026440D /* STLivenessModel.bundle in Resources */ = {isa = PBXBuildFile; fileRef = 18FF460C28E1B7AE0026440D /* STLivenessModel.bundle */; };
		331179A42E1684FC002179DC /* TKCardPreview.m in Sources */ = {isa = PBXBuildFile; fileRef = 331179A02E1684FC002179DC /* TKCardPreview.m */; };
		331179A52E1684FC002179DC /* TKIDCardPhotoView.m in Sources */ = {isa = PBXBuildFile; fileRef = 331179A22E1684FC002179DC /* TKIDCardPhotoView.m */; };
		331179A62E1684FC002179DC /* TKCardAlbumPreview.m in Sources */ = {isa = PBXBuildFile; fileRef = 3311799E2E1684FC002179DC /* TKCardAlbumPreview.m */; };
		3316E1832A2835E100E57A02 /* TKOpenPlugin60071.m in Sources */ = {isa = PBXBuildFile; fileRef = 3316E1822A2835E100E57A02 /* TKOpenPlugin60071.m */; };
		331ABDC22BA4107D00F0B963 /* TKOpenPlugin60095.m in Sources */ = {isa = PBXBuildFile; fileRef = 331ABDC12BA4107D00F0B963 /* TKOpenPlugin60095.m */; };
		3324DB272C47D86700D5E61B /* TKSDKAuth.lic in Resources */ = {isa = PBXBuildFile; fileRef = 3324DB262C47D86700D5E61B /* TKSDKAuth.lic */; };
		333DD1C22E24CF7E00DABD89 /* TKVideoRollTextView.m in Sources */ = {isa = PBXBuildFile; fileRef = 333DD1C12E24CF7E00DABD89 /* TKVideoRollTextView.m */; };
		334CE34C29FB651900FD2A69 /* (null) in Sources */ = {isa = PBXBuildFile; };
		334CE34D29FB651E00FD2A69 /* (null) in Sources */ = {isa = PBXBuildFile; };
		334D516F2C61BE6D00B1E35C /* PrivacyInfo.xcprivacy in Resources */ = {isa = PBXBuildFile; fileRef = 334D516E2C61BE6D00B1E35C /* PrivacyInfo.xcprivacy */; };
		3363023E2D83C202009BC4DC /* OAuth.xcframework in Frameworks */ = {isa = PBXBuildFile; fileRef = 336302352D83C202009BC4DC /* OAuth.xcframework */; };
		3363023F2D83C202009BC4DC /* TKOpenPlugin60041.m in Sources */ = {isa = PBXBuildFile; fileRef = 336302392D83C202009BC4DC /* TKOpenPlugin60041.m */; };
		336302402D83C202009BC4DC /* TKOpenPlugin60096.m in Sources */ = {isa = PBXBuildFile; fileRef = 3363023C2D83C202009BC4DC /* TKOpenPlugin60096.m */; };
		336302412D83C202009BC4DC /* TKOpenOneClickLoginViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 336302332D83C202009BC4DC /* TKOpenOneClickLoginViewController.m */; };
		336302422D83C202009BC4DC /* TKOpenOneClickLoginService.m in Sources */ = {isa = PBXBuildFile; fileRef = 336302372D83C202009BC4DC /* TKOpenOneClickLoginService.m */; };
		336BA7AF29BF2A0A00134194 /* opencv3.4.2.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 336BA7AD29BF2A0A00134194 /* opencv3.4.2.a */; };
		336BA7B029BF2A0A00134194 /* ISIDReaderPreviewSDK.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 336BA7AE29BF2A0A00134194 /* ISIDReaderPreviewSDK.framework */; };
		336BA7DA29BF2A1000134194 /* ISOpenSDKFoundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 336BA7B329BF2A1000134194 /* ISOpenSDKFoundation.framework */; };
		336BA7DB29BF2A1000134194 /* is_camera_closed.png in Resources */ = {isa = PBXBuildFile; fileRef = 336BA7B529BF2A1000134194 /* is_camera_closed.png */; };
		336BA7DC29BF2A1000134194 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 336BA7B629BF2A1000134194 /* <EMAIL> */; };
		336BA7DD29BF2A1000134194 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 336BA7B729BF2A1000134194 /* <EMAIL> */; };
		336BA7DE29BF2A1000134194 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 336BA7B829BF2A1000134194 /* <EMAIL> */; };
		336BA7DF29BF2A1000134194 /* is_camera_corner_highlight_Red.png in Resources */ = {isa = PBXBuildFile; fileRef = 336BA7B929BF2A1000134194 /* is_camera_corner_highlight_Red.png */; };
		336BA7E029BF2A1000134194 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 336BA7BA29BF2A1000134194 /* <EMAIL> */; };
		336BA7E129BF2A1000134194 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 336BA7BB29BF2A1000134194 /* <EMAIL> */; };
		336BA7E229BF2A1000134194 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 336BA7BC29BF2A1000134194 /* <EMAIL> */; };
		336BA7E329BF2A1000134194 /* is_camera_closed_ccb.png in Resources */ = {isa = PBXBuildFile; fileRef = 336BA7BD29BF2A1000134194 /* is_camera_closed_ccb.png */; };
		336BA7E429BF2A1000134194 /* is_camera_flash_on.png in Resources */ = {isa = PBXBuildFile; fileRef = 336BA7BE29BF2A1000134194 /* is_camera_flash_on.png */; };
		336BA7E529BF2A1000134194 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 336BA7BF29BF2A1000134194 /* <EMAIL> */; };
		336BA7E629BF2A1000134194 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 336BA7C029BF2A1000134194 /* <EMAIL> */; };
		336BA7E729BF2A1000134194 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 336BA7C129BF2A1000134194 /* <EMAIL> */; };
		336BA7E829BF2A1000134194 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 336BA7C229BF2A1000134194 /* <EMAIL> */; };
		336BA7E929BF2A1000134194 /* is_camera_flash__ccb_off.png in Resources */ = {isa = PBXBuildFile; fileRef = 336BA7C329BF2A1000134194 /* is_camera_flash__ccb_off.png */; };
		336BA7EA29BF2A1000134194 /* is_camera_corner.png in Resources */ = {isa = PBXBuildFile; fileRef = 336BA7C429BF2A1000134194 /* is_camera_corner.png */; };
		336BA7EB29BF2A1000134194 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 336BA7C529BF2A1000134194 /* <EMAIL> */; };
		336BA7EC29BF2A1000134194 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 336BA7C629BF2A1000134194 /* <EMAIL> */; };
		336BA7ED29BF2A1000134194 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 336BA7C729BF2A1000134194 /* <EMAIL> */; };
		336BA7EE29BF2A1000134194 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 336BA7C829BF2A1000134194 /* <EMAIL> */; };
		336BA7EF29BF2A1000134194 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 336BA7C929BF2A1000134194 /* <EMAIL> */; };
		336BA7F029BF2A1000134194 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 336BA7CA29BF2A1000134194 /* <EMAIL> */; };
		336BA7F129BF2A1000134194 /* is_camera_logo.png in Resources */ = {isa = PBXBuildFile; fileRef = 336BA7CB29BF2A1000134194 /* is_camera_logo.png */; };
		336BA7F229BF2A1000134194 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 336BA7CC29BF2A1000134194 /* <EMAIL> */; };
		336BA7F329BF2A1000134194 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 336BA7CD29BF2A1000134194 /* <EMAIL> */; };
		336BA7F429BF2A1000134194 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 336BA7CE29BF2A1000134194 /* <EMAIL> */; };
		336BA7F529BF2A1000134194 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 336BA7CF29BF2A1000134194 /* <EMAIL> */; };
		336BA7F629BF2A1000134194 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 336BA7D029BF2A1000134194 /* <EMAIL> */; };
		336BA7F729BF2A1000134194 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 336BA7D129BF2A1000134194 /* <EMAIL> */; };
		336BA7F829BF2A1000134194 /* is_camera_flash_ccb_on.png in Resources */ = {isa = PBXBuildFile; fileRef = 336BA7D229BF2A1000134194 /* is_camera_flash_ccb_on.png */; };
		336BA7F929BF2A1000134194 /* is_camera_corner_highlight.png in Resources */ = {isa = PBXBuildFile; fileRef = 336BA7D329BF2A1000134194 /* is_camera_corner_highlight.png */; };
		336BA7FA29BF2A1000134194 /* is_camera_flash_off.png in Resources */ = {isa = PBXBuildFile; fileRef = 336BA7D429BF2A1000134194 /* is_camera_flash_off.png */; };
		336BA7FB29BF2A1000134194 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 336BA7D529BF2A1000134194 /* <EMAIL> */; };
		336BA7FC29BF2A1000134194 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 336BA7D629BF2A1000134194 /* <EMAIL> */; };
		336BA7FD29BF2A1000134194 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 336BA7D729BF2A1000134194 /* <EMAIL> */; };
		336BA7FE29BF2A1000134194 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 336BA7D829BF2A1000134194 /* <EMAIL> */; };
		336BA7FF29BF2A1000134194 /* ISBankCard.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 336BA7D929BF2A1000134194 /* ISBankCard.framework */; };
		336D640C29C3297800BB96A1 /* TKOrganizationDoubleVideoResultSuccessViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 336D640629C3297800BB96A1 /* TKOrganizationDoubleVideoResultSuccessViewController.m */; };
		336D640D29C3297800BB96A1 /* TKOrganizationDoubleVideoResultBaseViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 336D640829C3297800BB96A1 /* TKOrganizationDoubleVideoResultBaseViewController.m */; };
		336D640E29C3297800BB96A1 /* TKOrganizationDoubleVideoResultFailureViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 336D640A29C3297800BB96A1 /* TKOrganizationDoubleVideoResultFailureViewController.m */; };
		336D641129C3298800BB96A1 /* TKOrganizationDoubleVideoGradientButton.m in Sources */ = {isa = PBXBuildFile; fileRef = 336D640F29C3298800BB96A1 /* TKOrganizationDoubleVideoGradientButton.m */; };
		337A9F0E2A37062500CE0434 /* AnyChatCoreSDK.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 337A9F0D2A37062500CE0434 /* AnyChatCoreSDK.framework */; };
		338D7FE22B312BDB0030979E /* QCloudTTS.xcframework in Frameworks */ = {isa = PBXBuildFile; fileRef = 338D7FE12B312BDB0030979E /* QCloudTTS.xcframework */; };
		338D7FE42B312C020030979E /* QCloudRealTime.xcframework in Frameworks */ = {isa = PBXBuildFile; fileRef = 338D7FE32B312C020030979E /* QCloudRealTime.xcframework */; };
		33A4ED7F2D081533007215C1 /* TKOpenDelegateManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 33A4ED7D2D081533007215C1 /* TKOpenDelegateManager.m */; };
		33B36AF92C21826900121BC3 /* TKOpenPlugin60046.m in Sources */ = {isa = PBXBuildFile; fileRef = 33B36AF72C21826900121BC3 /* TKOpenPlugin60046.m */; };
		33B3E7622E1BC248004C433C /* TKOCRTimeoutView.m in Sources */ = {isa = PBXBuildFile; fileRef = 33B3E7612E1BC248004C433C /* TKOCRTimeoutView.m */; };
		33BDE2932A7203CD004F30D1 /* TKOpenPlugin60094.m in Sources */ = {isa = PBXBuildFile; fileRef = 33BDE2912A7203CD004F30D1 /* TKOpenPlugin60094.m */; };
		33C207AC2A147A5100087D13 /* GLKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 33C207AB2A147A5100087D13 /* GLKit.framework */; };
		33C207AE2A147ACC00087D13 /* libc++.tbd in Frameworks */ = {isa = PBXBuildFile; fileRef = 33C207AD2A147ACB00087D13 /* libc++.tbd */; };
		33C207D92A148B2700087D13 /* TKALSpeechSynthesisManager.mm in Sources */ = {isa = PBXBuildFile; fileRef = 33C207B32A148B2700087D13 /* TKALSpeechSynthesisManager.mm */; };
		33C207DA2A148B2700087D13 /* TKSpeechSynthesisManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 33C207B42A148B2700087D13 /* TKSpeechSynthesisManager.m */; };
		33C207DB2A148B2700087D13 /* TKTencentSpeechSynthesisManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 33C207B62A148B2700087D13 /* TKTencentSpeechSynthesisManager.m */; };
		33C207DE2A148B2700087D13 /* TKringBuf.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 33C207BD2A148B2700087D13 /* TKringBuf.cpp */; };
		33C207DF2A148B2700087D13 /* TKNLSVoiceRecorder.m in Sources */ = {isa = PBXBuildFile; fileRef = 33C207BF2A148B2700087D13 /* TKNLSVoiceRecorder.m */; };
		33C207E12A148B2700087D13 /* TKTencentSpeechRecognizeManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 33C207C42A148B2700087D13 /* TKTencentSpeechRecognizeManager.m */; };
		33C207E22A148B2700087D13 /* TKSpeechRecognizeManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 33C207C52A148B2700087D13 /* TKSpeechRecognizeManager.m */; };
		33C207E42A148B2700087D13 /* TKALSpeechRecognizeManager.mm in Sources */ = {isa = PBXBuildFile; fileRef = 33C207CC2A148B2700087D13 /* TKALSpeechRecognizeManager.mm */; };
		33C207E52A148B2700087D13 /* TKNLSPlayAudio.mm in Sources */ = {isa = PBXBuildFile; fileRef = 33C207CD2A148B2700087D13 /* TKNLSPlayAudio.mm */; };
		33C207E62A148B2700087D13 /* TKOneWayVideoViewController.mm in Sources */ = {isa = PBXBuildFile; fileRef = 33C207D22A148B2700087D13 /* TKOneWayVideoViewController.mm */; };
		33C207E72A148B2700087D13 /* TKOpenPlugin60026.m in Sources */ = {isa = PBXBuildFile; fileRef = 33C207D32A148B2700087D13 /* TKOpenPlugin60026.m */; };
		33C207E82A148B2700087D13 /* TKOneWayVideoEndView.m in Sources */ = {isa = PBXBuildFile; fileRef = 33C207D52A148B2700087D13 /* TKOneWayVideoEndView.m */; };
		33C207E92A148B2700087D13 /* TKOneWayVideoView.m in Sources */ = {isa = PBXBuildFile; fileRef = 33C207D62A148B2700087D13 /* TKOneWayVideoView.m */; };
		33C207EB2A148B6100087D13 /* TKVideoWitnessViewController+AnyChat.m in Sources */ = {isa = PBXBuildFile; fileRef = B30FD8AB1EF11ED2000D3E94 /* TKVideoWitnessViewController+AnyChat.m */; };
		33C502AF2A0A4D5F0050DB0C /* nuisdk.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 33C502AD2A0A4D520050DB0C /* nuisdk.framework */; };
		33C502B02A0A4D5F0050DB0C /* nuisdk.framework in Embed Frameworks */ = {isa = PBXBuildFile; fileRef = 33C502AD2A0A4D520050DB0C /* nuisdk.framework */; settings = {ATTRIBUTES = (CodeSignOnCopy, RemoveHeadersOnCopy, ); }; };
		33C502B32A0A4D890050DB0C /* STLivenessDetector.xcframework in Frameworks */ = {isa = PBXBuildFile; fileRef = 33C502B12A0A4D860050DB0C /* STLivenessDetector.xcframework */; };
		33C502B42A0A4D890050DB0C /* STLivenessDetector.xcframework in Embed Frameworks */ = {isa = PBXBuildFile; fileRef = 33C502B12A0A4D860050DB0C /* STLivenessDetector.xcframework */; settings = {ATTRIBUTES = (CodeSignOnCopy, RemoveHeadersOnCopy, ); }; };
		33CB8E302DA6513D000EA7C4 /* TKOpenPlugin60817.m in Sources */ = {isa = PBXBuildFile; fileRef = 33CB8E2E2DA6513D000EA7C4 /* TKOpenPlugin60817.m */; };
		33DAFAA02C61E8C6009F2243 /* iflyMSC.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 33DAFA9F2C61E8C6009F2243 /* iflyMSC.framework */; };
		33DAFAA32C61E9D6009F2243 /* TKiflyNoAiSpeechSynthesisManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 33DAFAA22C61E9D6009F2243 /* TKiflyNoAiSpeechSynthesisManager.m */; };
		33DAFAA62C61E9E1009F2243 /* TKiflyNoAiSpeechRecognizeManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 33DAFAA52C61E9E1009F2243 /* TKiflyNoAiSpeechRecognizeManager.m */; };
		33DD1C822A97279F00DD3F74 /* TKTChatSmartTwoVideoManager.m in Sources */ = {isa = PBXBuildFile; fileRef = DC4455102A380E700073C229 /* TKTChatSmartTwoVideoManager.m */; };
		33DF5F502D1A9B0700F311AA /* TKOpenPlugin60813.m in Sources */ = {isa = PBXBuildFile; fileRef = 33DF5F4E2D1A9B0700F311AA /* TKOpenPlugin60813.m */; };
		33E2EA1B2E371F5D00037BE4 /* TKBankCardPhotoView.m in Sources */ = {isa = PBXBuildFile; fileRef = 33E2EA1A2E371F5D00037BE4 /* TKBankCardPhotoView.m */; };
		33EA0F432C9A69AB0066022A /* TKOpenPlugin60812.m in Sources */ = {isa = PBXBuildFile; fileRef = 33EA0F422C9A69AB0066022A /* TKOpenPlugin60812.m */; };
		33EFE3D42AA9677200D070FA /* TKVideoWitnessViewController+TChat.m in Sources */ = {isa = PBXBuildFile; fileRef = B32E91C81F18C95E005A6B6D /* TKVideoWitnessViewController+TChat.m */; };
		33FD40892E2F3AEE00687DD1 /* TKOpenPlugin60066.m in Sources */ = {isa = PBXBuildFile; fileRef = 33FD40872E2F3AEE00687DD1 /* TKOpenPlugin60066.m */; };
		B30DC50A1BCFC873007072FB /* Configuration.xml in Resources */ = {isa = PBXBuildFile; fileRef = B30DC5061BCFC873007072FB /* Configuration.xml */; };
		B30DC50B1BCFC873007072FB /* OpenPlugin.xml in Resources */ = {isa = PBXBuildFile; fileRef = B30DC5071BCFC873007072FB /* OpenPlugin.xml */; };
		B30DC50C1BCFC873007072FB /* SystemPlugin.xml in Resources */ = {isa = PBXBuildFile; fileRef = B30DC5081BCFC873007072FB /* SystemPlugin.xml */; };
		B30FD8A71EF1063F000D3E94 /* UIViewController+TKAuthorityKit.m in Sources */ = {isa = PBXBuildFile; fileRef = B30FD8A61EF1063F000D3E94 /* UIViewController+TKAuthorityKit.m */; };
		B30FD8AE1EF11ED2000D3E94 /* TKVideoWitnessViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = B30FD8A91EF11ED2000D3E94 /* TKVideoWitnessViewController.m */; };
		B30FD8B01EF11ED2000D3E94 /* TKVideoWitnessViewController+CommonKit.m in Sources */ = {isa = PBXBuildFile; fileRef = B30FD8AD1EF11ED2000D3E94 /* TKVideoWitnessViewController+CommonKit.m */; };
		B32C870C1D87E96E00373C19 /* config.plist in Resources */ = {isa = PBXBuildFile; fileRef = B32C870B1D87E96E00373C19 /* config.plist */; };
		B3386D6B1F271753006EF60A /* TKAVCaptureManager.m in Sources */ = {isa = PBXBuildFile; fileRef = B3386D6A1F271753006EF60A /* TKAVCaptureManager.m */; };
		B33FDC101F692BCB00C644F2 /* libxml2.tbd in Frameworks */ = {isa = PBXBuildFile; fileRef = B33FDC0F1F692BCB00C644F2 /* libxml2.tbd */; };
		B33FDC121F692BE400C644F2 /* libresolv.tbd in Frameworks */ = {isa = PBXBuildFile; fileRef = B33FDC111F692BE400C644F2 /* libresolv.tbd */; };
		B35087C41F0E367300A366A0 /* tk_video_icon_iphone.png in Resources */ = {isa = PBXBuildFile; fileRef = B35087C21F0E367300A366A0 /* tk_video_icon_iphone.png */; };
		B35087C51F0E367300A366A0 /* TKVideoWitnessViewController.xib in Resources */ = {isa = PBXBuildFile; fileRef = B35087C31F0E367300A366A0 /* TKVideoWitnessViewController.xib */; };
		B3556A8E1DDBF96B00C76E7C /* TKOpenPlugin60000.m in Sources */ = {isa = PBXBuildFile; fileRef = B35569F71DDBF96B00C76E7C /* TKOpenPlugin60000.m */; };
		B3556A8F1DDBF96B00C76E7C /* TKOpenPlugin60001.m in Sources */ = {isa = PBXBuildFile; fileRef = B35569FA1DDBF96B00C76E7C /* TKOpenPlugin60001.m */; };
		B3556A901DDBF96B00C76E7C /* TKOpenPlugin60002.m in Sources */ = {isa = PBXBuildFile; fileRef = B35569FD1DDBF96B00C76E7C /* TKOpenPlugin60002.m */; };
		B3556A911DDBF96B00C76E7C /* MTakeCardViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = B3556A001DDBF96B00C76E7C /* MTakeCardViewController.m */; };
		B3556A921DDBF96B00C76E7C /* TKOpenPlugin60003.m in Sources */ = {isa = PBXBuildFile; fileRef = B3556A031DDBF96B00C76E7C /* TKOpenPlugin60003.m */; };
		B3556A931DDBF96B00C76E7C /* TKOpenPlugin60004.m in Sources */ = {isa = PBXBuildFile; fileRef = B3556A061DDBF96B00C76E7C /* TKOpenPlugin60004.m */; };
		B3556A9F1DDBF96B00C76E7C /* TKOpenPlugin60005.m in Sources */ = {isa = PBXBuildFile; fileRef = B3556A1B1DDBF96B00C76E7C /* TKOpenPlugin60005.m */; };
		B3556AA01DDBF96B00C76E7C /* TkFDRecordController.m in Sources */ = {isa = PBXBuildFile; fileRef = B3556A1F1DDBF96B00C76E7C /* TkFDRecordController.m */; };
		B3556AA11DDBF96B00C76E7C /* TKRecordController.m in Sources */ = {isa = PBXBuildFile; fileRef = B3556A211DDBF96B00C76E7C /* TKRecordController.m */; };
		B3556AA21DDBF96B00C76E7C /* TKRecordModel.m in Sources */ = {isa = PBXBuildFile; fileRef = B3556A241DDBF96B00C76E7C /* TKRecordModel.m */; };
		B3556AA31DDBF96B00C76E7C /* TKOpenPlugin60006.m in Sources */ = {isa = PBXBuildFile; fileRef = B3556A261DDBF96B00C76E7C /* TKOpenPlugin60006.m */; };
		B3556AA41DDBF96B00C76E7C /* TKRecordView.m in Sources */ = {isa = PBXBuildFile; fileRef = B3556A291DDBF96B00C76E7C /* TKRecordView.m */; };
		B3556AAC1DDBF96B00C76E7C /* MTakeFaceViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = B3556A3C1DDBF96B00C76E7C /* MTakeFaceViewController.m */; };
		B3556AAD1DDBF96B00C76E7C /* TKOpenPlugin60008.m in Sources */ = {isa = PBXBuildFile; fileRef = B3556A3E1DDBF96B00C76E7C /* TKOpenPlugin60008.m */; };
		B3556AAE1DDBF96B00C76E7C /* TKOpenPlugin60010.m in Sources */ = {isa = PBXBuildFile; fileRef = B3556A411DDBF96B00C76E7C /* TKOpenPlugin60010.m */; };
		B3556AB01DDBF96B00C76E7C /* MTakeBigPictureViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = B3556A471DDBF96B00C76E7C /* MTakeBigPictureViewController.m */; };
		B3556AB11DDBF96B00C76E7C /* MTakePhotoViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = B3556A491DDBF96B00C76E7C /* MTakePhotoViewController.m */; };
		B3556AB21DDBF96B00C76E7C /* TKOpenPlugin60013.m in Sources */ = {isa = PBXBuildFile; fileRef = B3556A4B1DDBF96B00C76E7C /* TKOpenPlugin60013.m */; };
		B3556AB41DDBF96B00C76E7C /* MIDCardRecognizeViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = B3556A511DDBF96B00C76E7C /* MIDCardRecognizeViewController.m */; };
		B3556AB51DDBF96B00C76E7C /* TKOpenPlugin60014.m in Sources */ = {isa = PBXBuildFile; fileRef = B3556A531DDBF96B00C76E7C /* TKOpenPlugin60014.m */; };
		B3556AD71DDBF96B00C76E7C /* TKBankCardRecognizeViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = B3556A851DDBF96B00C76E7C /* TKBankCardRecognizeViewController.m */; };
		B3556AD81DDBF96B00C76E7C /* TKOpenPlugin60016.m in Sources */ = {isa = PBXBuildFile; fileRef = B3556A871DDBF96B00C76E7C /* TKOpenPlugin60016.m */; };
		B3556AD91DDBF96B00C76E7C /* TKOpenPlugin60017.m in Sources */ = {isa = PBXBuildFile; fileRef = B3556A8A1DDBF96B00C76E7C /* TKOpenPlugin60017.m */; };
		B3556ADA1DDBF96B00C76E7C /* TKOpenPlugin60018.m in Sources */ = {isa = PBXBuildFile; fileRef = B3556A8D1DDBF96B00C76E7C /* TKOpenPlugin60018.m */; };
		B3556ADB1DDBFA7300C76E7C /* chat_cancel_btn.png in Resources */ = {isa = PBXBuildFile; fileRef = B3556A131DDBF96B00C76E7C /* chat_cancel_btn.png */; };
		B3556ADC1DDBFA7300C76E7C /* kefu_bg_img.png in Resources */ = {isa = PBXBuildFile; fileRef = B3556A141DDBF96B00C76E7C /* kefu_bg_img.png */; };
		B3556ADD1DDBFA7300C76E7C /* page_bg.jpg in Resources */ = {isa = PBXBuildFile; fileRef = B3556A151DDBF96B00C76E7C /* page_bg.jpg */; };
		B3556ADE1DDBFA7300C76E7C /* TKAnyChatViewController.xib in Resources */ = {isa = PBXBuildFile; fileRef = B3556A161DDBF96B00C76E7C /* TKAnyChatViewController.xib */; };
		B3556ADF1DDBFA7300C76E7C /* TKAnyChatViewController4.xib in Resources */ = {isa = PBXBuildFile; fileRef = B3556A171DDBF96B00C76E7C /* TKAnyChatViewController4.xib */; };
		B35EBF3B1C4393770059F885 /* MNavViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = B35EBF3A1C4393770059F885 /* MNavViewController.m */; };
		B35EBF431C43942F0059F885 /* MDRadialProgressLabel.m in Sources */ = {isa = PBXBuildFile; fileRef = B35EBF3E1C43942F0059F885 /* MDRadialProgressLabel.m */; };
		B35EBF441C43942F0059F885 /* MDRadialProgressTheme.m in Sources */ = {isa = PBXBuildFile; fileRef = B35EBF401C43942F0059F885 /* MDRadialProgressTheme.m */; };
		B35EBF451C43942F0059F885 /* MDRadialProgressView.m in Sources */ = {isa = PBXBuildFile; fileRef = B35EBF421C43942F0059F885 /* MDRadialProgressView.m */; };
		B379D393206CD57000D1440E /* CoreTelephony.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = B379D392206CD57000D1440E /* CoreTelephony.framework */; };
		B37F84291BE365230016F93D /* OpenGLES.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = B37F84281BE365230016F93D /* OpenGLES.framework */; };
		B384CDCC1B8177B400AFD817 /* TKButton.m in Sources */ = {isa = PBXBuildFile; fileRef = B384CDC91B8177B400AFD817 /* TKButton.m */; };
		B384CDCD1B8177B400AFD817 /* TKCameraTools.m in Sources */ = {isa = PBXBuildFile; fileRef = B384CDCB1B8177B400AFD817 /* TKCameraTools.m */; };
		B386BA0E1F947E6C001CBD13 /* TKOpenPlugin60099.m in Sources */ = {isa = PBXBuildFile; fileRef = B386BA0D1F947E6C001CBD13 /* TKOpenPlugin60099.m */; };
		B38D07B71FC402EC0013DA81 /* TKMAlertViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = B38D07B51FC402EC0013DA81 /* TKMAlertViewController.m */; };
		B39BADD01B4D524F00F071D4 /* CoreLocation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = B39BADCF1B4D524F00F071D4 /* CoreLocation.framework */; };
		B39E930F208F0532006EC8A3 /* CoreMotion.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = B39E930E208F0532006EC8A3 /* CoreMotion.framework */; };
		B39E9311208F054A006EC8A3 /* MessageUI.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = B39E9310208F054A006EC8A3 /* MessageUI.framework */; };
		B39E9313208F05CD006EC8A3 /* Accelerate.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = B39E9312208F05CD006EC8A3 /* Accelerate.framework */; };
		B3A2DD331EA461A700B18842 /* TKCommonUtil.m in Sources */ = {isa = PBXBuildFile; fileRef = B3A2DD321EA461A700B18842 /* TKCommonUtil.m */; };
		B3AB8AFA1C15708900571BE6 /* TKOpenResource.bundle in Resources */ = {isa = PBXBuildFile; fileRef = B3AB8AF91C15708900571BE6 /* TKOpenResource.bundle */; };
		B3B108841B44DF2800546D96 /* libiconv.dylib in Frameworks */ = {isa = PBXBuildFile; fileRef = B3B108831B44DF2800546D96 /* libiconv.dylib */; };
		B3C6ADC71C92ADDD00FE95F0 /* AssetsLibrary.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = B3C6ADC61C92ADDD00FE95F0 /* AssetsLibrary.framework */; };
		B3C6ADC91C92ADEA00FE95F0 /* CoreMedia.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = B3C6ADC81C92ADEA00FE95F0 /* CoreMedia.framework */; };
		B3E1B3121B8DB81400CDD258 /* Resources in Resources */ = {isa = PBXBuildFile; fileRef = B3E1B2EF1B8D9FEF00CDD258 /* Resources */; };
		B3FACBB71AFDDF0E0088CDF1 /* main.m in Sources */ = {isa = PBXBuildFile; fileRef = B3FACBB61AFDDF0E0088CDF1 /* main.m */; };
		B3FACBBA1AFDDF0E0088CDF1 /* AppDelegate.m in Sources */ = {isa = PBXBuildFile; fileRef = B3FACBB91AFDDF0E0088CDF1 /* AppDelegate.m */; };
		B3FACBBD1AFDDF0E0088CDF1 /* ViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = B3FACBBC1AFDDF0E0088CDF1 /* ViewController.m */; };
		B3FACBC21AFDDF0E0088CDF1 /* Images.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = B3FACBC11AFDDF0E0088CDF1 /* Images.xcassets */; };
		B3FACBDD1AFDE2D10088CDF1 /* UIKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = B3FACBDC1AFDE2D10088CDF1 /* UIKit.framework */; };
		B3FACBDF1AFDE2E50088CDF1 /* QuartzCore.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = B3FACBDE1AFDE2E50088CDF1 /* QuartzCore.framework */; };
		B3FACBE11AFDE2F40088CDF1 /* Security.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = B3FACBE01AFDE2F40088CDF1 /* Security.framework */; };
		B3FACBE51AFDE30B0088CDF1 /* CFNetwork.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = B3FACBE41AFDE30B0088CDF1 /* CFNetwork.framework */; };
		B3FACBE71AFDE31A0088CDF1 /* SystemConfiguration.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = B3FACBE61AFDE31A0088CDF1 /* SystemConfiguration.framework */; };
		B3FACBE91AFDE33A0088CDF1 /* Foundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = B3FACBE81AFDE33A0088CDF1 /* Foundation.framework */; };
		B3FACBEB1AFDE3460088CDF1 /* CoreGraphics.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = B3FACBEA1AFDE3460088CDF1 /* CoreGraphics.framework */; };
		B3FACBEF1AFDE3750088CDF1 /* libsqlite3.dylib in Frameworks */ = {isa = PBXBuildFile; fileRef = B3FACBEE1AFDE3750088CDF1 /* libsqlite3.dylib */; };
		B3FACBF11AFDE3800088CDF1 /* libz.1.2.5.dylib in Frameworks */ = {isa = PBXBuildFile; fileRef = B3FACBF01AFDE3800088CDF1 /* libz.1.2.5.dylib */; };
		B3FACBF31AFDE3940088CDF1 /* libc++.dylib in Frameworks */ = {isa = PBXBuildFile; fileRef = B3FACBF21AFDE3940088CDF1 /* libc++.dylib */; };
		B3FCF6131BAFB2F3009AB7C0 /* LaunchScreen.xib in Resources */ = {isa = PBXBuildFile; fileRef = B3FACBC31AFDDF0E0088CDF1 /* LaunchScreen.xib */; };
		B3FEEB361B845D6D00468924 /* AVFoundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = B3FEEB351B845D6D00468924 /* AVFoundation.framework */; };
		B3FF6E3A1EC5B2A0009FAF95 /* tk_open in Resources */ = {isa = PBXBuildFile; fileRef = B3FF6E391EC5B2A0009FAF95 /* tk_open */; };
		************************ /* TKChatErrorConverter.m in Sources */ = {isa = PBXBuildFile; fileRef = ************************ /* TKChatErrorConverter.m */; };
		BE6CD0431B5000B7003DCF98 /* TKOpenController.m in Sources */ = {isa = PBXBuildFile; fileRef = BE6CCFB71B5000B6003DCF98 /* TKOpenController.m */; };
		BE6CD0441B5000B7003DCF98 /* TKOpenAccountService.m in Sources */ = {isa = PBXBuildFile; fileRef = BE6CCFBB1B5000B6003DCF98 /* TKOpenAccountService.m */; };
		BE6CD0471B5000B7003DCF98 /* YLProgressBar.m in Sources */ = {isa = PBXBuildFile; fileRef = BE6CCFC51B5000B6003DCF98 /* YLProgressBar.m */; };
		DC0AC8C529642E550068F203 /* TKOpenPlugin60087.m in Sources */ = {isa = PBXBuildFile; fileRef = DC0AC8BD29642E550068F203 /* TKOpenPlugin60087.m */; };
		DC0AC8C629642E550068F203 /* TKWatchVideoViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = DC0AC8C029642E550068F203 /* TKWatchVideoViewController.m */; };
		************************ /* TKBaseVideoRecordEndLandscapeView.m in Sources */ = {isa = PBXBuildFile; fileRef = ************************ /* TKBaseVideoRecordEndLandscapeView.m */; };
		************************ /* TKVideoRecordManager.mm in Sources */ = {isa = PBXBuildFile; fileRef = ************************ /* TKVideoRecordManager.mm */; };
		************************ /* TKSmartTwoVideoManager.m in Sources */ = {isa = PBXBuildFile; fileRef = ************************ /* TKSmartTwoVideoManager.m */; };
		DC4455182A380E720073C229 /* TKAnyChatSmartTwoVideoManager.m in Sources */ = {isa = PBXBuildFile; fileRef = DC44550F2A380E6F0073C229 /* TKAnyChatSmartTwoVideoManager.m */; };
		DC44551C2A380E720073C229 /* TKSampleBufferConverter.m in Sources */ = {isa = PBXBuildFile; fileRef = DC4455142A380E720073C229 /* TKSampleBufferConverter.m */; };
		DC44551F2A380F7B0073C229 /* TKSmartTwoVideoController.m in Sources */ = {isa = PBXBuildFile; fileRef = DC44551E2A380F7B0073C229 /* TKSmartTwoVideoController.m */; };
		DC4455302A3858530073C229 /* www in Resources */ = {isa = PBXBuildFile; fileRef = ************************ /* www */; };
		DC48AC652A56A0A2005B7D5C /* TKZFLandscapeRotationManager.m in Sources */ = {isa = PBXBuildFile; fileRef = DC48AC0B2A56A0A2005B7D5C /* TKZFLandscapeRotationManager.m */; };
		DC48AC662A56A0A2005B7D5C /* TKZFPresentTransition.m in Sources */ = {isa = PBXBuildFile; fileRef = DC48AC0C2A56A0A2005B7D5C /* TKZFPresentTransition.m */; };
		DC48AC672A56A0A2005B7D5C /* TKZFLandscapeRotationManager_iOS16.m in Sources */ = {isa = PBXBuildFile; fileRef = DC48AC0E2A56A0A2005B7D5C /* TKZFLandscapeRotationManager_iOS16.m */; };
		DC48AC682A56A0A2005B7D5C /* TKZFLandscapeViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = DC48AC0F2A56A0A2005B7D5C /* TKZFLandscapeViewController.m */; };
		DC48AC692A56A0A2005B7D5C /* TKZFLandscapeWindow.m in Sources */ = {isa = PBXBuildFile; fileRef = DC48AC122A56A0A2005B7D5C /* TKZFLandscapeWindow.m */; };
		************************ /* TKZFLandscapeRotationManager_iOS15.m in Sources */ = {isa = PBXBuildFile; fileRef = DC48AC142A56A0A2005B7D5C /* TKZFLandscapeRotationManager_iOS15.m */; };
		DC48AC6B2A56A0A2005B7D5C /* TKZFLandscapeViewController_iOS15.m in Sources */ = {isa = PBXBuildFile; fileRef = DC48AC182A56A0A2005B7D5C /* TKZFLandscapeViewController_iOS15.m */; };
		DC48AC6C2A56A0A2005B7D5C /* TKZFPortraitViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = DC48AC1B2A56A0A2005B7D5C /* TKZFPortraitViewController.m */; };
		************************ /* TKZFPersentInteractiveTransition.m in Sources */ = {isa = PBXBuildFile; fileRef = DC48AC1D2A56A0A2005B7D5C /* TKZFPersentInteractiveTransition.m */; };
		DC48AC6E2A56A0A2005B7D5C /* TKZFOrientationObserver.m in Sources */ = {isa = PBXBuildFile; fileRef = DC48AC1E2A56A0A2005B7D5C /* TKZFOrientationObserver.m */; };
		************************ /* UIImageView+TKZFCache.m in Sources */ = {isa = PBXBuildFile; fileRef = DC48AC222A56A0A2005B7D5C /* UIImageView+TKZFCache.m */; };
		DC48AC712A56A0A2005B7D5C /* UIScrollView+TKZFPlayer.m in Sources */ = {isa = PBXBuildFile; fileRef = DC48AC242A56A0A2005B7D5C /* UIScrollView+TKZFPlayer.m */; };
		DC48AC722A56A0A2005B7D5C /* TKZFPlayerGestureControl.m in Sources */ = {isa = PBXBuildFile; fileRef = DC48AC262A56A0A2005B7D5C /* TKZFPlayerGestureControl.m */; };
		DC48AC732A56A0A2005B7D5C /* TKZFPlayerNotification.m in Sources */ = {isa = PBXBuildFile; fileRef = DC48AC2B2A56A0A2005B7D5C /* TKZFPlayerNotification.m */; };
		DC48AC742A56A0A2005B7D5C /* TKZFReachabilityManager.m in Sources */ = {isa = PBXBuildFile; fileRef = DC48AC2D2A56A0A2005B7D5C /* TKZFReachabilityManager.m */; };
		DC48AC752A56A0A2005B7D5C /* TKZFKVOController.m in Sources */ = {isa = PBXBuildFile; fileRef = DC48AC2F2A56A0A2005B7D5C /* TKZFKVOController.m */; };
		DC48AC762A56A0A2005B7D5C /* TKZFPlayerLogManager.m in Sources */ = {isa = PBXBuildFile; fileRef = DC48AC302A56A0A2005B7D5C /* TKZFPlayerLogManager.m */; };
		DC48AC772A56A0A2005B7D5C /* TKZFNetworkSpeedMonitor.m in Sources */ = {isa = PBXBuildFile; fileRef = DC48AC322A56A0A2005B7D5C /* TKZFNetworkSpeedMonitor.m */; };
		DC48AC782A56A0A2005B7D5C /* TKZFUtilities.m in Sources */ = {isa = PBXBuildFile; fileRef = DC48AC352A56A0A2005B7D5C /* TKZFUtilities.m */; };
		DC48AC7A2A56A0A2005B7D5C /* TKZFSpeedLoadingView.m in Sources */ = {isa = PBXBuildFile; fileRef = DC48AC3A2A56A0A2005B7D5C /* TKZFSpeedLoadingView.m */; };
		DC48AC7B2A56A0A2005B7D5C /* TKZFLoadingView.m in Sources */ = {isa = PBXBuildFile; fileRef = DC48AC3D2A56A0A2005B7D5C /* TKZFLoadingView.m */; };
		DC48AC7C2A56A0A2005B7D5C /* TKSpeedSelectView.m in Sources */ = {isa = PBXBuildFile; fileRef = DC48AC3F2A56A0A2005B7D5C /* TKSpeedSelectView.m */; };
		DC48AC7D2A56A0A2005B7D5C /* TKZFVolumeBrightnessView.m in Sources */ = {isa = PBXBuildFile; fileRef = DC48AC422A56A0A2005B7D5C /* TKZFVolumeBrightnessView.m */; };
		DC48AC7E2A56A0A2005B7D5C /* TKZFPlayerStatusBar.m in Sources */ = {isa = PBXBuildFile; fileRef = DC48AC462A56A0A2005B7D5C /* TKZFPlayerStatusBar.m */; };
		DC48AC7F2A56A0A2005B7D5C /* TKZFPlayerView.m in Sources */ = {isa = PBXBuildFile; fileRef = DC48AC472A56A0A2005B7D5C /* TKZFPlayerView.m */; };
		DC48AC802A56A0A2005B7D5C /* TKVideoFragmentModel.m in Sources */ = {isa = PBXBuildFile; fileRef = DC48AC4B2A56A0A2005B7D5C /* TKVideoFragmentModel.m */; };
		************************ /* TKFragmentVideoView.m in Sources */ = {isa = PBXBuildFile; fileRef = DC48AC4C2A56A0A2005B7D5C /* TKFragmentVideoView.m */; };
		DC48AC822A56A0A2005B7D5C /* TKFragmentTableViewCell.m in Sources */ = {isa = PBXBuildFile; fileRef = DC48AC4D2A56A0A2005B7D5C /* TKFragmentTableViewCell.m */; };
		************************ /* TKZFLandScapeControlView.m in Sources */ = {isa = PBXBuildFile; fileRef = DC48AC512A56A0A2005B7D5C /* TKZFLandScapeControlView.m */; };
		DC48AC842A56A0A2005B7D5C /* TKZFPortraitControlView.m in Sources */ = {isa = PBXBuildFile; fileRef = DC48AC522A56A0A2005B7D5C /* TKZFPortraitControlView.m */; };
		DC48AC852A56A0A2005B7D5C /* TKPlayerControlView.m in Sources */ = {isa = PBXBuildFile; fileRef = DC48AC542A56A0A2005B7D5C /* TKPlayerControlView.m */; };
		************************ /* TKZFSmallFloatControlView.m in Sources */ = {isa = PBXBuildFile; fileRef = DC48AC572A56A0A2005B7D5C /* TKZFSmallFloatControlView.m */; };
		DC48AC872A56A0A2005B7D5C /* TKZFFloatView.m in Sources */ = {isa = PBXBuildFile; fileRef = DC48AC582A56A0A2005B7D5C /* TKZFFloatView.m */; };
		DC48AC882A56A0A2005B7D5C /* TKZFSliderView.m in Sources */ = {isa = PBXBuildFile; fileRef = DC48AC5D2A56A0A2005B7D5C /* TKZFSliderView.m */; };
		DC48AC892A56A0A2005B7D5C /* TKZFPlayerController.m in Sources */ = {isa = PBXBuildFile; fileRef = DC48AC602A56A0A2005B7D5C /* TKZFPlayerController.m */; };
		DC48AC8A2A56A0A2005B7D5C /* TKPlayer.m in Sources */ = {isa = PBXBuildFile; fileRef = DC48AC642A56A0A2005B7D5C /* TKPlayer.m */; };
		DC5366482A94B9240064288D /* TKChatVideoRecordManager.m in Sources */ = {isa = PBXBuildFile; fileRef = DC44550D2A380E6F0073C229 /* TKChatVideoRecordManager.m */; };
		************************ /* TKOpenPlugin60091.m in Sources */ = {isa = PBXBuildFile; fileRef = DC6758AC294AEFA6009771ED /* TKOpenPlugin60091.m */; };
		************************ /* TKDoubleOrdinaryVideoViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = DC6758B0294AF065009771ED /* TKDoubleOrdinaryVideoViewController.m */; };
		DC6758B5294AF380009771ED /* TKDoubleOrdinaryVideoEndView.m in Sources */ = {isa = PBXBuildFile; fileRef = DC6758B4294AF380009771ED /* TKDoubleOrdinaryVideoEndView.m */; };
		************************ /* TKDoubleOrdinaryVideoView.m in Sources */ = {isa = PBXBuildFile; fileRef = DC6758B7294AF8F8009771ED /* TKDoubleOrdinaryVideoView.m */; };
		DC6CDB872650F20D004E7295 /* TKOpenPlugin60059.m in Sources */ = {isa = PBXBuildFile; fileRef = DC6CDB862650F20D004E7295 /* TKOpenPlugin60059.m */; };
		DC6CDB902650F289004E7295 /* TKChatLiveDetectViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = DC6CDB8F2650F289004E7295 /* TKChatLiveDetectViewController.m */; };
		DC6CDB972650FF68004E7295 /* TKChatLiveFaceView.m in Sources */ = {isa = PBXBuildFile; fileRef = DC6CDB962650FF68004E7295 /* TKChatLiveFaceView.m */; };
		DC6F5D03262EB34400B87A5B /* TKChatVideoRecordEndView.m in Sources */ = {isa = PBXBuildFile; fileRef = DC6F5D02262EB34400B87A5B /* TKChatVideoRecordEndView.m */; };
		DC6F5D09262EB49800B87A5B /* TKChatVideoRecordView.m in Sources */ = {isa = PBXBuildFile; fileRef = DC6F5D08262EB49800B87A5B /* TKChatVideoRecordView.m */; };
		DC84D15B2857290800941BF5 /* TKSmartQuestionModel.m in Sources */ = {isa = PBXBuildFile; fileRef = DC84D1552857290700941BF5 /* TKSmartQuestionModel.m */; };
		DC84D15E2857291400941BF5 /* TKBaseVideoRecordViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = ************************ /* TKBaseVideoRecordViewController.m */; };
		DCB07F062970060B009D0140 /* TKWatchVideoVideoView.m in Sources */ = {isa = PBXBuildFile; fileRef = DC0AC8C229642E550068F203 /* TKWatchVideoVideoView.m */; };
		DCBFCEFD2A122B5600418EFE /* (null) in Resources */ = {isa = PBXBuildFile; };
		DCC0429F29434D1900BDF14D /* TKOpenPlugin60077.m in Sources */ = {isa = PBXBuildFile; fileRef = DCC0429529434D1900BDF14D /* TKOpenPlugin60077.m */; };
		DCC042A029434D1900BDF14D /* TKDoubleChatVideoRecordViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = DCC0429829434D1900BDF14D /* TKDoubleChatVideoRecordViewController.m */; };
		DCC042A129434D1900BDF14D /* TKDoubleChatVideoRecordView.m in Sources */ = {isa = PBXBuildFile; fileRef = DCC0429A29434D1900BDF14D /* TKDoubleChatVideoRecordView.m */; };
		DCC042A229434D1900BDF14D /* TKDoubleChatVideoRecordEndView.m in Sources */ = {isa = PBXBuildFile; fileRef = DCC0429C29434D1900BDF14D /* TKDoubleChatVideoRecordEndView.m */; };
		DCC042AC2947430200BDF14D /* TKOpenPlugin60089.m in Sources */ = {isa = PBXBuildFile; fileRef = DCC042A82947430200BDF14D /* TKOpenPlugin60089.m */; };
		DCC042AF294744A900BDF14D /* TKDoudleOneWayVideoViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = DCC042AE294744A900BDF14D /* TKDoudleOneWayVideoViewController.m */; };
		DCC042B22948161200BDF14D /* TKDoubleOneWayVideoView.m in Sources */ = {isa = PBXBuildFile; fileRef = DCC042B12948161200BDF14D /* TKDoubleOneWayVideoView.m */; };
		DCC042B52948166800BDF14D /* TKDoubleOneWayVideoEndView.m in Sources */ = {isa = PBXBuildFile; fileRef = DCC042B42948166800BDF14D /* TKDoubleOneWayVideoEndView.m */; };
		DCC262C2291E1C8400018C4E /* TKBaseVideoRecordLandscapeView.m in Sources */ = {isa = PBXBuildFile; fileRef = DCC262C1291E1C8400018C4E /* TKBaseVideoRecordLandscapeView.m */; };
		DCC5CE522B207FF7001C82A6 /* TKReadingView.m in Sources */ = {isa = PBXBuildFile; fileRef = DCC5CE502B207FF7001C82A6 /* TKReadingView.m */; };
		DCC7E7E326299A56006D378F /* TKOpenPlugin60057.m in Sources */ = {isa = PBXBuildFile; fileRef = DCC7E7E226299A56006D378F /* TKOpenPlugin60057.m */; };
		DCC7E86B262D7134006D378F /* TKTempService.m in Sources */ = {isa = PBXBuildFile; fileRef = DCC7E868262D7133006D378F /* TKTempService.m */; };
		DCC88A6D2A77BC5A00625DAF /* UIView+TKZFFrame.m in Sources */ = {isa = PBXBuildFile; fileRef = DC48AC212A56A0A2005B7D5C /* UIView+TKZFFrame.m */; };
		DCCDDA1B295C2AF5002F268B /* TKFaceDectTipView.m in Sources */ = {isa = PBXBuildFile; fileRef = DCCDDA1A295C2AF5002F268B /* TKFaceDectTipView.m */; };
		DCD47D3528E18E1600AE44C6 /* TKBaseVideoRecordEndView.m in Sources */ = {isa = PBXBuildFile; fileRef = DCD47D3128E18E1600AE44C6 /* TKBaseVideoRecordEndView.m */; };
		DCD47D3628E18E1600AE44C6 /* TKBaseVideoRecordView.m in Sources */ = {isa = PBXBuildFile; fileRef = DCD47D3228E18E1600AE44C6 /* TKBaseVideoRecordView.m */; };
		DCD47D4E28E1B18D00AE44C6 /* TKSmartVirtualManViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = DCD47D4628E1B18D00AE44C6 /* TKSmartVirtualManViewController.m */; };
		DCD47D4F28E1B18D00AE44C6 /* TKOpenPlugin60072.m in Sources */ = {isa = PBXBuildFile; fileRef = DCD47D4728E1B18D00AE44C6 /* TKOpenPlugin60072.m */; };
		DCD47D5028E1B18D00AE44C6 /* TKSmartVirtualManVideoEndView.m in Sources */ = {isa = PBXBuildFile; fileRef = DCD47D4B28E1B18D00AE44C6 /* TKSmartVirtualManVideoEndView.m */; };
		DCD47D5128E1B18D00AE44C6 /* TKSmartVirtualManVideoView.m in Sources */ = {isa = PBXBuildFile; fileRef = DCD47D4C28E1B18D00AE44C6 /* TKSmartVirtualManVideoView.m */; };
		DCE005092A95AD9E007D1C17 /* TKChatVideoRecordViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = DCC7E7E9262A9B92006D378F /* TKChatVideoRecordViewController.m */; };
		DCE005252A961A29007D1C17 /* TChat.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 33294B102A0A469800A72052 /* TChat.framework */; };
		DCE005262A961A29007D1C17 /* TChat.framework in Embed Frameworks */ = {isa = PBXBuildFile; fileRef = 33294B102A0A469800A72052 /* TChat.framework */; settings = {ATTRIBUTES = (CodeSignOnCopy, RemoveHeadersOnCopy, ); }; };
		DCEAC82829B1E5FB00581544 /* TKStatisticEventHelper.m in Sources */ = {isa = PBXBuildFile; fileRef = DCEAC82729B1E5FB00581544 /* TKStatisticEventHelper.m */; };
		DCECF967292F5EF000A802EE /* TKSVGEngine.mm in Sources */ = {isa = PBXBuildFile; fileRef = DCECF95B292F5EEF00A802EE /* TKSVGEngine.mm */; };
		DCECF968292F5EF000A802EE /* TKSVGLayer.m in Sources */ = {isa = PBXBuildFile; fileRef = DCECF95C292F5EF000A802EE /* TKSVGLayer.m */; };
		DCECF969292F5EF000A802EE /* TKSVGBezierPath.mm in Sources */ = {isa = PBXBuildFile; fileRef = DCECF95E292F5EF000A802EE /* TKSVGBezierPath.mm */; };
		DCECF96B292F5EF000A802EE /* TKSVGImageView.m in Sources */ = {isa = PBXBuildFile; fileRef = DCECF964292F5EF000A802EE /* TKSVGImageView.m */; };
		DCECF96E292F628A00A802EE /* TKSVGImage.m in Sources */ = {isa = PBXBuildFile; fileRef = DCECF96D292F628A00A802EE /* TKSVGImage.m */; };
		E22996F526AABD490039F6E9 /* SelectViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = E22996F426AABD490039F6E9 /* SelectViewController.m */; };
		E22996F626AABFB90039F6E9 /* Main.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = B3FACBBE1AFDDF0E0088CDF1 /* Main.storyboard */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		B3AA8EC21EBB2B0400255737 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = B3FACBA91AFDDF0E0088CDF1 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = B3D794591B8EA14F00768134;
			remoteInfo = build_TKOpenResource;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXCopyFilesBuildPhase section */
		04EE357922732298009EA9D1 /* Embed Frameworks */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = **********;
			dstPath = "";
			dstSubfolderSpec = 10;
			files = (
				188DB0C126245E9700F6732D /* FinApplet.framework in Embed Frameworks */,
				33C502B42A0A4D890050DB0C /* STLivenessDetector.xcframework in Embed Frameworks */,
				33C502B02A0A4D5F0050DB0C /* nuisdk.framework in Embed Frameworks */,
				DCE005262A961A29007D1C17 /* TChat.framework in Embed Frameworks */,
			);
			name = "Embed Frameworks";
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXCopyFilesBuildPhase section */

/* Begin PBXFileReference section */
		040B88412148BC19008DBE05 /* VideoToolbox.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = VideoToolbox.framework; path = System/Library/Frameworks/VideoToolbox.framework; sourceTree = SDKROOT; };
		042CE7032408DD7900B9AC15 /* TKOrdinaryOneVideoEndView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKOrdinaryOneVideoEndView.h; sourceTree = "<group>"; };
		042CE7042408DD7B00B9AC15 /* TKOrdinaryOneVideoEndView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKOrdinaryOneVideoEndView.m; sourceTree = "<group>"; };
		043015EA23FCC811004F0C17 /* TKOpenPlugin60028.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = TKOpenPlugin60028.h; sourceTree = "<group>"; };
		043015EB23FCC811004F0C17 /* TKOpenPlugin60028.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = TKOpenPlugin60028.m; sourceTree = "<group>"; };
		043015ED23FCC936004F0C17 /* TKFaceImageViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = TKFaceImageViewController.h; sourceTree = "<group>"; };
		043015EE23FCC936004F0C17 /* TKFaceImageViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = TKFaceImageViewController.m; sourceTree = "<group>"; };
		043015F123FCCDBE004F0C17 /* TKFaceImageView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKFaceImageView.m; sourceTree = "<group>"; };
		043015F223FCCDBE004F0C17 /* TKFaceImageView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKFaceImageView.h; sourceTree = "<group>"; };
		043884B823B05A240009F14D /* KeyBoard.xml */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.xml; path = KeyBoard.xml; sourceTree = "<group>"; };
		0441008A23C43BD1005B9D05 /* TKFxcAccountInfoType.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = TKFxcAccountInfoType.h; sourceTree = "<group>"; };
		0441008B23C43BD1005B9D05 /* TKFxcAccountInfoType.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = TKFxcAccountInfoType.m; sourceTree = "<group>"; };
		045ACEA522A4FC30004D8557 /* TKWebViewApp.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; path = TKWebViewApp.framework; sourceTree = "<group>"; };
		045EF860241BA81000032B22 /* TKOpenPlugin60032.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = TKOpenPlugin60032.h; sourceTree = "<group>"; };
		045EF861241BA81000032B22 /* TKOpenPlugin60032.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = TKOpenPlugin60032.m; sourceTree = "<group>"; };
		045EF864241BAF0100032B22 /* TKOpenPlugin60033.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = TKOpenPlugin60033.h; sourceTree = "<group>"; };
		045EF865241BAF0100032B22 /* TKOpenPlugin60033.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = TKOpenPlugin60033.m; sourceTree = "<group>"; };
		0465234D212E84C000F8C0D0 /* TKSignatureController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKSignatureController.h; sourceTree = "<group>"; };
		0465234E212E84C000F8C0D0 /* TKSignatureController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKSignatureController.m; sourceTree = "<group>"; };
		0465234F212E84C000F8C0D0 /* TKOpenPlugin60022.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKOpenPlugin60022.h; sourceTree = "<group>"; };
		04652350212E84C000F8C0D0 /* TKOpenPlugin60022.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKOpenPlugin60022.m; sourceTree = "<group>"; };
		04652352212E84C000F8C0D0 /* TKTouchView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKTouchView.h; sourceTree = "<group>"; };
		04652353212E84C000F8C0D0 /* TKTouchView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKTouchView.m; sourceTree = "<group>"; };
		0472AC9723580798008FC27E /* TKOpenQueueView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKOpenQueueView.h; sourceTree = "<group>"; };
		0472AC9823580798008FC27E /* TKOpenQueueView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKOpenQueueView.m; sourceTree = "<group>"; };
		0472AC9923580798008FC27E /* TKOpenVideoChatView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKOpenVideoChatView.h; sourceTree = "<group>"; };
		0472AC9A23580798008FC27E /* TKOpenVideoChatView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKOpenVideoChatView.m; sourceTree = "<group>"; };
		0472CA8023FB9DD4000B5EB7 /* TKOpenPlugin60007.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKOpenPlugin60007.h; sourceTree = "<group>"; };
		0472CA8123FB9DD4000B5EB7 /* TKOpenPlugin60007.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKOpenPlugin60007.m; sourceTree = "<group>"; };
		0472CA8923FB9EC4000B5EB7 /* TKLiveFaceViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = TKLiveFaceViewController.h; sourceTree = "<group>"; };
		0472CA8A23FB9EC4000B5EB7 /* TKLiveFaceViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = TKLiveFaceViewController.m; sourceTree = "<group>"; };
		0472CA8C23FBA05F000B5EB7 /* TKLiveFaceView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKLiveFaceView.m; sourceTree = "<group>"; };
		0472CA8D23FBA05F000B5EB7 /* TKLiveFaceView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKLiveFaceView.h; sourceTree = "<group>"; };
		047AF9672137BF5D003B1366 /* version.txt */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text; path = version.txt; sourceTree = "<group>"; };
		048DC9902407E66400A6F3EC /* TKOpenPlugin60030.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKOpenPlugin60030.h; sourceTree = "<group>"; };
		048DC9912407E66400A6F3EC /* TKOpenPlugin60030.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKOpenPlugin60030.m; sourceTree = "<group>"; };
		048DC9932407E66400A6F3EC /* TKOrdinaryOneVideoView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKOrdinaryOneVideoView.h; sourceTree = "<group>"; };
		048DC9942407E66400A6F3EC /* TKOrdinaryOneVideoView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKOrdinaryOneVideoView.m; sourceTree = "<group>"; };
		048DC9972407E6EF00A6F3EC /* TKOrdinaryOneVideoViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = TKOrdinaryOneVideoViewController.h; sourceTree = "<group>"; };
		048DC9982407E6EF00A6F3EC /* TKOrdinaryOneVideoViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = TKOrdinaryOneVideoViewController.m; sourceTree = "<group>"; };
		049A82E6211ECD6A00AA2048 /* TKAsset.bundle */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.plug-in"; path = TKAsset.bundle; sourceTree = "<group>"; };
		049A8301211ECD6B00AA2048 /* aes.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = aes.h; sourceTree = "<group>"; };
		049A8302211ECD6B00AA2048 /* asn1.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = asn1.h; sourceTree = "<group>"; };
		049A8303211ECD6B00AA2048 /* asn1_mac.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = asn1_mac.h; sourceTree = "<group>"; };
		049A8304211ECD6B00AA2048 /* asn1t.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = asn1t.h; sourceTree = "<group>"; };
		049A8305211ECD6B00AA2048 /* bio.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = bio.h; sourceTree = "<group>"; };
		049A8306211ECD6B00AA2048 /* blowfish.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = blowfish.h; sourceTree = "<group>"; };
		049A8307211ECD6B00AA2048 /* bn.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = bn.h; sourceTree = "<group>"; };
		049A8308211ECD6B00AA2048 /* buffer.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = buffer.h; sourceTree = "<group>"; };
		049A8309211ECD6B00AA2048 /* camellia.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = camellia.h; sourceTree = "<group>"; };
		049A830A211ECD6B00AA2048 /* cast.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = cast.h; sourceTree = "<group>"; };
		049A830B211ECD6B00AA2048 /* cmac.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = cmac.h; sourceTree = "<group>"; };
		049A830C211ECD6B00AA2048 /* cms.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = cms.h; sourceTree = "<group>"; };
		049A830D211ECD6B00AA2048 /* comp.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = comp.h; sourceTree = "<group>"; };
		049A830E211ECD6B00AA2048 /* conf.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = conf.h; sourceTree = "<group>"; };
		049A830F211ECD6B00AA2048 /* conf_api.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = conf_api.h; sourceTree = "<group>"; };
		049A8310211ECD6B00AA2048 /* crypto.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = crypto.h; sourceTree = "<group>"; };
		049A8311211ECD6B00AA2048 /* des.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = des.h; sourceTree = "<group>"; };
		049A8312211ECD6B00AA2048 /* des_old.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = des_old.h; sourceTree = "<group>"; };
		049A8313211ECD6B00AA2048 /* dh.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = dh.h; sourceTree = "<group>"; };
		049A8314211ECD6B00AA2048 /* dsa.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = dsa.h; sourceTree = "<group>"; };
		049A8315211ECD6B00AA2048 /* dso.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = dso.h; sourceTree = "<group>"; };
		049A8316211ECD6B00AA2048 /* dtls1.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = dtls1.h; sourceTree = "<group>"; };
		049A8317211ECD6B00AA2048 /* e_os2.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = e_os2.h; sourceTree = "<group>"; };
		049A8318211ECD6B00AA2048 /* ebcdic.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ebcdic.h; sourceTree = "<group>"; };
		049A8319211ECD6B00AA2048 /* ec.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ec.h; sourceTree = "<group>"; };
		049A831A211ECD6B00AA2048 /* ecdh.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ecdh.h; sourceTree = "<group>"; };
		049A831B211ECD6B00AA2048 /* ecdsa.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ecdsa.h; sourceTree = "<group>"; };
		049A831C211ECD6B00AA2048 /* engine.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = engine.h; sourceTree = "<group>"; };
		049A831D211ECD6B00AA2048 /* err.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = err.h; sourceTree = "<group>"; };
		049A831E211ECD6B00AA2048 /* evp.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = evp.h; sourceTree = "<group>"; };
		049A831F211ECD6B00AA2048 /* hmac.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = hmac.h; sourceTree = "<group>"; };
		049A8320211ECD6B00AA2048 /* idea.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = idea.h; sourceTree = "<group>"; };
		049A8321211ECD6B00AA2048 /* krb5_asn.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = krb5_asn.h; sourceTree = "<group>"; };
		049A8322211ECD6B00AA2048 /* kssl.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = kssl.h; sourceTree = "<group>"; };
		049A8323211ECD6B00AA2048 /* lhash.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = lhash.h; sourceTree = "<group>"; };
		049A8324211ECD6B00AA2048 /* md4.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = md4.h; sourceTree = "<group>"; };
		049A8325211ECD6B00AA2048 /* md5.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = md5.h; sourceTree = "<group>"; };
		049A8326211ECD6B00AA2048 /* mdc2.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = mdc2.h; sourceTree = "<group>"; };
		049A8327211ECD6B00AA2048 /* modes.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = modes.h; sourceTree = "<group>"; };
		049A8328211ECD6B00AA2048 /* obj_mac.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = obj_mac.h; sourceTree = "<group>"; };
		049A8329211ECD6B00AA2048 /* objects.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = objects.h; sourceTree = "<group>"; };
		049A832A211ECD6B00AA2048 /* ocsp.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ocsp.h; sourceTree = "<group>"; };
		049A832B211ECD6B00AA2048 /* opensslconf.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = opensslconf.h; sourceTree = "<group>"; };
		049A832C211ECD6B00AA2048 /* opensslv.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = opensslv.h; sourceTree = "<group>"; };
		049A832D211ECD6B00AA2048 /* ossl_typ.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ossl_typ.h; sourceTree = "<group>"; };
		049A832E211ECD6B00AA2048 /* pem.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = pem.h; sourceTree = "<group>"; };
		049A832F211ECD6B00AA2048 /* pem2.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = pem2.h; sourceTree = "<group>"; };
		049A8330211ECD6B00AA2048 /* pkcs12.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = pkcs12.h; sourceTree = "<group>"; };
		049A8331211ECD6B00AA2048 /* pkcs7.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = pkcs7.h; sourceTree = "<group>"; };
		049A8332211ECD6B00AA2048 /* pqueue.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = pqueue.h; sourceTree = "<group>"; };
		049A8333211ECD6B00AA2048 /* rand.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = rand.h; sourceTree = "<group>"; };
		049A8334211ECD6B00AA2048 /* rc2.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = rc2.h; sourceTree = "<group>"; };
		049A8335211ECD6B00AA2048 /* rc4.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = rc4.h; sourceTree = "<group>"; };
		049A8336211ECD6B00AA2048 /* ripemd.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ripemd.h; sourceTree = "<group>"; };
		049A8337211ECD6B00AA2048 /* rsa.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = rsa.h; sourceTree = "<group>"; };
		049A8338211ECD6B00AA2048 /* safestack.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = safestack.h; sourceTree = "<group>"; };
		049A8339211ECD6B00AA2048 /* seed.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = seed.h; sourceTree = "<group>"; };
		049A833A211ECD6B00AA2048 /* sha.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = sha.h; sourceTree = "<group>"; };
		049A833B211ECD6B00AA2048 /* srp.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = srp.h; sourceTree = "<group>"; };
		049A833C211ECD6B00AA2048 /* srtp.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = srtp.h; sourceTree = "<group>"; };
		049A833D211ECD6B00AA2048 /* ssl.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ssl.h; sourceTree = "<group>"; };
		049A833E211ECD6B00AA2048 /* ssl2.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ssl2.h; sourceTree = "<group>"; };
		049A833F211ECD6B00AA2048 /* ssl23.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ssl23.h; sourceTree = "<group>"; };
		049A8340211ECD6B00AA2048 /* ssl3.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ssl3.h; sourceTree = "<group>"; };
		049A8341211ECD6B00AA2048 /* stack.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = stack.h; sourceTree = "<group>"; };
		049A8342211ECD6B00AA2048 /* symhacks.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = symhacks.h; sourceTree = "<group>"; };
		049A8343211ECD6B00AA2048 /* tls1.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = tls1.h; sourceTree = "<group>"; };
		049A8344211ECD6B00AA2048 /* ts.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ts.h; sourceTree = "<group>"; };
		049A8345211ECD6B00AA2048 /* txt_db.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = txt_db.h; sourceTree = "<group>"; };
		049A8346211ECD6B00AA2048 /* ui.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ui.h; sourceTree = "<group>"; };
		049A8347211ECD6B00AA2048 /* ui_compat.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ui_compat.h; sourceTree = "<group>"; };
		049A8348211ECD6B00AA2048 /* whrlpool.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = whrlpool.h; sourceTree = "<group>"; };
		049A8349211ECD6B00AA2048 /* x509.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = x509.h; sourceTree = "<group>"; };
		049A834A211ECD6B00AA2048 /* x509_vfy.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = x509_vfy.h; sourceTree = "<group>"; };
		049A834B211ECD6B00AA2048 /* x509v3.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = x509v3.h; sourceTree = "<group>"; };
		049A834D211ECD6B00AA2048 /* libcrypto.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; path = libcrypto.a; sourceTree = "<group>"; };
		049A834E211ECD6B00AA2048 /* libssl.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; path = libssl.a; sourceTree = "<group>"; };
		04D9604822C3366E0010E4C6 /* Configuration_Open_BuriedPoint.xml */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.xml; path = Configuration_Open_BuriedPoint.xml; sourceTree = "<group>"; };
		04E5B288241CA7CD0069BCEE /* TKOpenPlugin60034.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = TKOpenPlugin60034.h; sourceTree = "<group>"; };
		04E5B289241CA7CD0069BCEE /* TKOpenPlugin60034.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = TKOpenPlugin60034.m; sourceTree = "<group>"; };
		04E5B28B241CA9E20069BCEE /* TKDirectVideoViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = TKDirectVideoViewController.h; sourceTree = "<group>"; };
		04E5B28C241CA9E20069BCEE /* TKDirectVideoViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = TKDirectVideoViewController.m; sourceTree = "<group>"; };
		04E5B28E241CABE20069BCEE /* TKDirectVideoChatView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKDirectVideoChatView.h; sourceTree = "<group>"; };
		04E5B28F241CABE20069BCEE /* TKDirectVideoChatView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKDirectVideoChatView.m; sourceTree = "<group>"; };
		04E5B29A241CB4030069BCEE /* TKDirectVideoModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = TKDirectVideoModel.h; sourceTree = "<group>"; };
		04E5B29B241CB4030069BCEE /* TKDirectVideoModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = TKDirectVideoModel.m; sourceTree = "<group>"; };
		18034322291CD426002C8E2C /* TKPlayerToolView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = TKPlayerToolView.h; sourceTree = "<group>"; };
		18034323291CD426002C8E2C /* TKPlayerToolView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = TKPlayerToolView.m; sourceTree = "<group>"; };
		1813893E244B269A007F96FA /* TKOpenPlugin60037.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKOpenPlugin60037.m; sourceTree = "<group>"; };
		18138940244B269A007F96FA /* TKTakeBankPhotoViewController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKTakeBankPhotoViewController.h; sourceTree = "<group>"; };
		18138941244B269A007F96FA /* TKTakeBankPhotoViewController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKTakeBankPhotoViewController.m; sourceTree = "<group>"; };
		18138942244B269A007F96FA /* TKOpenPlugin60037.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKOpenPlugin60037.h; sourceTree = "<group>"; };
		181D4C9F286E94F600679EB3 /* TKVideoAlertView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKVideoAlertView.h; sourceTree = "<group>"; };
		181D4CA0286E94F600679EB3 /* TKVideoAlertView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKVideoAlertView.m; sourceTree = "<group>"; };
		182CA7DC25BA6B820084C9F8 /* TKOpenPlugin60044.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKOpenPlugin60044.h; sourceTree = "<group>"; };
		182CA7DD25BA6B820084C9F8 /* TKOpenPlugin60044.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKOpenPlugin60044.m; sourceTree = "<group>"; };
		182F804525DFCDC900505CDE /* TKOpenPlugin60049.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKOpenPlugin60049.h; sourceTree = "<group>"; };
		182F804625DFCDC900505CDE /* TKOpenPlugin60049.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKOpenPlugin60049.m; sourceTree = "<group>"; };
		18339697245C27C000ECE057 /* TKOpenPlugin60039.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = TKOpenPlugin60039.h; sourceTree = "<group>"; };
		18339698245C27C000ECE057 /* TKOpenPlugin60039.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = TKOpenPlugin60039.m; sourceTree = "<group>"; };
		1833969F245C298B00ECE057 /* READMEZXJT.txt */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text; path = READMEZXJT.txt; sourceTree = "<group>"; };
		183D059C29384A8000346A76 /* TKOpenPlugin61003.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKOpenPlugin61003.h; sourceTree = "<group>"; };
		183D059E29384A8000346A76 /* TKOrganizationDoubleVideoViewController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKOrganizationDoubleVideoViewController.m; sourceTree = "<group>"; };
		183D059F29384A8000346A76 /* TKOrganizationDoubleVideoBaseViewController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKOrganizationDoubleVideoBaseViewController.m; sourceTree = "<group>"; };
		183D05A029384A8000346A76 /* TKOrganizationDoubleVideoNoVoiceViewController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKOrganizationDoubleVideoNoVoiceViewController.h; sourceTree = "<group>"; };
		183D05A129384A8000346A76 /* TKOrganizationDoubleVideoViewController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKOrganizationDoubleVideoViewController.h; sourceTree = "<group>"; };
		183D05A229384A8000346A76 /* TKOrganizationDoubleVideoBaseViewController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKOrganizationDoubleVideoBaseViewController.h; sourceTree = "<group>"; };
		183D05A329384A8000346A76 /* TKOrganizationDoubleVideoNoVoiceViewController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKOrganizationDoubleVideoNoVoiceViewController.m; sourceTree = "<group>"; };
		183D05A529384A8000346A76 /* TKOrganizationDoubleVideoAVManager.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKOrganizationDoubleVideoAVManager.m; sourceTree = "<group>"; };
		183D05A629384A8000346A76 /* TKOrganizationDoubleVideoAVManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKOrganizationDoubleVideoAVManager.h; sourceTree = "<group>"; };
		183D05A729384A8000346A76 /* TKOpenPlugin61003.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKOpenPlugin61003.m; sourceTree = "<group>"; };
		183D05AA29384A8000346A76 /* TKOrganizationDoubleVideoBottomRightView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKOrganizationDoubleVideoBottomRightView.h; sourceTree = "<group>"; };
		183D05AB29384A8000346A76 /* TKOrganizationDoubleVideoTimeView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKOrganizationDoubleVideoTimeView.h; sourceTree = "<group>"; };
		183D05AC29384A8000346A76 /* TKOrganizationDoubleVideoBottomTipView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKOrganizationDoubleVideoBottomTipView.m; sourceTree = "<group>"; };
		183D05AD29384A8000346A76 /* TKOrganizationDoubleVideoTimeView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKOrganizationDoubleVideoTimeView.m; sourceTree = "<group>"; };
		183D05AE29384A8000346A76 /* TKOrganizationDoubleVideoBottomRightView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKOrganizationDoubleVideoBottomRightView.m; sourceTree = "<group>"; };
		183D05AF29384A8000346A76 /* TKOrganizationDoubleVideoBottomTipView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKOrganizationDoubleVideoBottomTipView.h; sourceTree = "<group>"; };
		183D05B129384A8000346A76 /* TKOpenPlugin61001.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKOpenPlugin61001.m; sourceTree = "<group>"; };
		183D05B329384A8000346A76 /* TKOrganizationSinglePhotoViewController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKOrganizationSinglePhotoViewController.h; sourceTree = "<group>"; };
		183D05B429384A8000346A76 /* TKOrginizationDoublePhotoViewController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKOrginizationDoublePhotoViewController.h; sourceTree = "<group>"; };
		183D05B529384A8000346A76 /* TKOrganizationSinglePhotoViewController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKOrganizationSinglePhotoViewController.m; sourceTree = "<group>"; };
		183D05B629384A8000346A76 /* TKOrginizationDoublePhotoViewController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKOrginizationDoublePhotoViewController.m; sourceTree = "<group>"; };
		183D05B729384A8000346A76 /* TKOpenPlugin61001.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKOpenPlugin61001.h; sourceTree = "<group>"; };
		1854637F29669CB900553822 /* TKChatService.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKChatService.m; sourceTree = "<group>"; };
		1854638029669CBA00553822 /* TKChatService.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKChatService.h; sourceTree = "<group>"; };
		1854638129669CBA00553822 /* TKChatServiceDelegate.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKChatServiceDelegate.h; sourceTree = "<group>"; };
		18714D8624454DE7002D809B /* TKVideoMsgTableViewCell.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKVideoMsgTableViewCell.m; sourceTree = "<group>"; };
		18714D8724454DE7002D809B /* TKVideoMsgTableViewCell.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKVideoMsgTableViewCell.h; sourceTree = "<group>"; };
		1876A14C2949CF57007F0500 /* TKTakeIDPhotoViewController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKTakeIDPhotoViewController.h; sourceTree = "<group>"; };
		1876A14D2949CF57007F0500 /* TKTakeIDPhotoViewController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKTakeIDPhotoViewController.m; sourceTree = "<group>"; };
		187942C7255CD0AC0067C701 /* AdSupport.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = AdSupport.framework; path = System/Library/Frameworks/AdSupport.framework; sourceTree = SDKROOT; };
		187CC37028F15C5E003442D6 /* TKOpenViewStyleHelper.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKOpenViewStyleHelper.h; sourceTree = "<group>"; };
		187CC37128F15C5E003442D6 /* TKOpenViewStyleHelper.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKOpenViewStyleHelper.m; sourceTree = "<group>"; };
		187F35F926AAB44C0046E9E3 /* TKFaceImageLandscapeViewController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKFaceImageLandscapeViewController.h; sourceTree = "<group>"; };
		187F35FA26AAB44C0046E9E3 /* TKFaceImageLandscapeViewController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKFaceImageLandscapeViewController.m; sourceTree = "<group>"; };
		187F35FE26AAB4530046E9E3 /* TKFaceImageLandscapeView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKFaceImageLandscapeView.h; sourceTree = "<group>"; };
		187F35FF26AAB4530046E9E3 /* TKFaceImageLandscapeView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKFaceImageLandscapeView.m; sourceTree = "<group>"; };
		187F360426AAB4680046E9E3 /* TKOpenPlugin60062.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKOpenPlugin60062.h; sourceTree = "<group>"; };
		187F360626AAB4680046E9E3 /* TKOneWayLandscapeVideoViewController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKOneWayLandscapeVideoViewController.h; sourceTree = "<group>"; };
		187F360726AAB4680046E9E3 /* TKOneWayLandscapeVideoViewController.mm */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.objcpp; path = TKOneWayLandscapeVideoViewController.mm; sourceTree = "<group>"; };
		187F360826AAB4680046E9E3 /* TKOpenPlugin60062.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKOpenPlugin60062.m; sourceTree = "<group>"; };
		187F360A26AAB4680046E9E3 /* TKOneWayLandscapeVideoEndView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKOneWayLandscapeVideoEndView.m; sourceTree = "<group>"; };
		187F360B26AAB4680046E9E3 /* TKOneWayLandscapeVideoView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKOneWayLandscapeVideoView.m; sourceTree = "<group>"; };
		187F360C26AAB4680046E9E3 /* TKOneWayLandscapeVideoView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKOneWayLandscapeVideoView.h; sourceTree = "<group>"; };
		187F360D26AAB4680046E9E3 /* TKOneWayLandscapeVideoEndView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKOneWayLandscapeVideoEndView.h; sourceTree = "<group>"; };
		187F360F26AAB4680046E9E3 /* TKOpenPlugin60064.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKOpenPlugin60064.m; sourceTree = "<group>"; };
		187F361126AAB4680046E9E3 /* TKOpenBusinessLicenseViewController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKOpenBusinessLicenseViewController.m; sourceTree = "<group>"; };
		187F361226AAB4680046E9E3 /* TKOpenBusinessLicenseViewController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKOpenBusinessLicenseViewController.h; sourceTree = "<group>"; };
		187F361326AAB4680046E9E3 /* TKOpenPlugin60064.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKOpenPlugin60064.h; sourceTree = "<group>"; };
		187F361626AAB4680046E9E3 /* TKOpenPlugin61000.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKOpenPlugin61000.h; sourceTree = "<group>"; };
		187F361726AAB4680046E9E3 /* TKOpenPlugin61000.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKOpenPlugin61000.m; sourceTree = "<group>"; };
		188DB0B826245B4A00F6732D /* FinApplet.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; path = FinApplet.framework; sourceTree = "<group>"; };
		188DB0B926245B4A00F6732D /* TKAppletPluginManager.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKAppletPluginManager.m; sourceTree = "<group>"; };
		188DB0BA26245B4A00F6732D /* TKAppletPluginManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKAppletPluginManager.h; sourceTree = "<group>"; };
		188DB0BB26245B4A00F6732D /* README.txt */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text; path = README.txt; sourceTree = "<group>"; };
		188FC5B52468F43300F7D2A3 /* ZXJTBaseFramework.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; path = ZXJTBaseFramework.framework; sourceTree = "<group>"; };
		188FC5B62468F43300F7D2A3 /* JT_BasicControlImages.bundle */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.plug-in"; path = JT_BasicControlImages.bundle; sourceTree = "<group>"; };
		1891C47F2486148300C54EFF /* TKVideoReadAgreeView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKVideoReadAgreeView.m; sourceTree = "<group>"; };
		1891C4802486148300C54EFF /* TKVideoReadAgreeView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKVideoReadAgreeView.h; sourceTree = "<group>"; };
		18935000296547610052E77B /* DemoViewController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = DemoViewController.h; sourceTree = "<group>"; };
		18935002296547610052E77B /* TKOpenLoginViewController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKOpenLoginViewController.h; sourceTree = "<group>"; };
		18935003296547610052E77B /* TKOpenLoginViewController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKOpenLoginViewController.m; sourceTree = "<group>"; };
		18935004296547610052E77B /* DemoPageViewController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = DemoPageViewController.m; sourceTree = "<group>"; };
		18935006296547610052E77B /* DemoTableViewCell.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = DemoTableViewCell.h; sourceTree = "<group>"; };
		18935007296547610052E77B /* DemoTableViewCell.xib */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.xib; path = DemoTableViewCell.xib; sourceTree = "<group>"; };
		18935008296547610052E77B /* DemoTableViewCell.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = DemoTableViewCell.m; sourceTree = "<group>"; };
		18935009296547610052E77B /* DemoViewController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = DemoViewController.m; sourceTree = "<group>"; };
		1893500A296547610052E77B /* DemoPageViewController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = DemoPageViewController.h; sourceTree = "<group>"; };
		1893500C296547610052E77B /* TChatRtcOneWayVideoTestVC.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TChatRtcOneWayVideoTestVC.h; sourceTree = "<group>"; };
		1893500D296547610052E77B /* TestTextFieldView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TestTextFieldView.m; sourceTree = "<group>"; };
		1893500E296547610052E77B /* TKTakeIDCardTestVC.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKTakeIDCardTestVC.h; sourceTree = "<group>"; };
		1893500F296547610052E77B /* TestGuidePageVC.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TestGuidePageVC.m; sourceTree = "<group>"; };
		18935010296547610052E77B /* TChatRtcArtificialWitnessTestVC.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TChatRtcArtificialWitnessTestVC.h; sourceTree = "<group>"; };
		18935034296547610052E77B /* TChatRtcOneWayVideoTestVC.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TChatRtcOneWayVideoTestVC.m; sourceTree = "<group>"; };
		18935035296547610052E77B /* TestGuidePageVC.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TestGuidePageVC.h; sourceTree = "<group>"; };
		18935036296547610052E77B /* TKTakeIDCardTestVC.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKTakeIDCardTestVC.m; sourceTree = "<group>"; };
		18935037296547610052E77B /* TestTextFieldView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TestTextFieldView.h; sourceTree = "<group>"; };
		18935038296547610052E77B /* TChatRtcArtificialWitnessTestVC.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TChatRtcArtificialWitnessTestVC.m; sourceTree = "<group>"; };
		189350522965498F0052E77B /* IQTextView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = IQTextView.m; sourceTree = "<group>"; };
		189350532965498F0052E77B /* IQTextView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = IQTextView.h; sourceTree = "<group>"; };
		189350552965498F0052E77B /* IQUIView+IQKeyboardToolbar.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "IQUIView+IQKeyboardToolbar.h"; sourceTree = "<group>"; };
		189350562965498F0052E77B /* IQToolbar.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = IQToolbar.m; sourceTree = "<group>"; };
		189350572965498F0052E77B /* IQPreviousNextView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = IQPreviousNextView.h; sourceTree = "<group>"; };
		189350582965498F0052E77B /* IQTitleBarButtonItem.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = IQTitleBarButtonItem.m; sourceTree = "<group>"; };
		189350592965498F0052E77B /* IQBarButtonItem.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = IQBarButtonItem.m; sourceTree = "<group>"; };
		1893505A2965498F0052E77B /* IQUIView+IQKeyboardToolbar.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "IQUIView+IQKeyboardToolbar.m"; sourceTree = "<group>"; };
		1893505B2965498F0052E77B /* IQPreviousNextView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = IQPreviousNextView.m; sourceTree = "<group>"; };
		1893505C2965498F0052E77B /* IQToolbar.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = IQToolbar.h; sourceTree = "<group>"; };
		1893505D2965498F0052E77B /* IQBarButtonItem.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = IQBarButtonItem.h; sourceTree = "<group>"; };
		1893505E2965498F0052E77B /* IQTitleBarButtonItem.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = IQTitleBarButtonItem.h; sourceTree = "<group>"; };
		189350602965498F0052E77B /* IQKeyboardManagerConstantsInternal.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = IQKeyboardManagerConstantsInternal.h; sourceTree = "<group>"; };
		189350612965498F0052E77B /* IQKeyboardManagerConstants.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = IQKeyboardManagerConstants.h; sourceTree = "<group>"; };
		189350622965498F0052E77B /* IQKeyboardReturnKeyHandler.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = IQKeyboardReturnKeyHandler.h; sourceTree = "<group>"; };
		189350632965498F0052E77B /* IQKeyboardManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = IQKeyboardManager.h; sourceTree = "<group>"; };
		189350642965498F0052E77B /* IQKeyboardManager.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = IQKeyboardManager.m; sourceTree = "<group>"; };
		189350662965498F0052E77B /* IQNSArray+Sort.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "IQNSArray+Sort.m"; sourceTree = "<group>"; };
		189350672965498F0052E77B /* IQUIViewController+Additions.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "IQUIViewController+Additions.h"; sourceTree = "<group>"; };
		189350682965498F0052E77B /* IQUITextFieldView+Additions.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "IQUITextFieldView+Additions.m"; sourceTree = "<group>"; };
		189350692965498F0052E77B /* IQUIScrollView+Additions.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "IQUIScrollView+Additions.m"; sourceTree = "<group>"; };
		1893506A2965498F0052E77B /* IQUIView+Hierarchy.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "IQUIView+Hierarchy.m"; sourceTree = "<group>"; };
		1893506B2965498F0052E77B /* IQNSArray+Sort.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "IQNSArray+Sort.h"; sourceTree = "<group>"; };
		1893506C2965498F0052E77B /* IQUIScrollView+Additions.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "IQUIScrollView+Additions.h"; sourceTree = "<group>"; };
		1893506D2965498F0052E77B /* IQUITextFieldView+Additions.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "IQUITextFieldView+Additions.h"; sourceTree = "<group>"; };
		1893506E2965498F0052E77B /* IQUIViewController+Additions.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "IQUIViewController+Additions.m"; sourceTree = "<group>"; };
		1893506F2965498F0052E77B /* IQUIView+Hierarchy.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "IQUIView+Hierarchy.h"; sourceTree = "<group>"; };
		189350702965498F0052E77B /* IQKeyboardReturnKeyHandler.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = IQKeyboardReturnKeyHandler.m; sourceTree = "<group>"; };
		1895FCAF26959A9B00513E5F /* TKOpenPrivacyAgreementView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKOpenPrivacyAgreementView.h; sourceTree = "<group>"; };
		1895FCB126959A9B00513E5F /* TKOpenPrivacyAgreementView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKOpenPrivacyAgreementView.m; sourceTree = "<group>"; };
		189F778726D8E6AD00F4089E /* TTTAttributedLabel.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TTTAttributedLabel.h; sourceTree = "<group>"; };
		189F778826D8E6AE00F4089E /* TTTAttributedLabel.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TTTAttributedLabel.m; sourceTree = "<group>"; };
		18AF8643250771BA0094450D /* TKOpenPlugin60025.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKOpenPlugin60025.h; sourceTree = "<group>"; };
		18AF8644250771BA0094450D /* TKOpenPlugin60025.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKOpenPlugin60025.m; sourceTree = "<group>"; };
		18AF8647250778530094450D /* TKOpenPlugin60043.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = TKOpenPlugin60043.h; sourceTree = "<group>"; };
		18AF8648250778530094450D /* TKOpenPlugin60043.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = TKOpenPlugin60043.m; sourceTree = "<group>"; };
		18BBEF4E28A24AC100A26825 /* WebKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = WebKit.framework; path = System/Library/Frameworks/WebKit.framework; sourceTree = SDKROOT; };
		18C3192525F74AE2003B36C7 /* CallKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CallKit.framework; path = System/Library/Frameworks/CallKit.framework; sourceTree = SDKROOT; };
		18C46C35291A54A10076BAC0 /* TKChatTokenHelper.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKChatTokenHelper.h; sourceTree = "<group>"; };
		18C46C36291A54A10076BAC0 /* TKChatTokenHelper.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKChatTokenHelper.m; sourceTree = "<group>"; };
		18F129E825FEF96000F26F5C /* TKFaceDetectManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKFaceDetectManager.h; sourceTree = "<group>"; };
		18F129E925FEF96000F26F5C /* TKFaceDetectManager.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKFaceDetectManager.m; sourceTree = "<group>"; };
		18F129F525FF231600F26F5C /* TKOpenTipView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKOpenTipView.h; sourceTree = "<group>"; };
		18F129F725FF231600F26F5C /* TKOpenTipView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKOpenTipView.m; sourceTree = "<group>"; };
		18F12A1125FF24E000F26F5C /* TKOneWayVideoAlertTipView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKOneWayVideoAlertTipView.m; sourceTree = "<group>"; };
		18F12A1325FF24E000F26F5C /* TKOneWayVideoAlertTipView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKOneWayVideoAlertTipView.h; sourceTree = "<group>"; };
		18FC9C82295D89320081518D /* TKOpenPlugin60093.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKOpenPlugin60093.m; sourceTree = "<group>"; };
		18FC9C83295D89320081518D /* TKOpenPlugin60093.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKOpenPlugin60093.h; sourceTree = "<group>"; };
		18FF460B28E1B7AE0026440D /* SenseID_Liveness_Silent.lic */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text; path = SenseID_Liveness_Silent.lic; sourceTree = "<group>"; };
		18FF460C28E1B7AE0026440D /* STLivenessModel.bundle */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.plug-in"; path = STLivenessModel.bundle; sourceTree = "<group>"; };
		3311799D2E1684FC002179DC /* TKCardAlbumPreview.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = TKCardAlbumPreview.h; sourceTree = "<group>"; };
		3311799E2E1684FC002179DC /* TKCardAlbumPreview.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = TKCardAlbumPreview.m; sourceTree = "<group>"; };
		3311799F2E1684FC002179DC /* TKCardPreview.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = TKCardPreview.h; sourceTree = "<group>"; };
		331179A02E1684FC002179DC /* TKCardPreview.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = TKCardPreview.m; sourceTree = "<group>"; };
		331179A12E1684FC002179DC /* TKIDCardPhotoView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = TKIDCardPhotoView.h; sourceTree = "<group>"; };
		331179A22E1684FC002179DC /* TKIDCardPhotoView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = TKIDCardPhotoView.m; sourceTree = "<group>"; };
		3316E1812A2835E100E57A02 /* TKOpenPlugin60071.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKOpenPlugin60071.h; sourceTree = "<group>"; };
		3316E1822A2835E100E57A02 /* TKOpenPlugin60071.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKOpenPlugin60071.m; sourceTree = "<group>"; };
		331ABDC02BA4107D00F0B963 /* TKOpenPlugin60095.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKOpenPlugin60095.h; sourceTree = "<group>"; };
		331ABDC12BA4107D00F0B963 /* TKOpenPlugin60095.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKOpenPlugin60095.m; sourceTree = "<group>"; };
		3324DB262C47D86700D5E61B /* TKSDKAuth.lic */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text; path = TKSDKAuth.lic; sourceTree = "<group>"; };
		33294B102A0A469800A72052 /* TChat.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; path = TChat.framework; sourceTree = "<group>"; };
		333DD1C02E24CF7E00DABD89 /* TKVideoRollTextView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = TKVideoRollTextView.h; sourceTree = "<group>"; };
		333DD1C12E24CF7E00DABD89 /* TKVideoRollTextView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = TKVideoRollTextView.m; sourceTree = "<group>"; };
		334D516E2C61BE6D00B1E35C /* PrivacyInfo.xcprivacy */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.xml; path = PrivacyInfo.xcprivacy; sourceTree = "<group>"; };
		336302322D83C202009BC4DC /* TKOpenOneClickLoginViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = TKOpenOneClickLoginViewController.h; sourceTree = "<group>"; };
		336302332D83C202009BC4DC /* TKOpenOneClickLoginViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = TKOpenOneClickLoginViewController.m; sourceTree = "<group>"; };
		336302352D83C202009BC4DC /* OAuth.xcframework */ = {isa = PBXFileReference; expectedSignature = "AppleDeveloperProgram:CJ346CFVA9:Bin Liang"; lastKnownFileType = wrapper.xcframework; path = OAuth.xcframework; sourceTree = "<group>"; };
		336302362D83C202009BC4DC /* TKOpenOneClickLoginService.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = TKOpenOneClickLoginService.h; sourceTree = "<group>"; };
		336302372D83C202009BC4DC /* TKOpenOneClickLoginService.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = TKOpenOneClickLoginService.m; sourceTree = "<group>"; };
		336302382D83C202009BC4DC /* TKOpenPlugin60041.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = TKOpenPlugin60041.h; sourceTree = "<group>"; };
		336302392D83C202009BC4DC /* TKOpenPlugin60041.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = TKOpenPlugin60041.m; sourceTree = "<group>"; };
		3363023B2D83C202009BC4DC /* TKOpenPlugin60096.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = TKOpenPlugin60096.h; sourceTree = "<group>"; };
		3363023C2D83C202009BC4DC /* TKOpenPlugin60096.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = TKOpenPlugin60096.m; sourceTree = "<group>"; };
		336BA7AD29BF2A0A00134194 /* opencv3.4.2.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; path = opencv3.4.2.a; sourceTree = "<group>"; };
		336BA7AE29BF2A0A00134194 /* ISIDReaderPreviewSDK.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; path = ISIDReaderPreviewSDK.framework; sourceTree = "<group>"; };
		336BA7B329BF2A1000134194 /* ISOpenSDKFoundation.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; path = ISOpenSDKFoundation.framework; sourceTree = "<group>"; };
		336BA7B529BF2A1000134194 /* is_camera_closed.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = is_camera_closed.png; sourceTree = "<group>"; };
		336BA7B629BF2A1000134194 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		336BA7B729BF2A1000134194 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		336BA7B829BF2A1000134194 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		336BA7B929BF2A1000134194 /* is_camera_corner_highlight_Red.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = is_camera_corner_highlight_Red.png; sourceTree = "<group>"; };
		336BA7BA29BF2A1000134194 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		336BA7BB29BF2A1000134194 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		336BA7BC29BF2A1000134194 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		336BA7BD29BF2A1000134194 /* is_camera_closed_ccb.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = is_camera_closed_ccb.png; sourceTree = "<group>"; };
		336BA7BE29BF2A1000134194 /* is_camera_flash_on.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = is_camera_flash_on.png; sourceTree = "<group>"; };
		336BA7BF29BF2A1000134194 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		336BA7C029BF2A1000134194 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		336BA7C129BF2A1000134194 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		336BA7C229BF2A1000134194 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		336BA7C329BF2A1000134194 /* is_camera_flash__ccb_off.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = is_camera_flash__ccb_off.png; sourceTree = "<group>"; };
		336BA7C429BF2A1000134194 /* is_camera_corner.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = is_camera_corner.png; sourceTree = "<group>"; };
		336BA7C529BF2A1000134194 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		336BA7C629BF2A1000134194 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		336BA7C729BF2A1000134194 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		336BA7C829BF2A1000134194 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		336BA7C929BF2A1000134194 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		336BA7CA29BF2A1000134194 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		336BA7CB29BF2A1000134194 /* is_camera_logo.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = is_camera_logo.png; sourceTree = "<group>"; };
		336BA7CC29BF2A1000134194 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		336BA7CD29BF2A1000134194 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		336BA7CE29BF2A1000134194 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		336BA7CF29BF2A1000134194 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		336BA7D029BF2A1000134194 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		336BA7D129BF2A1000134194 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		336BA7D229BF2A1000134194 /* is_camera_flash_ccb_on.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = is_camera_flash_ccb_on.png; sourceTree = "<group>"; };
		336BA7D329BF2A1000134194 /* is_camera_corner_highlight.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = is_camera_corner_highlight.png; sourceTree = "<group>"; };
		336BA7D429BF2A1000134194 /* is_camera_flash_off.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = is_camera_flash_off.png; sourceTree = "<group>"; };
		336BA7D529BF2A1000134194 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		336BA7D629BF2A1000134194 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		336BA7D729BF2A1000134194 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		336BA7D829BF2A1000134194 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		336BA7D929BF2A1000134194 /* ISBankCard.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; path = ISBankCard.framework; sourceTree = "<group>"; };
		336D640629C3297800BB96A1 /* TKOrganizationDoubleVideoResultSuccessViewController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKOrganizationDoubleVideoResultSuccessViewController.m; sourceTree = "<group>"; };
		336D640729C3297800BB96A1 /* TKOrganizationDoubleVideoResultBaseViewController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKOrganizationDoubleVideoResultBaseViewController.h; sourceTree = "<group>"; };
		336D640829C3297800BB96A1 /* TKOrganizationDoubleVideoResultBaseViewController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKOrganizationDoubleVideoResultBaseViewController.m; sourceTree = "<group>"; };
		336D640929C3297800BB96A1 /* TKOrganizationDoubleVideoResultFailureViewController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKOrganizationDoubleVideoResultFailureViewController.h; sourceTree = "<group>"; };
		336D640A29C3297800BB96A1 /* TKOrganizationDoubleVideoResultFailureViewController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKOrganizationDoubleVideoResultFailureViewController.m; sourceTree = "<group>"; };
		336D640B29C3297800BB96A1 /* TKOrganizationDoubleVideoResultSuccessViewController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKOrganizationDoubleVideoResultSuccessViewController.h; sourceTree = "<group>"; };
		336D640F29C3298800BB96A1 /* TKOrganizationDoubleVideoGradientButton.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKOrganizationDoubleVideoGradientButton.m; sourceTree = "<group>"; };
		336D641029C3298800BB96A1 /* TKOrganizationDoubleVideoGradientButton.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKOrganizationDoubleVideoGradientButton.h; sourceTree = "<group>"; };
		337A9F0D2A37062500CE0434 /* AnyChatCoreSDK.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; path = AnyChatCoreSDK.framework; sourceTree = "<group>"; };
		338D7FE12B312BDB0030979E /* QCloudTTS.xcframework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.xcframework; path = QCloudTTS.xcframework; sourceTree = "<group>"; };
		338D7FE32B312C020030979E /* QCloudRealTime.xcframework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.xcframework; path = QCloudRealTime.xcframework; sourceTree = "<group>"; };
		33A4ED7D2D081533007215C1 /* TKOpenDelegateManager.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKOpenDelegateManager.m; sourceTree = "<group>"; };
		33A4ED7E2D081533007215C1 /* TKOpenDelegateManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKOpenDelegateManager.h; sourceTree = "<group>"; };
		33B36AF72C21826900121BC3 /* TKOpenPlugin60046.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKOpenPlugin60046.m; sourceTree = "<group>"; };
		33B36AF82C21826900121BC3 /* TKOpenPlugin60046.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKOpenPlugin60046.h; sourceTree = "<group>"; };
		33B3E7602E1BC248004C433C /* TKOCRTimeoutView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = TKOCRTimeoutView.h; sourceTree = "<group>"; };
		33B3E7612E1BC248004C433C /* TKOCRTimeoutView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = TKOCRTimeoutView.m; sourceTree = "<group>"; };
		33BDE2912A7203CD004F30D1 /* TKOpenPlugin60094.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKOpenPlugin60094.m; sourceTree = "<group>"; };
		33BDE2922A7203CD004F30D1 /* TKOpenPlugin60094.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKOpenPlugin60094.h; sourceTree = "<group>"; };
		33C207A32A146B1B00087D13 /* ZegoLiveRoom.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; path = ZegoLiveRoom.framework; sourceTree = "<group>"; };
		33C207A42A146B1B00087D13 /* ZegoQueue.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; path = ZegoQueue.framework; sourceTree = "<group>"; };
		33C207AB2A147A5100087D13 /* GLKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = GLKit.framework; path = System/Library/Frameworks/GLKit.framework; sourceTree = SDKROOT; };
		33C207AD2A147ACB00087D13 /* libc++.tbd */ = {isa = PBXFileReference; lastKnownFileType = "sourcecode.text-based-dylib-definition"; name = "libc++.tbd"; path = "usr/lib/libc++.tbd"; sourceTree = SDKROOT; };
		33C207B22A148B2700087D13 /* TKSpeechSynthesisManagerProtocol.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKSpeechSynthesisManagerProtocol.h; sourceTree = "<group>"; };
		33C207B32A148B2700087D13 /* TKALSpeechSynthesisManager.mm */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.objcpp; path = TKALSpeechSynthesisManager.mm; sourceTree = "<group>"; };
		33C207B42A148B2700087D13 /* TKSpeechSynthesisManager.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKSpeechSynthesisManager.m; sourceTree = "<group>"; };
		33C207B62A148B2700087D13 /* TKTencentSpeechSynthesisManager.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKTencentSpeechSynthesisManager.m; sourceTree = "<group>"; };
		33C207B82A148B2700087D13 /* TKSpeechSynthesisManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKSpeechSynthesisManager.h; sourceTree = "<group>"; };
		33C207BA2A148B2700087D13 /* TKALSpeechSynthesisManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKALSpeechSynthesisManager.h; sourceTree = "<group>"; };
		33C207BB2A148B2700087D13 /* TKTencentSpeechSynthesisManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKTencentSpeechSynthesisManager.h; sourceTree = "<group>"; };
		33C207BD2A148B2700087D13 /* TKringBuf.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = TKringBuf.cpp; sourceTree = "<group>"; };
		33C207BE2A148B2700087D13 /* TKNLSPlayAudio.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKNLSPlayAudio.h; sourceTree = "<group>"; };
		33C207BF2A148B2700087D13 /* TKNLSVoiceRecorder.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKNLSVoiceRecorder.m; sourceTree = "<group>"; };
		33C207C02A148B2700087D13 /* TKringBuf.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKringBuf.h; sourceTree = "<group>"; };
		33C207C42A148B2700087D13 /* TKTencentSpeechRecognizeManager.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKTencentSpeechRecognizeManager.m; sourceTree = "<group>"; };
		33C207C52A148B2700087D13 /* TKSpeechRecognizeManager.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKSpeechRecognizeManager.m; sourceTree = "<group>"; };
		33C207C62A148B2700087D13 /* TKSpeechRecognizeManagerProtocol.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKSpeechRecognizeManagerProtocol.h; sourceTree = "<group>"; };
		33C207C72A148B2700087D13 /* TKALSpeechRecognizeManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKALSpeechRecognizeManager.h; sourceTree = "<group>"; };
		33C207C82A148B2700087D13 /* TKTencentSpeechRecognizeManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKTencentSpeechRecognizeManager.h; sourceTree = "<group>"; };
		33C207CB2A148B2700087D13 /* TKSpeechRecognizeManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKSpeechRecognizeManager.h; sourceTree = "<group>"; };
		33C207CC2A148B2700087D13 /* TKALSpeechRecognizeManager.mm */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.objcpp; path = TKALSpeechRecognizeManager.mm; sourceTree = "<group>"; };
		33C207CD2A148B2700087D13 /* TKNLSPlayAudio.mm */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.objcpp; path = TKNLSPlayAudio.mm; sourceTree = "<group>"; };
		33C207CE2A148B2700087D13 /* TKNLSVoiceRecorder.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKNLSVoiceRecorder.h; sourceTree = "<group>"; };
		33C207CF2A148B2700087D13 /* TKOpenPlugin60026.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKOpenPlugin60026.h; sourceTree = "<group>"; };
		33C207D12A148B2700087D13 /* TKOneWayVideoViewController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKOneWayVideoViewController.h; sourceTree = "<group>"; };
		33C207D22A148B2700087D13 /* TKOneWayVideoViewController.mm */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.objcpp; path = TKOneWayVideoViewController.mm; sourceTree = "<group>"; };
		33C207D32A148B2700087D13 /* TKOpenPlugin60026.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKOpenPlugin60026.m; sourceTree = "<group>"; };
		33C207D52A148B2700087D13 /* TKOneWayVideoEndView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKOneWayVideoEndView.m; sourceTree = "<group>"; };
		33C207D62A148B2700087D13 /* TKOneWayVideoView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKOneWayVideoView.m; sourceTree = "<group>"; };
		33C207D72A148B2700087D13 /* TKOneWayVideoEndView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKOneWayVideoEndView.h; sourceTree = "<group>"; };
		33C207D82A148B2700087D13 /* TKOneWayVideoView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKOneWayVideoView.h; sourceTree = "<group>"; };
		33C502AD2A0A4D520050DB0C /* nuisdk.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; path = nuisdk.framework; sourceTree = "<group>"; };
		33C502B12A0A4D860050DB0C /* STLivenessDetector.xcframework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.xcframework; path = STLivenessDetector.xcframework; sourceTree = "<group>"; };
		33CB8E2D2DA6513D000EA7C4 /* TKOpenPlugin60817.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = TKOpenPlugin60817.h; sourceTree = "<group>"; };
		33CB8E2E2DA6513D000EA7C4 /* TKOpenPlugin60817.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = TKOpenPlugin60817.m; sourceTree = "<group>"; };
		33DAFA9F2C61E8C6009F2243 /* iflyMSC.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; path = iflyMSC.framework; sourceTree = "<group>"; };
		33DAFAA12C61E9D6009F2243 /* TKiflyNoAiSpeechSynthesisManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKiflyNoAiSpeechSynthesisManager.h; sourceTree = "<group>"; };
		33DAFAA22C61E9D6009F2243 /* TKiflyNoAiSpeechSynthesisManager.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKiflyNoAiSpeechSynthesisManager.m; sourceTree = "<group>"; };
		33DAFAA42C61E9E1009F2243 /* TKiflyNoAiSpeechRecognizeManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKiflyNoAiSpeechRecognizeManager.h; sourceTree = "<group>"; };
		33DAFAA52C61E9E1009F2243 /* TKiflyNoAiSpeechRecognizeManager.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKiflyNoAiSpeechRecognizeManager.m; sourceTree = "<group>"; };
		33DAFAA72C61F983009F2243 /* TKTChatRtcSmartTwoVideoManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKTChatRtcSmartTwoVideoManager.h; sourceTree = "<group>"; };
		33DAFAA82C61F983009F2243 /* TKTChatRtcSmartTwoVideoManager.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKTChatRtcSmartTwoVideoManager.m; sourceTree = "<group>"; };
		33DAFAAA2C61F99A009F2243 /* TChatRtc.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; path = TChatRtc.framework; sourceTree = "<group>"; };
		33DF5F4D2D1A9B0700F311AA /* TKOpenPlugin60813.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKOpenPlugin60813.h; sourceTree = "<group>"; };
		33DF5F4E2D1A9B0700F311AA /* TKOpenPlugin60813.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKOpenPlugin60813.m; sourceTree = "<group>"; };
		33E2EA192E371F5D00037BE4 /* TKBankCardPhotoView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = TKBankCardPhotoView.h; sourceTree = "<group>"; };
		33E2EA1A2E371F5D00037BE4 /* TKBankCardPhotoView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = TKBankCardPhotoView.m; sourceTree = "<group>"; };
		33EA0F412C9A69AB0066022A /* TKOpenPlugin60812.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKOpenPlugin60812.h; sourceTree = "<group>"; };
		33EA0F422C9A69AB0066022A /* TKOpenPlugin60812.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKOpenPlugin60812.m; sourceTree = "<group>"; };
		33FD40862E2F3AEE00687DD1 /* TKOpenPlugin60066.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = TKOpenPlugin60066.h; sourceTree = "<group>"; };
		33FD40872E2F3AEE00687DD1 /* TKOpenPlugin60066.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = TKOpenPlugin60066.m; sourceTree = "<group>"; };
		B30DC5061BCFC873007072FB /* Configuration.xml */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.xml; path = Configuration.xml; sourceTree = "<group>"; };
		B30DC5071BCFC873007072FB /* OpenPlugin.xml */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.xml; path = OpenPlugin.xml; sourceTree = "<group>"; };
		B30DC5081BCFC873007072FB /* SystemPlugin.xml */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.xml; path = SystemPlugin.xml; sourceTree = "<group>"; };
		B30FD8A51EF1063F000D3E94 /* UIViewController+TKAuthorityKit.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "UIViewController+TKAuthorityKit.h"; sourceTree = "<group>"; };
		B30FD8A61EF1063F000D3E94 /* UIViewController+TKAuthorityKit.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "UIViewController+TKAuthorityKit.m"; sourceTree = "<group>"; };
		B30FD8A81EF11ED2000D3E94 /* TKVideoWitnessViewController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKVideoWitnessViewController.h; sourceTree = "<group>"; };
		B30FD8A91EF11ED2000D3E94 /* TKVideoWitnessViewController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKVideoWitnessViewController.m; sourceTree = "<group>"; };
		B30FD8AA1EF11ED2000D3E94 /* TKVideoWitnessViewController+AnyChat.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "TKVideoWitnessViewController+AnyChat.h"; sourceTree = "<group>"; };
		B30FD8AB1EF11ED2000D3E94 /* TKVideoWitnessViewController+AnyChat.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "TKVideoWitnessViewController+AnyChat.m"; sourceTree = "<group>"; };
		B30FD8AC1EF11ED2000D3E94 /* TKVideoWitnessViewController+CommonKit.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "TKVideoWitnessViewController+CommonKit.h"; sourceTree = "<group>"; };
		B30FD8AD1EF11ED2000D3E94 /* TKVideoWitnessViewController+CommonKit.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "TKVideoWitnessViewController+CommonKit.m"; sourceTree = "<group>"; };
		B32C870B1D87E96E00373C19 /* config.plist */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.xml; path = config.plist; sourceTree = "<group>"; };
		B32E91C71F18C95E005A6B6D /* TKVideoWitnessViewController+TChat.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "TKVideoWitnessViewController+TChat.h"; sourceTree = "<group>"; };
		B32E91C81F18C95E005A6B6D /* TKVideoWitnessViewController+TChat.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "TKVideoWitnessViewController+TChat.m"; sourceTree = "<group>"; };
		B3386D691F271753006EF60A /* TKAVCaptureManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKAVCaptureManager.h; sourceTree = "<group>"; };
		B3386D6A1F271753006EF60A /* TKAVCaptureManager.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKAVCaptureManager.m; sourceTree = "<group>"; };
		B33FDC0F1F692BCB00C644F2 /* libxml2.tbd */ = {isa = PBXFileReference; lastKnownFileType = "sourcecode.text-based-dylib-definition"; name = libxml2.tbd; path = usr/lib/libxml2.tbd; sourceTree = SDKROOT; };
		B33FDC111F692BE400C644F2 /* libresolv.tbd */ = {isa = PBXFileReference; lastKnownFileType = "sourcecode.text-based-dylib-definition"; name = libresolv.tbd; path = usr/lib/libresolv.tbd; sourceTree = SDKROOT; };
		B35087C21F0E367300A366A0 /* tk_video_icon_iphone.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = tk_video_icon_iphone.png; sourceTree = "<group>"; };
		B35087C31F0E367300A366A0 /* TKVideoWitnessViewController.xib */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.xib; path = TKVideoWitnessViewController.xib; sourceTree = "<group>"; };
		B35569F61DDBF96B00C76E7C /* TKOpenPlugin60000.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKOpenPlugin60000.h; sourceTree = "<group>"; };
		B35569F71DDBF96B00C76E7C /* TKOpenPlugin60000.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKOpenPlugin60000.m; sourceTree = "<group>"; };
		B35569F91DDBF96B00C76E7C /* TKOpenPlugin60001.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKOpenPlugin60001.h; sourceTree = "<group>"; };
		B35569FA1DDBF96B00C76E7C /* TKOpenPlugin60001.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKOpenPlugin60001.m; sourceTree = "<group>"; };
		B35569FC1DDBF96B00C76E7C /* TKOpenPlugin60002.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKOpenPlugin60002.h; sourceTree = "<group>"; };
		B35569FD1DDBF96B00C76E7C /* TKOpenPlugin60002.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKOpenPlugin60002.m; sourceTree = "<group>"; };
		B35569FF1DDBF96B00C76E7C /* MTakeCardViewController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = MTakeCardViewController.h; sourceTree = "<group>"; };
		B3556A001DDBF96B00C76E7C /* MTakeCardViewController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = MTakeCardViewController.m; sourceTree = "<group>"; };
		B3556A021DDBF96B00C76E7C /* TKOpenPlugin60003.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKOpenPlugin60003.h; sourceTree = "<group>"; };
		B3556A031DDBF96B00C76E7C /* TKOpenPlugin60003.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKOpenPlugin60003.m; sourceTree = "<group>"; };
		B3556A051DDBF96B00C76E7C /* TKOpenPlugin60004.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKOpenPlugin60004.h; sourceTree = "<group>"; };
		B3556A061DDBF96B00C76E7C /* TKOpenPlugin60004.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKOpenPlugin60004.m; sourceTree = "<group>"; };
		B3556A131DDBF96B00C76E7C /* chat_cancel_btn.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = chat_cancel_btn.png; sourceTree = "<group>"; };
		B3556A141DDBF96B00C76E7C /* kefu_bg_img.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = kefu_bg_img.png; sourceTree = "<group>"; };
		B3556A151DDBF96B00C76E7C /* page_bg.jpg */ = {isa = PBXFileReference; lastKnownFileType = image.jpeg; path = page_bg.jpg; sourceTree = "<group>"; };
		B3556A161DDBF96B00C76E7C /* TKAnyChatViewController.xib */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.xib; path = TKAnyChatViewController.xib; sourceTree = "<group>"; };
		B3556A171DDBF96B00C76E7C /* TKAnyChatViewController4.xib */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.xib; path = TKAnyChatViewController4.xib; sourceTree = "<group>"; };
		B3556A1A1DDBF96B00C76E7C /* TKOpenPlugin60005.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKOpenPlugin60005.h; sourceTree = "<group>"; };
		B3556A1B1DDBF96B00C76E7C /* TKOpenPlugin60005.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKOpenPlugin60005.m; sourceTree = "<group>"; };
		B3556A1E1DDBF96B00C76E7C /* TkFDRecordController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TkFDRecordController.h; sourceTree = "<group>"; };
		B3556A1F1DDBF96B00C76E7C /* TkFDRecordController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TkFDRecordController.m; sourceTree = "<group>"; };
		B3556A201DDBF96B00C76E7C /* TkRecordController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TkRecordController.h; sourceTree = "<group>"; };
		B3556A211DDBF96B00C76E7C /* TKRecordController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKRecordController.m; sourceTree = "<group>"; };
		B3556A231DDBF96B00C76E7C /* TKRecordModel.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKRecordModel.h; sourceTree = "<group>"; };
		B3556A241DDBF96B00C76E7C /* TKRecordModel.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKRecordModel.m; sourceTree = "<group>"; };
		B3556A251DDBF96B00C76E7C /* TKOpenPlugin60006.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKOpenPlugin60006.h; sourceTree = "<group>"; };
		B3556A261DDBF96B00C76E7C /* TKOpenPlugin60006.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKOpenPlugin60006.m; sourceTree = "<group>"; };
		B3556A281DDBF96B00C76E7C /* TKRecordView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKRecordView.h; sourceTree = "<group>"; };
		B3556A291DDBF96B00C76E7C /* TKRecordView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKRecordView.m; sourceTree = "<group>"; };
		B3556A3B1DDBF96B00C76E7C /* MTakeFaceViewController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = MTakeFaceViewController.h; sourceTree = "<group>"; };
		B3556A3C1DDBF96B00C76E7C /* MTakeFaceViewController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = MTakeFaceViewController.m; sourceTree = "<group>"; };
		B3556A3D1DDBF96B00C76E7C /* TKOpenPlugin60008.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKOpenPlugin60008.h; sourceTree = "<group>"; };
		B3556A3E1DDBF96B00C76E7C /* TKOpenPlugin60008.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKOpenPlugin60008.m; sourceTree = "<group>"; };
		B3556A401DDBF96B00C76E7C /* TKOpenPlugin60010.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKOpenPlugin60010.h; sourceTree = "<group>"; };
		B3556A411DDBF96B00C76E7C /* TKOpenPlugin60010.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKOpenPlugin60010.m; sourceTree = "<group>"; };
		B3556A461DDBF96B00C76E7C /* MTakeBigPictureViewController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = MTakeBigPictureViewController.h; sourceTree = "<group>"; };
		B3556A471DDBF96B00C76E7C /* MTakeBigPictureViewController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = MTakeBigPictureViewController.m; sourceTree = "<group>"; };
		B3556A481DDBF96B00C76E7C /* MTakePhotoViewController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = MTakePhotoViewController.h; sourceTree = "<group>"; };
		B3556A491DDBF96B00C76E7C /* MTakePhotoViewController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = MTakePhotoViewController.m; sourceTree = "<group>"; };
		B3556A4A1DDBF96B00C76E7C /* TKOpenPlugin60013.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKOpenPlugin60013.h; sourceTree = "<group>"; };
		B3556A4B1DDBF96B00C76E7C /* TKOpenPlugin60013.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKOpenPlugin60013.m; sourceTree = "<group>"; };
		B3556A501DDBF96B00C76E7C /* MIDCardRecognizeViewController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = MIDCardRecognizeViewController.h; sourceTree = "<group>"; };
		B3556A511DDBF96B00C76E7C /* MIDCardRecognizeViewController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = MIDCardRecognizeViewController.m; sourceTree = "<group>"; };
		B3556A521DDBF96B00C76E7C /* TKOpenPlugin60014.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKOpenPlugin60014.h; sourceTree = "<group>"; };
		B3556A531DDBF96B00C76E7C /* TKOpenPlugin60014.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKOpenPlugin60014.m; sourceTree = "<group>"; };
		B3556A841DDBF96B00C76E7C /* TKBankCardRecognizeViewController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKBankCardRecognizeViewController.h; sourceTree = "<group>"; };
		B3556A851DDBF96B00C76E7C /* TKBankCardRecognizeViewController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKBankCardRecognizeViewController.m; sourceTree = "<group>"; };
		B3556A861DDBF96B00C76E7C /* TKOpenPlugin60016.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKOpenPlugin60016.h; sourceTree = "<group>"; };
		B3556A871DDBF96B00C76E7C /* TKOpenPlugin60016.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKOpenPlugin60016.m; sourceTree = "<group>"; };
		B3556A891DDBF96B00C76E7C /* TKOpenPlugin60017.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKOpenPlugin60017.h; sourceTree = "<group>"; };
		B3556A8A1DDBF96B00C76E7C /* TKOpenPlugin60017.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKOpenPlugin60017.m; sourceTree = "<group>"; };
		B3556A8C1DDBF96B00C76E7C /* TKOpenPlugin60018.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKOpenPlugin60018.h; sourceTree = "<group>"; };
		B3556A8D1DDBF96B00C76E7C /* TKOpenPlugin60018.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKOpenPlugin60018.m; sourceTree = "<group>"; };
		B35EBF391C4393770059F885 /* MNavViewController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = MNavViewController.h; sourceTree = "<group>"; };
		B35EBF3A1C4393770059F885 /* MNavViewController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = MNavViewController.m; sourceTree = "<group>"; };
		B35EBF3D1C43942F0059F885 /* MDRadialProgressLabel.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = MDRadialProgressLabel.h; sourceTree = "<group>"; };
		B35EBF3E1C43942F0059F885 /* MDRadialProgressLabel.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = MDRadialProgressLabel.m; sourceTree = "<group>"; };
		B35EBF3F1C43942F0059F885 /* MDRadialProgressTheme.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = MDRadialProgressTheme.h; sourceTree = "<group>"; };
		B35EBF401C43942F0059F885 /* MDRadialProgressTheme.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = MDRadialProgressTheme.m; sourceTree = "<group>"; };
		B35EBF411C43942F0059F885 /* MDRadialProgressView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = MDRadialProgressView.h; sourceTree = "<group>"; };
		B35EBF421C43942F0059F885 /* MDRadialProgressView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = MDRadialProgressView.m; sourceTree = "<group>"; };
		B379D392206CD57000D1440E /* CoreTelephony.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreTelephony.framework; path = System/Library/Frameworks/CoreTelephony.framework; sourceTree = SDKROOT; };
		B37F84281BE365230016F93D /* OpenGLES.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = OpenGLES.framework; path = System/Library/Frameworks/OpenGLES.framework; sourceTree = SDKROOT; };
		B384CDC81B8177B400AFD817 /* TKButton.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKButton.h; sourceTree = "<group>"; };
		B384CDC91B8177B400AFD817 /* TKButton.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKButton.m; sourceTree = "<group>"; };
		B384CDCA1B8177B400AFD817 /* TKCameraTools.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKCameraTools.h; sourceTree = "<group>"; };
		B384CDCB1B8177B400AFD817 /* TKCameraTools.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKCameraTools.m; sourceTree = "<group>"; };
		B386BA0C1F947E6C001CBD13 /* TKOpenPlugin60099.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKOpenPlugin60099.h; sourceTree = "<group>"; };
		B386BA0D1F947E6C001CBD13 /* TKOpenPlugin60099.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKOpenPlugin60099.m; sourceTree = "<group>"; };
		B38D07B51FC402EC0013DA81 /* TKMAlertViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = TKMAlertViewController.m; sourceTree = "<group>"; };
		B38D07B61FC402EC0013DA81 /* TKMAlertViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = TKMAlertViewController.h; sourceTree = "<group>"; };
		B39BADCF1B4D524F00F071D4 /* CoreLocation.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreLocation.framework; path = System/Library/Frameworks/CoreLocation.framework; sourceTree = SDKROOT; };
		B39E930E208F0532006EC8A3 /* CoreMotion.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreMotion.framework; path = System/Library/Frameworks/CoreMotion.framework; sourceTree = SDKROOT; };
		B39E9310208F054A006EC8A3 /* MessageUI.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = MessageUI.framework; path = System/Library/Frameworks/MessageUI.framework; sourceTree = SDKROOT; };
		B39E9312208F05CD006EC8A3 /* Accelerate.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Accelerate.framework; path = System/Library/Frameworks/Accelerate.framework; sourceTree = SDKROOT; };
		B3A2DD311EA461A700B18842 /* TKCommonUtil.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKCommonUtil.h; sourceTree = "<group>"; };
		B3A2DD321EA461A700B18842 /* TKCommonUtil.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKCommonUtil.m; sourceTree = "<group>"; };
		B3AB8AF91C15708900571BE6 /* TKOpenResource.bundle */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.plug-in"; path = TKOpenResource.bundle; sourceTree = "<group>"; };
		B3B108831B44DF2800546D96 /* libiconv.dylib */ = {isa = PBXFileReference; lastKnownFileType = "compiled.mach-o.dylib"; name = libiconv.dylib; path = usr/lib/libiconv.dylib; sourceTree = SDKROOT; };
		B3C6ADC61C92ADDD00FE95F0 /* AssetsLibrary.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = AssetsLibrary.framework; path = System/Library/Frameworks/AssetsLibrary.framework; sourceTree = SDKROOT; };
		B3C6ADC81C92ADEA00FE95F0 /* CoreMedia.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreMedia.framework; path = System/Library/Frameworks/CoreMedia.framework; sourceTree = SDKROOT; };
		B3E1B2EF1B8D9FEF00CDD258 /* Resources */ = {isa = PBXFileReference; lastKnownFileType = folder; path = Resources; sourceTree = "<group>"; };
		B3E1B30B1B8DB80800CDD258 /* TKOpenResource.bundle */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = TKOpenResource.bundle; sourceTree = BUILT_PRODUCTS_DIR; };
		B3E9E0D91C1170DC0012E37D /* TKOpenDelegate.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKOpenDelegate.h; sourceTree = "<group>"; };
		B3FACBB11AFDDF0E0088CDF1 /* 思迪TChatRtc.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = "思迪TChatRtc.app"; sourceTree = BUILT_PRODUCTS_DIR; };
		B3FACBB51AFDDF0E0088CDF1 /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		B3FACBB61AFDDF0E0088CDF1 /* main.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = main.m; sourceTree = "<group>"; };
		B3FACBB81AFDDF0E0088CDF1 /* AppDelegate.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = AppDelegate.h; sourceTree = "<group>"; };
		B3FACBB91AFDDF0E0088CDF1 /* AppDelegate.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = AppDelegate.m; sourceTree = "<group>"; };
		B3FACBBB1AFDDF0E0088CDF1 /* ViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ViewController.h; sourceTree = "<group>"; };
		B3FACBBC1AFDDF0E0088CDF1 /* ViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = ViewController.m; sourceTree = "<group>"; };
		B3FACBBF1AFDDF0E0088CDF1 /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Base; path = Base.lproj/Main.storyboard; sourceTree = "<group>"; };
		B3FACBC11AFDDF0E0088CDF1 /* Images.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Images.xcassets; sourceTree = "<group>"; };
		B3FACBC41AFDDF0E0088CDF1 /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.xib; name = Base; path = Base.lproj/LaunchScreen.xib; sourceTree = "<group>"; };
		B3FACBDC1AFDE2D10088CDF1 /* UIKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = UIKit.framework; path = System/Library/Frameworks/UIKit.framework; sourceTree = SDKROOT; };
		B3FACBDE1AFDE2E50088CDF1 /* QuartzCore.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = QuartzCore.framework; path = System/Library/Frameworks/QuartzCore.framework; sourceTree = SDKROOT; };
		B3FACBE01AFDE2F40088CDF1 /* Security.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Security.framework; path = System/Library/Frameworks/Security.framework; sourceTree = SDKROOT; };
		B3FACBE21AFDE3010088CDF1 /* MobileCoreServices.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = MobileCoreServices.framework; path = System/Library/Frameworks/MobileCoreServices.framework; sourceTree = SDKROOT; };
		B3FACBE41AFDE30B0088CDF1 /* CFNetwork.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CFNetwork.framework; path = System/Library/Frameworks/CFNetwork.framework; sourceTree = SDKROOT; };
		B3FACBE61AFDE31A0088CDF1 /* SystemConfiguration.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = SystemConfiguration.framework; path = System/Library/Frameworks/SystemConfiguration.framework; sourceTree = SDKROOT; };
		B3FACBE81AFDE33A0088CDF1 /* Foundation.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Foundation.framework; path = System/Library/Frameworks/Foundation.framework; sourceTree = SDKROOT; };
		B3FACBEA1AFDE3460088CDF1 /* CoreGraphics.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreGraphics.framework; path = System/Library/Frameworks/CoreGraphics.framework; sourceTree = SDKROOT; };
		B3FACBEC1AFDE35E0088CDF1 /* libstdc++.6.0.9.dylib */ = {isa = PBXFileReference; lastKnownFileType = "compiled.mach-o.dylib"; name = "libstdc++.6.0.9.dylib"; path = "usr/lib/libstdc++.6.0.9.dylib"; sourceTree = SDKROOT; };
		B3FACBEE1AFDE3750088CDF1 /* libsqlite3.dylib */ = {isa = PBXFileReference; lastKnownFileType = "compiled.mach-o.dylib"; name = libsqlite3.dylib; path = usr/lib/libsqlite3.dylib; sourceTree = SDKROOT; };
		B3FACBF01AFDE3800088CDF1 /* libz.1.2.5.dylib */ = {isa = PBXFileReference; lastKnownFileType = "compiled.mach-o.dylib"; name = libz.1.2.5.dylib; path = usr/lib/libz.1.2.5.dylib; sourceTree = SDKROOT; };
		B3FACBF21AFDE3940088CDF1 /* libc++.dylib */ = {isa = PBXFileReference; lastKnownFileType = "compiled.mach-o.dylib"; name = "libc++.dylib"; path = "usr/lib/libc++.dylib"; sourceTree = SDKROOT; };
		B3FACCBC1AFDF2F80088CDF1 /* TKOpenAccount.pch */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = TKOpenAccount.pch; sourceTree = "<group>"; };
		B3FEEB351B845D6D00468924 /* AVFoundation.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = AVFoundation.framework; path = System/Library/Frameworks/AVFoundation.framework; sourceTree = SDKROOT; };
		B3FF6E391EC5B2A0009FAF95 /* tk_open */ = {isa = PBXFileReference; lastKnownFileType = folder; path = tk_open; sourceTree = "<group>"; };
		B3FF6E3B1EC5B4F0009FAF95 /* TKYKHEmbedHelper.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKYKHEmbedHelper.h; sourceTree = "<group>"; };
		B3FF6E3C1EC5B4F0009FAF95 /* TKYKHEmbedHelper.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKYKHEmbedHelper.m; sourceTree = "<group>"; };
		************************ /* TKChatErrorConverter.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = TKChatErrorConverter.h; sourceTree = "<group>"; };
		************************ /* TKChatErrorConverter.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = TKChatErrorConverter.m; sourceTree = "<group>"; };
		BE05BBF32D39FAFB002505D6 /* TKRecordManagerProtocol.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = TKRecordManagerProtocol.h; sourceTree = "<group>"; };
		BE6CCFB61B5000B6003DCF98 /* TKOpenController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKOpenController.h; sourceTree = "<group>"; };
		BE6CCFB71B5000B6003DCF98 /* TKOpenController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKOpenController.m; sourceTree = "<group>"; };
		BE6CCFBA1B5000B6003DCF98 /* TKOpenAccountService.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKOpenAccountService.h; sourceTree = "<group>"; };
		BE6CCFBB1B5000B6003DCF98 /* TKOpenAccountService.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKOpenAccountService.m; sourceTree = "<group>"; };
		BE6CCFC41B5000B6003DCF98 /* YLProgressBar.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = YLProgressBar.h; sourceTree = "<group>"; };
		BE6CCFC51B5000B6003DCF98 /* YLProgressBar.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = YLProgressBar.m; sourceTree = "<group>"; };
		DC0AC8BD29642E550068F203 /* TKOpenPlugin60087.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKOpenPlugin60087.m; sourceTree = "<group>"; };
		DC0AC8BF29642E550068F203 /* TKWatchVideoViewController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKWatchVideoViewController.h; sourceTree = "<group>"; };
		DC0AC8C029642E550068F203 /* TKWatchVideoViewController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKWatchVideoViewController.m; sourceTree = "<group>"; };
		DC0AC8C229642E550068F203 /* TKWatchVideoVideoView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKWatchVideoVideoView.m; sourceTree = "<group>"; };
		DC0AC8C329642E550068F203 /* TKWatchVideoVideoView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKWatchVideoVideoView.h; sourceTree = "<group>"; };
		DC0AC8C429642E550068F203 /* TKOpenPlugin60087.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKOpenPlugin60087.h; sourceTree = "<group>"; };
		DC38DECF291E24BA00362354 /* TKBaseVideoRecordEndLandscapeView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = TKBaseVideoRecordEndLandscapeView.h; sourceTree = "<group>"; };
		************************ /* TKBaseVideoRecordEndLandscapeView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = TKBaseVideoRecordEndLandscapeView.m; sourceTree = "<group>"; };
		DC4455042A380E6C0073C229 /* TKVideoRecordManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKVideoRecordManager.h; sourceTree = "<group>"; };
		DC4455052A380E6C0073C229 /* TKAnyChatSmartTwoVideoManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKAnyChatSmartTwoVideoManager.h; sourceTree = "<group>"; };
		DC4455062A380E6D0073C229 /* TKZegoSmartTwoVideoManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKZegoSmartTwoVideoManager.h; sourceTree = "<group>"; };
		DC4455072A380E6D0073C229 /* TKSampleBufferConverter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKSampleBufferConverter.h; sourceTree = "<group>"; };
		************************ /* TKVideoRecordManager.mm */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.objcpp; path = TKVideoRecordManager.mm; sourceTree = "<group>"; };
		DC4455092A380E6D0073C229 /* TKSmartTwoVideoManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKSmartTwoVideoManager.h; sourceTree = "<group>"; };
		DC44550A2A380E6E0073C229 /* TKChatVideoRecordManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKChatVideoRecordManager.h; sourceTree = "<group>"; };
		DC44550C2A380E6E0073C229 /* TKSmartTwoVideoManagerProtocol.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKSmartTwoVideoManagerProtocol.h; sourceTree = "<group>"; };
		DC44550D2A380E6F0073C229 /* TKChatVideoRecordManager.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKChatVideoRecordManager.m; sourceTree = "<group>"; };
		************************ /* TKSmartTwoVideoManager.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKSmartTwoVideoManager.m; sourceTree = "<group>"; };
		DC44550F2A380E6F0073C229 /* TKAnyChatSmartTwoVideoManager.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKAnyChatSmartTwoVideoManager.m; sourceTree = "<group>"; };
		DC4455102A380E700073C229 /* TKTChatSmartTwoVideoManager.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKTChatSmartTwoVideoManager.m; sourceTree = "<group>"; };
		DC4455112A380E700073C229 /* TKZegoSmartTwoVideoManager.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKZegoSmartTwoVideoManager.m; sourceTree = "<group>"; };
		DC4455132A380E720073C229 /* TKTChatSmartTwoVideoManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKTChatSmartTwoVideoManager.h; sourceTree = "<group>"; };
		DC4455142A380E720073C229 /* TKSampleBufferConverter.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKSampleBufferConverter.m; sourceTree = "<group>"; };
		DC44551D2A380F7A0073C229 /* TKSmartTwoVideoController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKSmartTwoVideoController.h; sourceTree = "<group>"; };
		DC44551E2A380F7B0073C229 /* TKSmartTwoVideoController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKSmartTwoVideoController.m; sourceTree = "<group>"; };
		DC44552C2A3858330073C229 /* TKSampleBufferConverter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKSampleBufferConverter.h; sourceTree = "<group>"; };
		************************ /* www */ = {isa = PBXFileReference; lastKnownFileType = folder; path = www; sourceTree = "<group>"; };
		DC48AC0B2A56A0A2005B7D5C /* TKZFLandscapeRotationManager.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKZFLandscapeRotationManager.m; sourceTree = "<group>"; };
		DC48AC0C2A56A0A2005B7D5C /* TKZFPresentTransition.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKZFPresentTransition.m; sourceTree = "<group>"; };
		DC48AC0D2A56A0A2005B7D5C /* TKZFLandscapeViewController_iOS15.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKZFLandscapeViewController_iOS15.h; sourceTree = "<group>"; };
		DC48AC0E2A56A0A2005B7D5C /* TKZFLandscapeRotationManager_iOS16.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKZFLandscapeRotationManager_iOS16.m; sourceTree = "<group>"; };
		DC48AC0F2A56A0A2005B7D5C /* TKZFLandscapeViewController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKZFLandscapeViewController.m; sourceTree = "<group>"; };
		DC48AC102A56A0A2005B7D5C /* TKZFOrientationObserver.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKZFOrientationObserver.h; sourceTree = "<group>"; };
		DC48AC112A56A0A2005B7D5C /* TKZFPersentInteractiveTransition.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKZFPersentInteractiveTransition.h; sourceTree = "<group>"; };
		DC48AC122A56A0A2005B7D5C /* TKZFLandscapeWindow.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKZFLandscapeWindow.m; sourceTree = "<group>"; };
		DC48AC132A56A0A2005B7D5C /* TKZFPortraitViewController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKZFPortraitViewController.h; sourceTree = "<group>"; };
		DC48AC142A56A0A2005B7D5C /* TKZFLandscapeRotationManager_iOS15.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKZFLandscapeRotationManager_iOS15.m; sourceTree = "<group>"; };
		DC48AC152A56A0A2005B7D5C /* TKZFPresentTransition.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKZFPresentTransition.h; sourceTree = "<group>"; };
		DC48AC162A56A0A2005B7D5C /* TKZFLandscapeRotationManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKZFLandscapeRotationManager.h; sourceTree = "<group>"; };
		DC48AC172A56A0A2005B7D5C /* TKZFLandscapeRotationManager_iOS16.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKZFLandscapeRotationManager_iOS16.h; sourceTree = "<group>"; };
		DC48AC182A56A0A2005B7D5C /* TKZFLandscapeViewController_iOS15.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKZFLandscapeViewController_iOS15.m; sourceTree = "<group>"; };
		DC48AC192A56A0A2005B7D5C /* TKZFLandscapeViewController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKZFLandscapeViewController.h; sourceTree = "<group>"; };
		DC48AC1A2A56A0A2005B7D5C /* TKZFLandscapeRotationManager_iOS15.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKZFLandscapeRotationManager_iOS15.h; sourceTree = "<group>"; };
		DC48AC1B2A56A0A2005B7D5C /* TKZFPortraitViewController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKZFPortraitViewController.m; sourceTree = "<group>"; };
		DC48AC1C2A56A0A2005B7D5C /* TKZFLandscapeWindow.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKZFLandscapeWindow.h; sourceTree = "<group>"; };
		DC48AC1D2A56A0A2005B7D5C /* TKZFPersentInteractiveTransition.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKZFPersentInteractiveTransition.m; sourceTree = "<group>"; };
		DC48AC1E2A56A0A2005B7D5C /* TKZFOrientationObserver.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKZFOrientationObserver.m; sourceTree = "<group>"; };
		DC48AC202A56A0A2005B7D5C /* UIScrollView+TKZFPlayer.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "UIScrollView+TKZFPlayer.h"; sourceTree = "<group>"; };
		DC48AC212A56A0A2005B7D5C /* UIView+TKZFFrame.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "UIView+TKZFFrame.m"; sourceTree = "<group>"; };
		DC48AC222A56A0A2005B7D5C /* UIImageView+TKZFCache.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "UIImageView+TKZFCache.m"; sourceTree = "<group>"; };
		DC48AC232A56A0A2005B7D5C /* UIView+TKZFFrame.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "UIView+TKZFFrame.h"; sourceTree = "<group>"; };
		DC48AC242A56A0A2005B7D5C /* UIScrollView+TKZFPlayer.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "UIScrollView+TKZFPlayer.m"; sourceTree = "<group>"; };
		DC48AC252A56A0A2005B7D5C /* UIImageView+TKZFCache.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "UIImageView+TKZFCache.h"; sourceTree = "<group>"; };
		DC48AC262A56A0A2005B7D5C /* TKZFPlayerGestureControl.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKZFPlayerGestureControl.m; sourceTree = "<group>"; };
		DC48AC272A56A0A2005B7D5C /* TKZFPlayerController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKZFPlayerController.h; sourceTree = "<group>"; };
		DC48AC282A56A0A2005B7D5C /* TKPlayer.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKPlayer.h; sourceTree = "<group>"; };
		DC48AC2A2A56A0A2005B7D5C /* TKZFPlayerLogManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKZFPlayerLogManager.h; sourceTree = "<group>"; };
		DC48AC2B2A56A0A2005B7D5C /* TKZFPlayerNotification.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKZFPlayerNotification.m; sourceTree = "<group>"; };
		DC48AC2C2A56A0A2005B7D5C /* TKZFNetworkSpeedMonitor.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKZFNetworkSpeedMonitor.h; sourceTree = "<group>"; };
		DC48AC2D2A56A0A2005B7D5C /* TKZFReachabilityManager.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKZFReachabilityManager.m; sourceTree = "<group>"; };
		DC48AC2E2A56A0A2005B7D5C /* TKZFUtilities.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKZFUtilities.h; sourceTree = "<group>"; };
		DC48AC2F2A56A0A2005B7D5C /* TKZFKVOController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKZFKVOController.m; sourceTree = "<group>"; };
		DC48AC302A56A0A2005B7D5C /* TKZFPlayerLogManager.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKZFPlayerLogManager.m; sourceTree = "<group>"; };
		DC48AC312A56A0A2005B7D5C /* TKZFReachabilityManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKZFReachabilityManager.h; sourceTree = "<group>"; };
		DC48AC322A56A0A2005B7D5C /* TKZFNetworkSpeedMonitor.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKZFNetworkSpeedMonitor.m; sourceTree = "<group>"; };
		DC48AC332A56A0A2005B7D5C /* TKZFPlayerNotification.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKZFPlayerNotification.h; sourceTree = "<group>"; };
		DC48AC342A56A0A2005B7D5C /* TKZFKVOController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKZFKVOController.h; sourceTree = "<group>"; };
		DC48AC352A56A0A2005B7D5C /* TKZFUtilities.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKZFUtilities.m; sourceTree = "<group>"; };
		DC48AC372A56A0A2005B7D5C /* TKZFPlayerGestureControl.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKZFPlayerGestureControl.h; sourceTree = "<group>"; };
		DC48AC3A2A56A0A2005B7D5C /* TKZFSpeedLoadingView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKZFSpeedLoadingView.m; sourceTree = "<group>"; };
		DC48AC3B2A56A0A2005B7D5C /* TKZFLoadingView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKZFLoadingView.h; sourceTree = "<group>"; };
		DC48AC3C2A56A0A2005B7D5C /* TKZFSpeedLoadingView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKZFSpeedLoadingView.h; sourceTree = "<group>"; };
		DC48AC3D2A56A0A2005B7D5C /* TKZFLoadingView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKZFLoadingView.m; sourceTree = "<group>"; };
		DC48AC3F2A56A0A2005B7D5C /* TKSpeedSelectView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKSpeedSelectView.m; sourceTree = "<group>"; };
		DC48AC402A56A0A2005B7D5C /* TKSpeedSelectView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKSpeedSelectView.h; sourceTree = "<group>"; };
		DC48AC422A56A0A2005B7D5C /* TKZFVolumeBrightnessView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKZFVolumeBrightnessView.m; sourceTree = "<group>"; };
		DC48AC432A56A0A2005B7D5C /* TKZFVolumeBrightnessView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKZFVolumeBrightnessView.h; sourceTree = "<group>"; };
		DC48AC452A56A0A2005B7D5C /* TKZFPlayerStatusBar.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKZFPlayerStatusBar.h; sourceTree = "<group>"; };
		DC48AC462A56A0A2005B7D5C /* TKZFPlayerStatusBar.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKZFPlayerStatusBar.m; sourceTree = "<group>"; };
		DC48AC472A56A0A2005B7D5C /* TKZFPlayerView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKZFPlayerView.m; sourceTree = "<group>"; };
		DC48AC492A56A0A2005B7D5C /* TKFragmentVideoView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKFragmentVideoView.h; sourceTree = "<group>"; };
		DC48AC4A2A56A0A2005B7D5C /* TKFragmentTableViewCell.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKFragmentTableViewCell.h; sourceTree = "<group>"; };
		DC48AC4B2A56A0A2005B7D5C /* TKVideoFragmentModel.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKVideoFragmentModel.m; sourceTree = "<group>"; };
		DC48AC4C2A56A0A2005B7D5C /* TKFragmentVideoView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKFragmentVideoView.m; sourceTree = "<group>"; };
		DC48AC4D2A56A0A2005B7D5C /* TKFragmentTableViewCell.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKFragmentTableViewCell.m; sourceTree = "<group>"; };
		DC48AC4E2A56A0A2005B7D5C /* TKVideoFragmentModel.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKVideoFragmentModel.h; sourceTree = "<group>"; };
		DC48AC502A56A0A2005B7D5C /* TKPlayerControlView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKPlayerControlView.h; sourceTree = "<group>"; };
		DC48AC512A56A0A2005B7D5C /* TKZFLandScapeControlView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKZFLandScapeControlView.m; sourceTree = "<group>"; };
		DC48AC522A56A0A2005B7D5C /* TKZFPortraitControlView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKZFPortraitControlView.m; sourceTree = "<group>"; };
		DC48AC532A56A0A2005B7D5C /* TKZFPortraitControlView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKZFPortraitControlView.h; sourceTree = "<group>"; };
		DC48AC542A56A0A2005B7D5C /* TKPlayerControlView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKPlayerControlView.m; sourceTree = "<group>"; };
		DC48AC552A56A0A2005B7D5C /* TKZFLandScapeControlView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKZFLandScapeControlView.h; sourceTree = "<group>"; };
		DC48AC572A56A0A2005B7D5C /* TKZFSmallFloatControlView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKZFSmallFloatControlView.m; sourceTree = "<group>"; };
		DC48AC582A56A0A2005B7D5C /* TKZFFloatView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKZFFloatView.m; sourceTree = "<group>"; };
		DC48AC592A56A0A2005B7D5C /* TKZFSmallFloatControlView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKZFSmallFloatControlView.h; sourceTree = "<group>"; };
		DC48AC5A2A56A0A2005B7D5C /* TKZFFloatView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKZFFloatView.h; sourceTree = "<group>"; };
		DC48AC5B2A56A0A2005B7D5C /* TKZFPlayerView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKZFPlayerView.h; sourceTree = "<group>"; };
		DC48AC5D2A56A0A2005B7D5C /* TKZFSliderView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKZFSliderView.m; sourceTree = "<group>"; };
		DC48AC5E2A56A0A2005B7D5C /* TKZFSliderView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKZFSliderView.h; sourceTree = "<group>"; };
		DC48AC5F2A56A0A2005B7D5C /* TKZFPlayerConst.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKZFPlayerConst.h; sourceTree = "<group>"; };
		DC48AC602A56A0A2005B7D5C /* TKZFPlayerController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKZFPlayerController.m; sourceTree = "<group>"; };
		DC48AC622A56A0A2005B7D5C /* TKZFPlayerMediaControl.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKZFPlayerMediaControl.h; sourceTree = "<group>"; };
		DC48AC632A56A0A2005B7D5C /* TKZFPlayerMediaPlayback.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKZFPlayerMediaPlayback.h; sourceTree = "<group>"; };
		DC48AC642A56A0A2005B7D5C /* TKPlayer.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKPlayer.m; sourceTree = "<group>"; };
		DC6758AB294AEFA6009771ED /* TKOpenPlugin60091.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = TKOpenPlugin60091.h; sourceTree = "<group>"; };
		DC6758AC294AEFA6009771ED /* TKOpenPlugin60091.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = TKOpenPlugin60091.m; sourceTree = "<group>"; };
		DC6758AF294AF065009771ED /* TKDoubleOrdinaryVideoViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = TKDoubleOrdinaryVideoViewController.h; sourceTree = "<group>"; };
		DC6758B0294AF065009771ED /* TKDoubleOrdinaryVideoViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = TKDoubleOrdinaryVideoViewController.m; sourceTree = "<group>"; };
		DC6758B3294AF380009771ED /* TKDoubleOrdinaryVideoEndView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = TKDoubleOrdinaryVideoEndView.h; sourceTree = "<group>"; };
		DC6758B4294AF380009771ED /* TKDoubleOrdinaryVideoEndView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = TKDoubleOrdinaryVideoEndView.m; sourceTree = "<group>"; };
		DC6758B6294AF8F8009771ED /* TKDoubleOrdinaryVideoView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = TKDoubleOrdinaryVideoView.h; sourceTree = "<group>"; };
		DC6758B7294AF8F8009771ED /* TKDoubleOrdinaryVideoView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = TKDoubleOrdinaryVideoView.m; sourceTree = "<group>"; };
		DC6CDB852650F20D004E7295 /* TKOpenPlugin60059.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = TKOpenPlugin60059.h; sourceTree = "<group>"; };
		DC6CDB862650F20D004E7295 /* TKOpenPlugin60059.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = TKOpenPlugin60059.m; sourceTree = "<group>"; };
		DC6CDB8E2650F289004E7295 /* TKChatLiveDetectViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = TKChatLiveDetectViewController.h; sourceTree = "<group>"; };
		DC6CDB8F2650F289004E7295 /* TKChatLiveDetectViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = TKChatLiveDetectViewController.m; sourceTree = "<group>"; };
		DC6CDB952650FF68004E7295 /* TKChatLiveFaceView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = TKChatLiveFaceView.h; sourceTree = "<group>"; };
		DC6CDB962650FF68004E7295 /* TKChatLiveFaceView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = TKChatLiveFaceView.m; sourceTree = "<group>"; };
		DC6F5D01262EB34400B87A5B /* TKChatVideoRecordEndView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = TKChatVideoRecordEndView.h; sourceTree = "<group>"; };
		DC6F5D02262EB34400B87A5B /* TKChatVideoRecordEndView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = TKChatVideoRecordEndView.m; sourceTree = "<group>"; };
		DC6F5D07262EB49800B87A5B /* TKChatVideoRecordView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = TKChatVideoRecordView.h; sourceTree = "<group>"; };
		DC6F5D08262EB49800B87A5B /* TKChatVideoRecordView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = TKChatVideoRecordView.m; sourceTree = "<group>"; };
		DC84D1552857290700941BF5 /* TKSmartQuestionModel.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKSmartQuestionModel.m; sourceTree = "<group>"; };
		DC84D1562857290700941BF5 /* TKSmartQuestionModel.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKSmartQuestionModel.h; sourceTree = "<group>"; };
		DC84D1592857290700941BF5 /* TKBaseVideoRecordViewProtocol.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKBaseVideoRecordViewProtocol.h; sourceTree = "<group>"; };
		DC84D15A2857290700941BF5 /* TKBaseVideoRecordEndViewProtocol.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKBaseVideoRecordEndViewProtocol.h; sourceTree = "<group>"; };
		************************ /* TKBaseVideoRecordViewController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKBaseVideoRecordViewController.h; sourceTree = "<group>"; };
		************************ /* TKBaseVideoRecordViewController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKBaseVideoRecordViewController.m; sourceTree = "<group>"; };
		DCA22A25295AF8AA005C6688 /* TKChatRtcVideoRecordManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = TKChatRtcVideoRecordManager.m; sourceTree = "<group>"; };
		DCA22A26295AF8AA005C6688 /* TKChatRtcVideoRecordManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = TKChatRtcVideoRecordManager.h; sourceTree = "<group>"; };
		DCC0429529434D1900BDF14D /* TKOpenPlugin60077.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKOpenPlugin60077.m; sourceTree = "<group>"; };
		DCC0429729434D1900BDF14D /* TKDoubleChatVideoRecordViewController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKDoubleChatVideoRecordViewController.h; sourceTree = "<group>"; };
		DCC0429829434D1900BDF14D /* TKDoubleChatVideoRecordViewController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKDoubleChatVideoRecordViewController.m; sourceTree = "<group>"; };
		DCC0429A29434D1900BDF14D /* TKDoubleChatVideoRecordView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKDoubleChatVideoRecordView.m; sourceTree = "<group>"; };
		DCC0429B29434D1900BDF14D /* TKDoubleChatVideoRecordEndView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKDoubleChatVideoRecordEndView.h; sourceTree = "<group>"; };
		DCC0429C29434D1900BDF14D /* TKDoubleChatVideoRecordEndView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKDoubleChatVideoRecordEndView.m; sourceTree = "<group>"; };
		DCC0429D29434D1900BDF14D /* TKDoubleChatVideoRecordView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKDoubleChatVideoRecordView.h; sourceTree = "<group>"; };
		DCC0429E29434D1900BDF14D /* TKOpenPlugin60077.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKOpenPlugin60077.h; sourceTree = "<group>"; };
		DCC042A82947430200BDF14D /* TKOpenPlugin60089.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKOpenPlugin60089.m; sourceTree = "<group>"; };
		DCC042AA2947430200BDF14D /* TKOpenPlugin60089.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKOpenPlugin60089.h; sourceTree = "<group>"; };
		DCC042AD294744A900BDF14D /* TKDoudleOneWayVideoViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = TKDoudleOneWayVideoViewController.h; sourceTree = "<group>"; };
		DCC042AE294744A900BDF14D /* TKDoudleOneWayVideoViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = TKDoudleOneWayVideoViewController.m; sourceTree = "<group>"; };
		DCC042B02948161200BDF14D /* TKDoubleOneWayVideoView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = TKDoubleOneWayVideoView.h; sourceTree = "<group>"; };
		DCC042B12948161200BDF14D /* TKDoubleOneWayVideoView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = TKDoubleOneWayVideoView.m; sourceTree = "<group>"; };
		DCC042B32948166800BDF14D /* TKDoubleOneWayVideoEndView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = TKDoubleOneWayVideoEndView.h; sourceTree = "<group>"; };
		DCC042B42948166800BDF14D /* TKDoubleOneWayVideoEndView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = TKDoubleOneWayVideoEndView.m; sourceTree = "<group>"; };
		DCC262C0291E1C8400018C4E /* TKBaseVideoRecordLandscapeView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = TKBaseVideoRecordLandscapeView.h; sourceTree = "<group>"; };
		DCC262C1291E1C8400018C4E /* TKBaseVideoRecordLandscapeView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = TKBaseVideoRecordLandscapeView.m; sourceTree = "<group>"; };
		DCC5CE502B207FF7001C82A6 /* TKReadingView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKReadingView.m; sourceTree = "<group>"; };
		DCC5CE512B207FF7001C82A6 /* TKReadingView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKReadingView.h; sourceTree = "<group>"; };
		DCC7E7E126299A56006D378F /* TKOpenPlugin60057.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = TKOpenPlugin60057.h; sourceTree = "<group>"; };
		DCC7E7E226299A56006D378F /* TKOpenPlugin60057.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = TKOpenPlugin60057.m; sourceTree = "<group>"; };
		DCC7E7E8262A9B92006D378F /* TKChatVideoRecordViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = TKChatVideoRecordViewController.h; sourceTree = "<group>"; };
		DCC7E7E9262A9B92006D378F /* TKChatVideoRecordViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = TKChatVideoRecordViewController.m; sourceTree = "<group>"; };
		DCC7E868262D7133006D378F /* TKTempService.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKTempService.m; sourceTree = "<group>"; };
		DCC7E86A262D7133006D378F /* TKTempService.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKTempService.h; sourceTree = "<group>"; };
		DCCDDA19295C2AF5002F268B /* TKFaceDectTipView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = TKFaceDectTipView.h; sourceTree = "<group>"; };
		DCCDDA1A295C2AF5002F268B /* TKFaceDectTipView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = TKFaceDectTipView.m; sourceTree = "<group>"; };
		DCD47D3128E18E1600AE44C6 /* TKBaseVideoRecordEndView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKBaseVideoRecordEndView.m; sourceTree = "<group>"; };
		DCD47D3228E18E1600AE44C6 /* TKBaseVideoRecordView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKBaseVideoRecordView.m; sourceTree = "<group>"; };
		DCD47D3328E18E1600AE44C6 /* TKBaseVideoRecordEndView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKBaseVideoRecordEndView.h; sourceTree = "<group>"; };
		DCD47D3428E18E1600AE44C6 /* TKBaseVideoRecordView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKBaseVideoRecordView.h; sourceTree = "<group>"; };
		DCD47D4328E1B18D00AE44C6 /* TKOpenPlugin60072.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKOpenPlugin60072.h; sourceTree = "<group>"; };
		DCD47D4528E1B18D00AE44C6 /* TKSmartVirtualManViewController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKSmartVirtualManViewController.h; sourceTree = "<group>"; };
		DCD47D4628E1B18D00AE44C6 /* TKSmartVirtualManViewController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKSmartVirtualManViewController.m; sourceTree = "<group>"; };
		DCD47D4728E1B18D00AE44C6 /* TKOpenPlugin60072.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKOpenPlugin60072.m; sourceTree = "<group>"; };
		DCD47D4A28E1B18D00AE44C6 /* TKSmartVirtualManVideoView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKSmartVirtualManVideoView.h; sourceTree = "<group>"; };
		DCD47D4B28E1B18D00AE44C6 /* TKSmartVirtualManVideoEndView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKSmartVirtualManVideoEndView.m; sourceTree = "<group>"; };
		DCD47D4C28E1B18D00AE44C6 /* TKSmartVirtualManVideoView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKSmartVirtualManVideoView.m; sourceTree = "<group>"; };
		DCD47D4D28E1B18D00AE44C6 /* TKSmartVirtualManVideoEndView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKSmartVirtualManVideoEndView.h; sourceTree = "<group>"; };
		DCEAC82529B1E5FB00581544 /* TKStatisticEventHelper.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKStatisticEventHelper.h; sourceTree = "<group>"; };
		DCEAC82629B1E5FB00581544 /* TKStatisticEventHelperPrivate.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKStatisticEventHelperPrivate.h; sourceTree = "<group>"; };
		DCEAC82729B1E5FB00581544 /* TKStatisticEventHelper.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKStatisticEventHelper.m; sourceTree = "<group>"; };
		DCECF95B292F5EEF00A802EE /* TKSVGEngine.mm */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.objcpp; path = TKSVGEngine.mm; sourceTree = "<group>"; };
		DCECF95C292F5EF000A802EE /* TKSVGLayer.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKSVGLayer.m; sourceTree = "<group>"; };
		DCECF95D292F5EF000A802EE /* TKSVGBezierPath.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKSVGBezierPath.h; sourceTree = "<group>"; };
		DCECF95E292F5EF000A802EE /* TKSVGBezierPath.mm */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.objcpp; path = TKSVGBezierPath.mm; sourceTree = "<group>"; };
		DCECF961292F5EF000A802EE /* TKSVGEngine.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKSVGEngine.h; sourceTree = "<group>"; };
		DCECF963292F5EF000A802EE /* TKSVGImageView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKSVGImageView.h; sourceTree = "<group>"; };
		DCECF964292F5EF000A802EE /* TKSVGImageView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TKSVGImageView.m; sourceTree = "<group>"; };
		DCECF966292F5EF000A802EE /* TKSVGLayer.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TKSVGLayer.h; sourceTree = "<group>"; };
		DCECF96C292F628A00A802EE /* TKSVGImage.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = TKSVGImage.h; sourceTree = "<group>"; };
		DCECF96D292F628A00A802EE /* TKSVGImage.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = TKSVGImage.m; sourceTree = "<group>"; };
		E22996F326AABD490039F6E9 /* SelectViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SelectViewController.h; sourceTree = "<group>"; };
		E22996F426AABD490039F6E9 /* SelectViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SelectViewController.m; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		B3FACBAE1AFDDF0E0088CDF1 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				33C207AE2A147ACC00087D13 /* libc++.tbd in Frameworks */,
				3363023E2D83C202009BC4DC /* OAuth.xcframework in Frameworks */,
				33C207AC2A147A5100087D13 /* GLKit.framework in Frameworks */,
				18BBEF4F28A24AC100A26825 /* WebKit.framework in Frameworks */,
				187942C8255CD0AC0067C701 /* AdSupport.framework in Frameworks */,
				04441AA0221691F300CFDC2D /* VideoToolbox.framework in Frameworks */,
				B39E9313208F05CD006EC8A3 /* Accelerate.framework in Frameworks */,
				B39E9311208F054A006EC8A3 /* MessageUI.framework in Frameworks */,
				B39E930F208F0532006EC8A3 /* CoreMotion.framework in Frameworks */,
				336BA7DA29BF2A1000134194 /* ISOpenSDKFoundation.framework in Frameworks */,
				DCE005252A961A29007D1C17 /* TChat.framework in Frameworks */,
				B379D393206CD57000D1440E /* CoreTelephony.framework in Frameworks */,
				188DB0C026245E9700F6732D /* FinApplet.framework in Frameworks */,
				B33FDC121F692BE400C644F2 /* libresolv.tbd in Frameworks */,
				338D7FE22B312BDB0030979E /* QCloudTTS.xcframework in Frameworks */,
				B33FDC101F692BCB00C644F2 /* libxml2.tbd in Frameworks */,
				338D7FE42B312C020030979E /* QCloudRealTime.xcframework in Frameworks */,
				B3C6ADC91C92ADEA00FE95F0 /* CoreMedia.framework in Frameworks */,
				B37F84291BE365230016F93D /* OpenGLES.framework in Frameworks */,
				336BA7FF29BF2A1000134194 /* ISBankCard.framework in Frameworks */,
				049A8359211ECD6B00AA2048 /* libcrypto.a in Frameworks */,
				045ACEA622A4FC30004D8557 /* TKWebViewApp.framework in Frameworks */,
				B3FEEB361B845D6D00468924 /* AVFoundation.framework in Frameworks */,
				33C502B32A0A4D890050DB0C /* STLivenessDetector.xcframework in Frameworks */,
				B39BADD01B4D524F00F071D4 /* CoreLocation.framework in Frameworks */,
				B3B108841B44DF2800546D96 /* libiconv.dylib in Frameworks */,
				B3C6ADC71C92ADDD00FE95F0 /* AssetsLibrary.framework in Frameworks */,
				33C502AF2A0A4D5F0050DB0C /* nuisdk.framework in Frameworks */,
				B3FACBF31AFDE3940088CDF1 /* libc++.dylib in Frameworks */,
				B3FACBF11AFDE3800088CDF1 /* libz.1.2.5.dylib in Frameworks */,
				337A9F0E2A37062500CE0434 /* AnyChatCoreSDK.framework in Frameworks */,
				B3FACBEF1AFDE3750088CDF1 /* libsqlite3.dylib in Frameworks */,
				B3FACBEB1AFDE3460088CDF1 /* CoreGraphics.framework in Frameworks */,
				B3FACBE91AFDE33A0088CDF1 /* Foundation.framework in Frameworks */,
				336BA7B029BF2A0A00134194 /* ISIDReaderPreviewSDK.framework in Frameworks */,
				188FC5B72468F43300F7D2A3 /* ZXJTBaseFramework.framework in Frameworks */,
				B3FACBE71AFDE31A0088CDF1 /* SystemConfiguration.framework in Frameworks */,
				336BA7AF29BF2A0A00134194 /* opencv3.4.2.a in Frameworks */,
				B3FACBE51AFDE30B0088CDF1 /* CFNetwork.framework in Frameworks */,
				049A835A211ECD6B00AA2048 /* libssl.a in Frameworks */,
				33DAFAA02C61E8C6009F2243 /* iflyMSC.framework in Frameworks */,
				B3FACBE11AFDE2F40088CDF1 /* Security.framework in Frameworks */,
				B3FACBDF1AFDE2E50088CDF1 /* QuartzCore.framework in Frameworks */,
				B3FACBDD1AFDE2D10088CDF1 /* UIKit.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		043015E823FCC755004F0C17 /* TKOpenPlugin60028 */ = {
			isa = PBXGroup;
			children = (
				043015F023FCCDBE004F0C17 /* View */,
				043015E923FCC755004F0C17 /* Controller */,
				043015EA23FCC811004F0C17 /* TKOpenPlugin60028.h */,
				043015EB23FCC811004F0C17 /* TKOpenPlugin60028.m */,
			);
			path = TKOpenPlugin60028;
			sourceTree = "<group>";
		};
		043015E923FCC755004F0C17 /* Controller */ = {
			isa = PBXGroup;
			children = (
				187F35F926AAB44C0046E9E3 /* TKFaceImageLandscapeViewController.h */,
				187F35FA26AAB44C0046E9E3 /* TKFaceImageLandscapeViewController.m */,
				043015ED23FCC936004F0C17 /* TKFaceImageViewController.h */,
				043015EE23FCC936004F0C17 /* TKFaceImageViewController.m */,
			);
			path = Controller;
			sourceTree = "<group>";
		};
		043015F023FCCDBE004F0C17 /* View */ = {
			isa = PBXGroup;
			children = (
				187F35FE26AAB4530046E9E3 /* TKFaceImageLandscapeView.h */,
				187F35FF26AAB4530046E9E3 /* TKFaceImageLandscapeView.m */,
				043015F223FCCDBE004F0C17 /* TKFaceImageView.h */,
				043015F123FCCDBE004F0C17 /* TKFaceImageView.m */,
			);
			path = View;
			sourceTree = "<group>";
		};
		045EF85F241BA74F00032B22 /* TKOpenPlugin60032 */ = {
			isa = PBXGroup;
			children = (
				1876A14B2949CF57007F0500 /* Controller */,
				045EF860241BA81000032B22 /* TKOpenPlugin60032.h */,
				045EF861241BA81000032B22 /* TKOpenPlugin60032.m */,
			);
			path = TKOpenPlugin60032;
			sourceTree = "<group>";
		};
		045EF863241BAEE400032B22 /* TKOpenPlugin60033 */ = {
			isa = PBXGroup;
			children = (
				045EF864241BAF0100032B22 /* TKOpenPlugin60033.h */,
				045EF865241BAF0100032B22 /* TKOpenPlugin60033.m */,
			);
			path = TKOpenPlugin60033;
			sourceTree = "<group>";
		};
		0465234B212E84C000F8C0D0 /* TKOpenPlugin60022 */ = {
			isa = PBXGroup;
			children = (
				0465234C212E84C000F8C0D0 /* controllers */,
				0465234F212E84C000F8C0D0 /* TKOpenPlugin60022.h */,
				04652350212E84C000F8C0D0 /* TKOpenPlugin60022.m */,
				04652351212E84C000F8C0D0 /* views */,
			);
			path = TKOpenPlugin60022;
			sourceTree = "<group>";
		};
		0465234C212E84C000F8C0D0 /* controllers */ = {
			isa = PBXGroup;
			children = (
				0465234D212E84C000F8C0D0 /* TKSignatureController.h */,
				0465234E212E84C000F8C0D0 /* TKSignatureController.m */,
			);
			path = controllers;
			sourceTree = "<group>";
		};
		04652351212E84C000F8C0D0 /* views */ = {
			isa = PBXGroup;
			children = (
				04652352212E84C000F8C0D0 /* TKTouchView.h */,
				04652353212E84C000F8C0D0 /* TKTouchView.m */,
			);
			path = views;
			sourceTree = "<group>";
		};
		0472AC9623580798008FC27E /* View */ = {
			isa = PBXGroup;
			children = (
				18714D8724454DE7002D809B /* TKVideoMsgTableViewCell.h */,
				18714D8624454DE7002D809B /* TKVideoMsgTableViewCell.m */,
				0472AC9723580798008FC27E /* TKOpenQueueView.h */,
				0472AC9823580798008FC27E /* TKOpenQueueView.m */,
				0472AC9923580798008FC27E /* TKOpenVideoChatView.h */,
				0472AC9A23580798008FC27E /* TKOpenVideoChatView.m */,
				1891C4802486148300C54EFF /* TKVideoReadAgreeView.h */,
				1891C47F2486148300C54EFF /* TKVideoReadAgreeView.m */,
				333DD1C02E24CF7E00DABD89 /* TKVideoRollTextView.h */,
				333DD1C12E24CF7E00DABD89 /* TKVideoRollTextView.m */,
			);
			path = View;
			sourceTree = "<group>";
		};
		0472CA7A23FB9DD4000B5EB7 /* TKOpenPlugin60007 */ = {
			isa = PBXGroup;
			children = (
				0472CA8723FB9E50000B5EB7 /* Controller */,
				0472CA8823FB9E50000B5EB7 /* View */,
				0472CA8023FB9DD4000B5EB7 /* TKOpenPlugin60007.h */,
				0472CA8123FB9DD4000B5EB7 /* TKOpenPlugin60007.m */,
			);
			path = TKOpenPlugin60007;
			sourceTree = "<group>";
		};
		0472CA8723FB9E50000B5EB7 /* Controller */ = {
			isa = PBXGroup;
			children = (
				0472CA8923FB9EC4000B5EB7 /* TKLiveFaceViewController.h */,
				0472CA8A23FB9EC4000B5EB7 /* TKLiveFaceViewController.m */,
			);
			path = Controller;
			sourceTree = "<group>";
		};
		0472CA8823FB9E50000B5EB7 /* View */ = {
			isa = PBXGroup;
			children = (
				0472CA8D23FBA05F000B5EB7 /* TKLiveFaceView.h */,
				0472CA8C23FBA05F000B5EB7 /* TKLiveFaceView.m */,
			);
			path = View;
			sourceTree = "<group>";
		};
		048DC98E2407E66400A6F3EC /* TKOpenPlugin60030 */ = {
			isa = PBXGroup;
			children = (
				048DC9922407E66400A6F3EC /* View */,
				048DC98F2407E66400A6F3EC /* Controller */,
				048DC9902407E66400A6F3EC /* TKOpenPlugin60030.h */,
				048DC9912407E66400A6F3EC /* TKOpenPlugin60030.m */,
			);
			path = TKOpenPlugin60030;
			sourceTree = "<group>";
		};
		048DC98F2407E66400A6F3EC /* Controller */ = {
			isa = PBXGroup;
			children = (
				048DC9972407E6EF00A6F3EC /* TKOrdinaryOneVideoViewController.h */,
				048DC9982407E6EF00A6F3EC /* TKOrdinaryOneVideoViewController.m */,
			);
			path = Controller;
			sourceTree = "<group>";
		};
		048DC9922407E66400A6F3EC /* View */ = {
			isa = PBXGroup;
			children = (
				181D4C9F286E94F600679EB3 /* TKVideoAlertView.h */,
				181D4CA0286E94F600679EB3 /* TKVideoAlertView.m */,
				18F12A1325FF24E000F26F5C /* TKOneWayVideoAlertTipView.h */,
				18F12A1125FF24E000F26F5C /* TKOneWayVideoAlertTipView.m */,
				18F129F525FF231600F26F5C /* TKOpenTipView.h */,
				18F129F725FF231600F26F5C /* TKOpenTipView.m */,
				042CE7032408DD7900B9AC15 /* TKOrdinaryOneVideoEndView.h */,
				042CE7042408DD7B00B9AC15 /* TKOrdinaryOneVideoEndView.m */,
				048DC9932407E66400A6F3EC /* TKOrdinaryOneVideoView.h */,
				048DC9942407E66400A6F3EC /* TKOrdinaryOneVideoView.m */,
				18034322291CD426002C8E2C /* TKPlayerToolView.h */,
				18034323291CD426002C8E2C /* TKPlayerToolView.m */,
			);
			path = View;
			sourceTree = "<group>";
		};
		049A82E7211ECD6B00AA2048 /* THFMWK */ = {
			isa = PBXGroup;
			children = (
				049A82FE211ECD6B00AA2048 /* OpensslSDK */,
			);
			path = THFMWK;
			sourceTree = "<group>";
		};
		049A82FE211ECD6B00AA2048 /* OpensslSDK */ = {
			isa = PBXGroup;
			children = (
				049A82FF211ECD6B00AA2048 /* include */,
				049A834C211ECD6B00AA2048 /* libs */,
			);
			path = OpensslSDK;
			sourceTree = "<group>";
		};
		049A82FF211ECD6B00AA2048 /* include */ = {
			isa = PBXGroup;
			children = (
				049A8300211ECD6B00AA2048 /* openssl */,
			);
			path = include;
			sourceTree = "<group>";
		};
		049A8300211ECD6B00AA2048 /* openssl */ = {
			isa = PBXGroup;
			children = (
				049A8301211ECD6B00AA2048 /* aes.h */,
				049A8302211ECD6B00AA2048 /* asn1.h */,
				049A8303211ECD6B00AA2048 /* asn1_mac.h */,
				049A8304211ECD6B00AA2048 /* asn1t.h */,
				049A8305211ECD6B00AA2048 /* bio.h */,
				049A8306211ECD6B00AA2048 /* blowfish.h */,
				049A8307211ECD6B00AA2048 /* bn.h */,
				049A8308211ECD6B00AA2048 /* buffer.h */,
				049A8309211ECD6B00AA2048 /* camellia.h */,
				049A830A211ECD6B00AA2048 /* cast.h */,
				049A830B211ECD6B00AA2048 /* cmac.h */,
				049A830C211ECD6B00AA2048 /* cms.h */,
				049A830D211ECD6B00AA2048 /* comp.h */,
				049A830E211ECD6B00AA2048 /* conf.h */,
				049A830F211ECD6B00AA2048 /* conf_api.h */,
				049A8310211ECD6B00AA2048 /* crypto.h */,
				049A8311211ECD6B00AA2048 /* des.h */,
				049A8312211ECD6B00AA2048 /* des_old.h */,
				049A8313211ECD6B00AA2048 /* dh.h */,
				049A8314211ECD6B00AA2048 /* dsa.h */,
				049A8315211ECD6B00AA2048 /* dso.h */,
				049A8316211ECD6B00AA2048 /* dtls1.h */,
				049A8317211ECD6B00AA2048 /* e_os2.h */,
				049A8318211ECD6B00AA2048 /* ebcdic.h */,
				049A8319211ECD6B00AA2048 /* ec.h */,
				049A831A211ECD6B00AA2048 /* ecdh.h */,
				049A831B211ECD6B00AA2048 /* ecdsa.h */,
				049A831C211ECD6B00AA2048 /* engine.h */,
				049A831D211ECD6B00AA2048 /* err.h */,
				049A831E211ECD6B00AA2048 /* evp.h */,
				049A831F211ECD6B00AA2048 /* hmac.h */,
				049A8320211ECD6B00AA2048 /* idea.h */,
				049A8321211ECD6B00AA2048 /* krb5_asn.h */,
				049A8322211ECD6B00AA2048 /* kssl.h */,
				049A8323211ECD6B00AA2048 /* lhash.h */,
				049A8324211ECD6B00AA2048 /* md4.h */,
				049A8325211ECD6B00AA2048 /* md5.h */,
				049A8326211ECD6B00AA2048 /* mdc2.h */,
				049A8327211ECD6B00AA2048 /* modes.h */,
				049A8328211ECD6B00AA2048 /* obj_mac.h */,
				049A8329211ECD6B00AA2048 /* objects.h */,
				049A832A211ECD6B00AA2048 /* ocsp.h */,
				049A832B211ECD6B00AA2048 /* opensslconf.h */,
				049A832C211ECD6B00AA2048 /* opensslv.h */,
				049A832D211ECD6B00AA2048 /* ossl_typ.h */,
				049A832E211ECD6B00AA2048 /* pem.h */,
				049A832F211ECD6B00AA2048 /* pem2.h */,
				049A8330211ECD6B00AA2048 /* pkcs12.h */,
				049A8331211ECD6B00AA2048 /* pkcs7.h */,
				049A8332211ECD6B00AA2048 /* pqueue.h */,
				049A8333211ECD6B00AA2048 /* rand.h */,
				049A8334211ECD6B00AA2048 /* rc2.h */,
				049A8335211ECD6B00AA2048 /* rc4.h */,
				049A8336211ECD6B00AA2048 /* ripemd.h */,
				049A8337211ECD6B00AA2048 /* rsa.h */,
				049A8338211ECD6B00AA2048 /* safestack.h */,
				049A8339211ECD6B00AA2048 /* seed.h */,
				049A833A211ECD6B00AA2048 /* sha.h */,
				049A833B211ECD6B00AA2048 /* srp.h */,
				049A833C211ECD6B00AA2048 /* srtp.h */,
				049A833D211ECD6B00AA2048 /* ssl.h */,
				049A833E211ECD6B00AA2048 /* ssl2.h */,
				049A833F211ECD6B00AA2048 /* ssl23.h */,
				049A8340211ECD6B00AA2048 /* ssl3.h */,
				049A8341211ECD6B00AA2048 /* stack.h */,
				049A8342211ECD6B00AA2048 /* symhacks.h */,
				049A8343211ECD6B00AA2048 /* tls1.h */,
				049A8344211ECD6B00AA2048 /* ts.h */,
				049A8345211ECD6B00AA2048 /* txt_db.h */,
				049A8346211ECD6B00AA2048 /* ui.h */,
				049A8347211ECD6B00AA2048 /* ui_compat.h */,
				049A8348211ECD6B00AA2048 /* whrlpool.h */,
				049A8349211ECD6B00AA2048 /* x509.h */,
				049A834A211ECD6B00AA2048 /* x509_vfy.h */,
				049A834B211ECD6B00AA2048 /* x509v3.h */,
			);
			path = openssl;
			sourceTree = "<group>";
		};
		049A834C211ECD6B00AA2048 /* libs */ = {
			isa = PBXGroup;
			children = (
				049A834D211ECD6B00AA2048 /* libcrypto.a */,
				049A834E211ECD6B00AA2048 /* libssl.a */,
			);
			path = libs;
			sourceTree = "<group>";
		};
		04BC08AA2383B7B400BF4179 /* QCloud_TTS */ = {
			isa = PBXGroup;
			children = (
				338D7FE12B312BDB0030979E /* QCloudTTS.xcframework */,
			);
			path = QCloud_TTS;
			sourceTree = "<group>";
		};
		04C9121D2382304300BE5C63 /* QCloudSDK */ = {
			isa = PBXGroup;
			children = (
				338D7FE32B312C020030979E /* QCloudRealTime.xcframework */,
			);
			path = QCloudSDK;
			sourceTree = "<group>";
		};
		04E5B284241CA6D30069BCEE /* TKOpenPlugin60034 */ = {
			isa = PBXGroup;
			children = (
				04E5B285241CA6D30069BCEE /* Controller */,
				04E5B286241CA6D30069BCEE /* Helper */,
				04E5B287241CA6D30069BCEE /* View */,
				04E5B288241CA7CD0069BCEE /* TKOpenPlugin60034.h */,
				04E5B289241CA7CD0069BCEE /* TKOpenPlugin60034.m */,
			);
			path = TKOpenPlugin60034;
			sourceTree = "<group>";
		};
		04E5B285241CA6D30069BCEE /* Controller */ = {
			isa = PBXGroup;
			children = (
				04E5B28B241CA9E20069BCEE /* TKDirectVideoViewController.h */,
				04E5B28C241CA9E20069BCEE /* TKDirectVideoViewController.m */,
			);
			path = Controller;
			sourceTree = "<group>";
		};
		04E5B286241CA6D30069BCEE /* Helper */ = {
			isa = PBXGroup;
			children = (
				04E5B29A241CB4030069BCEE /* TKDirectVideoModel.h */,
				04E5B29B241CB4030069BCEE /* TKDirectVideoModel.m */,
			);
			path = Helper;
			sourceTree = "<group>";
		};
		04E5B287241CA6D30069BCEE /* View */ = {
			isa = PBXGroup;
			children = (
				04E5B28E241CABE20069BCEE /* TKDirectVideoChatView.h */,
				04E5B28F241CABE20069BCEE /* TKDirectVideoChatView.m */,
			);
			path = View;
			sourceTree = "<group>";
		};
		1813893D244B269A007F96FA /* TKOpenPlugin60037 */ = {
			isa = PBXGroup;
			children = (
				18138942244B269A007F96FA /* TKOpenPlugin60037.h */,
				1813893E244B269A007F96FA /* TKOpenPlugin60037.m */,
				1813893F244B269A007F96FA /* Controller */,
			);
			path = TKOpenPlugin60037;
			sourceTree = "<group>";
		};
		1813893F244B269A007F96FA /* Controller */ = {
			isa = PBXGroup;
			children = (
				18138940244B269A007F96FA /* TKTakeBankPhotoViewController.h */,
				18138941244B269A007F96FA /* TKTakeBankPhotoViewController.m */,
			);
			path = Controller;
			sourceTree = "<group>";
		};
		182CA7DB25BA6B820084C9F8 /* TKOpenPlugin60044 */ = {
			isa = PBXGroup;
			children = (
				182CA7DC25BA6B820084C9F8 /* TKOpenPlugin60044.h */,
				182CA7DD25BA6B820084C9F8 /* TKOpenPlugin60044.m */,
			);
			path = TKOpenPlugin60044;
			sourceTree = "<group>";
		};
		182F804425DFCDC900505CDE /* TKOpenPlugin60049 */ = {
			isa = PBXGroup;
			children = (
				182F804525DFCDC900505CDE /* TKOpenPlugin60049.h */,
				182F804625DFCDC900505CDE /* TKOpenPlugin60049.m */,
			);
			path = TKOpenPlugin60049;
			sourceTree = "<group>";
		};
		1833968C245C27A600ECE057 /* TKOpenPlugin60039 */ = {
			isa = PBXGroup;
			children = (
				18339697245C27C000ECE057 /* TKOpenPlugin60039.h */,
				18339698245C27C000ECE057 /* TKOpenPlugin60039.m */,
				1833969A245C298B00ECE057 /* ZXJTVideoSDK */,
			);
			path = TKOpenPlugin60039;
			sourceTree = "<group>";
		};
		1833969A245C298B00ECE057 /* ZXJTVideoSDK */ = {
			isa = PBXGroup;
			children = (
				188FC5B62468F43300F7D2A3 /* JT_BasicControlImages.bundle */,
				188FC5B52468F43300F7D2A3 /* ZXJTBaseFramework.framework */,
				1833969F245C298B00ECE057 /* READMEZXJT.txt */,
			);
			path = ZXJTVideoSDK;
			sourceTree = "<group>";
		};
		183D059B29384A8000346A76 /* TKOpenPlugin61003 */ = {
			isa = PBXGroup;
			children = (
				183D059C29384A8000346A76 /* TKOpenPlugin61003.h */,
				183D059D29384A8000346A76 /* Controller */,
				183D05A429384A8000346A76 /* Manager */,
				183D05A729384A8000346A76 /* TKOpenPlugin61003.m */,
				183D05A929384A8000346A76 /* View */,
			);
			path = TKOpenPlugin61003;
			sourceTree = "<group>";
		};
		183D059D29384A8000346A76 /* Controller */ = {
			isa = PBXGroup;
			children = (
				336D640729C3297800BB96A1 /* TKOrganizationDoubleVideoResultBaseViewController.h */,
				336D640829C3297800BB96A1 /* TKOrganizationDoubleVideoResultBaseViewController.m */,
				336D640929C3297800BB96A1 /* TKOrganizationDoubleVideoResultFailureViewController.h */,
				336D640A29C3297800BB96A1 /* TKOrganizationDoubleVideoResultFailureViewController.m */,
				336D640B29C3297800BB96A1 /* TKOrganizationDoubleVideoResultSuccessViewController.h */,
				336D640629C3297800BB96A1 /* TKOrganizationDoubleVideoResultSuccessViewController.m */,
				183D059E29384A8000346A76 /* TKOrganizationDoubleVideoViewController.m */,
				183D059F29384A8000346A76 /* TKOrganizationDoubleVideoBaseViewController.m */,
				183D05A029384A8000346A76 /* TKOrganizationDoubleVideoNoVoiceViewController.h */,
				183D05A129384A8000346A76 /* TKOrganizationDoubleVideoViewController.h */,
				183D05A229384A8000346A76 /* TKOrganizationDoubleVideoBaseViewController.h */,
				183D05A329384A8000346A76 /* TKOrganizationDoubleVideoNoVoiceViewController.m */,
			);
			path = Controller;
			sourceTree = "<group>";
		};
		183D05A429384A8000346A76 /* Manager */ = {
			isa = PBXGroup;
			children = (
				183D05A529384A8000346A76 /* TKOrganizationDoubleVideoAVManager.m */,
				183D05A629384A8000346A76 /* TKOrganizationDoubleVideoAVManager.h */,
			);
			path = Manager;
			sourceTree = "<group>";
		};
		183D05A929384A8000346A76 /* View */ = {
			isa = PBXGroup;
			children = (
				336D641029C3298800BB96A1 /* TKOrganizationDoubleVideoGradientButton.h */,
				336D640F29C3298800BB96A1 /* TKOrganizationDoubleVideoGradientButton.m */,
				183D05AA29384A8000346A76 /* TKOrganizationDoubleVideoBottomRightView.h */,
				183D05AB29384A8000346A76 /* TKOrganizationDoubleVideoTimeView.h */,
				183D05AC29384A8000346A76 /* TKOrganizationDoubleVideoBottomTipView.m */,
				183D05AD29384A8000346A76 /* TKOrganizationDoubleVideoTimeView.m */,
				183D05AE29384A8000346A76 /* TKOrganizationDoubleVideoBottomRightView.m */,
				183D05AF29384A8000346A76 /* TKOrganizationDoubleVideoBottomTipView.h */,
			);
			path = View;
			sourceTree = "<group>";
		};
		183D05B029384A8000346A76 /* TKOpenPlugin61001 */ = {
			isa = PBXGroup;
			children = (
				183D05B129384A8000346A76 /* TKOpenPlugin61001.m */,
				183D05B229384A8000346A76 /* Controller */,
				183D05B729384A8000346A76 /* TKOpenPlugin61001.h */,
			);
			path = TKOpenPlugin61001;
			sourceTree = "<group>";
		};
		183D05B229384A8000346A76 /* Controller */ = {
			isa = PBXGroup;
			children = (
				183D05B329384A8000346A76 /* TKOrganizationSinglePhotoViewController.h */,
				183D05B429384A8000346A76 /* TKOrginizationDoublePhotoViewController.h */,
				183D05B529384A8000346A76 /* TKOrganizationSinglePhotoViewController.m */,
				183D05B629384A8000346A76 /* TKOrginizationDoublePhotoViewController.m */,
			);
			path = Controller;
			sourceTree = "<group>";
		};
		1876A14B2949CF57007F0500 /* Controller */ = {
			isa = PBXGroup;
			children = (
				1876A14C2949CF57007F0500 /* TKTakeIDPhotoViewController.h */,
				1876A14D2949CF57007F0500 /* TKTakeIDPhotoViewController.m */,
			);
			path = Controller;
			sourceTree = "<group>";
		};
		187942C1255CC61D0067C701 /* AliNUI */ = {
			isa = PBXGroup;
			children = (
				33C502AD2A0A4D520050DB0C /* nuisdk.framework */,
			);
			path = AliNUI;
			sourceTree = "<group>";
		};
		187F360326AAB4680046E9E3 /* TKOpenPlugin60062 */ = {
			isa = PBXGroup;
			children = (
				187F360426AAB4680046E9E3 /* TKOpenPlugin60062.h */,
				187F360526AAB4680046E9E3 /* Controller */,
				187F360826AAB4680046E9E3 /* TKOpenPlugin60062.m */,
				187F360926AAB4680046E9E3 /* View */,
			);
			path = TKOpenPlugin60062;
			sourceTree = "<group>";
		};
		187F360526AAB4680046E9E3 /* Controller */ = {
			isa = PBXGroup;
			children = (
				187F360626AAB4680046E9E3 /* TKOneWayLandscapeVideoViewController.h */,
				187F360726AAB4680046E9E3 /* TKOneWayLandscapeVideoViewController.mm */,
			);
			path = Controller;
			sourceTree = "<group>";
		};
		187F360926AAB4680046E9E3 /* View */ = {
			isa = PBXGroup;
			children = (
				187F360A26AAB4680046E9E3 /* TKOneWayLandscapeVideoEndView.m */,
				187F360B26AAB4680046E9E3 /* TKOneWayLandscapeVideoView.m */,
				187F360C26AAB4680046E9E3 /* TKOneWayLandscapeVideoView.h */,
				187F360D26AAB4680046E9E3 /* TKOneWayLandscapeVideoEndView.h */,
			);
			path = View;
			sourceTree = "<group>";
		};
		187F360E26AAB4680046E9E3 /* TKOpenPlugin60064 */ = {
			isa = PBXGroup;
			children = (
				187F360F26AAB4680046E9E3 /* TKOpenPlugin60064.m */,
				187F361026AAB4680046E9E3 /* Controller */,
				187F361326AAB4680046E9E3 /* TKOpenPlugin60064.h */,
				187F361426AAB4680046E9E3 /* View */,
			);
			path = TKOpenPlugin60064;
			sourceTree = "<group>";
		};
		187F361026AAB4680046E9E3 /* Controller */ = {
			isa = PBXGroup;
			children = (
				187F361126AAB4680046E9E3 /* TKOpenBusinessLicenseViewController.m */,
				187F361226AAB4680046E9E3 /* TKOpenBusinessLicenseViewController.h */,
			);
			path = Controller;
			sourceTree = "<group>";
		};
		187F361426AAB4680046E9E3 /* View */ = {
			isa = PBXGroup;
			children = (
			);
			path = View;
			sourceTree = "<group>";
		};
		187F361526AAB4680046E9E3 /* TKOpenPlugin61000 */ = {
			isa = PBXGroup;
			children = (
				187F361626AAB4680046E9E3 /* TKOpenPlugin61000.h */,
				187F361726AAB4680046E9E3 /* TKOpenPlugin61000.m */,
			);
			path = TKOpenPlugin61000;
			sourceTree = "<group>";
		};
		188DB0B726245B4A00F6732D /* TKAppletPlugin */ = {
			isa = PBXGroup;
			children = (
				188DB0BA26245B4A00F6732D /* TKAppletPluginManager.h */,
				188DB0B926245B4A00F6732D /* TKAppletPluginManager.m */,
				188DB0B826245B4A00F6732D /* FinApplet.framework */,
				188DB0BB26245B4A00F6732D /* README.txt */,
			);
			path = TKAppletPlugin;
			sourceTree = "<group>";
		};
		18934FFF296547610052E77B /* Demo */ = {
			isa = PBXGroup;
			children = (
				18935000296547610052E77B /* DemoViewController.h */,
				18935001296547610052E77B /* NativeLogin */,
				18935004296547610052E77B /* DemoPageViewController.m */,
				18935005296547610052E77B /* Cell */,
				18935009296547610052E77B /* DemoViewController.m */,
				1893500A296547610052E77B /* DemoPageViewController.h */,
			);
			path = Demo;
			sourceTree = "<group>";
		};
		18935001296547610052E77B /* NativeLogin */ = {
			isa = PBXGroup;
			children = (
				18935002296547610052E77B /* TKOpenLoginViewController.h */,
				18935003296547610052E77B /* TKOpenLoginViewController.m */,
			);
			path = NativeLogin;
			sourceTree = "<group>";
		};
		18935005296547610052E77B /* Cell */ = {
			isa = PBXGroup;
			children = (
				18935006296547610052E77B /* DemoTableViewCell.h */,
				18935007296547610052E77B /* DemoTableViewCell.xib */,
				18935008296547610052E77B /* DemoTableViewCell.m */,
			);
			path = Cell;
			sourceTree = "<group>";
		};
		1893500B296547610052E77B /* Test */ = {
			isa = PBXGroup;
			children = (
				189350502965498F0052E77B /* IQKeyBoardManager */,
				1893500C296547610052E77B /* TChatRtcOneWayVideoTestVC.h */,
				1893500D296547610052E77B /* TestTextFieldView.m */,
				1893500E296547610052E77B /* TKTakeIDCardTestVC.h */,
				1893500F296547610052E77B /* TestGuidePageVC.m */,
				18935010296547610052E77B /* TChatRtcArtificialWitnessTestVC.h */,
				18935034296547610052E77B /* TChatRtcOneWayVideoTestVC.m */,
				18935035296547610052E77B /* TestGuidePageVC.h */,
				18935036296547610052E77B /* TKTakeIDCardTestVC.m */,
				18935037296547610052E77B /* TestTextFieldView.h */,
				18935038296547610052E77B /* TChatRtcArtificialWitnessTestVC.m */,
			);
			path = Test;
			sourceTree = "<group>";
		};
		189350502965498F0052E77B /* IQKeyBoardManager */ = {
			isa = PBXGroup;
			children = (
				189350512965498F0052E77B /* IQTextView */,
				189350542965498F0052E77B /* IQToolbar */,
				1893505F2965498F0052E77B /* Constants */,
				189350622965498F0052E77B /* IQKeyboardReturnKeyHandler.h */,
				189350632965498F0052E77B /* IQKeyboardManager.h */,
				189350642965498F0052E77B /* IQKeyboardManager.m */,
				189350652965498F0052E77B /* Categories */,
				189350702965498F0052E77B /* IQKeyboardReturnKeyHandler.m */,
			);
			path = IQKeyBoardManager;
			sourceTree = "<group>";
		};
		189350512965498F0052E77B /* IQTextView */ = {
			isa = PBXGroup;
			children = (
				189350522965498F0052E77B /* IQTextView.m */,
				189350532965498F0052E77B /* IQTextView.h */,
			);
			path = IQTextView;
			sourceTree = "<group>";
		};
		189350542965498F0052E77B /* IQToolbar */ = {
			isa = PBXGroup;
			children = (
				189350552965498F0052E77B /* IQUIView+IQKeyboardToolbar.h */,
				189350562965498F0052E77B /* IQToolbar.m */,
				189350572965498F0052E77B /* IQPreviousNextView.h */,
				189350582965498F0052E77B /* IQTitleBarButtonItem.m */,
				189350592965498F0052E77B /* IQBarButtonItem.m */,
				1893505A2965498F0052E77B /* IQUIView+IQKeyboardToolbar.m */,
				1893505B2965498F0052E77B /* IQPreviousNextView.m */,
				1893505C2965498F0052E77B /* IQToolbar.h */,
				1893505D2965498F0052E77B /* IQBarButtonItem.h */,
				1893505E2965498F0052E77B /* IQTitleBarButtonItem.h */,
			);
			path = IQToolbar;
			sourceTree = "<group>";
		};
		1893505F2965498F0052E77B /* Constants */ = {
			isa = PBXGroup;
			children = (
				189350602965498F0052E77B /* IQKeyboardManagerConstantsInternal.h */,
				189350612965498F0052E77B /* IQKeyboardManagerConstants.h */,
			);
			path = Constants;
			sourceTree = "<group>";
		};
		189350652965498F0052E77B /* Categories */ = {
			isa = PBXGroup;
			children = (
				189350662965498F0052E77B /* IQNSArray+Sort.m */,
				189350672965498F0052E77B /* IQUIViewController+Additions.h */,
				189350682965498F0052E77B /* IQUITextFieldView+Additions.m */,
				189350692965498F0052E77B /* IQUIScrollView+Additions.m */,
				1893506A2965498F0052E77B /* IQUIView+Hierarchy.m */,
				1893506B2965498F0052E77B /* IQNSArray+Sort.h */,
				1893506C2965498F0052E77B /* IQUIScrollView+Additions.h */,
				1893506D2965498F0052E77B /* IQUITextFieldView+Additions.h */,
				1893506E2965498F0052E77B /* IQUIViewController+Additions.m */,
				1893506F2965498F0052E77B /* IQUIView+Hierarchy.h */,
			);
			path = Categories;
			sourceTree = "<group>";
		};
		18AF8642250771BA0094450D /* TKOpenPlugin60025 */ = {
			isa = PBXGroup;
			children = (
				18AF8643250771BA0094450D /* TKOpenPlugin60025.h */,
				18AF8644250771BA0094450D /* TKOpenPlugin60025.m */,
			);
			path = TKOpenPlugin60025;
			sourceTree = "<group>";
		};
		18AF8646250778200094450D /* TKOpenPlugin60043 */ = {
			isa = PBXGroup;
			children = (
				18AF8647250778530094450D /* TKOpenPlugin60043.h */,
				18AF8648250778530094450D /* TKOpenPlugin60043.m */,
			);
			path = TKOpenPlugin60043;
			sourceTree = "<group>";
		};
		18F129E725FEF96000F26F5C /* FaceDetect */ = {
			isa = PBXGroup;
			children = (
				18F129E825FEF96000F26F5C /* TKFaceDetectManager.h */,
				18F129E925FEF96000F26F5C /* TKFaceDetectManager.m */,
			);
			path = FaceDetect;
			sourceTree = "<group>";
		};
		18FC9C81295D89320081518D /* TKOpenPlugin60093 */ = {
			isa = PBXGroup;
			children = (
				18FC9C82295D89320081518D /* TKOpenPlugin60093.m */,
				18FC9C83295D89320081518D /* TKOpenPlugin60093.h */,
			);
			path = TKOpenPlugin60093;
			sourceTree = "<group>";
		};
		18FF460A28E1B7AE0026440D /* libSTSilentLivenessController */ = {
			isa = PBXGroup;
			children = (
				33C502B12A0A4D860050DB0C /* STLivenessDetector.xcframework */,
				18FF460B28E1B7AE0026440D /* SenseID_Liveness_Silent.lic */,
				18FF460C28E1B7AE0026440D /* STLivenessModel.bundle */,
			);
			path = libSTSilentLivenessController;
			sourceTree = "<group>";
		};
		331179A32E1684FC002179DC /* IDCardPhotoView */ = {
			isa = PBXGroup;
			children = (
				33E2EA192E371F5D00037BE4 /* TKBankCardPhotoView.h */,
				33E2EA1A2E371F5D00037BE4 /* TKBankCardPhotoView.m */,
				3311799D2E1684FC002179DC /* TKCardAlbumPreview.h */,
				3311799E2E1684FC002179DC /* TKCardAlbumPreview.m */,
				3311799F2E1684FC002179DC /* TKCardPreview.h */,
				331179A02E1684FC002179DC /* TKCardPreview.m */,
				331179A12E1684FC002179DC /* TKIDCardPhotoView.h */,
				331179A22E1684FC002179DC /* TKIDCardPhotoView.m */,
				33B3E7602E1BC248004C433C /* TKOCRTimeoutView.h */,
				33B3E7612E1BC248004C433C /* TKOCRTimeoutView.m */,
			);
			path = IDCardPhotoView;
			sourceTree = "<group>";
		};
		3316E1802A2835E100E57A02 /* TKOpenPlugin60071 */ = {
			isa = PBXGroup;
			children = (
				3316E1812A2835E100E57A02 /* TKOpenPlugin60071.h */,
				3316E1822A2835E100E57A02 /* TKOpenPlugin60071.m */,
			);
			path = TKOpenPlugin60071;
			sourceTree = "<group>";
		};
		331ABDBF2BA4107D00F0B963 /* TKOpenPlugin60095 */ = {
			isa = PBXGroup;
			children = (
				331ABDC02BA4107D00F0B963 /* TKOpenPlugin60095.h */,
				331ABDC12BA4107D00F0B963 /* TKOpenPlugin60095.m */,
			);
			path = TKOpenPlugin60095;
			sourceTree = "<group>";
		};
		336302342D83C202009BC4DC /* Controller */ = {
			isa = PBXGroup;
			children = (
				336302322D83C202009BC4DC /* TKOpenOneClickLoginViewController.h */,
				336302332D83C202009BC4DC /* TKOpenOneClickLoginViewController.m */,
			);
			path = Controller;
			sourceTree = "<group>";
		};
		3363023A2D83C202009BC4DC /* TKOpenPlugin60041 */ = {
			isa = PBXGroup;
			children = (
				336302342D83C202009BC4DC /* Controller */,
				336302352D83C202009BC4DC /* OAuth.xcframework */,
				336302362D83C202009BC4DC /* TKOpenOneClickLoginService.h */,
				336302372D83C202009BC4DC /* TKOpenOneClickLoginService.m */,
				336302382D83C202009BC4DC /* TKOpenPlugin60041.h */,
				336302392D83C202009BC4DC /* TKOpenPlugin60041.m */,
			);
			path = TKOpenPlugin60041;
			sourceTree = "<group>";
		};
		3363023D2D83C202009BC4DC /* TKOpenPlugin60096 */ = {
			isa = PBXGroup;
			children = (
				3363023B2D83C202009BC4DC /* TKOpenPlugin60096.h */,
				3363023C2D83C202009BC4DC /* TKOpenPlugin60096.m */,
			);
			path = TKOpenPlugin60096;
			sourceTree = "<group>";
		};
		336BA7AC29BF2A0A00134194 /* IDCadrSDK */ = {
			isa = PBXGroup;
			children = (
				336BA7AD29BF2A0A00134194 /* opencv3.4.2.a */,
				336BA7AE29BF2A0A00134194 /* ISIDReaderPreviewSDK.framework */,
			);
			path = IDCadrSDK;
			sourceTree = "<group>";
		};
		336BA7B129BF2A1000134194 /* BankCardSDK */ = {
			isa = PBXGroup;
			children = (
				336BA7B229BF2A1000134194 /* ISOpenSDKFoundation.embeddedframework */,
				336BA7D929BF2A1000134194 /* ISBankCard.framework */,
			);
			path = BankCardSDK;
			sourceTree = "<group>";
		};
		336BA7B229BF2A1000134194 /* ISOpenSDKFoundation.embeddedframework */ = {
			isa = PBXGroup;
			children = (
				336BA7B329BF2A1000134194 /* ISOpenSDKFoundation.framework */,
				336BA7B429BF2A1000134194 /* ISImages */,
			);
			path = ISOpenSDKFoundation.embeddedframework;
			sourceTree = "<group>";
		};
		336BA7B429BF2A1000134194 /* ISImages */ = {
			isa = PBXGroup;
			children = (
				336BA7B529BF2A1000134194 /* is_camera_closed.png */,
				336BA7B629BF2A1000134194 /* <EMAIL> */,
				336BA7B729BF2A1000134194 /* <EMAIL> */,
				336BA7B829BF2A1000134194 /* <EMAIL> */,
				336BA7B929BF2A1000134194 /* is_camera_corner_highlight_Red.png */,
				336BA7BA29BF2A1000134194 /* <EMAIL> */,
				336BA7BB29BF2A1000134194 /* <EMAIL> */,
				336BA7BC29BF2A1000134194 /* <EMAIL> */,
				336BA7BD29BF2A1000134194 /* is_camera_closed_ccb.png */,
				336BA7BE29BF2A1000134194 /* is_camera_flash_on.png */,
				336BA7BF29BF2A1000134194 /* <EMAIL> */,
				336BA7C029BF2A1000134194 /* <EMAIL> */,
				336BA7C129BF2A1000134194 /* <EMAIL> */,
				336BA7C229BF2A1000134194 /* <EMAIL> */,
				336BA7C329BF2A1000134194 /* is_camera_flash__ccb_off.png */,
				336BA7C429BF2A1000134194 /* is_camera_corner.png */,
				336BA7C529BF2A1000134194 /* <EMAIL> */,
				336BA7C629BF2A1000134194 /* <EMAIL> */,
				336BA7C729BF2A1000134194 /* <EMAIL> */,
				336BA7C829BF2A1000134194 /* <EMAIL> */,
				336BA7C929BF2A1000134194 /* <EMAIL> */,
				336BA7CA29BF2A1000134194 /* <EMAIL> */,
				336BA7CB29BF2A1000134194 /* is_camera_logo.png */,
				336BA7CC29BF2A1000134194 /* <EMAIL> */,
				336BA7CD29BF2A1000134194 /* <EMAIL> */,
				336BA7CE29BF2A1000134194 /* <EMAIL> */,
				336BA7CF29BF2A1000134194 /* <EMAIL> */,
				336BA7D029BF2A1000134194 /* <EMAIL> */,
				336BA7D129BF2A1000134194 /* <EMAIL> */,
				336BA7D229BF2A1000134194 /* is_camera_flash_ccb_on.png */,
				336BA7D329BF2A1000134194 /* is_camera_corner_highlight.png */,
				336BA7D429BF2A1000134194 /* is_camera_flash_off.png */,
				336BA7D529BF2A1000134194 /* <EMAIL> */,
				336BA7D629BF2A1000134194 /* <EMAIL> */,
				336BA7D729BF2A1000134194 /* <EMAIL> */,
				336BA7D829BF2A1000134194 /* <EMAIL> */,
			);
			path = ISImages;
			sourceTree = "<group>";
		};
		33B36AF62C21826900121BC3 /* TKOpenPlugin60046 */ = {
			isa = PBXGroup;
			children = (
				33B36AF72C21826900121BC3 /* TKOpenPlugin60046.m */,
				33B36AF82C21826900121BC3 /* TKOpenPlugin60046.h */,
			);
			path = TKOpenPlugin60046;
			sourceTree = "<group>";
		};
		33BDE2902A7203CD004F30D1 /* TKOpenPlugin60094 */ = {
			isa = PBXGroup;
			children = (
				33BDE2912A7203CD004F30D1 /* TKOpenPlugin60094.m */,
				33BDE2922A7203CD004F30D1 /* TKOpenPlugin60094.h */,
			);
			path = TKOpenPlugin60094;
			sourceTree = "<group>";
		};
		33C207A22A146B1B00087D13 /* Zego */ = {
			isa = PBXGroup;
			children = (
				33C207A32A146B1B00087D13 /* ZegoLiveRoom.framework */,
				33C207A42A146B1B00087D13 /* ZegoQueue.framework */,
			);
			path = Zego;
			sourceTree = "<group>";
		};
		33C207AF2A148B2700087D13 /* TKOpenPlugin60026 */ = {
			isa = PBXGroup;
			children = (
				33C207B02A148B2700087D13 /* Util */,
				33C207CF2A148B2700087D13 /* TKOpenPlugin60026.h */,
				33C207D02A148B2700087D13 /* Controller */,
				33C207D32A148B2700087D13 /* TKOpenPlugin60026.m */,
				33C207D42A148B2700087D13 /* View */,
			);
			path = TKOpenPlugin60026;
			sourceTree = "<group>";
		};
		33C207B02A148B2700087D13 /* Util */ = {
			isa = PBXGroup;
			children = (
				33C207B12A148B2700087D13 /* SpeechSynthesis */,
				33C207BD2A148B2700087D13 /* TKringBuf.cpp */,
				33C207BE2A148B2700087D13 /* TKNLSPlayAudio.h */,
				33C207BF2A148B2700087D13 /* TKNLSVoiceRecorder.m */,
				33C207C02A148B2700087D13 /* TKringBuf.h */,
				33C207C12A148B2700087D13 /* SpeechRecognize */,
				33C207CD2A148B2700087D13 /* TKNLSPlayAudio.mm */,
				33C207CE2A148B2700087D13 /* TKNLSVoiceRecorder.h */,
			);
			path = Util;
			sourceTree = "<group>";
		};
		33C207B12A148B2700087D13 /* SpeechSynthesis */ = {
			isa = PBXGroup;
			children = (
				33C207B22A148B2700087D13 /* TKSpeechSynthesisManagerProtocol.h */,
				33C207B32A148B2700087D13 /* TKALSpeechSynthesisManager.mm */,
				33C207B42A148B2700087D13 /* TKSpeechSynthesisManager.m */,
				33C207B62A148B2700087D13 /* TKTencentSpeechSynthesisManager.m */,
				33C207B82A148B2700087D13 /* TKSpeechSynthesisManager.h */,
				33C207BA2A148B2700087D13 /* TKALSpeechSynthesisManager.h */,
				33C207BB2A148B2700087D13 /* TKTencentSpeechSynthesisManager.h */,
				33DAFAA12C61E9D6009F2243 /* TKiflyNoAiSpeechSynthesisManager.h */,
				33DAFAA22C61E9D6009F2243 /* TKiflyNoAiSpeechSynthesisManager.m */,
			);
			path = SpeechSynthesis;
			sourceTree = "<group>";
		};
		33C207C12A148B2700087D13 /* SpeechRecognize */ = {
			isa = PBXGroup;
			children = (
				33C207C42A148B2700087D13 /* TKTencentSpeechRecognizeManager.m */,
				33C207C52A148B2700087D13 /* TKSpeechRecognizeManager.m */,
				33C207C62A148B2700087D13 /* TKSpeechRecognizeManagerProtocol.h */,
				33C207C72A148B2700087D13 /* TKALSpeechRecognizeManager.h */,
				33C207C82A148B2700087D13 /* TKTencentSpeechRecognizeManager.h */,
				33C207CB2A148B2700087D13 /* TKSpeechRecognizeManager.h */,
				33C207CC2A148B2700087D13 /* TKALSpeechRecognizeManager.mm */,
				33DAFAA42C61E9E1009F2243 /* TKiflyNoAiSpeechRecognizeManager.h */,
				33DAFAA52C61E9E1009F2243 /* TKiflyNoAiSpeechRecognizeManager.m */,
			);
			path = SpeechRecognize;
			sourceTree = "<group>";
		};
		33C207D02A148B2700087D13 /* Controller */ = {
			isa = PBXGroup;
			children = (
				33C207D12A148B2700087D13 /* TKOneWayVideoViewController.h */,
				33C207D22A148B2700087D13 /* TKOneWayVideoViewController.mm */,
			);
			path = Controller;
			sourceTree = "<group>";
		};
		33C207D42A148B2700087D13 /* View */ = {
			isa = PBXGroup;
			children = (
				33C207D52A148B2700087D13 /* TKOneWayVideoEndView.m */,
				33C207D62A148B2700087D13 /* TKOneWayVideoView.m */,
				33C207D72A148B2700087D13 /* TKOneWayVideoEndView.h */,
				33C207D82A148B2700087D13 /* TKOneWayVideoView.h */,
			);
			path = View;
			sourceTree = "<group>";
		};
		33CB8E2F2DA6513D000EA7C4 /* TKOpenPlugin60817 */ = {
			isa = PBXGroup;
			children = (
				33CB8E2D2DA6513D000EA7C4 /* TKOpenPlugin60817.h */,
				33CB8E2E2DA6513D000EA7C4 /* TKOpenPlugin60817.m */,
			);
			path = TKOpenPlugin60817;
			sourceTree = "<group>";
		};
		33DF5F4F2D1A9B0700F311AA /* TKOpenPlugin60813 */ = {
			isa = PBXGroup;
			children = (
				33DF5F4D2D1A9B0700F311AA /* TKOpenPlugin60813.h */,
				33DF5F4E2D1A9B0700F311AA /* TKOpenPlugin60813.m */,
			);
			path = TKOpenPlugin60813;
			sourceTree = "<group>";
		};
		33EA0F402C9A69AB0066022A /* TKOpenPlugin60812 */ = {
			isa = PBXGroup;
			children = (
				33EA0F412C9A69AB0066022A /* TKOpenPlugin60812.h */,
				33EA0F422C9A69AB0066022A /* TKOpenPlugin60812.m */,
			);
			path = TKOpenPlugin60812;
			sourceTree = "<group>";
		};
		33FD40882E2F3AEE00687DD1 /* TKOpenPlugin60066 */ = {
			isa = PBXGroup;
			children = (
				33FD40862E2F3AEE00687DD1 /* TKOpenPlugin60066.h */,
				33FD40872E2F3AEE00687DD1 /* TKOpenPlugin60066.m */,
			);
			path = TKOpenPlugin60066;
			sourceTree = "<group>";
		};
		B32E91C41F18C95E005A6B6D /* TChat */ = {
			isa = PBXGroup;
			children = (
				B32E91C51F18C95E005A6B6D /* SDK */,
				B32E91C71F18C95E005A6B6D /* TKVideoWitnessViewController+TChat.h */,
				B32E91C81F18C95E005A6B6D /* TKVideoWitnessViewController+TChat.m */,
				DCA229B4295ACB34005C6688 /* TChatRtc */,
			);
			path = TChat;
			sourceTree = "<group>";
		};
		B32E91C51F18C95E005A6B6D /* SDK */ = {
			isa = PBXGroup;
			children = (
				33DAFAAA2C61F99A009F2243 /* TChatRtc.framework */,
				33294B102A0A469800A72052 /* TChat.framework */,
			);
			path = SDK;
			sourceTree = "<group>";
		};
		B35569F41DDBF96B00C76E7C /* Plugins */ = {
			isa = PBXGroup;
			children = (
				33FD40882E2F3AEE00687DD1 /* TKOpenPlugin60066 */,
				33CB8E2F2DA6513D000EA7C4 /* TKOpenPlugin60817 */,
				3363023A2D83C202009BC4DC /* TKOpenPlugin60041 */,
				3363023D2D83C202009BC4DC /* TKOpenPlugin60096 */,
				33DF5F4F2D1A9B0700F311AA /* TKOpenPlugin60813 */,
				33EA0F402C9A69AB0066022A /* TKOpenPlugin60812 */,
				33B36AF62C21826900121BC3 /* TKOpenPlugin60046 */,
				331ABDBF2BA4107D00F0B963 /* TKOpenPlugin60095 */,
				33BDE2902A7203CD004F30D1 /* TKOpenPlugin60094 */,
				3316E1802A2835E100E57A02 /* TKOpenPlugin60071 */,
				33C207AF2A148B2700087D13 /* TKOpenPlugin60026 */,
				DC0AC8BC29642E550068F203 /* TKOpenPlugin60087 */,
				18FC9C81295D89320081518D /* TKOpenPlugin60093 */,
				DC6758AA294AEF8B009771ED /* TKOpenPlugin60091 */,
				DCC042A72947430200BDF14D /* TKOpenPlugin60089 */,
				048DC98E2407E66400A6F3EC /* TKOpenPlugin60030 */,
				DCC0429429434D1900BDF14D /* TKOpenPlugin60077 */,
				183D05B029384A8000346A76 /* TKOpenPlugin61001 */,
				183D059B29384A8000346A76 /* TKOpenPlugin61003 */,
				187F360326AAB4680046E9E3 /* TKOpenPlugin60062 */,
				187F360E26AAB4680046E9E3 /* TKOpenPlugin60064 */,
				187F361526AAB4680046E9E3 /* TKOpenPlugin61000 */,
				DC6CDB842650F1FC004E7295 /* TKOpenPlugin60059 */,
				DCC7E7DF26299A29006D378F /* TKOpenPlugin60057 */,
				DCD47D4228E1B18D00AE44C6 /* TKOpenPlugin60072 */,
				182F804425DFCDC900505CDE /* TKOpenPlugin60049 */,
				182CA7DB25BA6B820084C9F8 /* TKOpenPlugin60044 */,
				18AF8646250778200094450D /* TKOpenPlugin60043 */,
				18AF8642250771BA0094450D /* TKOpenPlugin60025 */,
				1833968C245C27A600ECE057 /* TKOpenPlugin60039 */,
				1813893D244B269A007F96FA /* TKOpenPlugin60037 */,
				04E5B284241CA6D30069BCEE /* TKOpenPlugin60034 */,
				045EF863241BAEE400032B22 /* TKOpenPlugin60033 */,
				045EF85F241BA74F00032B22 /* TKOpenPlugin60032 */,
				043015E823FCC755004F0C17 /* TKOpenPlugin60028 */,
				0472CA7A23FB9DD4000B5EB7 /* TKOpenPlugin60007 */,
				0465234B212E84C000F8C0D0 /* TKOpenPlugin60022 */,
				B35569F51DDBF96B00C76E7C /* TKOpenPlugin60000 */,
				B35569F81DDBF96B00C76E7C /* TKOpenPlugin60001 */,
				B35569FB1DDBF96B00C76E7C /* TKOpenPlugin60002 */,
				B3556A011DDBF96B00C76E7C /* TKOpenPlugin60003 */,
				B3556A041DDBF96B00C76E7C /* TKOpenPlugin60004 */,
				B3556A071DDBF96B00C76E7C /* TKOpenPlugin60005 */,
				B3556A1C1DDBF96B00C76E7C /* TKOpenPlugin60006 */,
				B3556A391DDBF96B00C76E7C /* TKOpenPlugin60008 */,
				B3556A3F1DDBF96B00C76E7C /* TKOpenPlugin60010 */,
				B3556A421DDBF96B00C76E7C /* TKOpenPlugin60013 */,
				B3556A4C1DDBF96B00C76E7C /* TKOpenPlugin60014 */,
				B3556A691DDBF96B00C76E7C /* TKOpenPlugin60016 */,
				B3556A881DDBF96B00C76E7C /* TKOpenPlugin60017 */,
				B3556A8B1DDBF96B00C76E7C /* TKOpenPlugin60018 */,
				B386BA0B1F947E6C001CBD13 /* TKOpenPlugin60099 */,
			);
			path = Plugins;
			sourceTree = "<group>";
		};
		B35569F51DDBF96B00C76E7C /* TKOpenPlugin60000 */ = {
			isa = PBXGroup;
			children = (
				B35569F61DDBF96B00C76E7C /* TKOpenPlugin60000.h */,
				B35569F71DDBF96B00C76E7C /* TKOpenPlugin60000.m */,
			);
			path = TKOpenPlugin60000;
			sourceTree = "<group>";
		};
		B35569F81DDBF96B00C76E7C /* TKOpenPlugin60001 */ = {
			isa = PBXGroup;
			children = (
				B35569F91DDBF96B00C76E7C /* TKOpenPlugin60001.h */,
				B35569FA1DDBF96B00C76E7C /* TKOpenPlugin60001.m */,
			);
			path = TKOpenPlugin60001;
			sourceTree = "<group>";
		};
		B35569FB1DDBF96B00C76E7C /* TKOpenPlugin60002 */ = {
			isa = PBXGroup;
			children = (
				B35569FC1DDBF96B00C76E7C /* TKOpenPlugin60002.h */,
				B35569FD1DDBF96B00C76E7C /* TKOpenPlugin60002.m */,
				B35569FE1DDBF96B00C76E7C /* views */,
			);
			path = TKOpenPlugin60002;
			sourceTree = "<group>";
		};
		B35569FE1DDBF96B00C76E7C /* views */ = {
			isa = PBXGroup;
			children = (
				B35569FF1DDBF96B00C76E7C /* MTakeCardViewController.h */,
				B3556A001DDBF96B00C76E7C /* MTakeCardViewController.m */,
			);
			path = views;
			sourceTree = "<group>";
		};
		B3556A011DDBF96B00C76E7C /* TKOpenPlugin60003 */ = {
			isa = PBXGroup;
			children = (
				B3556A021DDBF96B00C76E7C /* TKOpenPlugin60003.h */,
				B3556A031DDBF96B00C76E7C /* TKOpenPlugin60003.m */,
			);
			path = TKOpenPlugin60003;
			sourceTree = "<group>";
		};
		B3556A041DDBF96B00C76E7C /* TKOpenPlugin60004 */ = {
			isa = PBXGroup;
			children = (
				B3556A051DDBF96B00C76E7C /* TKOpenPlugin60004.h */,
				B3556A061DDBF96B00C76E7C /* TKOpenPlugin60004.m */,
			);
			path = TKOpenPlugin60004;
			sourceTree = "<group>";
		};
		B3556A071DDBF96B00C76E7C /* TKOpenPlugin60005 */ = {
			isa = PBXGroup;
			children = (
				0472AC9623580798008FC27E /* View */,
				B3556A081DDBF96B00C76E7C /* Controller */,
				B3556A121DDBF96B00C76E7C /* Resources */,
				DCA6A87A29CC5E7D003D0FE7 /* SmartCtr */,
				B3556A1A1DDBF96B00C76E7C /* TKOpenPlugin60005.h */,
				B3556A1B1DDBF96B00C76E7C /* TKOpenPlugin60005.m */,
				1854638029669CBA00553822 /* TKChatService.h */,
				1854637F29669CB900553822 /* TKChatService.m */,
				1854638129669CBA00553822 /* TKChatServiceDelegate.h */,
			);
			path = TKOpenPlugin60005;
			sourceTree = "<group>";
		};
		B3556A081DDBF96B00C76E7C /* Controller */ = {
			isa = PBXGroup;
			children = (
				B30FD8A81EF11ED2000D3E94 /* TKVideoWitnessViewController.h */,
				B30FD8A91EF11ED2000D3E94 /* TKVideoWitnessViewController.m */,
				B30FD8AA1EF11ED2000D3E94 /* TKVideoWitnessViewController+AnyChat.h */,
				B30FD8AB1EF11ED2000D3E94 /* TKVideoWitnessViewController+AnyChat.m */,
				B30FD8AC1EF11ED2000D3E94 /* TKVideoWitnessViewController+CommonKit.h */,
				B30FD8AD1EF11ED2000D3E94 /* TKVideoWitnessViewController+CommonKit.m */,
			);
			path = Controller;
			sourceTree = "<group>";
		};
		B3556A121DDBF96B00C76E7C /* Resources */ = {
			isa = PBXGroup;
			children = (
				B3556A131DDBF96B00C76E7C /* chat_cancel_btn.png */,
				B3556A141DDBF96B00C76E7C /* kefu_bg_img.png */,
				B3556A151DDBF96B00C76E7C /* page_bg.jpg */,
				B35087C21F0E367300A366A0 /* tk_video_icon_iphone.png */,
				B3556A161DDBF96B00C76E7C /* TKAnyChatViewController.xib */,
				B3556A171DDBF96B00C76E7C /* TKAnyChatViewController4.xib */,
				B35087C31F0E367300A366A0 /* TKVideoWitnessViewController.xib */,
			);
			path = Resources;
			sourceTree = "<group>";
		};
		B3556A1C1DDBF96B00C76E7C /* TKOpenPlugin60006 */ = {
			isa = PBXGroup;
			children = (
				B3556A1D1DDBF96B00C76E7C /* Controller */,
				B3556A221DDBF96B00C76E7C /* Model */,
				B3556A251DDBF96B00C76E7C /* TKOpenPlugin60006.h */,
				B3556A261DDBF96B00C76E7C /* TKOpenPlugin60006.m */,
				B3556A271DDBF96B00C76E7C /* views */,
			);
			path = TKOpenPlugin60006;
			sourceTree = "<group>";
		};
		B3556A1D1DDBF96B00C76E7C /* Controller */ = {
			isa = PBXGroup;
			children = (
				B3556A1E1DDBF96B00C76E7C /* TkFDRecordController.h */,
				B3556A1F1DDBF96B00C76E7C /* TkFDRecordController.m */,
				B3556A201DDBF96B00C76E7C /* TkRecordController.h */,
				B3556A211DDBF96B00C76E7C /* TKRecordController.m */,
			);
			path = Controller;
			sourceTree = "<group>";
		};
		B3556A221DDBF96B00C76E7C /* Model */ = {
			isa = PBXGroup;
			children = (
				B3556A231DDBF96B00C76E7C /* TKRecordModel.h */,
				B3556A241DDBF96B00C76E7C /* TKRecordModel.m */,
			);
			path = Model;
			sourceTree = "<group>";
		};
		B3556A271DDBF96B00C76E7C /* views */ = {
			isa = PBXGroup;
			children = (
				B3556A281DDBF96B00C76E7C /* TKRecordView.h */,
				B3556A291DDBF96B00C76E7C /* TKRecordView.m */,
			);
			path = views;
			sourceTree = "<group>";
		};
		B3556A391DDBF96B00C76E7C /* TKOpenPlugin60008 */ = {
			isa = PBXGroup;
			children = (
				B3556A3A1DDBF96B00C76E7C /* Controller */,
				B3556A3D1DDBF96B00C76E7C /* TKOpenPlugin60008.h */,
				B3556A3E1DDBF96B00C76E7C /* TKOpenPlugin60008.m */,
			);
			path = TKOpenPlugin60008;
			sourceTree = "<group>";
		};
		B3556A3A1DDBF96B00C76E7C /* Controller */ = {
			isa = PBXGroup;
			children = (
				B3556A3B1DDBF96B00C76E7C /* MTakeFaceViewController.h */,
				B3556A3C1DDBF96B00C76E7C /* MTakeFaceViewController.m */,
			);
			path = Controller;
			sourceTree = "<group>";
		};
		B3556A3F1DDBF96B00C76E7C /* TKOpenPlugin60010 */ = {
			isa = PBXGroup;
			children = (
				B3556A401DDBF96B00C76E7C /* TKOpenPlugin60010.h */,
				B3556A411DDBF96B00C76E7C /* TKOpenPlugin60010.m */,
			);
			path = TKOpenPlugin60010;
			sourceTree = "<group>";
		};
		B3556A421DDBF96B00C76E7C /* TKOpenPlugin60013 */ = {
			isa = PBXGroup;
			children = (
				B3556A431DDBF96B00C76E7C /* MTakePhoto */,
				B3556A4A1DDBF96B00C76E7C /* TKOpenPlugin60013.h */,
				B3556A4B1DDBF96B00C76E7C /* TKOpenPlugin60013.m */,
			);
			path = TKOpenPlugin60013;
			sourceTree = "<group>";
		};
		B3556A431DDBF96B00C76E7C /* MTakePhoto */ = {
			isa = PBXGroup;
			children = (
				B3556A461DDBF96B00C76E7C /* MTakeBigPictureViewController.h */,
				B3556A471DDBF96B00C76E7C /* MTakeBigPictureViewController.m */,
				B3556A481DDBF96B00C76E7C /* MTakePhotoViewController.h */,
				B3556A491DDBF96B00C76E7C /* MTakePhotoViewController.m */,
			);
			path = MTakePhoto;
			sourceTree = "<group>";
		};
		B3556A4C1DDBF96B00C76E7C /* TKOpenPlugin60014 */ = {
			isa = PBXGroup;
			children = (
				336BA7AC29BF2A0A00134194 /* IDCadrSDK */,
				B3556A4F1DDBF96B00C76E7C /* MTakePhoto */,
				B3556A521DDBF96B00C76E7C /* TKOpenPlugin60014.h */,
				B3556A531DDBF96B00C76E7C /* TKOpenPlugin60014.m */,
			);
			path = TKOpenPlugin60014;
			sourceTree = "<group>";
		};
		B3556A4F1DDBF96B00C76E7C /* MTakePhoto */ = {
			isa = PBXGroup;
			children = (
				B3556A501DDBF96B00C76E7C /* MIDCardRecognizeViewController.h */,
				B3556A511DDBF96B00C76E7C /* MIDCardRecognizeViewController.m */,
			);
			path = MTakePhoto;
			sourceTree = "<group>";
		};
		B3556A691DDBF96B00C76E7C /* TKOpenPlugin60016 */ = {
			isa = PBXGroup;
			children = (
				336BA7B129BF2A1000134194 /* BankCardSDK */,
				B3556A831DDBF96B00C76E7C /* Controller */,
				B3556A861DDBF96B00C76E7C /* TKOpenPlugin60016.h */,
				B3556A871DDBF96B00C76E7C /* TKOpenPlugin60016.m */,
			);
			path = TKOpenPlugin60016;
			sourceTree = "<group>";
		};
		B3556A831DDBF96B00C76E7C /* Controller */ = {
			isa = PBXGroup;
			children = (
				B3556A841DDBF96B00C76E7C /* TKBankCardRecognizeViewController.h */,
				B3556A851DDBF96B00C76E7C /* TKBankCardRecognizeViewController.m */,
			);
			path = Controller;
			sourceTree = "<group>";
		};
		B3556A881DDBF96B00C76E7C /* TKOpenPlugin60017 */ = {
			isa = PBXGroup;
			children = (
				B3556A891DDBF96B00C76E7C /* TKOpenPlugin60017.h */,
				B3556A8A1DDBF96B00C76E7C /* TKOpenPlugin60017.m */,
			);
			path = TKOpenPlugin60017;
			sourceTree = "<group>";
		};
		B3556A8B1DDBF96B00C76E7C /* TKOpenPlugin60018 */ = {
			isa = PBXGroup;
			children = (
				B3556A8C1DDBF96B00C76E7C /* TKOpenPlugin60018.h */,
				B3556A8D1DDBF96B00C76E7C /* TKOpenPlugin60018.m */,
			);
			path = TKOpenPlugin60018;
			sourceTree = "<group>";
		};
		B35EBF3C1C43942F0059F885 /* MDRadialProgress */ = {
			isa = PBXGroup;
			children = (
				B35EBF3D1C43942F0059F885 /* MDRadialProgressLabel.h */,
				B35EBF3E1C43942F0059F885 /* MDRadialProgressLabel.m */,
				B35EBF3F1C43942F0059F885 /* MDRadialProgressTheme.h */,
				B35EBF401C43942F0059F885 /* MDRadialProgressTheme.m */,
				B35EBF411C43942F0059F885 /* MDRadialProgressView.h */,
				B35EBF421C43942F0059F885 /* MDRadialProgressView.m */,
			);
			path = MDRadialProgress;
			sourceTree = "<group>";
		};
		B386BA0B1F947E6C001CBD13 /* TKOpenPlugin60099 */ = {
			isa = PBXGroup;
			children = (
				B386BA0C1F947E6C001CBD13 /* TKOpenPlugin60099.h */,
				B386BA0D1F947E6C001CBD13 /* TKOpenPlugin60099.m */,
			);
			path = TKOpenPlugin60099;
			sourceTree = "<group>";
		};
		B3AB88921C15704600571BE6 /* TKFMWK */ = {
			isa = PBXGroup;
			children = (
				3324DB262C47D86700D5E61B /* TKSDKAuth.lic */,
				045ACEA522A4FC30004D8557 /* TKWebViewApp.framework */,
				049A82E7211ECD6B00AA2048 /* THFMWK */,
				049A82E6211ECD6A00AA2048 /* TKAsset.bundle */,
				B3AB8AF91C15708900571BE6 /* TKOpenResource.bundle */,
			);
			path = TKFMWK;
			sourceTree = "<group>";
		};
		B3BF85B21F4D5D3A0060B3E9 /* AnyChat */ = {
			isa = PBXGroup;
			children = (
				337A9F0D2A37062500CE0434 /* AnyChatCoreSDK.framework */,
			);
			path = AnyChat;
			sourceTree = "<group>";
		};
		B3E1B30C1B8DB80800CDD258 /* TKOpenResource */ = {
			isa = PBXGroup;
			children = (
				043884B823B05A240009F14D /* KeyBoard.xml */,
				04D9604822C3366E0010E4C6 /* Configuration_Open_BuriedPoint.xml */,
				B3FF6E391EC5B2A0009FAF95 /* tk_open */,
				B32C870B1D87E96E00373C19 /* config.plist */,
				B30DC5061BCFC873007072FB /* Configuration.xml */,
				B30DC5071BCFC873007072FB /* OpenPlugin.xml */,
				B30DC5081BCFC873007072FB /* SystemPlugin.xml */,
			);
			path = TKOpenResource;
			sourceTree = "<group>";
		};
		B3FACBA81AFDDF0E0088CDF1 = {
			isa = PBXGroup;
			children = (
				B3E1B2EF1B8D9FEF00CDD258 /* Resources */,
				BE6CCF3A1B5000B6003DCF98 /* Thinkive */,
				************************ /* www */,
				B3E1B30C1B8DB80800CDD258 /* TKOpenResource */,
				B3FACBDB1AFDE2590088CDF1 /* Frameworks */,
				B3FACBB31AFDDF0E0088CDF1 /* TKOpenAccount-Standard */,
				B3FACBB21AFDDF0E0088CDF1 /* Products */,
			);
			sourceTree = "<group>";
		};
		B3FACBB21AFDDF0E0088CDF1 /* Products */ = {
			isa = PBXGroup;
			children = (
				B3FACBB11AFDDF0E0088CDF1 /* 思迪TChatRtc.app */,
				B3E1B30B1B8DB80800CDD258 /* TKOpenResource.bundle */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		B3FACBB31AFDDF0E0088CDF1 /* TKOpenAccount-Standard */ = {
			isa = PBXGroup;
			children = (
				B3FACBB81AFDDF0E0088CDF1 /* AppDelegate.h */,
				B3FACBB91AFDDF0E0088CDF1 /* AppDelegate.m */,
				B3FACBBB1AFDDF0E0088CDF1 /* ViewController.h */,
				B3FACBBC1AFDDF0E0088CDF1 /* ViewController.m */,
				E22996F326AABD490039F6E9 /* SelectViewController.h */,
				E22996F426AABD490039F6E9 /* SelectViewController.m */,
				18934FFF296547610052E77B /* Demo */,
				1893500B296547610052E77B /* Test */,
				DCC7E86A262D7133006D378F /* TKTempService.h */,
				DCC7E868262D7133006D378F /* TKTempService.m */,
				1895FCAF26959A9B00513E5F /* TKOpenPrivacyAgreementView.h */,
				1895FCB126959A9B00513E5F /* TKOpenPrivacyAgreementView.m */,
				189F778726D8E6AD00F4089E /* TTTAttributedLabel.h */,
				189F778826D8E6AE00F4089E /* TTTAttributedLabel.m */,
				334D516E2C61BE6D00B1E35C /* PrivacyInfo.xcprivacy */,
				B3FACBBE1AFDDF0E0088CDF1 /* Main.storyboard */,
				B3FACBC11AFDDF0E0088CDF1 /* Images.xcassets */,
				B3FACBC31AFDDF0E0088CDF1 /* LaunchScreen.xib */,
				B3FACBB41AFDDF0E0088CDF1 /* Supporting Files */,
			);
			path = "TKOpenAccount-Standard";
			sourceTree = "<group>";
		};
		B3FACBB41AFDDF0E0088CDF1 /* Supporting Files */ = {
			isa = PBXGroup;
			children = (
				B3FACBB51AFDDF0E0088CDF1 /* Info.plist */,
				B3FACBB61AFDDF0E0088CDF1 /* main.m */,
				B3FACCBC1AFDF2F80088CDF1 /* TKOpenAccount.pch */,
			);
			name = "Supporting Files";
			sourceTree = "<group>";
		};
		B3FACBDB1AFDE2590088CDF1 /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				33C207AD2A147ACB00087D13 /* libc++.tbd */,
				33C207AB2A147A5100087D13 /* GLKit.framework */,
				18BBEF4E28A24AC100A26825 /* WebKit.framework */,
				18C3192525F74AE2003B36C7 /* CallKit.framework */,
				187942C7255CD0AC0067C701 /* AdSupport.framework */,
				040B88412148BC19008DBE05 /* VideoToolbox.framework */,
				B39E9312208F05CD006EC8A3 /* Accelerate.framework */,
				B39E9310208F054A006EC8A3 /* MessageUI.framework */,
				B39E930E208F0532006EC8A3 /* CoreMotion.framework */,
				B379D392206CD57000D1440E /* CoreTelephony.framework */,
				B33FDC111F692BE400C644F2 /* libresolv.tbd */,
				B33FDC0F1F692BCB00C644F2 /* libxml2.tbd */,
				B3C6ADC81C92ADEA00FE95F0 /* CoreMedia.framework */,
				B3C6ADC61C92ADDD00FE95F0 /* AssetsLibrary.framework */,
				B37F84281BE365230016F93D /* OpenGLES.framework */,
				B3FEEB351B845D6D00468924 /* AVFoundation.framework */,
				B39BADCF1B4D524F00F071D4 /* CoreLocation.framework */,
				B3B108831B44DF2800546D96 /* libiconv.dylib */,
				B3FACBF21AFDE3940088CDF1 /* libc++.dylib */,
				B3FACBF01AFDE3800088CDF1 /* libz.1.2.5.dylib */,
				B3FACBEE1AFDE3750088CDF1 /* libsqlite3.dylib */,
				B3FACBEC1AFDE35E0088CDF1 /* libstdc++.6.0.9.dylib */,
				B3FACBEA1AFDE3460088CDF1 /* CoreGraphics.framework */,
				B3FACBE81AFDE33A0088CDF1 /* Foundation.framework */,
				B3FACBE61AFDE31A0088CDF1 /* SystemConfiguration.framework */,
				B3FACBE41AFDE30B0088CDF1 /* CFNetwork.framework */,
				B3FACBE21AFDE3010088CDF1 /* MobileCoreServices.framework */,
				B3FACBE01AFDE2F40088CDF1 /* Security.framework */,
				B3FACBDE1AFDE2E50088CDF1 /* QuartzCore.framework */,
				B3FACBDC1AFDE2D10088CDF1 /* UIKit.framework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		B3FB08B31F09EA5C00EC895E /* Modules */ = {
			isa = PBXGroup;
			children = (
				18FF460A28E1B7AE0026440D /* libSTSilentLivenessController */,
				188DB0B726245B4A00F6732D /* TKAppletPlugin */,
				187942C1255CC61D0067C701 /* AliNUI */,
				04BC08AA2383B7B400BF4179 /* QCloud_TTS */,
				04C9121D2382304300BE5C63 /* QCloudSDK */,
				B3FB08B41F09EA5C00EC895E /* Video */,
				DC9BF9332617254400A37295 /* Ifly */,
			);
			path = Modules;
			sourceTree = "<group>";
		};
		B3FB08B41F09EA5C00EC895E /* Video */ = {
			isa = PBXGroup;
			children = (
				33C207A22A146B1B00087D13 /* Zego */,
				B3BF85B21F4D5D3A0060B3E9 /* AnyChat */,
				B32E91C41F18C95E005A6B6D /* TChat */,
			);
			path = Video;
			sourceTree = "<group>";
		};
		BE6CCF3A1B5000B6003DCF98 /* Thinkive */ = {
			isa = PBXGroup;
			children = (
				047AF9672137BF5D003B1366 /* version.txt */,
				BE6CCFB41B5000B6003DCF98 /* Classes */,
				B3FB08B31F09EA5C00EC895E /* Modules */,
				B35569F41DDBF96B00C76E7C /* Plugins */,
				B3AB88921C15704600571BE6 /* TKFMWK */,
			);
			path = Thinkive;
			sourceTree = "<group>";
		};
		BE6CCFB41B5000B6003DCF98 /* Classes */ = {
			isa = PBXGroup;
			children = (
				DC84D1542857290700941BF5 /* Models */,
				DC84D1572857290700941BF5 /* Protocols */,
				BE6CCFB51B5000B6003DCF98 /* Controllers */,
				BE6CCFB81B5000B6003DCF98 /* Services */,
				BE6CCFBC1B5000B6003DCF98 /* Utils */,
				BE6CCFBF1B5000B6003DCF98 /* Vendors */,
				DCD47D3028E18E1600AE44C6 /* Views */,
			);
			path = Classes;
			sourceTree = "<group>";
		};
		BE6CCFB51B5000B6003DCF98 /* Controllers */ = {
			isa = PBXGroup;
			children = (
				B35EBF391C4393770059F885 /* MNavViewController.h */,
				B35EBF3A1C4393770059F885 /* MNavViewController.m */,
				B38D07B61FC402EC0013DA81 /* TKMAlertViewController.h */,
				B38D07B51FC402EC0013DA81 /* TKMAlertViewController.m */,
				BE6CCFB61B5000B6003DCF98 /* TKOpenController.h */,
				BE6CCFB71B5000B6003DCF98 /* TKOpenController.m */,
				B3E9E0D91C1170DC0012E37D /* TKOpenDelegate.h */,
				33A4ED7E2D081533007215C1 /* TKOpenDelegateManager.h */,
				33A4ED7D2D081533007215C1 /* TKOpenDelegateManager.m */,
				B30FD8A51EF1063F000D3E94 /* UIViewController+TKAuthorityKit.h */,
				B30FD8A61EF1063F000D3E94 /* UIViewController+TKAuthorityKit.m */,
				0441008A23C43BD1005B9D05 /* TKFxcAccountInfoType.h */,
				0441008B23C43BD1005B9D05 /* TKFxcAccountInfoType.m */,
				************************ /* TKBaseVideoRecordViewController.h */,
				************************ /* TKBaseVideoRecordViewController.m */,
			);
			path = Controllers;
			sourceTree = "<group>";
		};
		BE6CCFB81B5000B6003DCF98 /* Services */ = {
			isa = PBXGroup;
			children = (
				BE6CCFB91B5000B6003DCF98 /* OpenAccount */,
			);
			path = Services;
			sourceTree = "<group>";
		};
		BE6CCFB91B5000B6003DCF98 /* OpenAccount */ = {
			isa = PBXGroup;
			children = (
				BE6CCFBA1B5000B6003DCF98 /* TKOpenAccountService.h */,
				BE6CCFBB1B5000B6003DCF98 /* TKOpenAccountService.m */,
			);
			path = OpenAccount;
			sourceTree = "<group>";
		};
		BE6CCFBC1B5000B6003DCF98 /* Utils */ = {
			isa = PBXGroup;
			children = (
				DCEAC82429B1E5E000581544 /* Statistic */,
				DCECF95A292F5EBB00A802EE /* SVG */,
				18F129E725FEF96000F26F5C /* FaceDetect */,
				DCBFCBB12A0E472600418EFE /* VideoRecord */,
				************************ /* Player */,
				B3386D691F271753006EF60A /* TKAVCaptureManager.h */,
				B3386D6A1F271753006EF60A /* TKAVCaptureManager.m */,
				B3FF6E3B1EC5B4F0009FAF95 /* TKYKHEmbedHelper.h */,
				B3FF6E3C1EC5B4F0009FAF95 /* TKYKHEmbedHelper.m */,
				B3A2DD311EA461A700B18842 /* TKCommonUtil.h */,
				B3A2DD321EA461A700B18842 /* TKCommonUtil.m */,
				B384CDC81B8177B400AFD817 /* TKButton.h */,
				B384CDC91B8177B400AFD817 /* TKButton.m */,
				B384CDCA1B8177B400AFD817 /* TKCameraTools.h */,
				B384CDCB1B8177B400AFD817 /* TKCameraTools.m */,
				187CC37028F15C5E003442D6 /* TKOpenViewStyleHelper.h */,
				187CC37128F15C5E003442D6 /* TKOpenViewStyleHelper.m */,
				18C46C35291A54A10076BAC0 /* TKChatTokenHelper.h */,
				18C46C36291A54A10076BAC0 /* TKChatTokenHelper.m */,
			);
			path = Utils;
			sourceTree = "<group>";
		};
		BE6CCFBF1B5000B6003DCF98 /* Vendors */ = {
			isa = PBXGroup;
			children = (
				B35EBF3C1C43942F0059F885 /* MDRadialProgress */,
				BE6CCFC31B5000B6003DCF98 /* YLProgressBar */,
			);
			path = Vendors;
			sourceTree = "<group>";
		};
		BE6CCFC31B5000B6003DCF98 /* YLProgressBar */ = {
			isa = PBXGroup;
			children = (
				BE6CCFC41B5000B6003DCF98 /* YLProgressBar.h */,
				BE6CCFC51B5000B6003DCF98 /* YLProgressBar.m */,
			);
			path = YLProgressBar;
			sourceTree = "<group>";
		};
		DC0AC8BC29642E550068F203 /* TKOpenPlugin60087 */ = {
			isa = PBXGroup;
			children = (
				DC0AC8BD29642E550068F203 /* TKOpenPlugin60087.m */,
				DC0AC8BE29642E550068F203 /* Controller */,
				DC0AC8C129642E550068F203 /* View */,
				DC0AC8C429642E550068F203 /* TKOpenPlugin60087.h */,
			);
			path = TKOpenPlugin60087;
			sourceTree = "<group>";
		};
		DC0AC8BE29642E550068F203 /* Controller */ = {
			isa = PBXGroup;
			children = (
				DC0AC8BF29642E550068F203 /* TKWatchVideoViewController.h */,
				DC0AC8C029642E550068F203 /* TKWatchVideoViewController.m */,
			);
			path = Controller;
			sourceTree = "<group>";
		};
		DC0AC8C129642E550068F203 /* View */ = {
			isa = PBXGroup;
			children = (
				DC0AC8C229642E550068F203 /* TKWatchVideoVideoView.m */,
				DC0AC8C329642E550068F203 /* TKWatchVideoVideoView.h */,
			);
			path = View;
			sourceTree = "<group>";
		};
		************************ /* Player */ = {
			isa = PBXGroup;
			children = (
				DC48AC1F2A56A0A2005B7D5C /* Category */,
				DC48AC0A2A56A0A2005B7D5C /* Orientation */,
				DC48AC612A56A0A2005B7D5C /* Protocols */,
				DC48AC282A56A0A2005B7D5C /* TKPlayer.h */,
				DC48AC642A56A0A2005B7D5C /* TKPlayer.m */,
				DC48AC5F2A56A0A2005B7D5C /* TKZFPlayerConst.h */,
				DC48AC272A56A0A2005B7D5C /* TKZFPlayerController.h */,
				DC48AC602A56A0A2005B7D5C /* TKZFPlayerController.m */,
				DC48AC372A56A0A2005B7D5C /* TKZFPlayerGestureControl.h */,
				DC48AC262A56A0A2005B7D5C /* TKZFPlayerGestureControl.m */,
				DC48AC292A56A0A2005B7D5C /* Utils */,
				DC48AC382A56A0A2005B7D5C /* View */,
			);
			path = Player;
			sourceTree = "<group>";
		};
		DC48AC0A2A56A0A2005B7D5C /* Orientation */ = {
			isa = PBXGroup;
			children = (
				DC48AC1A2A56A0A2005B7D5C /* TKZFLandscapeRotationManager_iOS15.h */,
				DC48AC142A56A0A2005B7D5C /* TKZFLandscapeRotationManager_iOS15.m */,
				DC48AC172A56A0A2005B7D5C /* TKZFLandscapeRotationManager_iOS16.h */,
				DC48AC0E2A56A0A2005B7D5C /* TKZFLandscapeRotationManager_iOS16.m */,
				DC48AC162A56A0A2005B7D5C /* TKZFLandscapeRotationManager.h */,
				DC48AC0B2A56A0A2005B7D5C /* TKZFLandscapeRotationManager.m */,
				DC48AC0D2A56A0A2005B7D5C /* TKZFLandscapeViewController_iOS15.h */,
				DC48AC182A56A0A2005B7D5C /* TKZFLandscapeViewController_iOS15.m */,
				DC48AC192A56A0A2005B7D5C /* TKZFLandscapeViewController.h */,
				DC48AC0F2A56A0A2005B7D5C /* TKZFLandscapeViewController.m */,
				DC48AC1C2A56A0A2005B7D5C /* TKZFLandscapeWindow.h */,
				DC48AC122A56A0A2005B7D5C /* TKZFLandscapeWindow.m */,
				DC48AC102A56A0A2005B7D5C /* TKZFOrientationObserver.h */,
				DC48AC1E2A56A0A2005B7D5C /* TKZFOrientationObserver.m */,
				DC48AC112A56A0A2005B7D5C /* TKZFPersentInteractiveTransition.h */,
				DC48AC1D2A56A0A2005B7D5C /* TKZFPersentInteractiveTransition.m */,
				DC48AC132A56A0A2005B7D5C /* TKZFPortraitViewController.h */,
				DC48AC1B2A56A0A2005B7D5C /* TKZFPortraitViewController.m */,
				DC48AC152A56A0A2005B7D5C /* TKZFPresentTransition.h */,
				DC48AC0C2A56A0A2005B7D5C /* TKZFPresentTransition.m */,
			);
			path = Orientation;
			sourceTree = "<group>";
		};
		DC48AC1F2A56A0A2005B7D5C /* Category */ = {
			isa = PBXGroup;
			children = (
				DC48AC252A56A0A2005B7D5C /* UIImageView+TKZFCache.h */,
				DC48AC222A56A0A2005B7D5C /* UIImageView+TKZFCache.m */,
				DC48AC202A56A0A2005B7D5C /* UIScrollView+TKZFPlayer.h */,
				DC48AC242A56A0A2005B7D5C /* UIScrollView+TKZFPlayer.m */,
				DC48AC232A56A0A2005B7D5C /* UIView+TKZFFrame.h */,
				DC48AC212A56A0A2005B7D5C /* UIView+TKZFFrame.m */,
			);
			path = Category;
			sourceTree = "<group>";
		};
		DC48AC292A56A0A2005B7D5C /* Utils */ = {
			isa = PBXGroup;
			children = (
				DC48AC2A2A56A0A2005B7D5C /* TKZFPlayerLogManager.h */,
				DC48AC2B2A56A0A2005B7D5C /* TKZFPlayerNotification.m */,
				DC48AC2C2A56A0A2005B7D5C /* TKZFNetworkSpeedMonitor.h */,
				DC48AC2D2A56A0A2005B7D5C /* TKZFReachabilityManager.m */,
				DC48AC2E2A56A0A2005B7D5C /* TKZFUtilities.h */,
				DC48AC2F2A56A0A2005B7D5C /* TKZFKVOController.m */,
				DC48AC302A56A0A2005B7D5C /* TKZFPlayerLogManager.m */,
				DC48AC312A56A0A2005B7D5C /* TKZFReachabilityManager.h */,
				DC48AC322A56A0A2005B7D5C /* TKZFNetworkSpeedMonitor.m */,
				DC48AC332A56A0A2005B7D5C /* TKZFPlayerNotification.h */,
				DC48AC342A56A0A2005B7D5C /* TKZFKVOController.h */,
				DC48AC352A56A0A2005B7D5C /* TKZFUtilities.m */,
			);
			path = Utils;
			sourceTree = "<group>";
		};
		DC48AC382A56A0A2005B7D5C /* View */ = {
			isa = PBXGroup;
			children = (
				DC48AC392A56A0A2005B7D5C /* LoadingView */,
				DC48AC3E2A56A0A2005B7D5C /* SpeedSelectView */,
				DC48AC412A56A0A2005B7D5C /* VolumeBrightness */,
				DC48AC442A56A0A2005B7D5C /* StatusBar */,
				DC48AC472A56A0A2005B7D5C /* TKZFPlayerView.m */,
				DC48AC482A56A0A2005B7D5C /* FragmentView */,
				DC48AC4F2A56A0A2005B7D5C /* ControlView */,
				DC48AC562A56A0A2005B7D5C /* FloatView */,
				DC48AC5B2A56A0A2005B7D5C /* TKZFPlayerView.h */,
				DC48AC5C2A56A0A2005B7D5C /* SliderView */,
			);
			path = View;
			sourceTree = "<group>";
		};
		DC48AC392A56A0A2005B7D5C /* LoadingView */ = {
			isa = PBXGroup;
			children = (
				DC48AC3A2A56A0A2005B7D5C /* TKZFSpeedLoadingView.m */,
				DC48AC3B2A56A0A2005B7D5C /* TKZFLoadingView.h */,
				DC48AC3C2A56A0A2005B7D5C /* TKZFSpeedLoadingView.h */,
				DC48AC3D2A56A0A2005B7D5C /* TKZFLoadingView.m */,
			);
			path = LoadingView;
			sourceTree = "<group>";
		};
		DC48AC3E2A56A0A2005B7D5C /* SpeedSelectView */ = {
			isa = PBXGroup;
			children = (
				DC48AC3F2A56A0A2005B7D5C /* TKSpeedSelectView.m */,
				DC48AC402A56A0A2005B7D5C /* TKSpeedSelectView.h */,
			);
			path = SpeedSelectView;
			sourceTree = "<group>";
		};
		DC48AC412A56A0A2005B7D5C /* VolumeBrightness */ = {
			isa = PBXGroup;
			children = (
				DC48AC422A56A0A2005B7D5C /* TKZFVolumeBrightnessView.m */,
				DC48AC432A56A0A2005B7D5C /* TKZFVolumeBrightnessView.h */,
			);
			path = VolumeBrightness;
			sourceTree = "<group>";
		};
		DC48AC442A56A0A2005B7D5C /* StatusBar */ = {
			isa = PBXGroup;
			children = (
				DC48AC452A56A0A2005B7D5C /* TKZFPlayerStatusBar.h */,
				DC48AC462A56A0A2005B7D5C /* TKZFPlayerStatusBar.m */,
			);
			path = StatusBar;
			sourceTree = "<group>";
		};
		DC48AC482A56A0A2005B7D5C /* FragmentView */ = {
			isa = PBXGroup;
			children = (
				DC48AC492A56A0A2005B7D5C /* TKFragmentVideoView.h */,
				DC48AC4A2A56A0A2005B7D5C /* TKFragmentTableViewCell.h */,
				DC48AC4B2A56A0A2005B7D5C /* TKVideoFragmentModel.m */,
				DC48AC4C2A56A0A2005B7D5C /* TKFragmentVideoView.m */,
				DC48AC4D2A56A0A2005B7D5C /* TKFragmentTableViewCell.m */,
				DC48AC4E2A56A0A2005B7D5C /* TKVideoFragmentModel.h */,
			);
			path = FragmentView;
			sourceTree = "<group>";
		};
		DC48AC4F2A56A0A2005B7D5C /* ControlView */ = {
			isa = PBXGroup;
			children = (
				DC48AC502A56A0A2005B7D5C /* TKPlayerControlView.h */,
				DC48AC512A56A0A2005B7D5C /* TKZFLandScapeControlView.m */,
				DC48AC522A56A0A2005B7D5C /* TKZFPortraitControlView.m */,
				DC48AC532A56A0A2005B7D5C /* TKZFPortraitControlView.h */,
				DC48AC542A56A0A2005B7D5C /* TKPlayerControlView.m */,
				DC48AC552A56A0A2005B7D5C /* TKZFLandScapeControlView.h */,
			);
			path = ControlView;
			sourceTree = "<group>";
		};
		DC48AC562A56A0A2005B7D5C /* FloatView */ = {
			isa = PBXGroup;
			children = (
				DC48AC572A56A0A2005B7D5C /* TKZFSmallFloatControlView.m */,
				DC48AC582A56A0A2005B7D5C /* TKZFFloatView.m */,
				DC48AC592A56A0A2005B7D5C /* TKZFSmallFloatControlView.h */,
				DC48AC5A2A56A0A2005B7D5C /* TKZFFloatView.h */,
			);
			path = FloatView;
			sourceTree = "<group>";
		};
		DC48AC5C2A56A0A2005B7D5C /* SliderView */ = {
			isa = PBXGroup;
			children = (
				DC48AC5D2A56A0A2005B7D5C /* TKZFSliderView.m */,
				DC48AC5E2A56A0A2005B7D5C /* TKZFSliderView.h */,
			);
			path = SliderView;
			sourceTree = "<group>";
		};
		DC48AC612A56A0A2005B7D5C /* Protocols */ = {
			isa = PBXGroup;
			children = (
				DC48AC622A56A0A2005B7D5C /* TKZFPlayerMediaControl.h */,
				DC48AC632A56A0A2005B7D5C /* TKZFPlayerMediaPlayback.h */,
			);
			path = Protocols;
			sourceTree = "<group>";
		};
		DC6758AA294AEF8B009771ED /* TKOpenPlugin60091 */ = {
			isa = PBXGroup;
			children = (
				DC6758B2294AF072009771ED /* View */,
				DC6758AE294AF02B009771ED /* Controller */,
				DC6758AB294AEFA6009771ED /* TKOpenPlugin60091.h */,
				DC6758AC294AEFA6009771ED /* TKOpenPlugin60091.m */,
			);
			path = TKOpenPlugin60091;
			sourceTree = "<group>";
		};
		DC6758AE294AF02B009771ED /* Controller */ = {
			isa = PBXGroup;
			children = (
				DC6758AF294AF065009771ED /* TKDoubleOrdinaryVideoViewController.h */,
				DC6758B0294AF065009771ED /* TKDoubleOrdinaryVideoViewController.m */,
			);
			path = Controller;
			sourceTree = "<group>";
		};
		DC6758B2294AF072009771ED /* View */ = {
			isa = PBXGroup;
			children = (
				DC6758B6294AF8F8009771ED /* TKDoubleOrdinaryVideoView.h */,
				DC6758B7294AF8F8009771ED /* TKDoubleOrdinaryVideoView.m */,
				DC6758B3294AF380009771ED /* TKDoubleOrdinaryVideoEndView.h */,
				DC6758B4294AF380009771ED /* TKDoubleOrdinaryVideoEndView.m */,
			);
			path = View;
			sourceTree = "<group>";
		};
		DC6CDB842650F1FC004E7295 /* TKOpenPlugin60059 */ = {
			isa = PBXGroup;
			children = (
				DC6CDB8D2650F22A004E7295 /* Controller */,
				DC6CDB8C2650F223004E7295 /* View */,
				DC6CDB852650F20D004E7295 /* TKOpenPlugin60059.h */,
				DC6CDB862650F20D004E7295 /* TKOpenPlugin60059.m */,
			);
			path = TKOpenPlugin60059;
			sourceTree = "<group>";
		};
		DC6CDB8C2650F223004E7295 /* View */ = {
			isa = PBXGroup;
			children = (
				DC6CDB952650FF68004E7295 /* TKChatLiveFaceView.h */,
				DC6CDB962650FF68004E7295 /* TKChatLiveFaceView.m */,
			);
			path = View;
			sourceTree = "<group>";
		};
		DC6CDB8D2650F22A004E7295 /* Controller */ = {
			isa = PBXGroup;
			children = (
				DC6CDB8E2650F289004E7295 /* TKChatLiveDetectViewController.h */,
				DC6CDB8F2650F289004E7295 /* TKChatLiveDetectViewController.m */,
			);
			path = Controller;
			sourceTree = "<group>";
		};
		DC84D1542857290700941BF5 /* Models */ = {
			isa = PBXGroup;
			children = (
				DC84D1552857290700941BF5 /* TKSmartQuestionModel.m */,
				DC84D1562857290700941BF5 /* TKSmartQuestionModel.h */,
			);
			path = Models;
			sourceTree = "<group>";
		};
		DC84D1572857290700941BF5 /* Protocols */ = {
			isa = PBXGroup;
			children = (
				BE05BBF32D39FAFB002505D6 /* TKRecordManagerProtocol.h */,
				DC84D1592857290700941BF5 /* TKBaseVideoRecordViewProtocol.h */,
				DC84D15A2857290700941BF5 /* TKBaseVideoRecordEndViewProtocol.h */,
			);
			path = Protocols;
			sourceTree = "<group>";
		};
		DC9BF9332617254400A37295 /* Ifly */ = {
			isa = PBXGroup;
			children = (
				33DAFA9F2C61E8C6009F2243 /* iflyMSC.framework */,
			);
			path = Ifly;
			sourceTree = "<group>";
		};
		DCA229B4295ACB34005C6688 /* TChatRtc */ = {
			isa = PBXGroup;
			children = (
				DCA22A26295AF8AA005C6688 /* TKChatRtcVideoRecordManager.h */,
				DCA22A25295AF8AA005C6688 /* TKChatRtcVideoRecordManager.m */,
			);
			path = TChatRtc;
			sourceTree = "<group>";
		};
		DCA6A87A29CC5E7D003D0FE7 /* SmartCtr */ = {
			isa = PBXGroup;
			children = (
				DC44551D2A380F7A0073C229 /* TKSmartTwoVideoController.h */,
				DC44551E2A380F7B0073C229 /* TKSmartTwoVideoController.m */,
			);
			path = SmartCtr;
			sourceTree = "<group>";
		};
		DCBFCBB12A0E472600418EFE /* VideoRecord */ = {
			isa = PBXGroup;
			children = (
				************************ /* TKChatErrorConverter.h */,
				************************ /* TKChatErrorConverter.m */,
				DC4455052A380E6C0073C229 /* TKAnyChatSmartTwoVideoManager.h */,
				DC44550F2A380E6F0073C229 /* TKAnyChatSmartTwoVideoManager.m */,
				DC44550A2A380E6E0073C229 /* TKChatVideoRecordManager.h */,
				DC44550D2A380E6F0073C229 /* TKChatVideoRecordManager.m */,
				DC4455072A380E6D0073C229 /* TKSampleBufferConverter.h */,
				33DAFAA72C61F983009F2243 /* TKTChatRtcSmartTwoVideoManager.h */,
				33DAFAA82C61F983009F2243 /* TKTChatRtcSmartTwoVideoManager.m */,
				DC4455142A380E720073C229 /* TKSampleBufferConverter.m */,
				DC4455092A380E6D0073C229 /* TKSmartTwoVideoManager.h */,
				************************ /* TKSmartTwoVideoManager.m */,
				DC44550C2A380E6E0073C229 /* TKSmartTwoVideoManagerProtocol.h */,
				DC4455132A380E720073C229 /* TKTChatSmartTwoVideoManager.h */,
				DC4455102A380E700073C229 /* TKTChatSmartTwoVideoManager.m */,
				DC4455042A380E6C0073C229 /* TKVideoRecordManager.h */,
				************************ /* TKVideoRecordManager.mm */,
				DC4455062A380E6D0073C229 /* TKZegoSmartTwoVideoManager.h */,
				DC4455112A380E700073C229 /* TKZegoSmartTwoVideoManager.m */,
				DC44552C2A3858330073C229 /* TKSampleBufferConverter.h */,
			);
			path = VideoRecord;
			sourceTree = "<group>";
		};
		DCC0429429434D1900BDF14D /* TKOpenPlugin60077 */ = {
			isa = PBXGroup;
			children = (
				DCC0429529434D1900BDF14D /* TKOpenPlugin60077.m */,
				DCC0429629434D1900BDF14D /* Controller */,
				DCC0429929434D1900BDF14D /* View */,
				DCC0429E29434D1900BDF14D /* TKOpenPlugin60077.h */,
			);
			path = TKOpenPlugin60077;
			sourceTree = "<group>";
		};
		DCC0429629434D1900BDF14D /* Controller */ = {
			isa = PBXGroup;
			children = (
				DCC0429729434D1900BDF14D /* TKDoubleChatVideoRecordViewController.h */,
				DCC0429829434D1900BDF14D /* TKDoubleChatVideoRecordViewController.m */,
			);
			path = Controller;
			sourceTree = "<group>";
		};
		DCC0429929434D1900BDF14D /* View */ = {
			isa = PBXGroup;
			children = (
				DCC0429A29434D1900BDF14D /* TKDoubleChatVideoRecordView.m */,
				DCC0429B29434D1900BDF14D /* TKDoubleChatVideoRecordEndView.h */,
				DCC0429C29434D1900BDF14D /* TKDoubleChatVideoRecordEndView.m */,
				DCC0429D29434D1900BDF14D /* TKDoubleChatVideoRecordView.h */,
			);
			path = View;
			sourceTree = "<group>";
		};
		DCC042A72947430200BDF14D /* TKOpenPlugin60089 */ = {
			isa = PBXGroup;
			children = (
				DCC042A92947430200BDF14D /* Controller */,
				DCC042AA2947430200BDF14D /* TKOpenPlugin60089.h */,
				DCC042A82947430200BDF14D /* TKOpenPlugin60089.m */,
				DCC042AB2947430200BDF14D /* View */,
			);
			path = TKOpenPlugin60089;
			sourceTree = "<group>";
		};
		DCC042A92947430200BDF14D /* Controller */ = {
			isa = PBXGroup;
			children = (
				DCC042AD294744A900BDF14D /* TKDoudleOneWayVideoViewController.h */,
				DCC042AE294744A900BDF14D /* TKDoudleOneWayVideoViewController.m */,
			);
			path = Controller;
			sourceTree = "<group>";
		};
		DCC042AB2947430200BDF14D /* View */ = {
			isa = PBXGroup;
			children = (
				DCC042B02948161200BDF14D /* TKDoubleOneWayVideoView.h */,
				DCC042B12948161200BDF14D /* TKDoubleOneWayVideoView.m */,
				DCC042B32948166800BDF14D /* TKDoubleOneWayVideoEndView.h */,
				DCC042B42948166800BDF14D /* TKDoubleOneWayVideoEndView.m */,
			);
			path = View;
			sourceTree = "<group>";
		};
		DCC5CE4F2B207FF7001C82A6 /* ReadingView */ = {
			isa = PBXGroup;
			children = (
				DCC5CE512B207FF7001C82A6 /* TKReadingView.h */,
				DCC5CE502B207FF7001C82A6 /* TKReadingView.m */,
			);
			path = ReadingView;
			sourceTree = "<group>";
		};
		DCC7E7DF26299A29006D378F /* TKOpenPlugin60057 */ = {
			isa = PBXGroup;
			children = (
				DCC7E7E726299FB7006D378F /* View */,
				DCC7E7E626299FAE006D378F /* Controller */,
				DCC7E7E126299A56006D378F /* TKOpenPlugin60057.h */,
				DCC7E7E226299A56006D378F /* TKOpenPlugin60057.m */,
			);
			path = TKOpenPlugin60057;
			sourceTree = "<group>";
		};
		DCC7E7E626299FAE006D378F /* Controller */ = {
			isa = PBXGroup;
			children = (
				DCC7E7E8262A9B92006D378F /* TKChatVideoRecordViewController.h */,
				DCC7E7E9262A9B92006D378F /* TKChatVideoRecordViewController.m */,
			);
			path = Controller;
			sourceTree = "<group>";
		};
		DCC7E7E726299FB7006D378F /* View */ = {
			isa = PBXGroup;
			children = (
				DC6F5D01262EB34400B87A5B /* TKChatVideoRecordEndView.h */,
				DC6F5D02262EB34400B87A5B /* TKChatVideoRecordEndView.m */,
				DC6F5D07262EB49800B87A5B /* TKChatVideoRecordView.h */,
				DC6F5D08262EB49800B87A5B /* TKChatVideoRecordView.m */,
			);
			path = View;
			sourceTree = "<group>";
		};
		DCCDDA18295C2AC5002F268B /* FaceDetectTipView */ = {
			isa = PBXGroup;
			children = (
				DCCDDA19295C2AF5002F268B /* TKFaceDectTipView.h */,
				DCCDDA1A295C2AF5002F268B /* TKFaceDectTipView.m */,
			);
			path = FaceDetectTipView;
			sourceTree = "<group>";
		};
		DCD47D3028E18E1600AE44C6 /* Views */ = {
			isa = PBXGroup;
			children = (
				331179A32E1684FC002179DC /* IDCardPhotoView */,
				DCC5CE4F2B207FF7001C82A6 /* ReadingView */,
				DCCDDA18295C2AC5002F268B /* FaceDetectTipView */,
				DCD47D3328E18E1600AE44C6 /* TKBaseVideoRecordEndView.h */,
				DCD47D3128E18E1600AE44C6 /* TKBaseVideoRecordEndView.m */,
				DCD47D3428E18E1600AE44C6 /* TKBaseVideoRecordView.h */,
				DCD47D3228E18E1600AE44C6 /* TKBaseVideoRecordView.m */,
				DCC262C0291E1C8400018C4E /* TKBaseVideoRecordLandscapeView.h */,
				DCC262C1291E1C8400018C4E /* TKBaseVideoRecordLandscapeView.m */,
				DC38DECF291E24BA00362354 /* TKBaseVideoRecordEndLandscapeView.h */,
				************************ /* TKBaseVideoRecordEndLandscapeView.m */,
			);
			path = Views;
			sourceTree = "<group>";
		};
		DCD47D4228E1B18D00AE44C6 /* TKOpenPlugin60072 */ = {
			isa = PBXGroup;
			children = (
				DCD47D4328E1B18D00AE44C6 /* TKOpenPlugin60072.h */,
				DCD47D4428E1B18D00AE44C6 /* Controller */,
				DCD47D4728E1B18D00AE44C6 /* TKOpenPlugin60072.m */,
				DCD47D4928E1B18D00AE44C6 /* View */,
			);
			path = TKOpenPlugin60072;
			sourceTree = "<group>";
		};
		DCD47D4428E1B18D00AE44C6 /* Controller */ = {
			isa = PBXGroup;
			children = (
				DCD47D4528E1B18D00AE44C6 /* TKSmartVirtualManViewController.h */,
				DCD47D4628E1B18D00AE44C6 /* TKSmartVirtualManViewController.m */,
			);
			path = Controller;
			sourceTree = "<group>";
		};
		DCD47D4928E1B18D00AE44C6 /* View */ = {
			isa = PBXGroup;
			children = (
				DCD47D4A28E1B18D00AE44C6 /* TKSmartVirtualManVideoView.h */,
				DCD47D4B28E1B18D00AE44C6 /* TKSmartVirtualManVideoEndView.m */,
				DCD47D4C28E1B18D00AE44C6 /* TKSmartVirtualManVideoView.m */,
				DCD47D4D28E1B18D00AE44C6 /* TKSmartVirtualManVideoEndView.h */,
			);
			path = View;
			sourceTree = "<group>";
		};
		DCEAC82429B1E5E000581544 /* Statistic */ = {
			isa = PBXGroup;
			children = (
				DCEAC82529B1E5FB00581544 /* TKStatisticEventHelper.h */,
				DCEAC82729B1E5FB00581544 /* TKStatisticEventHelper.m */,
				DCEAC82629B1E5FB00581544 /* TKStatisticEventHelperPrivate.h */,
			);
			path = Statistic;
			sourceTree = "<group>";
		};
		DCECF95A292F5EBB00A802EE /* SVG */ = {
			isa = PBXGroup;
			children = (
				DCECF95D292F5EF000A802EE /* TKSVGBezierPath.h */,
				DCECF95E292F5EF000A802EE /* TKSVGBezierPath.mm */,
				DCECF961292F5EF000A802EE /* TKSVGEngine.h */,
				DCECF95B292F5EEF00A802EE /* TKSVGEngine.mm */,
				DCECF963292F5EF000A802EE /* TKSVGImageView.h */,
				DCECF964292F5EF000A802EE /* TKSVGImageView.m */,
				DCECF966292F5EF000A802EE /* TKSVGLayer.h */,
				DCECF95C292F5EF000A802EE /* TKSVGLayer.m */,
				DCECF96C292F628A00A802EE /* TKSVGImage.h */,
				DCECF96D292F628A00A802EE /* TKSVGImage.m */,
			);
			path = SVG;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		B3E1B30A1B8DB80800CDD258 /* TKOpenResource */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = B3E1B30F1B8DB80800CDD258 /* Build configuration list for PBXNativeTarget "TKOpenResource" */;
			buildPhases = (
				B3E1B3091B8DB80800CDD258 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = TKOpenResource;
			productName = TKOpenResource;
			productReference = B3E1B30B1B8DB80800CDD258 /* TKOpenResource.bundle */;
			productType = "com.apple.product-type.bundle";
		};
		B3FACBB01AFDDF0E0088CDF1 /* TKOpenAccount-Standard */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = B3FACBD41AFDDF0E0088CDF1 /* Build configuration list for PBXNativeTarget "TKOpenAccount-Standard" */;
			buildPhases = (
				B3FACBAD1AFDDF0E0088CDF1 /* Sources */,
				B3FACBAE1AFDDF0E0088CDF1 /* Frameworks */,
				B3FACBAF1AFDDF0E0088CDF1 /* Resources */,
				04EE357922732298009EA9D1 /* Embed Frameworks */,
				33294B0A2A0A453500A72052 /* ShellScript */,
			);
			buildRules = (
			);
			dependencies = (
				B3AA8EC31EBB2B0400255737 /* PBXTargetDependency */,
			);
			name = "TKOpenAccount-Standard";
			productName = "TKOpenAccount-Standard";
			productReference = B3FACBB11AFDDF0E0088CDF1 /* 思迪TChatRtc.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		B3FACBA91AFDDF0E0088CDF1 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				LastSwiftUpdateCheck = 1230;
				LastUpgradeCheck = 0630;
				ORGANIZATIONNAME = thinkive;
				TargetAttributes = {
					B3D794591B8EA14F00768134 = {
						CreatedOnToolsVersion = 6.4;
					};
					B3E1B30A1B8DB80800CDD258 = {
						CreatedOnToolsVersion = 6.4;
						DevelopmentTeam = HCXVYWN652;
						ProvisioningStyle = Automatic;
					};
					B3FACBB01AFDDF0E0088CDF1 = {
						CreatedOnToolsVersion = 6.3;
					};
				};
			};
			buildConfigurationList = B3FACBAC1AFDDF0E0088CDF1 /* Build configuration list for PBXProject "TKOpenAccount-Standard" */;
			compatibilityVersion = "Xcode 3.2";
			developmentRegion = English;
			hasScannedForEncodings = 0;
			knownRegions = (
				English,
				en,
				Base,
			);
			mainGroup = B3FACBA81AFDDF0E0088CDF1;
			productRefGroup = B3FACBB21AFDDF0E0088CDF1 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				B3FACBB01AFDDF0E0088CDF1 /* TKOpenAccount-Standard */,
				B3E1B30A1B8DB80800CDD258 /* TKOpenResource */,
				B3D794591B8EA14F00768134 /* build_TKOpenResource */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		B3E1B3091B8DB80800CDD258 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
				B3556ADB1DDBFA7300C76E7C /* chat_cancel_btn.png in Resources */,
				B3556ADC1DDBFA7300C76E7C /* kefu_bg_img.png in Resources */,
				04D9604922C3366E0010E4C6 /* Configuration_Open_BuriedPoint.xml in Resources */,
				B3556ADD1DDBFA7300C76E7C /* page_bg.jpg in Resources */,
				B3556ADE1DDBFA7300C76E7C /* TKAnyChatViewController.xib in Resources */,
				B3556ADF1DDBFA7300C76E7C /* TKAnyChatViewController4.xib in Resources */,
				B30DC50A1BCFC873007072FB /* Configuration.xml in Resources */,
				B35087C41F0E367300A366A0 /* tk_video_icon_iphone.png in Resources */,
				B3E1B3121B8DB81400CDD258 /* Resources in Resources */,
				B30DC50B1BCFC873007072FB /* OpenPlugin.xml in Resources */,
				B32C870C1D87E96E00373C19 /* config.plist in Resources */,
				043884B923B05A240009F14D /* KeyBoard.xml in Resources */,
				B3FF6E3A1EC5B2A0009FAF95 /* tk_open in Resources */,
				B35087C51F0E367300A366A0 /* TKVideoWitnessViewController.xib in Resources */,
				B30DC50C1BCFC873007072FB /* SystemPlugin.xml in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		B3FACBAF1AFDDF0E0088CDF1 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
				336BA7FD29BF2A1000134194 /* <EMAIL> in Resources */,
				336BA7F929BF2A1000134194 /* is_camera_corner_highlight.png in Resources */,
				336BA7E729BF2A1000134194 /* <EMAIL> in Resources */,
				18FF460F28E1B7AE0026440D /* STLivenessModel.bundle in Resources */,
				336BA7E129BF2A1000134194 /* <EMAIL> in Resources */,
				336BA7F229BF2A1000134194 /* <EMAIL> in Resources */,
				336BA7ED29BF2A1000134194 /* <EMAIL> in Resources */,
				336BA7E429BF2A1000134194 /* is_camera_flash_on.png in Resources */,
				DCBFCEFD2A122B5600418EFE /* (null) in Resources */,
				B3FCF6131BAFB2F3009AB7C0 /* LaunchScreen.xib in Resources */,
				336BA7F429BF2A1000134194 /* <EMAIL> in Resources */,
				DC4455302A3858530073C229 /* www in Resources */,
				336BA7F829BF2A1000134194 /* is_camera_flash_ccb_on.png in Resources */,
				336BA7FC29BF2A1000134194 /* <EMAIL> in Resources */,
				336BA7EB29BF2A1000134194 /* <EMAIL> in Resources */,
				336BA7F029BF2A1000134194 /* <EMAIL> in Resources */,
				336BA7EA29BF2A1000134194 /* is_camera_corner.png in Resources */,
				188DB0BF26245B4A00F6732D /* README.txt in Resources */,
				336BA7E529BF2A1000134194 /* <EMAIL> in Resources */,
				B3AB8AFA1C15708900571BE6 /* TKOpenResource.bundle in Resources */,
				336BA7DC29BF2A1000134194 /* <EMAIL> in Resources */,
				183396A3245C298B00ECE057 /* READMEZXJT.txt in Resources */,
				336BA7E029BF2A1000134194 /* <EMAIL> in Resources */,
				336BA7E929BF2A1000134194 /* is_camera_flash__ccb_off.png in Resources */,
				336BA7DB29BF2A1000134194 /* is_camera_closed.png in Resources */,
				B3FACBC21AFDDF0E0088CDF1 /* Images.xcassets in Resources */,
				047AF9682137BF5E003B1366 /* version.txt in Resources */,
				336BA7E829BF2A1000134194 /* <EMAIL> in Resources */,
				E22996F626AABFB90039F6E9 /* Main.storyboard in Resources */,
				336BA7EF29BF2A1000134194 /* <EMAIL> in Resources */,
				336BA7E629BF2A1000134194 /* <EMAIL> in Resources */,
				336BA7E329BF2A1000134194 /* is_camera_closed_ccb.png in Resources */,
				188FC5B82468F43300F7D2A3 /* JT_BasicControlImages.bundle in Resources */,
				336BA7F529BF2A1000134194 /* <EMAIL> in Resources */,
				336BA7DD29BF2A1000134194 /* <EMAIL> in Resources */,
				336BA7F629BF2A1000134194 /* <EMAIL> in Resources */,
				18FF460E28E1B7AE0026440D /* SenseID_Liveness_Silent.lic in Resources */,
				336BA7EE29BF2A1000134194 /* <EMAIL> in Resources */,
				336BA7F129BF2A1000134194 /* is_camera_logo.png in Resources */,
				336BA7DF29BF2A1000134194 /* is_camera_corner_highlight_Red.png in Resources */,
				1893503B296547610052E77B /* DemoTableViewCell.xib in Resources */,
				336BA7FE29BF2A1000134194 /* <EMAIL> in Resources */,
				334D516F2C61BE6D00B1E35C /* PrivacyInfo.xcprivacy in Resources */,
				049A8350211ECD6B00AA2048 /* TKAsset.bundle in Resources */,
				336BA7EC29BF2A1000134194 /* <EMAIL> in Resources */,
				336BA7FB29BF2A1000134194 /* <EMAIL> in Resources */,
				3324DB272C47D86700D5E61B /* TKSDKAuth.lic in Resources */,
				336BA7DE29BF2A1000134194 /* <EMAIL> in Resources */,
				336BA7F729BF2A1000134194 /* <EMAIL> in Resources */,
				336BA7FA29BF2A1000134194 /* is_camera_flash_off.png in Resources */,
				336BA7F329BF2A1000134194 /* <EMAIL> in Resources */,
				336BA7E229BF2A1000134194 /* <EMAIL> in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		33294B0A2A0A453500A72052 /* ShellScript */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
			);
			outputFileListPaths = (
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "APP_PATH=\"${TARGET_BUILD_DIR}/${WRAPPER_NAME}\"\n\n# This script loops through the frameworks embedded in the application and\n# removes unused architectures.\n\nfind \"$APP_PATH\" -name '*.framework' -type d | while read -r FRAMEWORK\ndo\n    FRAMEWORK_EXECUTABLE_NAME=$(defaults read \"$FRAMEWORK/Info.plist\" CFBundleExecutable)\n    FRAMEWORK_EXECUTABLE_PATH=\"$FRAMEWORK/$FRAMEWORK_EXECUTABLE_NAME\"\n    echo \"Executable is $FRAMEWORK_EXECUTABLE_PATH\"\n\n    # 检查文件是否存在\n    if [ ! -f \"$FRAMEWORK_EXECUTABLE_PATH\" ]; then\n        echo \"Error: File does not exist: $FRAMEWORK_EXECUTABLE_PATH\"\n        continue\n    fi\n\n    # 检查文件是否包含多个架构\n    echo \"Checking architectures for $FRAMEWORK_EXECUTABLE_NAME\"\n    LIPO_OUTPUT=$(lipo -info \"$FRAMEWORK_EXECUTABLE_PATH\" 2>&1)\n\n    echo \"LIPO_OUTPUT: $LIPO_OUTPUT\"\n    \n    if [ $? -ne 0 ]; then\n        echo \"Error running lipo: $LIPO_OUTPUT\"\n        continue\n    fi\n    \n    # 输出示例Non-fat file: /Users/<USER>/Library/Developer/Xcode/DerivedData/TKWealthDemo-gxceytazperrlscjcsmavemrmrrr/Build/Products/Debug-iphoneos/TKWealthDemo.app/Frameworks/TKWebViewApp.framework/TKWebViewApp is architecture: arm64\n    ARCHS_IN_FILE=$(echo \"$LIPO_OUTPUT\" | awk -F': ' '{print $3}' | tr -d ' ')\n    echo \"Architectures found: $ARCHS_IN_FILE\"\n    \n    if [ -z \"$ARCHS_IN_FILE\" ]; then\n        echo \"Warning: No architectures found in $FRAMEWORK_EXECUTABLE_NAME\"\n        continue\n    fi\n    \n    ARCH_COUNT=$(echo \"$ARCHS_IN_FILE\" | wc -w)\n    echo \"Architecture count: $ARCH_COUNT\"\n    \n    if [[ \"$LIPO_OUTPUT\" == *\"fat file\"* && \"$ARCH_COUNT\" -gt 1 ]]; then\n        # 如果是 fat file且有多个架构，按原来的逻辑处理\n        EXTRACTED_ARCHS=()\n        for ARCH in $ARCHS\n        do\n            echo \"Extracting $ARCH from $FRAMEWORK_EXECUTABLE_NAME\"\n            lipo -extract \"$ARCH\" \"$FRAMEWORK_EXECUTABLE_PATH\" -o \"$FRAMEWORK_EXECUTABLE_PATH-$ARCH\"\n            EXTRACTED_ARCHS+=(\"$FRAMEWORK_EXECUTABLE_PATH-$ARCH\")\n        done\n\n        echo \"Merging extracted architectures: ${ARCHS}\"\n        lipo -o \"$FRAMEWORK_EXECUTABLE_PATH-merged\" -create \"${EXTRACTED_ARCHS[@]}\"\n        rm \"${EXTRACTED_ARCHS[@]}\"\n        echo \"Replacing original executable with thinned version\"\n        rm \"$FRAMEWORK_EXECUTABLE_PATH\"\n        mv \"$FRAMEWORK_EXECUTABLE_PATH-merged\" \"$FRAMEWORK_EXECUTABLE_PATH\"\n    else\n        # 如果是单个架构，检查是否是我们需要的架构之一\n        CURRENT_ARCH=$(echo \"$ARCHS_IN_FILE\" | tr -d ' ')\n        echo \"Current architecture: $CURRENT_ARCH\"\n        ARCH_FOUND=0\n        for ARCH in $ARCHS\n        do\n            if [ \"$CURRENT_ARCH\" = \"$ARCH\" ]; then\n                ARCH_FOUND=1\n                break\n            fi\n        done\n        \n        if [ \"$ARCH_FOUND\" -eq 0 ]; then\n            echo \"Warning: $FRAMEWORK_EXECUTABLE_NAME contains only $CURRENT_ARCH architecture, which is not in the target architectures ($ARCHS)\"\n        else\n            echo \"Framework $FRAMEWORK_EXECUTABLE_NAME already contains the correct architecture ($CURRENT_ARCH)\"\n        fi\n    fi\n\ndone \n";
		};
		B3D7945D1B8EA15400768134 /* ShellScript */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputPaths = (
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "# Sets the target folders and the final framework product.\nFMK_NAME=TKOpenResource\n# Install dir will be the final output to the framework.\n# The following line create it in the root folder of the current project.\nINSTALL_DIR=${SRCROOT}/Thinkive/TKFMWK/${FMK_NAME}.bundle\n\n# Working dir will be deleted after the framework creation.\nWRK_DIR=build\nDEVICE_DIR=${WRK_DIR}/Release-iphoneos/${FMK_NAME}.bundle\n#SIMULATOR_DIR=${WRK_DIR}/Release-iphonesimulator/${FMK_NAME}.bundle\n# Building both architectures.\nxcodebuild -configuration \"Release\" -target \"${FMK_NAME}\" -sdk iphoneos\n#xcodebuild -configuration \"Release\" -target \"${FMK_NAME}\" -sdk iphonesimulator\n# Cleaning the oldest.\nif [ -d \"${INSTALL_DIR}\" ]\nthen\nrm -rf \"${INSTALL_DIR}\"\nfi\n# Creates and renews the final product folder.\nmkdir -p \"${INSTALL_DIR}\"\n# Copies the headers and resources files to the final product folder.\ncp -R \"${DEVICE_DIR}/\" \"${INSTALL_DIR}/\"\n\n# Cleaning the oldest.\nrm -r \"${WRK_DIR}\"\n";
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		B3FACBAD1AFDDF0E0088CDF1 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				DC48AC7C2A56A0A2005B7D5C /* TKSpeedSelectView.m in Sources */,
				DC48AC892A56A0A2005B7D5C /* TKZFPlayerController.m in Sources */,
				DC48AC842A56A0A2005B7D5C /* TKZFPortraitControlView.m in Sources */,
				DC48AC652A56A0A2005B7D5C /* TKZFLandscapeRotationManager.m in Sources */,
				331179A42E1684FC002179DC /* TKCardPreview.m in Sources */,
				331179A52E1684FC002179DC /* TKIDCardPhotoView.m in Sources */,
				331179A62E1684FC002179DC /* TKCardAlbumPreview.m in Sources */,
				189350762965498F0052E77B /* IQPreviousNextView.m in Sources */,
				334CE34D29FB651E00FD2A69 /* (null) in Sources */,
				1893507A2965498F0052E77B /* IQUIScrollView+Additions.m in Sources */,
				043015EC23FCC811004F0C17 /* TKOpenPlugin60028.m in Sources */,
				189350712965498F0052E77B /* IQTextView.m in Sources */,
				0472CA8623FB9DD4000B5EB7 /* TKOpenPlugin60007.m in Sources */,
				33A4ED7F2D081533007215C1 /* TKOpenDelegateManager.m in Sources */,
				B30FD8A71EF1063F000D3E94 /* UIViewController+TKAuthorityKit.m in Sources */,
				B3556A8E1DDBF96B00C76E7C /* TKOpenPlugin60000.m in Sources */,
				B384CDCC1B8177B400AFD817 /* TKButton.m in Sources */,
				B3556AB01DDBF96B00C76E7C /* MTakeBigPictureViewController.m in Sources */,
				18F129F825FF231600F26F5C /* TKOpenTipView.m in Sources */,
				DCD47D5128E1B18D00AE44C6 /* TKSmartVirtualManVideoView.m in Sources */,
				DCC042B22948161200BDF14D /* TKDoubleOneWayVideoView.m in Sources */,
				189350752965498F0052E77B /* IQUIView+IQKeyboardToolbar.m in Sources */,
				0472CA8B23FB9EC4000B5EB7 /* TKLiveFaceViewController.m in Sources */,
				187F361826AAB4680046E9E3 /* TKOneWayLandscapeVideoViewController.mm in Sources */,
				DC44551F2A380F7B0073C229 /* TKSmartTwoVideoController.m in Sources */,
				331ABDC22BA4107D00F0B963 /* TKOpenPlugin60095.m in Sources */,
				048DC9962407E66400A6F3EC /* TKOrdinaryOneVideoView.m in Sources */,
				04E5B290241CABE20069BCEE /* TKDirectVideoChatView.m in Sources */,
				1893504E296547610052E77B /* TKTakeIDCardTestVC.m in Sources */,
				DC0AC8C629642E550068F203 /* TKWatchVideoViewController.m in Sources */,
				189F778926D8E6AE00F4089E /* TTTAttributedLabel.m in Sources */,
				333DD1C22E24CF7E00DABD89 /* TKVideoRollTextView.m in Sources */,
				DC48AC672A56A0A2005B7D5C /* TKZFLandscapeRotationManager_iOS16.m in Sources */,
				DC48AC742A56A0A2005B7D5C /* TKZFReachabilityManager.m in Sources */,
				DCB07F062970060B009D0140 /* TKWatchVideoVideoView.m in Sources */,
				183D05C229384A8000346A76 /* TKOrganizationSinglePhotoViewController.m in Sources */,
				B35EBF451C43942F0059F885 /* MDRadialProgressView.m in Sources */,
				B35EBF3B1C4393770059F885 /* MNavViewController.m in Sources */,
				DCC7E86B262D7134006D378F /* TKTempService.m in Sources */,
				33C207E42A148B2700087D13 /* TKALSpeechRecognizeManager.mm in Sources */,
				182CA7DE25BA6B820084C9F8 /* TKOpenPlugin60044.m in Sources */,
				33C207E92A148B2700087D13 /* TKOneWayVideoView.m in Sources */,
				DC48AC872A56A0A2005B7D5C /* TKZFFloatView.m in Sources */,
				B3FACBBD1AFDDF0E0088CDF1 /* ViewController.m in Sources */,
				DCEAC82829B1E5FB00581544 /* TKStatisticEventHelper.m in Sources */,
				DC48AC822A56A0A2005B7D5C /* TKFragmentTableViewCell.m in Sources */,
				B30FD8B01EF11ED2000D3E94 /* TKVideoWitnessViewController+CommonKit.m in Sources */,
				B384CDCD1B8177B400AFD817 /* TKCameraTools.m in Sources */,
				1893504D296547610052E77B /* TChatRtcOneWayVideoTestVC.m in Sources */,
				189350772965498F0052E77B /* IQKeyboardManager.m in Sources */,
				18714D8824454DE7002D809B /* TKVideoMsgTableViewCell.m in Sources */,
				043015EF23FCC936004F0C17 /* TKFaceImageViewController.m in Sources */,
				DC48AC722A56A0A2005B7D5C /* TKZFPlayerGestureControl.m in Sources */,
				18FC9C84295D89320081518D /* TKOpenPlugin60093.m in Sources */,
				BE6CD0471B5000B7003DCF98 /* YLProgressBar.m in Sources */,
				DCECF96E292F628A00A802EE /* TKSVGImage.m in Sources */,
				DCECF96B292F5EF000A802EE /* TKSVGImageView.m in Sources */,
				18138943244B269A007F96FA /* TKOpenPlugin60037.m in Sources */,
				B3556AAE1DDBF96B00C76E7C /* TKOpenPlugin60010.m in Sources */,
				04652354212E84C100F8C0D0 /* TKSignatureController.m in Sources */,
				DC48AC802A56A0A2005B7D5C /* TKVideoFragmentModel.m in Sources */,
				043015F323FCCDBF004F0C17 /* TKFaceImageView.m in Sources */,
				DCC042A029434D1900BDF14D /* TKDoubleChatVideoRecordViewController.m in Sources */,
				33C207DE2A148B2700087D13 /* TKringBuf.cpp in Sources */,
				1876A14E2949CF57007F0500 /* TKTakeIDPhotoViewController.m in Sources */,
				189350742965498F0052E77B /* IQBarButtonItem.m in Sources */,
				33C207E82A148B2700087D13 /* TKOneWayVideoEndView.m in Sources */,
				DC48AC7D2A56A0A2005B7D5C /* TKZFVolumeBrightnessView.m in Sources */,
				E22996F526AABD490039F6E9 /* SelectViewController.m in Sources */,
				336D640C29C3297800BB96A1 /* TKOrganizationDoubleVideoResultSuccessViewController.m in Sources */,
				336D640D29C3297800BB96A1 /* TKOrganizationDoubleVideoResultBaseViewController.m in Sources */,
				188DB0BE26245B4A00F6732D /* TKAppletPluginManager.m in Sources */,
				DC5366482A94B9240064288D /* TKChatVideoRecordManager.m in Sources */,
				1893503D296547610052E77B /* DemoViewController.m in Sources */,
				DC48AC692A56A0A2005B7D5C /* TKZFLandscapeWindow.m in Sources */,
				187F361A26AAB4680046E9E3 /* TKOneWayLandscapeVideoEndView.m in Sources */,
				DC44551C2A380E720073C229 /* TKSampleBufferConverter.m in Sources */,
				3363023F2D83C202009BC4DC /* TKOpenPlugin60041.m in Sources */,
				336302402D83C202009BC4DC /* TKOpenPlugin60096.m in Sources */,
				336302412D83C202009BC4DC /* TKOpenOneClickLoginViewController.m in Sources */,
				336302422D83C202009BC4DC /* TKOpenOneClickLoginService.m in Sources */,
				B3556AB21DDBF96B00C76E7C /* TKOpenPlugin60013.m in Sources */,
				DC48AC782A56A0A2005B7D5C /* TKZFUtilities.m in Sources */,
				DCC262C2291E1C8400018C4E /* TKBaseVideoRecordLandscapeView.m in Sources */,
				04E5B28A241CA7CD0069BCEE /* TKOpenPlugin60034.m in Sources */,
				************************ /* TKSmartTwoVideoManager.m in Sources */,
				048DC9992407E6EF00A6F3EC /* TKOrdinaryOneVideoViewController.m in Sources */,
				183D05BB29384A8000346A76 /* TKOrganizationDoubleVideoNoVoiceViewController.m in Sources */,
				DCD47D3528E18E1600AE44C6 /* TKBaseVideoRecordEndView.m in Sources */,
				33C207E62A148B2700087D13 /* TKOneWayVideoViewController.mm in Sources */,
				33C207E72A148B2700087D13 /* TKOpenPlugin60026.m in Sources */,
				1891C4812486148300C54EFF /* TKVideoReadAgreeView.m in Sources */,
				************************ /* TKVideoRecordManager.mm in Sources */,
				187F361B26AAB4680046E9E3 /* TKOneWayLandscapeVideoView.m in Sources */,
				DCD47D5028E1B18D00AE44C6 /* TKSmartVirtualManVideoEndView.m in Sources */,
				DC48AC7A2A56A0A2005B7D5C /* TKZFSpeedLoadingView.m in Sources */,
				181D4CA1286E94F600679EB3 /* TKVideoAlertView.m in Sources */,
				B3556A931DDBF96B00C76E7C /* TKOpenPlugin60004.m in Sources */,
				B3556AD91DDBF96B00C76E7C /* TKOpenPlugin60017.m in Sources */,
				B3386D6B1F271753006EF60A /* TKAVCaptureManager.m in Sources */,
				182F804725DFCDC900505CDE /* TKOpenPlugin60049.m in Sources */,
				B3556AB51DDBF96B00C76E7C /* TKOpenPlugin60014.m in Sources */,
				B3556AA11DDBF96B00C76E7C /* TKRecordController.m in Sources */,
				1893503C296547610052E77B /* DemoTableViewCell.m in Sources */,
				33C207D92A148B2700087D13 /* TKALSpeechSynthesisManager.mm in Sources */,
				33DF5F502D1A9B0700F311AA /* TKOpenPlugin60813.m in Sources */,
				183D05C329384A8000346A76 /* TKOrginizationDoublePhotoViewController.m in Sources */,
				DCC0429F29434D1900BDF14D /* TKOpenPlugin60077.m in Sources */,
				B35EBF431C43942F0059F885 /* MDRadialProgressLabel.m in Sources */,
				33C207E22A148B2700087D13 /* TKSpeechRecognizeManager.m in Sources */,
				DC6758B5294AF380009771ED /* TKDoubleOrdinaryVideoEndView.m in Sources */,
				18F12A1425FF24E000F26F5C /* TKOneWayVideoAlertTipView.m in Sources */,
				1893507D2965498F0052E77B /* IQKeyboardReturnKeyHandler.m in Sources */,
				************************ /* TKChatErrorConverter.m in Sources */,
				B3556AA41DDBF96B00C76E7C /* TKRecordView.m in Sources */,
				187F361926AAB4680046E9E3 /* TKOpenPlugin60062.m in Sources */,
				187F360026AAB4530046E9E3 /* TKFaceImageLandscapeView.m in Sources */,
				18034324291CD426002C8E2C /* TKPlayerToolView.m in Sources */,
				DC48AC712A56A0A2005B7D5C /* UIScrollView+TKZFPlayer.m in Sources */,
				DC48AC682A56A0A2005B7D5C /* TKZFLandscapeViewController.m in Sources */,
				B3FACBBA1AFDDF0E0088CDF1 /* AppDelegate.m in Sources */,
				B35EBF441C43942F0059F885 /* MDRadialProgressTheme.m in Sources */,
				DCECF967292F5EF000A802EE /* TKSVGEngine.mm in Sources */,
				1893507B2965498F0052E77B /* IQUIView+Hierarchy.m in Sources */,
				DC84D15E2857291400941BF5 /* TKBaseVideoRecordViewController.m in Sources */,
				189350722965498F0052E77B /* IQToolbar.m in Sources */,
				183D05BF29384A8000346A76 /* TKOrganizationDoubleVideoTimeView.m in Sources */,
				1893503E296547610052E77B /* TestTextFieldView.m in Sources */,
				1854638229669CBA00553822 /* TKChatService.m in Sources */,
				DCC7E7E326299A56006D378F /* TKOpenPlugin60057.m in Sources */,
				33E2EA1B2E371F5D00037BE4 /* TKBankCardPhotoView.m in Sources */,
				DCC88A6D2A77BC5A00625DAF /* UIView+TKZFFrame.m in Sources */,
				18339699245C27C000ECE057 /* TKOpenPlugin60039.m in Sources */,
				04652355212E84C100F8C0D0 /* TKOpenPlugin60022.m in Sources */,
				B3556A901DDBF96B00C76E7C /* TKOpenPlugin60002.m in Sources */,
				1893507C2965498F0052E77B /* IQUIViewController+Additions.m in Sources */,
				DCD47D4F28E1B18D00AE44C6 /* TKOpenPlugin60072.m in Sources */,
				DC48AC6B2A56A0A2005B7D5C /* TKZFLandscapeViewController_iOS15.m in Sources */,
				************************ /* TKDoubleOrdinaryVideoViewController.m in Sources */,
				187F361E26AAB4680046E9E3 /* TKOpenPlugin61000.m in Sources */,
				DC6CDB972650FF68004E7295 /* TKChatLiveFaceView.m in Sources */,
				DCC042A229434D1900BDF14D /* TKDoubleChatVideoRecordEndView.m in Sources */,
				************************ /* TKOpenPlugin60091.m in Sources */,
				045EF866241BAF0100032B22 /* TKOpenPlugin60033.m in Sources */,
				0441008C23C43BD1005B9D05 /* TKFxcAccountInfoType.m in Sources */,
				DC6CDB872650F20D004E7295 /* TKOpenPlugin60059.m in Sources */,
				183D05B929384A8000346A76 /* TKOrganizationDoubleVideoViewController.m in Sources */,
				189350782965498F0052E77B /* IQNSArray+Sort.m in Sources */,
				33B36AF92C21826900121BC3 /* TKOpenPlugin60046.m in Sources */,
				18935039296547610052E77B /* TKOpenLoginViewController.m in Sources */,
				************************ /* TKDoubleOrdinaryVideoView.m in Sources */,
				183D05BA29384A8000346A76 /* TKOrganizationDoubleVideoBaseViewController.m in Sources */,
				183D05BE29384A8000346A76 /* TKOrganizationDoubleVideoBottomTipView.m in Sources */,
				33BDE2932A7203CD004F30D1 /* TKOpenPlugin60094.m in Sources */,
				33DD1C822A97279F00DD3F74 /* TKTChatSmartTwoVideoManager.m in Sources */,
				DC6CDB902650F289004E7295 /* TKChatLiveDetectViewController.m in Sources */,
				************************ /* UIImageView+TKZFCache.m in Sources */,
				DCECF968292F5EF000A802EE /* TKSVGLayer.m in Sources */,
				B3556AA01DDBF96B00C76E7C /* TkFDRecordController.m in Sources */,
				DCECF969292F5EF000A802EE /* TKSVGBezierPath.mm in Sources */,
				DCCDDA1B295C2AF5002F268B /* TKFaceDectTipView.m in Sources */,
				1893504F296547610052E77B /* TChatRtcArtificialWitnessTestVC.m in Sources */,
				04652356212E84C100F8C0D0 /* TKTouchView.m in Sources */,
				************************ /* TKBaseVideoRecordEndLandscapeView.m in Sources */,
				33DAFAA62C61E9E1009F2243 /* TKiflyNoAiSpeechRecognizeManager.m in Sources */,
				B3556AA21DDBF96B00C76E7C /* TKRecordModel.m in Sources */,
				B3556AD81DDBF96B00C76E7C /* TKOpenPlugin60016.m in Sources */,
				336D641129C3298800BB96A1 /* TKOrganizationDoubleVideoGradientButton.m in Sources */,
				DC48AC7F2A56A0A2005B7D5C /* TKZFPlayerView.m in Sources */,
				183D05BC29384A8000346A76 /* TKOrganizationDoubleVideoAVManager.m in Sources */,
				33FD40892E2F3AEE00687DD1 /* TKOpenPlugin60066.m in Sources */,
				04E5B28D241CA9E20069BCEE /* TKDirectVideoViewController.m in Sources */,
				************************ /* TKZFSmallFloatControlView.m in Sources */,
				18C46C37291A54A10076BAC0 /* TKChatTokenHelper.m in Sources */,
				DC0AC8C529642E550068F203 /* TKOpenPlugin60087.m in Sources */,
				DCC042B52948166800BDF14D /* TKDoubleOneWayVideoEndView.m in Sources */,
				187F361C26AAB4680046E9E3 /* TKOpenPlugin60064.m in Sources */,
				1893503F296547610052E77B /* TestGuidePageVC.m in Sources */,
				BE6CD0441B5000B7003DCF98 /* TKOpenAccountService.m in Sources */,
				187F361D26AAB4680046E9E3 /* TKOpenBusinessLicenseViewController.m in Sources */,
				33B3E7622E1BC248004C433C /* TKOCRTimeoutView.m in Sources */,
				DCC5CE522B207FF7001C82A6 /* TKReadingView.m in Sources */,
				18F129EB25FEF96000F26F5C /* TKFaceDetectManager.m in Sources */,
				183D05C129384A8000346A76 /* TKOpenPlugin61001.m in Sources */,
				33DAFAA32C61E9D6009F2243 /* TKiflyNoAiSpeechSynthesisManager.m in Sources */,
				183D05BD29384A8000346A76 /* TKOpenPlugin61003.m in Sources */,
				************************ /* TKZFLandscapeRotationManager_iOS15.m in Sources */,
				************************ /* TKFragmentVideoView.m in Sources */,
				************************ /* TKZFLandScapeControlView.m in Sources */,
				33CB8E302DA6513D000EA7C4 /* TKOpenPlugin60817.m in Sources */,
				************************ /* TKZFPersentInteractiveTransition.m in Sources */,
				DCD47D3628E18E1600AE44C6 /* TKBaseVideoRecordView.m in Sources */,
				B38D07B71FC402EC0013DA81 /* TKMAlertViewController.m in Sources */,
				18138944244B269A007F96FA /* TKTakeBankPhotoViewController.m in Sources */,
				04E5B29C241CB4030069BCEE /* TKDirectVideoModel.m in Sources */,
				33EFE3D42AA9677200D070FA /* TKVideoWitnessViewController+TChat.m in Sources */,
				DCD47D4E28E1B18D00AE44C6 /* TKSmartVirtualManViewController.m in Sources */,
				33C207DA2A148B2700087D13 /* TKSpeechSynthesisManager.m in Sources */,
				33C207E12A148B2700087D13 /* TKTencentSpeechRecognizeManager.m in Sources */,
				B3556AD71DDBF96B00C76E7C /* TKBankCardRecognizeViewController.m in Sources */,
				B3556A8F1DDBF96B00C76E7C /* TKOpenPlugin60001.m in Sources */,
				189350792965498F0052E77B /* IQUITextFieldView+Additions.m in Sources */,
				045EF862241BA81000032B22 /* TKOpenPlugin60032.m in Sources */,
				DC48AC8A2A56A0A2005B7D5C /* TKPlayer.m in Sources */,
				1893503A296547610052E77B /* DemoPageViewController.m in Sources */,
				1895FCB326959A9B00513E5F /* TKOpenPrivacyAgreementView.m in Sources */,
				187F35FB26AAB44C0046E9E3 /* TKFaceImageLandscapeViewController.m in Sources */,
				DC48AC6E2A56A0A2005B7D5C /* TKZFOrientationObserver.m in Sources */,
				0472AC9F23580798008FC27E /* TKOpenVideoChatView.m in Sources */,
				DC84D15B2857290800941BF5 /* TKSmartQuestionModel.m in Sources */,
				B3556ADA1DDBF96B00C76E7C /* TKOpenPlugin60018.m in Sources */,
				33C207EB2A148B6100087D13 /* TKVideoWitnessViewController+AnyChat.m in Sources */,
				DC48AC882A56A0A2005B7D5C /* TKZFSliderView.m in Sources */,
				336D640E29C3297800BB96A1 /* TKOrganizationDoubleVideoResultFailureViewController.m in Sources */,
				B3A2DD331EA461A700B18842 /* TKCommonUtil.m in Sources */,
				DC48AC7E2A56A0A2005B7D5C /* TKZFPlayerStatusBar.m in Sources */,
				18AF8649250778530094450D /* TKOpenPlugin60043.m in Sources */,
				DC6F5D09262EB49800B87A5B /* TKChatVideoRecordView.m in Sources */,
				DC48AC752A56A0A2005B7D5C /* TKZFKVOController.m in Sources */,
				B3556A911DDBF96B00C76E7C /* MTakeCardViewController.m in Sources */,
				18AF8645250771BA0094450D /* TKOpenPlugin60025.m in Sources */,
				0472CA8E23FBA05F000B5EB7 /* TKLiveFaceView.m in Sources */,
				33C207DF2A148B2700087D13 /* TKNLSVoiceRecorder.m in Sources */,
				DC48AC6C2A56A0A2005B7D5C /* TKZFPortraitViewController.m in Sources */,
				DC6F5D03262EB34400B87A5B /* TKChatVideoRecordEndView.m in Sources */,
				DCE005092A95AD9E007D1C17 /* TKChatVideoRecordViewController.m in Sources */,
				042CE7052408DD7B00B9AC15 /* TKOrdinaryOneVideoEndView.m in Sources */,
				DCC042AC2947430200BDF14D /* TKOpenPlugin60089.m in Sources */,
				B30FD8AE1EF11ED2000D3E94 /* TKVideoWitnessViewController.m in Sources */,
				B3556A921DDBF96B00C76E7C /* TKOpenPlugin60003.m in Sources */,
				048DC9952407E66400A6F3EC /* TKOpenPlugin60030.m in Sources */,
				334CE34C29FB651900FD2A69 /* (null) in Sources */,
				DC48AC662A56A0A2005B7D5C /* TKZFPresentTransition.m in Sources */,
				DCC042AF294744A900BDF14D /* TKDoudleOneWayVideoViewController.m in Sources */,
				B3556AB41DDBF96B00C76E7C /* MIDCardRecognizeViewController.m in Sources */,
				B3556AA31DDBF96B00C76E7C /* TKOpenPlugin60006.m in Sources */,
				B3556AAD1DDBF96B00C76E7C /* TKOpenPlugin60008.m in Sources */,
				33EA0F432C9A69AB0066022A /* TKOpenPlugin60812.m in Sources */,
				DC48AC762A56A0A2005B7D5C /* TKZFPlayerLogManager.m in Sources */,
				B3556A9F1DDBF96B00C76E7C /* TKOpenPlugin60005.m in Sources */,
				BE6CD0431B5000B7003DCF98 /* TKOpenController.m in Sources */,
				DC48AC772A56A0A2005B7D5C /* TKZFNetworkSpeedMonitor.m in Sources */,
				33C207E52A148B2700087D13 /* TKNLSPlayAudio.mm in Sources */,
				B3556AAC1DDBF96B00C76E7C /* MTakeFaceViewController.m in Sources */,
				DCC042A129434D1900BDF14D /* TKDoubleChatVideoRecordView.m in Sources */,
				DC48AC732A56A0A2005B7D5C /* TKZFPlayerNotification.m in Sources */,
				3316E1832A2835E100E57A02 /* TKOpenPlugin60071.m in Sources */,
				DC48AC852A56A0A2005B7D5C /* TKPlayerControlView.m in Sources */,
				B3FACBB71AFDDF0E0088CDF1 /* main.m in Sources */,
				0472AC9E23580798008FC27E /* TKOpenQueueView.m in Sources */,
				189350732965498F0052E77B /* IQTitleBarButtonItem.m in Sources */,
				B386BA0E1F947E6C001CBD13 /* TKOpenPlugin60099.m in Sources */,
				DC48AC7B2A56A0A2005B7D5C /* TKZFLoadingView.m in Sources */,
				183D05C029384A8000346A76 /* TKOrganizationDoubleVideoBottomRightView.m in Sources */,
				B3556AB11DDBF96B00C76E7C /* MTakePhotoViewController.m in Sources */,
				187CC37228F15C5E003442D6 /* TKOpenViewStyleHelper.m in Sources */,
				DC4455182A380E720073C229 /* TKAnyChatSmartTwoVideoManager.m in Sources */,
				33C207DB2A148B2700087D13 /* TKTencentSpeechSynthesisManager.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		B3AA8EC31EBB2B0400255737 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = B3D794591B8EA14F00768134 /* build_TKOpenResource */;
			targetProxy = B3AA8EC21EBB2B0400255737 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin PBXVariantGroup section */
		B3FACBBE1AFDDF0E0088CDF1 /* Main.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				B3FACBBF1AFDDF0E0088CDF1 /* Base */,
			);
			name = Main.storyboard;
			sourceTree = "<group>";
		};
		B3FACBC31AFDDF0E0088CDF1 /* LaunchScreen.xib */ = {
			isa = PBXVariantGroup;
			children = (
				B3FACBC41AFDDF0E0088CDF1 /* Base */,
			);
			name = LaunchScreen.xib;
			sourceTree = "<group>";
		};
/* End PBXVariantGroup section */

/* Begin XCBuildConfiguration section */
		B3D7945B1B8EA14F00768134 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_IDENTITY = "";
				PRODUCT_NAME = "$(TARGET_NAME)";
			};
			name = Debug;
		};
		B3D7945C1B8EA14F00768134 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_IDENTITY = "";
				PRODUCT_NAME = "$(TARGET_NAME)";
			};
			name = Release;
		};
		B3E1B3101B8DB80800CDD258 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_IDENTITY = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				COMBINE_HIDPI_IMAGES = YES;
				DEBUG_INFORMATION_FORMAT = dwarf;
				DEVELOPMENT_TEAM = HCXVYWN652;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				INFOPLIST_FILE = TKOpenResource/Info.plist;
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Bundles";
				MACOSX_DEPLOYMENT_TARGET = 10.10;
				ONLY_ACTIVE_ARCH = NO;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE = "";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				WRAPPER_EXTENSION = bundle;
			};
			name = Debug;
		};
		B3E1B3111B8DB80800CDD258 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				COMBINE_HIDPI_IMAGES = YES;
				DEVELOPMENT_TEAM = HCXVYWN652;
				INFOPLIST_FILE = TKOpenResource/Info.plist;
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Bundles";
				MACOSX_DEPLOYMENT_TARGET = 10.10;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE = "";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				WRAPPER_EXTENSION = bundle;
			};
			name = Release;
		};
		B3FACBD21AFDDF0E0088CDF1 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_SYMBOLS_PRIVATE_EXTERN = NO;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 11.0;
				MTL_ENABLE_DEBUG_INFO = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		B3FACBD31AFDDF0E0088CDF1 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 11.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				SDKROOT = iphoneos;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		B3FACBD51AFDDF0E0088CDF1 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ARCHS = "$(ARCHS_STANDARD)";
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_LAUNCHIMAGE_NAME = LaunchImage;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CODE_SIGN_IDENTITY = "iPhone Developer";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer: Pengmin Liu (56K32XM5GT)";
				CODE_SIGN_STYLE = Manual;
				CURRENT_PROJECT_VERSION = 35;
				DEVELOPMENT_TEAM = R4QUJ9NZUW;
				ENABLE_BITCODE = NO;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/Thinkive/TKFMWK",
					"$(PROJECT_DIR)/Thinkive/Plugins/TKOpenPlugin60014/IDCadrSDK",
					"$(PROJECT_DIR)/Thinkive/Plugins/TKOpenPlugin60016/BankCardSDK",
					"$(PROJECT_DIR)/Thinkive/Plugins/TKOpenPlugin60016/BankCardSDK/ISOpenSDKFoundation.embeddedframework",
					"$(PROJECT_DIR)/libs",
					"$(PROJECT_DIR)",
					"$(PROJECT_DIR)/Thinkive/Modules/Video/TChat/SDK",
					"$(PROJECT_DIR)/Thinkive/ShareSDK/Required",
					"$(PROJECT_DIR)/Thinkive/ShareSDK/ShareSDK",
					"$(PROJECT_DIR)/Thinkive/ShareSDK/ShareSDK/Support/Optional",
					"$(PROJECT_DIR)/Thinkive/ShareSDK/ShareSDK/Support/PlatformConnector",
					"$(PROJECT_DIR)/Thinkive/ShareSDK/ShareSDK/Support/PlatformSDK/QQSDK",
					"$(PROJECT_DIR)/Thinkive/ShareSDK/ShareSDK/Support/Required",
					"$(PROJECT_DIR)/Thinkive/Modules/AliyunNlsSdk",
					"$(PROJECT_DIR)/Thinkive/Modules/TencentASR",
					"$(PROJECT_DIR)/Thinkive/Modules/QCloudSDK",
					"$(PROJECT_DIR)/Thinkive/Plugins/TKOpenPlugin60007/SDK",
					"$(PROJECT_DIR)/Thinkive/Plugins/TKOpenPlugin60039/ZXJTVideoSDK",
					"$(PROJECT_DIR)/Thinkive/Modules/AliNUI",
					"$(PROJECT_DIR)/Thinkive/Modules/Ifly",
					"$(PROJECT_DIR)/Thinkive/Modules/TKAppletPlugin",
					"$(PROJECT_DIR)/Thinkive/Plugins/TKOpenPlugin60014/HHKVOCR/ISOpenSDKFoundation.embeddedframework",
					"$(PROJECT_DIR)/Thinkive/Plugins/TKOpenPlugin60014/HHKVOCR",
					"$(PROJECT_DIR)/Thinkive/Modules/THS/SDK",
					"$(PROJECT_DIR)/Thinkive/Modules/THS_TTS",
					"$(PROJECT_DIR)/Thinkive/Modules/Video",
					"$(PROJECT_DIR)/Thinkive/Modules/Video/Zego",
					"$(PROJECT_DIR)/Thinkive/Modules/Video/AnyChat",
				);
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_PREFIX_HEADER = "TKOpenAccount-Standard/TKOpenAccount.pch";
				HEADER_SEARCH_PATHS = (
					"$(inherited)",
					/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include,
					"$(PROJECT_DIR)/Thinkive/TKFMWK/THFMWK/OpensslSDK/include",
					"\"$(SRCROOT)\"/Thinkive/3Lib/AnychatSDK/include",
				);
				INFOPLIST_FILE = "TKOpenAccount-Standard/Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 11.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				LIBRARY_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/Thinkive/3Lib/AnychatSDK/libs/audioprocess",
					"$(PROJECT_DIR)/Thinkive/3Lib/AnychatSDK/libs",
					"$(PROJECT_DIR)/Thinkive/TKFMWK/THFMWK/appbase/ZBarSDK",
					"$(PROJECT_DIR)/Thinkive/3Lib/TChatSDK",
					"$(PROJECT_DIR)/Thinkive/Modules/Video/AnyChat/SDK/libs",
					"$(PROJECT_DIR)",
					"$(PROJECT_DIR)/Thinkive/Modules/Video/AnyChat/AnychatSDK/libs/audioprocess",
					"$(PROJECT_DIR)/Thinkive/Modules/Video/AnyChat/AnychatSDK/libs",
					"$(PROJECT_DIR)/Thinkive/TKFMWK/THFMWK/OpensslSDK/libs",
					"$(PROJECT_DIR)/Thinkive/Modules/libSTSilentLivenessController",
					"$(PROJECT_DIR)/Thinkive/Modules/QCloudSDK",
					"$(PROJECT_DIR)/Thinkive/Modules/QCloud_TTS",
					"$(PROJECT_DIR)/Thinkive/Plugins/TKOpenPlugin60014/IDCadrSDK",
					"$(PROJECT_DIR)/Thinkive/Modules/THS/SDK",
					"$(PROJECT_DIR)/Thinkive/Modules/THS_TTS",
					"$(PROJECT_DIR)/Thinkive/Modules/Video/TChat/SDK/TChatRtc.framework\\ ",
				);
				MARKETING_VERSION = 5.0.0;
				ONLY_ACTIVE_ARCH = YES;
				OTHER_CFLAGS = "";
				OTHER_CODE_SIGN_FLAGS = "";
				OTHER_LDFLAGS = "-ObjC";
				PRODUCT_BUNDLE_IDENTIFIER = com.thinkive.openaccountAdhoc;
				PRODUCT_NAME = "思迪TChatRtc";
				PROVISIONING_PROFILE = "";
				PROVISIONING_PROFILE_SPECIFIER = iOSDev;
				TARGETED_DEVICE_FAMILY = 1;
				VALIDATE_WORKSPACE = NO;
			};
			name = Debug;
		};
		B3FACBD61AFDDF0E0088CDF1 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ARCHS = "$(ARCHS_STANDARD)";
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_LAUNCHIMAGE_NAME = LaunchImage;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CODE_SIGN_ENTITLEMENTS = "";
				CODE_SIGN_IDENTITY = "iPhone Distribution";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Distribution: Shenzhen Sidi Information Technology Co.,Ltd. (R4QUJ9NZUW)";
				CODE_SIGN_STYLE = Manual;
				CURRENT_PROJECT_VERSION = 35;
				DEVELOPMENT_TEAM = R4QUJ9NZUW;
				"DEVELOPMENT_TEAM[sdk=iphoneos*]" = R4QUJ9NZUW;
				ENABLE_BITCODE = NO;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/Thinkive/TKFMWK",
					"$(PROJECT_DIR)/Thinkive/Plugins/TKOpenPlugin60014/IDCadrSDK",
					"$(PROJECT_DIR)/Thinkive/Plugins/TKOpenPlugin60016/BankCardSDK",
					"$(PROJECT_DIR)/Thinkive/Plugins/TKOpenPlugin60016/BankCardSDK/ISOpenSDKFoundation.embeddedframework",
					"$(PROJECT_DIR)/libs",
					"$(PROJECT_DIR)",
					"$(PROJECT_DIR)/Thinkive/Modules/Video/TChat/SDK",
					"$(PROJECT_DIR)/Thinkive/ShareSDK/Required",
					"$(PROJECT_DIR)/Thinkive/ShareSDK/ShareSDK",
					"$(PROJECT_DIR)/Thinkive/ShareSDK/ShareSDK/Support/Optional",
					"$(PROJECT_DIR)/Thinkive/ShareSDK/ShareSDK/Support/PlatformConnector",
					"$(PROJECT_DIR)/Thinkive/ShareSDK/ShareSDK/Support/PlatformSDK/QQSDK",
					"$(PROJECT_DIR)/Thinkive/ShareSDK/ShareSDK/Support/Required",
					"$(PROJECT_DIR)/Thinkive/Modules/AliyunNlsSdk",
					"$(PROJECT_DIR)/Thinkive/Modules/TencentASR",
					"$(PROJECT_DIR)/Thinkive/Modules/QCloudSDK",
					"$(PROJECT_DIR)/Thinkive/Plugins/TKOpenPlugin60007/SDK",
					"$(PROJECT_DIR)/Thinkive/Plugins/TKOpenPlugin60039/ZXJTVideoSDK",
					"$(PROJECT_DIR)/Thinkive/Modules/AliNUI",
					"$(PROJECT_DIR)/Thinkive/Modules/Ifly",
					"$(PROJECT_DIR)/Thinkive/Modules/TKAppletPlugin",
					"$(PROJECT_DIR)/Thinkive/Plugins/TKOpenPlugin60014/HHKVOCR/ISOpenSDKFoundation.embeddedframework",
					"$(PROJECT_DIR)/Thinkive/Plugins/TKOpenPlugin60014/HHKVOCR",
					"$(PROJECT_DIR)/Thinkive/Modules/THS/SDK",
					"$(PROJECT_DIR)/Thinkive/Modules/THS_TTS",
					"$(PROJECT_DIR)/Thinkive/Modules/Video",
					"$(PROJECT_DIR)/Thinkive/Modules/Video/Zego",
					"$(PROJECT_DIR)/Thinkive/Modules/Video/AnyChat",
				);
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_PREFIX_HEADER = "TKOpenAccount-Standard/TKOpenAccount.pch";
				HEADER_SEARCH_PATHS = (
					"$(inherited)",
					/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include,
					"$(PROJECT_DIR)/Thinkive/TKFMWK/THFMWK/OpensslSDK/include",
					"\"$(SRCROOT)\"/Thinkive/3Lib/AnychatSDK/include",
				);
				INFOPLIST_FILE = "TKOpenAccount-Standard/Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 11.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				LIBRARY_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/Thinkive/3Lib/AnychatSDK/libs/audioprocess",
					"$(PROJECT_DIR)/Thinkive/3Lib/AnychatSDK/libs",
					"$(PROJECT_DIR)/Thinkive/TKFMWK/THFMWK/appbase/ZBarSDK",
					"$(PROJECT_DIR)/Thinkive/3Lib/TChatSDK",
					"$(PROJECT_DIR)/Thinkive/Modules/Video/AnyChat/SDK/libs",
					"$(PROJECT_DIR)",
					"$(PROJECT_DIR)/Thinkive/Modules/Video/AnyChat/AnychatSDK/libs/audioprocess",
					"$(PROJECT_DIR)/Thinkive/Modules/Video/AnyChat/AnychatSDK/libs",
					"$(PROJECT_DIR)/Thinkive/TKFMWK/THFMWK/OpensslSDK/libs",
					"$(PROJECT_DIR)/Thinkive/Modules/libSTSilentLivenessController",
					"$(PROJECT_DIR)/Thinkive/Modules/QCloudSDK",
					"$(PROJECT_DIR)/Thinkive/Modules/QCloud_TTS",
					"$(PROJECT_DIR)/Thinkive/Plugins/TKOpenPlugin60014/IDCadrSDK",
					"$(PROJECT_DIR)/Thinkive/Modules/THS/SDK",
					"$(PROJECT_DIR)/Thinkive/Modules/THS_TTS",
					"$(PROJECT_DIR)/Thinkive/Modules/Video/TChat/SDK/TChatRtc.framework\\ ",
				);
				MARKETING_VERSION = 5.0.0;
				OTHER_CFLAGS = "";
				OTHER_CODE_SIGN_FLAGS = "";
				OTHER_LDFLAGS = "-ObjC";
				PRODUCT_BUNDLE_IDENTIFIER = com.thinkive.openaccountAdhoc;
				PRODUCT_NAME = "思迪TChatRtc";
				PROVISIONING_PROFILE = "55d7de79-fb23-49f2-97df-d8ac728cfcdf";
				PROVISIONING_PROFILE_SPECIFIER = iOSDev;
				"PROVISIONING_PROFILE_SPECIFIER[sdk=iphoneos*]" = "开户上架";
				TARGETED_DEVICE_FAMILY = 1;
				VALIDATE_WORKSPACE = NO;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		B3D7945A1B8EA14F00768134 /* Build configuration list for PBXAggregateTarget "build_TKOpenResource" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				B3D7945B1B8EA14F00768134 /* Debug */,
				B3D7945C1B8EA14F00768134 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		B3E1B30F1B8DB80800CDD258 /* Build configuration list for PBXNativeTarget "TKOpenResource" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				B3E1B3101B8DB80800CDD258 /* Debug */,
				B3E1B3111B8DB80800CDD258 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		B3FACBAC1AFDDF0E0088CDF1 /* Build configuration list for PBXProject "TKOpenAccount-Standard" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				B3FACBD21AFDDF0E0088CDF1 /* Debug */,
				B3FACBD31AFDDF0E0088CDF1 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		B3FACBD41AFDDF0E0088CDF1 /* Build configuration list for PBXNativeTarget "TKOpenAccount-Standard" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				B3FACBD51AFDDF0E0088CDF1 /* Debug */,
				B3FACBD61AFDDF0E0088CDF1 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = B3FACBA91AFDDF0E0088CDF1 /* Project object */;
}
