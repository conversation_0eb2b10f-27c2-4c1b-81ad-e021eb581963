<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.Storyboard.XIB" version="3.0" toolsVersion="18122" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" colorMatched="YES" initialViewController="BYZ-38-t0r">
    <device id="retina6_7" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="18093"/>
        <capability name="System colors in document resources" minToolsVersion="11.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <scenes>
        <!--Select View Controller-->
        <scene sceneID="tne-QT-ifu">
            <objects>
                <viewController storyboardIdentifier="select" id="BYZ-38-t0r" customClass="SelectViewController" sceneMemberID="viewController">
                    <layoutGuides>
                        <viewControllerLayoutGuide type="top" id="y3c-jy-aDJ"/>
                        <viewControllerLayoutGuide type="bottom" id="wfy-db-euE"/>
                    </layoutGuides>
                    <view key="view" contentMode="scaleToFill" id="8bC-Xf-vdC">
                        <rect key="frame" x="0.0" y="0.0" width="428" height="926"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="YZM-zO-aZG">
                                <rect key="frame" x="20" y="501" width="388" height="43"/>
                                <color key="backgroundColor" systemColor="systemBlueColor"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="43" id="63c-wM-K4t"/>
                                </constraints>
                                <state key="normal" title="确定">
                                    <color key="titleColor" systemColor="systemBackgroundColor"/>
                                </state>
                                <connections>
                                    <action selector="buttonAction:" destination="BYZ-38-t0r" eventType="touchUpInside" id="tzB-aS-8FD"/>
                                </connections>
                            </button>
                            <textField opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" borderStyle="roundedRect" placeholder="请输入html地址" textAlignment="natural" minimumFontSize="17" translatesAutoresizingMaskIntoConstraints="NO" id="C7A-2J-xXY">
                                <rect key="frame" x="20" y="446" width="388" height="34"/>
                                <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                <textInputTraits key="textInputTraits"/>
                            </textField>
                        </subviews>
                        <color key="backgroundColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                        <color key="tintColor" red="0.090196078431372548" green="0.10196078431372549" blue="0.10196078431372549" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                        <constraints>
                            <constraint firstItem="YZM-zO-aZG" firstAttribute="trailing" secondItem="C7A-2J-xXY" secondAttribute="trailing" id="2VU-eY-t7g"/>
                            <constraint firstItem="YZM-zO-aZG" firstAttribute="leading" secondItem="C7A-2J-xXY" secondAttribute="leading" id="6PF-nO-0aG"/>
                            <constraint firstItem="C7A-2J-xXY" firstAttribute="leading" secondItem="8bC-Xf-vdC" secondAttribute="leadingMargin" id="Jbs-JF-K9E"/>
                            <constraint firstAttribute="trailingMargin" secondItem="C7A-2J-xXY" secondAttribute="trailing" id="aIC-HN-iE9"/>
                            <constraint firstItem="C7A-2J-xXY" firstAttribute="centerY" secondItem="8bC-Xf-vdC" secondAttribute="centerY" id="f7b-fh-1NJ"/>
                            <constraint firstItem="YZM-zO-aZG" firstAttribute="top" secondItem="C7A-2J-xXY" secondAttribute="bottom" constant="21" id="jVz-YY-cSd"/>
                        </constraints>
                    </view>
                    <connections>
                        <outlet property="textField" destination="C7A-2J-xXY" id="Jz8-A2-fo5"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="dkx-z0-nzr" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="135.98130841121494" y="133.47732181425488"/>
        </scene>
    </scenes>
    <resources>
        <systemColor name="systemBackgroundColor">
            <color white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
        </systemColor>
        <systemColor name="systemBlueColor">
            <color red="0.0" green="0.47843137254901963" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </systemColor>
    </resources>
</document>
