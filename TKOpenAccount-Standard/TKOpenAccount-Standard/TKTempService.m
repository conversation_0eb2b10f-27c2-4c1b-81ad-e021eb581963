//
//  TKTempService.m
//  TKApp
//
//  Created by 叶璐 on 15/4/21.
//  Copyright (c) 2015年 liubao. All rights reserved.
//

#import "TKTempService.h"

@implementation TKTempService

-(void)getTokenWithURL:(NSString *)url param:(NSMutableDictionary *)reqParam callBackFunc:(CallBackFunc)callBackFunc {
    
    ReqParamVo *reqParamVo = [self createReqParamVo];
    reqParamVo.url = url;
    reqParamVo.protocol = TKDaoType_Http;
    reqParamVo.isShowWait = YES;
    
    NSMutableDictionary *tempReqParam = [reqParam mutableCopy];

    reqParamVo.reqParam = tempReqParam;
    [self invoke:reqParamVo callBackFunc:callBackFunc];
}

-(void)handleNetworkWithURL:(NSString *)url param:(NSMutableDictionary *)reqParam callBackFunc:(CallBackFunc)callBackFunc
{
    NSMutableDictionary *newParam = [NSMutableDictionary dictionaryWithDictionary:reqParam];
    
    ReqParamVo *reqParamVo = [self createReqParamVo];
    reqParamVo.url = url;
    reqParamVo.protocol = TKDaoType_Http;
    id extParams = newParam[@"extParams"];
    if (extParams && [extParams isKindOfClass:[NSDictionary class]]) {
        [newParam addEntriesFromDictionary:extParams];
    }
    if (newParam[@"serverAccessType"] && [newParam[@"serverAccessType"] integerValue] == 1) {
        reqParamVo.protocol  = TKDaoType_Socket;
        if (extParams && [extParams isKindOfClass:[NSDictionary class]]) {
            if (extParams[@"url"]) {
                reqParamVo.url = extParams[@"url"];
            }
            reqParamVo.companyId = extParams[@"companyId"];
            reqParamVo.systemId  = extParams[@"systemId"];
        }
        reqParamVo.dataType  = TKDataType_Compress_Encryt;
    }
    reqParamVo.isShowWait = NO;
    reqParamVo.isUpload = NO;
    reqParamVo.timeOut = 10.0f;
    
    // 处理特殊参数-requestHeaders
    if (newParam[@"requestHeaders"]) {
        if ([newParam[@"requestHeaders"] isKindOfClass:[NSString class]]) {
            reqParamVo.headerFieldDic= [TKDataHelper jsonToDictionary:newParam[@"requestHeaders"]];
        }else{
            reqParamVo.headerFieldDic = (NSDictionary *)newParam[@"requestHeaders"];
        }
        
        [newParam removeObjectForKey:@"requestHeaders"];
    }
    // 处理特殊参数-requestParams
    if (newParam[@"requestParams"]) {
        if ([newParam[@"requestParams"] isKindOfClass:[NSString class]]) {
            [newParam addEntriesFromDictionary:[TKDataHelper jsonToDictionary:newParam[@"requestParams"]]];
        }else{
            [newParam addEntriesFromDictionary:(NSDictionary *)newParam[@"requestParams"]];
        }
        
        [newParam removeObjectForKey:@"requestParams"];
    }
    
    if (reqParam[@"isRestFull"]) {
        // 微服务处理
        if ([reqParam[@"isRestFull"] isKindOfClass:[NSString class]] ||
            [reqParam[@"isRestFull"] isKindOfClass:[NSNumber class]]) {
            if ([[reqParam getStringWithKey:@"isRestFull"] isEqualToString:@"1"]) {
                reqParamVo.isRestFull = YES;
//                reqParamVo.contentType = TKContentType_WWW_FORM;
            }
        }
        
        [reqParam removeObjectForKey:@"isRestFull"];
    }
    
    if (reqParam[@"isURLSign"]) {
        // 微服务处理
        if ([reqParam[@"isURLSign"] isKindOfClass:[NSString class]] ||
            [reqParam[@"isURLSign"] isKindOfClass:[NSNumber class]]) {
            if ([[reqParam getStringWithKey:@"isURLSign"] isEqualToString:@"1"]) {
                reqParamVo.isURLSign = YES;
                reqParamVo.signKey = @"uFSCpZUT24jPeofBtkFTEXIKNZAKWw1vXD/ABnpapSUeT9CVhloh0e0kmq2PagBU";
                reqParamVo.signAppId = @"default";
            }
        }
        
        [reqParam removeObjectForKey:@"isURLSign"];
    }

    
    NSMutableDictionary *tempReqParam = [newParam mutableCopy];
    if (extParams) {
        [tempReqParam removeObjectForKey:@"extParams"];
    }
    reqParamVo.reqParam = tempReqParam;
    
    [self invoke:reqParamVo callBackFunc:[self handleGeneralNetworkError:callBackFunc]];
}

- (void)handleStatisticEventWithURL:(NSString *)url param:(NSMutableDictionary *)reqParam callBackFunc:(CallBackFunc)callBackFunc
{
    NSMutableDictionary *newParam = [NSMutableDictionary dictionaryWithDictionary:reqParam];
    
    ReqParamVo *reqParamVo = [self createReqParamVo];
    reqParamVo.url = url;
    reqParamVo.protocol = TKDaoType_Http;
    reqParamVo.httpMethod = @"post";
    id extParams = newParam[@"extParams"];
    reqParamVo.isRestFull = YES;
    
    if (extParams && [extParams isKindOfClass:[NSDictionary class]]) {
        [newParam addEntriesFromDictionary:extParams];
    }
    
    // 处理特殊参数-requestHeaders
    if (newParam[@"requestHeaders"]) {
        if ([newParam[@"requestHeaders"] isKindOfClass:[NSString class]]) {
            reqParamVo.headerFieldDic= [TKDataHelper jsonToDictionary:newParam[@"requestHeaders"]];
        }else{
            reqParamVo.headerFieldDic = (NSDictionary *)newParam[@"requestHeaders"];
        }
        
        [newParam removeObjectForKey:@"requestHeaders"];
    }

    reqParamVo.isShowWait = NO;
    reqParamVo.isUpload = NO;
    reqParamVo.timeOut = 10.0f;
    
    NSMutableDictionary *tempReqParam = [newParam mutableCopy];
    if (extParams) {
        [tempReqParam removeObjectForKey:@"extParams"];
    }
    reqParamVo.reqParam = tempReqParam;
    
    [self invoke:reqParamVo callBackFunc:[self handleGeneralNetworkError:callBackFunc]];
}

- (CallBackFunc)handleGeneralNetworkError:(CallBackFunc)originCallBackFunc
{
    CallBackFunc newCallBackFunc = ^(ResultVo *resultVo) {
        
        if (resultVo.errorNo == 920 ||  // 用户未登陆
            resultVo.errorNo == 922 ||  // 无效的Token
            resultVo.errorNo == 924) {  // 用户Token已过期
            
            [[NSNotificationCenter defaultCenter] postNotificationName:TK_OPEN_TOKEN_INVALID_NOTIFICATION object:@{@"errorNo" : @(resultVo.errorNo), @"errorInfo" : [TKStringHelper isNotEmpty:resultVo.errorInfo] ? resultVo.errorInfo : @""}];
            
        }
        
        if (originCallBackFunc) originCallBackFunc(resultVo);
    };
    
    return newCallBackFunc;
}


@end
