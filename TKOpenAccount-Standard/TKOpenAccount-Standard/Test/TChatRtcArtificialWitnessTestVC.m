//
//  TChatRtcArtificialWitnessTestVC.m
//  TKOpenAccount-Standard
//
//  Created by <PERSON> on 2022/11/16.
//  Copyright © 2022 thinkive. All rights reserved.
//

#import "TChatRtcArtificialWitnessTestVC.h"
#import "DemoTableViewCell.h"
#import "TestTextFieldView.h"

@interface TChatRtcArtificialWitnessTestVC ()<UITableViewDelegate, UITableViewDataSource, UIScrollViewDelegate>

@property (nonatomic, readwrite, strong) UIScrollView *baseScrollView;
@property (nonatomic, strong) UITableView *tableView;
@property (nonatomic, strong) NSMutableArray *dataSoureArray;
@property (nonatomic, strong) NSMutableDictionary *requestParam;//请求参数
@property (nonatomic, readwrite, strong) TestTextFieldView *sessionUrlParamTextFieldView;
@property (nonatomic, readwrite, strong) TestTextFieldView *singnalUrlParamTextFieldView;
@property (nonatomic, readwrite, strong) TestTextFieldView *roomIDParamTextFieldView;


@end

@implementation TChatRtcArtificialWitnessTestVC

- (instancetype)initWithParam:(NSMutableDictionary *)param title:(NSString *)title {
    self=[super init];
    if (self) {
        self.requestParam=param;
        self.title = title;
    }
    return self;
}

- (void)viewDidLoad {
    [super viewDidLoad];
    
    [self initData];
    [self setupUI];
    
}

- (void)viewWillDisappear:(BOOL)animated {
    [super viewWillDisappear:animated];
    
    // 隐藏房间ID label
    self.roomIDLabel.hidden = YES;
}

// MARK: - setupUI
- (void)setupUI {
    [self.navigationItem setTitle:self.title];
//    [self.navigationController.navigationBar setTranslucent:YES];
    self.view.backgroundColor = UIColor.whiteColor;
    [self.view addSubview:self.baseScrollView];
    [self.baseScrollView addSubview:self.tableView];
    [self.tableView reloadData];
    
    // 可选属性UI
    [self.baseScrollView addSubview:self.sessionUrlParamTextFieldView];
    [self.baseScrollView addSubview:self.singnalUrlParamTextFieldView];
    [self.baseScrollView addSubview:self.roomIDParamTextFieldView];
    self.roomIDLabel.text = [NSString stringWithFormat:@"rid:%@", self.roomIDParamTextFieldView.textField.text];
    self.roomIDLabel.hidden = NO;
}


// MARK: - init data
- (void)initData {
    
    NSArray *sectionArray = sectionArray = @[
//        @[[TKStringHelper isNotEmpty:self.title] ? self.title : @""]
        @[@"开始视频"]
    ];
    
    
    for (NSInteger i = 0; i < sectionArray.count; i ++) {
        
        NSArray *titleArray = sectionArray[i];
        NSMutableArray *tempArr = [NSMutableArray array];
        for (int j = 0; j < titleArray.count; j++) {
            NSDictionary *dict = @{
                @"title" : titleArray[j],
            };
            [tempArr addObject:dict];
        }
        
        [self.dataSoureArray addObject:tempArr];
    }
}

#pragma mark - Selector
- (void)handleAlertWithRowAtIndexPath:(NSIndexPath *)indexPath  {
    
    [self artificialWitness];
}

- (void)artificialWitness {
    NSString *roomID = [TKStringHelper isNotEmpty:self.roomIDParamTextFieldView.textField.text] ? self.roomIDParamTextFieldView.textField.text : @"";
    NSString *sessionUrl = [TKStringHelper isNotEmpty:self.sessionUrlParamTextFieldView.textField.text] ? self.sessionUrlParamTextFieldView.textField.text : @"";
    if ([TKStringHelper isNotEmpty:roomID] && [TKStringHelper isNotEmpty:sessionUrl]) {
        sessionUrl = [TKCommonUtil url:sessionUrl appendingParamStr:[NSString stringWithFormat:@"roomid=%@", roomID]];
    }
    NSString *singnalUrl = [TKStringHelper isNotEmpty:self.singnalUrlParamTextFieldView.textField.text] ? self.singnalUrlParamTextFieldView.textField.text : @"";
    
    NSMutableDictionary *param =[[NSMutableDictionary alloc] init];
    param[@"funcNo"]=@"60034";//功能号
    param[@"mainColor"]=@"#FD671A";//主色调16进制值，不传默认蓝色
//    param[@"userId"]= [self.requestParam getStringWithKey:@"userId"];//用户ID
    param[@"userName"]=@"思小迪";//用户姓名
    param[@"orgId"]=@"2925";//营业部编号
//    param[@"mainColor"]=@"#FD671A";//主色调16进制值，不传默认蓝色
    param[@"netWorkStatus"]=@"WIFI";
//    param[@"url"]=@"https://opt-dev.thinkive.com:15149/wa-queue-server/servlet/json";//连接排队的服务器地址
    param[@"isRejectToH5"]=@"1";//是否将内容返回:默认是0，1:将见证坐席内容全返給h5（4.0用）
    param[@"isNewView"]=@"0";//是否走新版视频界面：0：走新版界面，其他都走老板版界面
    param[@"isShowHeadRect"]=@"0";//是否走新版视频界面：0：走新版界面，其他都走老板版界面
    param[@"version"]=@"4.0";//走3.0还是4.0排队
    param[@"videoType"]=@"5";//视频类型:0:TChat视频，1:AnyChat视频（默认）
    param[@"biz_type"]=@"010001";
    param[@"origin"]=@"2";
    param[@"isRestFull"]=@"1";
    param[@"isURLSign"]=@"1";
    param[@"requestSignKey"]=@"f080556a782d50087beebb0fae7aabd2";
    param[@"_sessionUrl"]=sessionUrl;
    param[@"_singnalUrl"]=singnalUrl;
    param[@"roomId"]=roomID;//房间号
    param[@"showStaffInfo"]=@"职业编号：7788\n工号：9527";//视频窗口显示坐席信息
    
    __weak typeof(self) weakSelf = self;
    [self callPlugin:@"60034" param:param callBackFunc:^(NSMutableDictionary *result) {
        
        NSString *errorNo = [result getStringWithKey:@"error_no"];
        NSString *errorInfo = [result getStringWithKey:@"error_info"];
        NSString *message = [result getStringWithKey:@"message"];
        
        [TKAlertHelper showAlert:[NSString stringWithFormat:@"错误号:%@\n错误信息:%@\n结果信息:%@", errorNo, errorInfo, message] title:@"结果" okBtnText:@"确定" btnHandler:^(NSInteger buttonIndex) {
            
        } parentViewController:weakSelf];
    }];
}

- (void)callPlugin:(NSString *)pluginNumer param:(NSDictionary *)param callBackFunc:(TKPluginCallBackFunc)callBackFunc
{
    ResultVo *vo = [[TKPluginInvokeCenter shareInstance] callPlugin:pluginNumer param:param moduleName:@"open" isH5:NO callBackFunc:^(NSMutableDictionary *result) {
        
        if (callBackFunc) callBackFunc(result);
    }];
    
    if (vo.errorNo != 0) {
        NSString *errorMsg = [TKStringHelper isNotEmpty:vo.errorInfo] ? vo.errorInfo : @"运行插件出错";
        
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            [TKAlertHelper showAlert:[NSString stringWithFormat:@"错误信息:%@", errorMsg] title:@"错误提示" okBtnText:@"确定" btnHandler:nil parentViewController:self];
        });
    }
    
    TKLogInfo(@"调用插件号%@的插件，结果为%@", pluginNumer, vo);
}


// MARK: - UITableViewDelegate,UITableViewDataSource
- (NSInteger)numberOfSectionsInTableView:(UITableView *)tableView {
    return self.dataSoureArray.count;
}

- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    if (section >= self.dataSoureArray.count) return 1;
    
    NSArray *tempArray = self.dataSoureArray[section];
    return tempArray.count ? tempArray.count : 1;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    NSString *cellIndentifier = NSStringFromClass([self class]);

    UITableViewCell *cell = [tableView dequeueReusableCellWithIdentifier:cellIndentifier];
    if (cell == nil) {
//        cell = [[UITableViewCell alloc] initWithStyle:UITableViewCellStyleSubtitle reuseIdentifier:cellIndentifier];
        cell = [[DemoTableViewCell alloc] initWithStyle:UITableViewCellStyleSubtitle reuseIdentifier:cellIndentifier];
    }

    return cell;
}

- (void)tableView:(UITableView *)tableView willDisplayCell:(DemoTableViewCell *)cell forRowAtIndexPath:(NSIndexPath *)indexPath {
    
    NSArray *tempArray = self.dataSoureArray[indexPath.section];
    NSDictionary *dict = [tempArray objectAtIndex:indexPath.row];
    [cell.titleButton setTitle:[dict objectForKey:@"title"] forState:UIControlStateNormal];
    
//    cell.textLabel.text = [dict objectForKey:@"title"];
//    cell.detailTextLabel.text = [dict objectForKey:@"detailTitle"];
//    cell.imageView.image = [UIImage imageNamed:[dict objectForKey:@"image"]];
}

- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath {
    [tableView deselectRowAtIndexPath:indexPath animated:YES];
    
    [self handleAlertWithRowAtIndexPath:indexPath];
}

- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath {
    return 60;
}


#pragma mark - lazyloading
- (UIScrollView *)baseScrollView {
    if (_baseScrollView == nil) {
        _baseScrollView = [[UIScrollView alloc] initWithFrame:self.view.bounds];
        _baseScrollView.pagingEnabled = NO;
        _baseScrollView.showsVerticalScrollIndicator = NO;
        _baseScrollView.showsHorizontalScrollIndicator = NO;
        
    }
    return _baseScrollView;
}

- (UITableView *)tableView {
    if (_tableView == nil) {
        
        CGFloat cellHeight = 60.0f;
        NSArray *tempArray = self.dataSoureArray.firstObject;
        
        _tableView = [[UITableView alloc] initWithFrame:CGRectMake(0, 0, UISCREEN_WIDTH, cellHeight * tempArray.count + 72) style:UITableViewStyleGrouped];
        _tableView.delegate = self;
        _tableView.dataSource = self;
        _tableView.showsVerticalScrollIndicator = NO;
        _tableView.separatorStyle = UITableViewCellSeparatorStyleSingleLine;
        _tableView.scrollEnabled = NO;
//        _tableView.contentInset = UIEdgeInsetsMake(NAVBAR_HEIGHT + STATUSBAR_HEIGHT, 0, IPHONEX_BUTTOM_HEIGHT, 0);
        _tableView.rowHeight = cellHeight;
        [_tableView registerNib:[UINib nibWithNibName:@"DemoTableViewCell" bundle:nil] forCellReuseIdentifier:NSStringFromClass(self.class)];
        _tableView.backgroundColor = UIColor.whiteColor;
    }
    return _tableView;
}

- (TestTextFieldView *)sessionUrlParamTextFieldView {
    if (_sessionUrlParamTextFieldView == nil) {
        _sessionUrlParamTextFieldView = [[TestTextFieldView alloc] initWithFrame:CGRectMake(self.tableView.TKLeft, self.tableView.TKBottom, self.tableView.TKWidth, 60) title:@"会话地址"];
        _sessionUrlParamTextFieldView.textField.text = @"http://xc-tchat.thinkive.com:9600/createSession";
    }
    return _sessionUrlParamTextFieldView;
}

- (TestTextFieldView *)singnalUrlParamTextFieldView {
    if (_singnalUrlParamTextFieldView == nil) {
        _singnalUrlParamTextFieldView = [[TestTextFieldView alloc] initWithFrame:CGRectMake(self.tableView.TKLeft, self.sessionUrlParamTextFieldView.TKBottom + 10, self.tableView.TKWidth, self.sessionUrlParamTextFieldView.TKHeight) title:@"信令地址"];
        _singnalUrlParamTextFieldView.textField.text = @"wss://xc-tchat.thinkive.com:9601";
    }
    return _singnalUrlParamTextFieldView;
}

- (TestTextFieldView *)roomIDParamTextFieldView {
    if (_roomIDParamTextFieldView == nil) {
        _roomIDParamTextFieldView = [[TestTextFieldView alloc] initWithFrame:CGRectMake(self.tableView.TKLeft, self.singnalUrlParamTextFieldView.TKBottom + 10, self.tableView.TKWidth, self.sessionUrlParamTextFieldView.TKHeight) title:@"房间ID"];
        _roomIDParamTextFieldView.textField.text = @"10000";
    }
    return _roomIDParamTextFieldView;
}

- (NSMutableArray *)dataSoureArray {
    if (_dataSoureArray == nil) {
        _dataSoureArray = [[NSMutableArray alloc] init];
    }
    return _dataSoureArray;
}

- (UILabel *)roomIDLabel {

    return [[UIApplication sharedApplication].keyWindow viewWithTag:888888];;
}

@end
