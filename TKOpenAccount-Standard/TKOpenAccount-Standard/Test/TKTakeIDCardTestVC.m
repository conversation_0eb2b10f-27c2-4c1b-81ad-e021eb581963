//
//  TKTakeIDCardTestVC.m
//  TKOpenAccount-Standard
//
//  Created by <PERSON> on 2022/11/16.
//  Copyright © 2022 thinkive. All rights reserved.
//

#import "TKTakeIDCardTestVC.h"
#import "DemoTableViewCell.h"
#import "TestTextFieldView.h"

@interface TKTakeIDCardTestVC ()<UITableViewDelegate, UITableViewDataSource, UIScrollViewDelegate>

@property (nonatomic, readwrite, strong) UIScrollView *baseScrollView;
@property (nonatomic, strong) UITableView *tableView;
@property (nonatomic, strong) NSMutableArray *dataSoureArray;
@property (nonatomic, strong) NSMutableDictionary *requestParam;//请求参数
@property (nonatomic, readwrite, strong) TestTextFieldView *urlParamTextFieldView;
@property (nonatomic, readwrite, strong) TestTextFieldView *imgTypeParamTextFieldView;
@property (nonatomic, readwrite, strong) TestTextFieldView *previewUrlParamTextFieldView;
@property (nonatomic, readwrite, strong) TestTextFieldView *roomIDParamTextFieldView;


@end

@implementation TKTakeIDCardTestVC

- (instancetype)initWithParam:(NSMutableDictionary *)param title:(NSString *)title {
    self=[super init];
    if (self) {
        self.requestParam=param;
        self.title = title;
    }
    return self;
}

- (void)viewDidLoad {
    [super viewDidLoad];
    
    [self initData];
    [self setupUI];
    
}



// MARK: - setupUI
- (void)setupUI {
    [self.navigationItem setTitle:self.title];
//    [self.navigationController.navigationBar setTranslucent:YES];
    self.view.backgroundColor = UIColor.whiteColor;
    [self.view addSubview:self.baseScrollView];
    [self.baseScrollView addSubview:self.tableView];
    [self.tableView reloadData];
    
    // 可选属性UI
    [self.baseScrollView addSubview:self.urlParamTextFieldView];
    [self.baseScrollView addSubview:self.imgTypeParamTextFieldView];
    [self.baseScrollView addSubview:self.previewUrlParamTextFieldView];
//    [self.baseScrollView addSubview:self.roomIDParamTextFieldView];
    self.roomIDLabel.hidden = YES;
}


// MARK: - init data
- (void)initData {
    
    NSArray *sectionArray = sectionArray = @[
//        @[[TKStringHelper isNotEmpty:self.title] ? self.title : @""]
        @[@"开始拍摄身份证"]
    ];
    
    
    for (NSInteger i = 0; i < sectionArray.count; i ++) {
        
        NSArray *titleArray = sectionArray[i];
        NSMutableArray *tempArr = [NSMutableArray array];
        for (int j = 0; j < titleArray.count; j++) {
            NSDictionary *dict = @{
                @"title" : titleArray[j],
            };
            [tempArr addObject:dict];
        }
        
        [self.dataSoureArray addObject:tempArr];
    }
}

#pragma mark - Selector
- (void)handleAlertWithRowAtIndexPath:(NSIndexPath *)indexPath  {
    
    [self smartOneWayVideoRecord];
}

- (void)smartOneWayVideoRecord {
//    NSString *roomID = [TKStringHelper isNotEmpty:self.roomIDParamTextFieldView.textField.text] ? self.roomIDParamTextFieldView.textField.text : @"";
    NSString *previewUrl = [TKStringHelper isNotEmpty:self.previewUrlParamTextFieldView.textField.text] ? self.previewUrlParamTextFieldView.textField.text : @"";
    NSString *sessionUrl = [TKStringHelper isNotEmpty:self.urlParamTextFieldView.textField.text] ? self.urlParamTextFieldView.textField.text : @"";
//    if ([TKStringHelper isNotEmpty:roomID] && [TKStringHelper isNotEmpty:sessionUrl]) {
//        sessionUrl = [TKCommonUtil url:sessionUrl appendingParamStr:[NSString stringWithFormat:@"roomid=%@", roomID]];
//    }
    NSString *singnalUrl = [TKStringHelper isNotEmpty:self.imgTypeParamTextFieldView.textField.text] ? self.imgTypeParamTextFieldView.textField.text : @"";

    
    // 让传入的参数
    NSMutableDictionary *param =[[NSMutableDictionary alloc] init];
    
    param[@"isUpload"]=@"0";//原生插件直接调用不走思迪上传
    param[@"action"]=@"pai";//pai:拍照界面
    param[@"mainColor"]=@"#FD671A";//主色调16进制值，不传默认蓝色
    param[@"imgType"]=singnalUrl;//身份证正反面：4：身份证正面，5：身份证反面；
    param[@"isNeedSample"]=previewUrl;//照片结果展示是否需要显示示例部分:1：需要示例显示；其他不需要（默认不需要）
        
    NSString *pluginNo = [param getStringWithKey:@"funcNo"];    // 取出插件号
    if ([TKStringHelper isEmpty:pluginNo]) {
        // 插件号不能为空
        // 回调报错信息给h5
    }
    NSString *moduleName = [param getStringWithKey:@"moduleName"];
    //插件调用
    [[TKPluginInvokeCenter shareInstance] callPlugin:pluginNo param:param moduleName:[TKStringHelper isNotEmpty:moduleName] ? moduleName : @"open" callBackFunc:^(NSMutableDictionary *result) {
        // 回调给原生的结果，可以直接回调给h5
        TKLogInfo(@"result = %@", result);
    }];
    
    __weak typeof(self) weakSelf = self;
    [self callPlugin:@"60013" param:param callBackFunc:^(NSMutableDictionary *result) {
        
        // 隐藏房间ID label
        weakSelf.roomIDLabel.hidden = YES;
        
        NSString *errorNo = [result getStringWithKey:@"error_no"];
        NSString *errorInfo = [result getStringWithKey:@"error_info"];
        NSString *filePath = [result getStringWithKey:@"filePath"];
        
        if ([errorNo isEqualToString:@"0"]) {
            [TKAlertHelper showAlert:[NSString stringWithFormat:@"视频录制完成:%@",filePath] title:@"结果" okBtnText:@"确定" btnHandler:^(NSInteger buttonIndex) {
                
            } parentViewController:weakSelf];
        } else {
            if (result[@"error_info"]) {
                
                dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                    [TKAlertHelper showAlert:[NSString stringWithFormat:@"错误号:%@\n错误信息:%@", errorNo, errorInfo] title:@"错误提示" okBtnText:@"确定" btnHandler:nil parentViewController:self];
                });
            }
        }
    }];
}

- (void)callPlugin:(NSString *)pluginNumer param:(NSDictionary *)param callBackFunc:(TKPluginCallBackFunc)callBackFunc
{
    ResultVo *vo = [[TKPluginInvokeCenter shareInstance] callPlugin:pluginNumer param:param moduleName:@"open" isH5:NO callBackFunc:^(NSMutableDictionary *result) {
        
        if (callBackFunc) callBackFunc(result);
    }];
    
    if (vo.errorNo != 0) {
        
        NSString *errorMsg = [TKStringHelper isNotEmpty:vo.errorInfo] ? vo.errorInfo : @"运行插件出错";
        
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            [TKAlertHelper showAlert:[NSString stringWithFormat:@"错误信息:%@", errorMsg] title:@"错误提示" okBtnText:@"确定" btnHandler:nil parentViewController:self];
        });
    }
    
    TKLogInfo(@"调用插件号%@的插件，结果为%@", pluginNumer, vo);
}




// MARK: - UITableViewDelegate,UITableViewDataSource
- (NSInteger)numberOfSectionsInTableView:(UITableView *)tableView {
    return self.dataSoureArray.count;
}

- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    if (section >= self.dataSoureArray.count) return 1;
    
    NSArray *tempArray = self.dataSoureArray[section];
    return tempArray.count ? tempArray.count : 1;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    NSString *cellIndentifier = NSStringFromClass([self class]);

    UITableViewCell *cell = [tableView dequeueReusableCellWithIdentifier:cellIndentifier];
    if (cell == nil) {
//        cell = [[UITableViewCell alloc] initWithStyle:UITableViewCellStyleSubtitle reuseIdentifier:cellIndentifier];
        cell = [[DemoTableViewCell alloc] initWithStyle:UITableViewCellStyleSubtitle reuseIdentifier:cellIndentifier];
    }

    return cell;
}

- (void)tableView:(UITableView *)tableView willDisplayCell:(DemoTableViewCell *)cell forRowAtIndexPath:(NSIndexPath *)indexPath {
    
    NSArray *tempArray = self.dataSoureArray[indexPath.section];
    NSDictionary *dict = [tempArray objectAtIndex:indexPath.row];
    [cell.titleButton setTitle:[dict objectForKey:@"title"] forState:UIControlStateNormal];
    
//    cell.textLabel.text = [dict objectForKey:@"title"];
//    cell.detailTextLabel.text = [dict objectForKey:@"detailTitle"];
//    cell.imageView.image = [UIImage imageNamed:[dict objectForKey:@"image"]];
}

- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath {
    [tableView deselectRowAtIndexPath:indexPath animated:YES];
    
    [self handleAlertWithRowAtIndexPath:indexPath];
}

- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath {
    return 60;
}


#pragma mark - lazyloading
- (UIScrollView *)baseScrollView {
    if (_baseScrollView == nil) {
        _baseScrollView = [[UIScrollView alloc] initWithFrame:self.view.bounds];
        _baseScrollView.pagingEnabled = NO;
        _baseScrollView.showsVerticalScrollIndicator = NO;
        _baseScrollView.showsHorizontalScrollIndicator = NO;
        
    }
    return _baseScrollView;
}

- (UITableView *)tableView {
    if (_tableView == nil) {
        
        CGFloat cellHeight = 60.0f;
        NSArray *tempArray = self.dataSoureArray.firstObject;
        
        _tableView = [[UITableView alloc] initWithFrame:CGRectMake(0, 0, UISCREEN_WIDTH, cellHeight * tempArray.count + 72) style:UITableViewStyleGrouped];
        _tableView.delegate = self;
        _tableView.dataSource = self;
        _tableView.showsVerticalScrollIndicator = NO;
        _tableView.separatorStyle = UITableViewCellSeparatorStyleSingleLine;
        _tableView.scrollEnabled = NO;
//        _tableView.contentInset = UIEdgeInsetsMake(NAVBAR_HEIGHT + STATUSBAR_HEIGHT, 0, IPHONEX_BUTTOM_HEIGHT, 0);
        _tableView.rowHeight = cellHeight;
        [_tableView registerNib:[UINib nibWithNibName:@"DemoTableViewCell" bundle:nil] forCellReuseIdentifier:@"TKTakeIDCardTestVC"];
        _tableView.backgroundColor = UIColor.whiteColor;
    }
    return _tableView;
}

- (TestTextFieldView *)urlParamTextFieldView {
    if (_urlParamTextFieldView == nil) {
        _urlParamTextFieldView = [[TestTextFieldView alloc] initWithFrame:CGRectMake(self.tableView.TKLeft, self.tableView.TKBottom, self.tableView.TKWidth, 60) title:@"身份证上传地址"];
        _urlParamTextFieldView.textField.text = @"";
    }
    return _urlParamTextFieldView;
}

- (TestTextFieldView *)imgTypeParamTextFieldView {
    if (_imgTypeParamTextFieldView == nil) {
        _imgTypeParamTextFieldView = [[TestTextFieldView alloc] initWithFrame:CGRectMake(self.tableView.TKLeft, self.urlParamTextFieldView.TKBottom + 10, self.tableView.TKWidth, self.urlParamTextFieldView.TKHeight) title:@"4,5正反面连拍，4正面，5反面"];
        _imgTypeParamTextFieldView.textField.text = @"4,5";
    }
    return _imgTypeParamTextFieldView;
}

- (TestTextFieldView *)previewUrlParamTextFieldView {
    if (_previewUrlParamTextFieldView == nil) {
        _previewUrlParamTextFieldView = [[TestTextFieldView alloc] initWithFrame:CGRectMake(self.tableView.TKLeft, self.imgTypeParamTextFieldView.TKBottom + 10, self.tableView.TKWidth, self.urlParamTextFieldView.TKHeight) title:@"预览示例：1：需要示例显示；其他不需要"];
//        _previewUrlParamTextFieldView.textField.text = @"http://xc-tchat.thinkive.com:9600/VideoPlaytAction";
        _previewUrlParamTextFieldView.textField.text = @"1";
    }
    return _previewUrlParamTextFieldView;
}

- (TestTextFieldView *)roomIDParamTextFieldView {
    if (_roomIDParamTextFieldView == nil) {
        _roomIDParamTextFieldView = [[TestTextFieldView alloc] initWithFrame:CGRectMake(self.tableView.TKLeft, self.previewUrlParamTextFieldView.TKBottom + 10, self.tableView.TKWidth, self.urlParamTextFieldView.TKHeight) title:@"房间ID"];
        _roomIDParamTextFieldView.textField.text = @"10000";
    }
    return _roomIDParamTextFieldView;
}

- (NSMutableArray *)dataSoureArray {
    if (_dataSoureArray == nil) {
        _dataSoureArray = [[NSMutableArray alloc] init];
    }
    return _dataSoureArray;
}

- (UILabel *)roomIDLabel {

    return [[UIApplication sharedApplication].keyWindow viewWithTag:888888];;
}

@end
