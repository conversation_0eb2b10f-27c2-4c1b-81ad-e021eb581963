//
//  TestGuidePageVC.m
//  TKOpenAccount-Standard
//
//  Created by <PERSON> on 2022/11/16.
//  Copyright © 2022 thinkive. All rights reserved.
//

#import "TestGuidePageVC.h"
#import "DemoTableViewCell.h"
#import "TChatRtcOneWayVideoTestVC.h"
#import "TChatRtcArtificialWitnessTestVC.h"
#import "TKTakeIDCardTestVC.h"

@interface TestGuidePageVC ()<UITableViewDelegate, UITableViewDataSource>

@property (nonatomic, strong) UITableView *tableView;
@property (nonatomic, strong) NSMutableArray *dataSoureArray;
@property (nonatomic, strong) NSMutableDictionary *requestParam;//请求参数
@property (nonatomic, readwrite, strong) UILabel *roomIDLabel;

@end

@implementation TestGuidePageVC

- (void)viewDidLoad {
    [super viewDidLoad];
    
    [self initData];
    [self setupUI];
}



// MARK: - setupUI
- (void)setupUI {
//    [self.navigationItem setTitle:@""];
//    [self.navigationController.navigationBar setTranslucent:YES];
    self.view.backgroundColor = UIColor.whiteColor;
    [self.view addSubview:self.tableView];
    [self.tableView reloadData];
    
    [[UIApplication sharedApplication].keyWindow addSubview:self.roomIDLabel];
    self.roomIDLabel.hidden = YES;
}

- (UITableView *)tableView {
    if (_tableView == nil) {
        _tableView = [[UITableView alloc] initWithFrame:CGRectMake(0, 0, UISCREEN_WIDTH, UISCREEN_HEIGHT) style:UITableViewStyleGrouped];
        _tableView.delegate = self;
        _tableView.dataSource = self;
        _tableView.showsVerticalScrollIndicator = NO;
        _tableView.separatorStyle = UITableViewCellSeparatorStyleSingleLine;
        _tableView.contentInset = UIEdgeInsetsMake(0, 0, IPHONEX_BUTTOM_HEIGHT, 0);
        _tableView.rowHeight = 60.0f;
        [_tableView registerNib:[UINib nibWithNibName:@"DemoTableViewCell" bundle:nil] forCellReuseIdentifier:@"TestGuidePageVC"];
        _tableView.backgroundColor = UIColor.whiteColor;
    }
    return _tableView;
}



// MARK: - init data
- (void)initData {
//    NSArray *titleArray = @[@"身份证上传", @"银行卡拍照", @"大头照拍照", @"人脸识别", @"智能见证", @"数字人见证", @"视频见证"];
//    NSArray *sectionArray = @[@[@"身份证上传"],
//                            @[@"银行卡拍照", @"大头照拍照"],
//                            @[@"人脸识别", @"智能见证", @"数字人见证"],
//                            @[@"视频见证"]];
    NSArray *sectionArray = @[
        @[@"智能单向视频"],
        @[@"双向视频见证"],
        @[@"身份证拍照"],
    ];
    
    for (NSInteger i = 0; i < sectionArray.count; i ++) {
        
        NSArray *titleArray = sectionArray[i];
        NSMutableArray *tempArr = [NSMutableArray array];
        for (int j = 0; j < titleArray.count; j++) {
            NSDictionary *dict = @{
                @"title" : titleArray[j],
            };
            [tempArr addObject:dict];
        }
        
        [self.dataSoureArray addObject:tempArr];
    }
    
}

- (NSMutableArray *)dataSoureArray {
    if (_dataSoureArray == nil) {
        _dataSoureArray = [[NSMutableArray alloc] init];
    }
    return _dataSoureArray;
}


// MARK: - UITableViewDelegate,UITableViewDataSource
- (NSInteger)numberOfSectionsInTableView:(UITableView *)tableView {
    return self.dataSoureArray.count;
}

- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    if (section >= self.dataSoureArray.count) return 1;
    
    NSArray *tempArray = self.dataSoureArray[section];
    return tempArray.count ? tempArray.count : 1;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    NSString *cellIndentifier = NSStringFromClass([self class]);

    UITableViewCell *cell = [tableView dequeueReusableCellWithIdentifier:cellIndentifier];
    if (cell == nil) {
//        cell = [[UITableViewCell alloc] initWithStyle:UITableViewCellStyleSubtitle reuseIdentifier:cellIndentifier];
        cell = [[DemoTableViewCell alloc] initWithStyle:UITableViewCellStyleSubtitle reuseIdentifier:cellIndentifier];
    }

    return cell;
}

- (void)tableView:(UITableView *)tableView willDisplayCell:(DemoTableViewCell *)cell forRowAtIndexPath:(NSIndexPath *)indexPath {
    
    NSArray *tempArray = self.dataSoureArray[indexPath.section];
    NSDictionary *dict = [tempArray objectAtIndex:indexPath.row];
    [cell.titleButton setTitle:[dict objectForKey:@"title"] forState:UIControlStateNormal];
    
//    cell.textLabel.text = [dict objectForKey:@"title"];
//    cell.detailTextLabel.text = [dict objectForKey:@"detailTitle"];
//    cell.imageView.image = [UIImage imageNamed:[dict objectForKey:@"image"]];
}

- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath {
    [tableView deselectRowAtIndexPath:indexPath animated:YES];
    
    [self handleAlertWithRowAtIndexPath:indexPath];
}

- (void)handleAlertWithRowAtIndexPath:(NSIndexPath *)indexPath  {
    
    NSArray *tempArray = self.dataSoureArray[indexPath.section];
    NSDictionary *dict = [tempArray objectAtIndex:indexPath.row];
    NSString *title = [dict objectForKey:@"title"];
    
    [self jumpToTestPage:indexPath.section title:title];
}


- (void)jumpToTestPage:(TestPageType)type title:(NSString *)title
{
    UIViewController *page = nil;
    switch (type) {
        case TestPageTypeTChatRtcOneWayVideo:
            page = [[TChatRtcOneWayVideoTestVC alloc] initWithParam:self.requestParam title:title];
            break;
        case TestPageTypeTChatRtcArtificialWitness:
            page = [[TChatRtcArtificialWitnessTestVC alloc] initWithParam:self.requestParam title:title];
            break;
        case TestPageTypeTakeIDCard:
            page = [[TKTakeIDCardTestVC alloc] initWithParam:self.requestParam title:title];
            break;
        default:
            break;
    }
    
    if (page != nil) [self.navigationController pushViewController:page animated:YES];
}

- (UILabel *)roomIDLabel {
    if (_roomIDLabel == nil) {
        _roomIDLabel = [[UILabel alloc] initWithFrame:CGRectMake(5, UISCREEN_HEIGHT - 20 - 5, 200, 20)];
        _roomIDLabel.text = @"rid:unknown";
        _roomIDLabel.font = [UIFont fontWithName:@"PingFang SC" size:15];
        _roomIDLabel.tag = 888888;
        _roomIDLabel.textColor = [UIColor redColor];
        _roomIDLabel.textAlignment = NSTextAlignmentLeft;
    }
    return _roomIDLabel;
}

@end
