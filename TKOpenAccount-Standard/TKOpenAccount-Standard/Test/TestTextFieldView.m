//
//  TestTextFieldView.m
//  TKOpenAccount-Standard
//
//  Created by <PERSON> on 2022/11/16.
//  Copyright © 2022 thinkive. All rights reserved.
//

#import "TestTextFieldView.h"

@interface TestTextFieldView()<UITextFieldDelegate>

@property (nonatomic, readwrite, copy) NSString *title;

@end

@implementation TestTextFieldView

- (instancetype)initWithFrame:(CGRect)frame title:(NSString *)title {
    if (self = [super initWithFrame:frame]) {
        self.title = title;
        [self addSubview:self.tipLabel];
        [self addSubview:self.textField];
    }
    
    return self;
}

- (instancetype)initWithTitle:(NSString *)title {
    if (self = [super init]) {
        self.title = title;
        [self addSubview:self.tipLabel];
        [self addSubview:self.textField];
    }
    return self;
}

/**
 *  <AUTHOR> 2022年08月16日09:40:41
 *  @return 头部提示语
 */
-(UILabel *)tipLabel{
    if (!_tipLabel) {
        _tipLabel=[[UILabel alloc] initWithFrame:CGRectMake(0, 0, self.TKWidth, self.TKHeight * 0.5)];
        _tipLabel.backgroundColor=[TKUIHelper colorWithHexString:@"#F3F3F3"];
        _tipLabel.textColor=[TKUIHelper colorWithHexString:@"#333333"];
        _tipLabel.text = [NSString stringWithFormat:@"    请输入%@", self.title];
    }
    return _tipLabel;
}


/**
 *  <AUTHOR> 2022年08月16日10:55:19
 *  @return 手机输入框
 */
-(UITextField *)textField{
    if (!_textField) {
        float x = 15;
        float width = self.TKWidth-2*x;
        _textField=[[UITextField alloc] initWithFrame:CGRectMake(x, self.tipLabel.TKBottom, width, self.TKHeight * 0.5)];
        _textField.font = [UIFont systemFontOfSize:13];
        _textField.keyboardType=UIKeyboardTypeDefault;
        [_textField setBackgroundColor:[UIColor whiteColor]];
        [_textField setPlaceholder:[NSString stringWithFormat:@"请输入%@", self.title]];
//        UILabel *label=[[UILabel alloc] init];
//        label.text=[NSString stringWithFormat:@"%@    ", self.title];
//        label.textColor=[TKUIHelper colorWithHexString:@"#000000"];
//        label.backgroundColor=[UIColor clearColor];
//        _textField.leftView=label;
        _textField.leftViewMode=UITextFieldViewModeAlways;
//        _textField.delegate=self;
    }
    return _textField;
}

#pragma mark UITextFieldDelegate
//-(BOOL)textField:(UITextField *)textField shouldChangeCharactersInRange:(NSRange)range replacementString:(NSString *)string{
//    //限制手机号输入长度
//    if (textField == self.textField) {
//        if (range.location<11) {
//            return YES;
//        }else{
//            return NO;
//        }
//    }
//    return YES;
//}


@end
