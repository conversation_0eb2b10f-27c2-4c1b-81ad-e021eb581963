//
//  TChatRtcOneWayVideoTestVC.m
//  TKOpenAccount-Standard
//
//  Created by <PERSON> on 2022/11/16.
//  Copyright © 2022 thinkive. All rights reserved.
//

#import "TChatRtcOneWayVideoTestVC.h"
#import "DemoTableViewCell.h"
#import "TestTextFieldView.h"

@interface TChatRtcOneWayVideoTestVC ()<UITableViewDelegate, UITableViewDataSource, UIScrollViewDelegate>

@property (nonatomic, readwrite, strong) UIScrollView *baseScrollView;
@property (nonatomic, strong) UITableView *tableView;
@property (nonatomic, strong) NSMutableArray *dataSoureArray;
@property (nonatomic, strong) NSMutableDictionary *requestParam;//请求参数
@property (nonatomic, readwrite, strong) TestTextFieldView *sessionUrlParamTextFieldView;
@property (nonatomic, readwrite, strong) TestTextFieldView *singnalUrlParamTextFieldView;
@property (nonatomic, readwrite, strong) TestTextFieldView *previewUrlParamTextFieldView;
@property (nonatomic, readwrite, strong) TestTextFieldView *roomIDParamTextFieldView;


@end

@implementation TChatRtcOneWayVideoTestVC

- (instancetype)initWithParam:(NSMutableDictionary *)param title:(NSString *)title {
    self=[super init];
    if (self) {
        self.requestParam=param;
        self.title = title;
    }
    return self;
}

- (void)viewDidLoad {
    [super viewDidLoad];
    
    [self initData];
    [self setupUI];
    
}



// MARK: - setupUI
- (void)setupUI {
    [self.navigationItem setTitle:self.title];
//    [self.navigationController.navigationBar setTranslucent:YES];
    self.view.backgroundColor = UIColor.whiteColor;
    [self.view addSubview:self.baseScrollView];
    [self.baseScrollView addSubview:self.tableView];
    [self.tableView reloadData];
    
    // 可选属性UI
    [self.baseScrollView addSubview:self.sessionUrlParamTextFieldView];
    [self.baseScrollView addSubview:self.singnalUrlParamTextFieldView];
    [self.baseScrollView addSubview:self.previewUrlParamTextFieldView];
//    [self.baseScrollView addSubview:self.roomIDParamTextFieldView];
    self.roomIDLabel.hidden = YES;
}


// MARK: - init data
- (void)initData {
    
    NSArray *sectionArray = sectionArray = @[
//        @[[TKStringHelper isNotEmpty:self.title] ? self.title : @""]
        @[@"开始视频"]
    ];
    
    
    for (NSInteger i = 0; i < sectionArray.count; i ++) {
        
        NSArray *titleArray = sectionArray[i];
        NSMutableArray *tempArr = [NSMutableArray array];
        for (int j = 0; j < titleArray.count; j++) {
            NSDictionary *dict = @{
                @"title" : titleArray[j],
            };
            [tempArr addObject:dict];
        }
        
        [self.dataSoureArray addObject:tempArr];
    }
}

#pragma mark - Selector
- (void)handleAlertWithRowAtIndexPath:(NSIndexPath *)indexPath  {
    
    [self smartOneWayVideoRecord];
}

- (void)smartOneWayVideoRecord {
//    NSString *roomID = [TKStringHelper isNotEmpty:self.roomIDParamTextFieldView.textField.text] ? self.roomIDParamTextFieldView.textField.text : @"";
    NSString *previewUrl = [TKStringHelper isNotEmpty:self.previewUrlParamTextFieldView.textField.text] ? self.previewUrlParamTextFieldView.textField.text : @"";
    NSString *sessionUrl = [TKStringHelper isNotEmpty:self.sessionUrlParamTextFieldView.textField.text] ? self.sessionUrlParamTextFieldView.textField.text : @"";
//    if ([TKStringHelper isNotEmpty:roomID] && [TKStringHelper isNotEmpty:sessionUrl]) {
//        sessionUrl = [TKCommonUtil url:sessionUrl appendingParamStr:[NSString stringWithFormat:@"roomid=%@", roomID]];
//    }
    NSString *singnalUrl = [TKStringHelper isNotEmpty:self.singnalUrlParamTextFieldView.textField.text] ? self.singnalUrlParamTextFieldView.textField.text : @"";
    
    NSMutableDictionary *param = @{
        @"requestHeaders": self.requestParam[@"requestHeaders"] ? self.requestParam[@"requestHeaders"] : @{},
        @"uploadTipString": @"请您确认：我是***，已知晓证券市场风险，已阅读并充分理解网上开户协议条款，自愿选择在思迪证券开立账户，并承诺所提供的信息及证件真实、合法、有效。",
        @"isRestFull" : @"1",
        @"isURLSign" : @"1",
        @"requestSignKey" : @"f080556a782d50087beebb0fae7aabd2",
        @"videoType" : @"1",
        @"_videoPreviewUrl" : previewUrl,
        @"_sessionUrl" : sessionUrl,
        @"_singnalUrl" : singnalUrl,
        @"maxFailureCountPerAsrNoVoice" : @"1",
        @"roomid" : @"10000",
    }.mutableCopy;
    
    [param addEntriesFromDictionary:[self getTipsParam]];
    
    __weak typeof(self) weakSelf = self;
    [self callPlugin:@"60057" param:param callBackFunc:^(NSMutableDictionary *result) {
        
        // 隐藏房间ID label
        weakSelf.roomIDLabel.hidden = YES;
        
        NSString *errorNo = [result getStringWithKey:@"error_no"];
        NSString *errorInfo = [result getStringWithKey:@"error_info"];
        NSString *filePath = [result getStringWithKey:@"filePath"];
        
        if ([errorNo isEqualToString:@"0"]) {
            [TKAlertHelper showAlert:[NSString stringWithFormat:@"视频录制完成:%@",filePath] title:@"结果" okBtnText:@"确定" btnHandler:^(NSInteger buttonIndex) {
                
            } parentViewController:weakSelf];
        } else if ([errorNo isEqualToString:@"-10"]) {
            if (result[@"error_info"]) {
                
                dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
//                    [TKAlertHelper showAlert:result[@"error_info"] title:@"错误提示" okBtnText:@"确定" btnHandler:^(NSInteger buttonIndex) {
//
//                        [self.navigationController popToRootViewControllerAnimated:YES];
//                    } parentViewController:self];
                    [TKAlertHelper showAlert:[NSString stringWithFormat:@"错误号:%@\n错误信息:%@", errorNo, errorInfo] title:@"错误提示" okBtnText:@"确定" btnHandler:nil parentViewController:self];
                });
            }
        } else {
            if (result[@"error_info"]) {
                
                dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                    [TKAlertHelper showAlert:[NSString stringWithFormat:@"错误号:%@\n错误信息:%@", errorNo, errorInfo] title:@"错误提示" okBtnText:@"确定" btnHandler:nil parentViewController:self];
                });
            }
        }
    }];
}

- (void)callPlugin:(NSString *)pluginNumer param:(NSDictionary *)param callBackFunc:(TKPluginCallBackFunc)callBackFunc
{
    ResultVo *vo = [[TKPluginInvokeCenter shareInstance] callPlugin:pluginNumer param:param moduleName:@"open" isH5:NO callBackFunc:^(NSMutableDictionary *result) {
        
        if (callBackFunc) callBackFunc(result);
    }];
    
    if (vo.errorNo != 0) {
        
        NSString *errorMsg = [TKStringHelper isNotEmpty:vo.errorInfo] ? vo.errorInfo : @"运行插件出错";
        
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            [TKAlertHelper showAlert:[NSString stringWithFormat:@"错误信息:%@", errorMsg] title:@"错误提示" okBtnText:@"确定" btnHandler:nil parentViewController:self];
        });
    }
    
    TKLogInfo(@"调用插件号%@的插件，结果为%@", pluginNumer, vo);
}


- (NSMutableDictionary *)getTipsParam
{
    NSMutableDictionary *param = [NSMutableDictionary dictionary];
    
    //  提示语音
    NSMutableArray *beforeVideoArray = [[NSMutableArray alloc] init];
    
//    NSMutableDictionary *beforeVideoDic1 = [[NSMutableDictionary alloc] init];
//    //提示问题会语音播报
//    beforeVideoDic1[@"fileSource"]=@"2";
//    beforeVideoDic1[@"tipContent"]=@"请您保持全脸在人像框内";
////    beforeVideoDic1[@"tipTitle"] = @"<font style=\"font-size:22px;\" color=\"#ff5900\">宋先生您好，欢迎来到中信建投证券开户。</font>";
//    [beforeVideoArray addObject:beforeVideoDic1];
//
    NSMutableDictionary *beforeVideoDic2 = [[NSMutableDictionary alloc] init];
    //提示问题会语音播报
    beforeVideoDic2[@"fileSource"]=@"2";
    beforeVideoDic2[@"tipContent"]=@"尊敬的客户，请您在“滴”声后大声使用“是”或“不是”回答以下问题。";
//    beforeVideoDic1[@"tipTitle"] = @"<font style=\"font-size:22px;\" color=\"#ff5900\">宋先生您好，欢迎来到中信建投证券开户。</font>";
    [beforeVideoArray addObject:beforeVideoDic2];

//    param[@"beforeVideoArray"] = beforeVideoArray;
    param[@"beforeVideoArray"] = [TKDataHelper arrayToJson:beforeVideoArray];

    //问题数组
    NSMutableArray *questionArray = [[NSMutableArray alloc] init];
    NSMutableDictionary *questionDic1 = [[NSMutableDictionary alloc] init];
    //提示问题会语音播报
    questionDic1[@"fileSource"]=@"2";
//    questionDic1[@"fileName"]=@"5.mp4";
    questionDic1[@"tipContent"]=@"您是否为测试本人，且已知晓证券市场风险，已阅读且充分理解开户协议条款，自愿在思迪证券开户？";
//    questionDic1[@"tipTitle"]=@"<font style=\"font-size:22px;\" color='#FFFFFF'>第一个问题，请问您是否是宋朝增本人在中信建投证券申请开户？请用“是”或“不是”回答。<\/font>";   // 回答提示
    questionDic1[@"prompt"] = @"回答“是”或“不是”";   // 回答提示
//    questionDic1[@"prompt"] = @"<font style=\"font-size:22px;\" color='#FFFFFF'>回答<font style=\"font-size:22px;\" color=\"#FF5900\">“是”<\/font>或<font style=\"font-size:22px;\" color=\"#FF5900\">“不是”<\/font><\/font>";   // 回答提示
    questionDic1[@"standardans"] = @"^(是的|是|对的|嗯)$";   //需要回答的问题，正确回答的正则
    questionDic1[@"failans"] = @"^(不是的|不是)$"; //需要回答的问题，错误回答的正则
    questionDic1[@"waitTime"] = @"10";  //该问题预留的回答时间
    // 错误提示
//    NSMutableDictionary *errorTip1 = [[NSMutableDictionary alloc] init];
//    errorTip1[@"fileSource"]=@"2";
//    errorTip1[@"fileName"]=@"8.mp4";
//    errorTip1[@"tipContent"]=@"您好，由于办理开立业务需本人申请，请客户本人进行视频录制。稍后请您点击屏幕下方“开始录制”按钮重新录制。";
////    errorTip1[@"tipTitle"]=@"<font style=\"font-size:22px;\" color='#FFFFFF'>您好，由于办理开立业务需本人申请，请客户本人进行视频录制。稍后请您点击屏幕下方“开始录制”按钮重新录制。<\/font>";
//    questionDic1[@"errorTip"] = errorTip1;  //错误提示
//    // 没有声音提示
//    NSMutableDictionary *noVoiceTip1 = [[NSMutableDictionary alloc] init];
//    noVoiceTip1[@"fileSource"]=@"2";
////    noVoiceTip1[@"fileName"]=@"9.mp4";
//    noVoiceTip1[@"tipContent"]=@"您好，未听清您的回复，请您用“是”或“不是”重新回答。";
////    noVoiceTip1[@"tipTitle"]=@"<font style=\"font-size:22px;\" color='#FFFFFF'>您好，未听清您的回复，请您用“是”或“否”重新回答。<\/font>";
//    questionDic1[@"noVoiceTip"] = noVoiceTip1;  //错误提示
    // 正确回答提示
//    NSMutableDictionary *standardansTip1 = [[NSMutableDictionary alloc] init];
//    standardansTip1[@"fileSource"]=@"1";
//    standardansTip1[@"fileName"]=@"6.mp4";
//    standardansTip1[@"tipContent"]=@"好的。谢谢您";
//    questionDic1[@"standardansTip"] = standardansTip1;
    [questionArray addObject:questionDic1];
    
    NSMutableDictionary *questionDic3 = [[NSMutableDictionary alloc] init];
    //提示问题会语音播报
    questionDic3[@"fileSource"]=@"2";
    questionDic3[@"tipContent"]=@"录制完毕，感谢您的配合。";
//    beforeVideoDic1[@"tipTitle"] = @"<font style=\"font-size:22px;\" color=\"#ff5900\">宋先生您好，欢迎来到中信建投证券开户。</font>";
    [questionArray addObject:questionDic3];

//    param[@"questionArray"] = questionArray;
    param[@"questionArray"] = [TKDataHelper arrayToJson:questionArray];
    
    //  结束提示语音
//    NSMutableArray *afterVideoArray = [[NSMutableArray alloc] init];
//    NSMutableDictionary *afterVideoDic1 = [[NSMutableDictionary alloc] init];
//    //提示问题会语音播报
//    afterVideoDic1[@"fileSource"]=@"2";
//    afterVideoDic1[@"fileName"]=@"7.mp4";
//    afterVideoDic1[@"tipContent"]=@"录制完毕，感谢您的配合。";
////    afterVideoDic1[@"tipTitle"]=@"<font style=\"font-size:22px;\"color='#FFFFFF'>好的，视频录制完毕，感谢您的配合。<\/font>";
//    [afterVideoArray addObject:afterVideoDic1];
//
////    param[@"afterVideoArray"] = afterVideoArray;
//    param[@"afterVideoArray"] = [TKDataHelper arrayToJson:afterVideoArray];
    
    //  全局错误提示语音
//    NSMutableDictionary *errorTipDic = [[NSMutableDictionary alloc] init];
//    NSMutableDictionary *overNoVoiceCountTip = [[NSMutableDictionary alloc] init];
//    // 多次错误
//    overNoVoiceCountTip[@"fileSource"]=@"1";
//    overNoVoiceCountTip[@"fileName"]=@"12.mp4";
//    overNoVoiceCountTip[@"tipContent"]=@"您好，由于无法识别您本次的回答，请您选择周边安静的场所，重新申请视频。为了尽快帮您录制完成，我也帮您准备了人工视频方式，您可以点击“返回”按钮后进行选择。";
////    overNoVoiceCountTip[@"tipTitle"]=@"<font style=\"font-size:22px;\"color='#FFFFFF'>您好，由于无法识别您本次的回答，请您选择周边安静的场所，重新申请视频。为了尽快帮您录制完成，我也帮您准备了人工视频方式，您可以点击“返回”按钮后进行选择。<\/font>";
//    errorTipDic[@"overNoVoiceCountTip"] = overNoVoiceCountTip;
//
//    // 多张人脸
//    NSMutableDictionary *overPresonCountTip = [[NSMutableDictionary alloc] init];
//    overPresonCountTip[@"fileSource"]=@"1";
//    overPresonCountTip[@"fileName"]=@"13.mp4";
//    overPresonCountTip[@"tipContent"]=@"您好，由于需您本人独自完成视频录制，请您重新录制视频，并确保视频中其他人不出现在镜头中。感谢您的配合，一会儿见！";
////    overPresonCountTip[@"tipTitle"]=@"<font style=\"font-size:22px;\"color='#FFFFFF'>您好，由于需您本人独自完成视频录制，请您重新录制视频，并确保视频中其他人不出现在镜头中。感谢您的配合，一会儿见！<\/font>";
//    errorTipDic[@"overPresonCountTip"] = overPresonCountTip;
//
//    // 多次超出屏幕
//    NSMutableDictionary *overScreenCountTip = [[NSMutableDictionary alloc] init];
//    overScreenCountTip[@"fileSource"]=@"1";
//    overScreenCountTip[@"fileName"]=@"14.mp4";
//    overScreenCountTip[@"tipContent"]=@"您好，由于本次视频中您多次离开镜头，请您重新录制视频，并确保过程中面部始终保持在镜头内。感谢您的配合，一会儿见！";
////    overScreenCountTip[@"tipTitle"]=@"<font style=\"font-size:22px;\"color='#FFFFFF'>您好，由于本次视频中您多次离开镜头，请您重新录制视频，并确保过程中面部始终保持在镜头内。感谢您的配合，一会儿见！<\/font>";
//    errorTipDic[@"overScreenCountTip"] = overScreenCountTip;
//
//    // 多次人脸比对失败
//    NSMutableDictionary *overCompareCountTip = [[NSMutableDictionary alloc] init];
//    overCompareCountTip[@"fileSource"]=@"1";
//    overCompareCountTip[@"fileName"]=@"15.mp4";
//    overCompareCountTip[@"tipContent"]=@"您好，由于本次视频中您多次离开镜头，请您重新录制视频，并确保过程中面部始终保持在镜头内。感谢您的配合，一会儿见！";
////    overCompareCountTip[@"tipTitle"]=@"<font style=\"font-size:22px;\"color='#FFFFFF'>您好，由于本次视频中您多次离开镜头，请您重新录制视频，并确保过程中面部始终保持在镜头内。感谢您的配合，一会儿见！<\/font>";
//    errorTipDic[@"overCompareCountTip"] = overCompareCountTip;
//
////    param[@"errorTipJson"] = errorTipDic;
//    param[@"errorTipJson"] = [TKDataHelper dictionaryToJson:errorTipDic];
    
    return param;
}


// MARK: - UITableViewDelegate,UITableViewDataSource
- (NSInteger)numberOfSectionsInTableView:(UITableView *)tableView {
    return self.dataSoureArray.count;
}

- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    if (section >= self.dataSoureArray.count) return 1;
    
    NSArray *tempArray = self.dataSoureArray[section];
    return tempArray.count ? tempArray.count : 1;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    NSString *cellIndentifier = NSStringFromClass([self class]);

    UITableViewCell *cell = [tableView dequeueReusableCellWithIdentifier:cellIndentifier];
    if (cell == nil) {
//        cell = [[UITableViewCell alloc] initWithStyle:UITableViewCellStyleSubtitle reuseIdentifier:cellIndentifier];
        cell = [[DemoTableViewCell alloc] initWithStyle:UITableViewCellStyleSubtitle reuseIdentifier:cellIndentifier];
    }

    return cell;
}

- (void)tableView:(UITableView *)tableView willDisplayCell:(DemoTableViewCell *)cell forRowAtIndexPath:(NSIndexPath *)indexPath {
    
    NSArray *tempArray = self.dataSoureArray[indexPath.section];
    NSDictionary *dict = [tempArray objectAtIndex:indexPath.row];
    [cell.titleButton setTitle:[dict objectForKey:@"title"] forState:UIControlStateNormal];
    
//    cell.textLabel.text = [dict objectForKey:@"title"];
//    cell.detailTextLabel.text = [dict objectForKey:@"detailTitle"];
//    cell.imageView.image = [UIImage imageNamed:[dict objectForKey:@"image"]];
}

- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath {
    [tableView deselectRowAtIndexPath:indexPath animated:YES];
    
    [self handleAlertWithRowAtIndexPath:indexPath];
}

- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath {
    return 60;
}


#pragma mark - lazyloading
- (UIScrollView *)baseScrollView {
    if (_baseScrollView == nil) {
        _baseScrollView = [[UIScrollView alloc] initWithFrame:self.view.bounds];
        _baseScrollView.pagingEnabled = NO;
        _baseScrollView.showsVerticalScrollIndicator = NO;
        _baseScrollView.showsHorizontalScrollIndicator = NO;
        
    }
    return _baseScrollView;
}

- (UITableView *)tableView {
    if (_tableView == nil) {
        
        CGFloat cellHeight = 60.0f;
        NSArray *tempArray = self.dataSoureArray.firstObject;
        
        _tableView = [[UITableView alloc] initWithFrame:CGRectMake(0, 0, UISCREEN_WIDTH, cellHeight * tempArray.count + 72) style:UITableViewStyleGrouped];
        _tableView.delegate = self;
        _tableView.dataSource = self;
        _tableView.showsVerticalScrollIndicator = NO;
        _tableView.separatorStyle = UITableViewCellSeparatorStyleSingleLine;
        _tableView.scrollEnabled = NO;
//        _tableView.contentInset = UIEdgeInsetsMake(NAVBAR_HEIGHT + STATUSBAR_HEIGHT, 0, IPHONEX_BUTTOM_HEIGHT, 0);
        _tableView.rowHeight = cellHeight;
        [_tableView registerNib:[UINib nibWithNibName:@"DemoTableViewCell" bundle:nil] forCellReuseIdentifier:@"TChatRtcOneWayVideoTestVC"];
        _tableView.backgroundColor = UIColor.whiteColor;
    }
    return _tableView;
}

- (TestTextFieldView *)sessionUrlParamTextFieldView {
    if (_sessionUrlParamTextFieldView == nil) {
        _sessionUrlParamTextFieldView = [[TestTextFieldView alloc] initWithFrame:CGRectMake(self.tableView.TKLeft, self.tableView.TKBottom, self.tableView.TKWidth, 60) title:@"会话地址"];
        _sessionUrlParamTextFieldView.textField.text = @"http://xc-tchat.thinkive.com:9600/createSession";
    }
    return _sessionUrlParamTextFieldView;
}

- (TestTextFieldView *)singnalUrlParamTextFieldView {
    if (_singnalUrlParamTextFieldView == nil) {
        _singnalUrlParamTextFieldView = [[TestTextFieldView alloc] initWithFrame:CGRectMake(self.tableView.TKLeft, self.sessionUrlParamTextFieldView.TKBottom + 10, self.tableView.TKWidth, self.sessionUrlParamTextFieldView.TKHeight) title:@"信令地址"];
        _singnalUrlParamTextFieldView.textField.text = @"wss://192.168.95.193:19000";
    }
    return _singnalUrlParamTextFieldView;
}

- (TestTextFieldView *)previewUrlParamTextFieldView {
    if (_previewUrlParamTextFieldView == nil) {
        _previewUrlParamTextFieldView = [[TestTextFieldView alloc] initWithFrame:CGRectMake(self.tableView.TKLeft, self.singnalUrlParamTextFieldView.TKBottom + 10, self.tableView.TKWidth, self.sessionUrlParamTextFieldView.TKHeight) title:@"预览地址"];
//        _previewUrlParamTextFieldView.textField.text = @"http://xc-tchat.thinkive.com:9600/VideoPlaytAction";
        _previewUrlParamTextFieldView.textField.text = @"https://xc-tchat.thinkive.com:9604";
    }
    return _previewUrlParamTextFieldView;
}

- (TestTextFieldView *)roomIDParamTextFieldView {
    if (_roomIDParamTextFieldView == nil) {
        _roomIDParamTextFieldView = [[TestTextFieldView alloc] initWithFrame:CGRectMake(self.tableView.TKLeft, self.previewUrlParamTextFieldView.TKBottom + 10, self.tableView.TKWidth, self.sessionUrlParamTextFieldView.TKHeight) title:@"房间ID"];
        _roomIDParamTextFieldView.textField.text = @"10000";
    }
    return _roomIDParamTextFieldView;
}

- (NSMutableArray *)dataSoureArray {
    if (_dataSoureArray == nil) {
        _dataSoureArray = [[NSMutableArray alloc] init];
    }
    return _dataSoureArray;
}

- (UILabel *)roomIDLabel {

    return [[UIApplication sharedApplication].keyWindow viewWithTag:888888];;
}

@end
