//
//  AppDelegate.m
//  TKOpenAccount-Standard
//
//  Created by <PERSON><PERSON> on 15/5/9.
//  Copyright (c) 2015年 thinkive. All rights reserved.
//

#import "AppDelegate.h"
#import "TKOpenController.h"
//#import "ModelLoading.h"
#define H5_URL @"H5Url"
#import "MNavViewController.h"
#import <TChat/TChatCore.h>
#import <TChat/TChatDefine.h>
#import <AnyChatCoreSDK/AnyChatCoreSDK.h>
#import <ISIDReaderPreviewSDK/ISIDReaderPreviewSDK.h>
#import <ISBankCard/ISBankCardController.h>

#import "ViewController.h"
#import "TKOpenPrivacyAgreementView.h"
#import "TKOpenAccountService.h"

#import "TKOpenLoginViewController.h"
#import "DemoViewController.h"
#import "DemoPageViewController.h"

#import "TestGuidePageVC.h"

@interface AppDelegate ()<TKOpenDelegate,TKOpenPrivacyAgreementDelegate,TKStatisticEventHelperDelegate,TKDeviceInfoDelegate>

{
    UIViewController *viewCtr;
    TKOpenController *mCtl;
    TKOpenPrivacyAgreementView *privacyView;
    ViewController *_vc;
}

@end

@implementation AppDelegate


- (BOOL)application:(UIApplication *)application didFinishLaunchingWithOptions:(NSDictionary *)launchOptions
{
    
    [super application:application didFinishLaunchingWithOptions:launchOptions];
    
    // 注册隐私协议flag修改通知
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(userAgreeFlagIsChange:) name:NOTE_CHANGE_USER_AGREE_RIGHT object:nil];
    
//    NSString *ocrVersion= [ISIDCardReaderController getSDKVersion];
    
//    [TChatCore InitSDK];
//    int MainVer  =[TChatCore GetSDKOptionInt:TKCC_SO_CORESDK_MAIN_VERSION];
//    int SubVer  =[TChatCore GetSDKOptionInt:TKCC_SO_CORESDK_SUB_VERSION];
//    int StageVer  =[TChatCore GetSDKOptionInt:TKCC_SO_CORESDK_STAGE_VERSION];
//    NSString *versionTime=[TChatCore GetSDKOptionString:TKCC_SO_CORESDK_BUILD_TIME];
    
//    NSString *version=[AnyChatPlatform GetSDKVersion];
    
//    NSString *string=[ISIDCardReaderController getSDKVersion];
//    NSString *bankstring=[ISBankCardController getSDKVersion];


    
//    [[TKAppStartManager shareInstance] launch];
    
//    TKChatVideoRecordViewController *vc = [[TKChatVideoRecordViewController alloc] initWithParam:@{}.mutableCopy];
//    [mCtl presentViewController:vc animated:YES completion:nil];
    
//    // 调试服务端录制代码
//    ViewController *vc1 = [ViewController new];
//    vc1.view;
//    _vc = vc1;
//    self.window.rootViewController = vc1;
    
    //原生登陆界面模拟
//    UIViewController *vc = [TKOpenLoginViewController new];
//    UIViewController *vc = [DemoViewController new];
//    UIViewController *vc = [[DemoPageViewController alloc] initWithParam:@{} type:DemoPageTypeLiveDetect];
//    UIViewController *vc = [TestGuidePageVC new];
//    UINavigationController *nav = [[TKGestureNavigationController alloc] initWithRootViewController:vc];
//    self.window.rootViewController = nav;
    
//    UIStoryboard *mainStoryboard = [UIStoryboard storyboardWithName:@"Main" bundle:nil];
//    UIViewController *controller = [mainStoryboard instantiateViewControllerWithIdentifier:@"select"];
//    self.window.rootViewController = controller;
    
    [TKStatisticEventHelper shareInstance].delegate = self;
    
    viewCtr=[[UIViewController alloc] init];
    self.window.rootViewController = viewCtr;
    
    [self addPrivacyAgreement];
    return YES;
}

- (void)userAgreeFlagIsChange:(NSNotification *)noti {
    BOOL isAgree = [NSString stringWithFormat:@"%@", noti.object].boolValue;
    //设置同意协议标识位
    [[TKCacheManager shareInstance] saveCacheData:[NSString stringWithFormat:@"%i", isAgree] cacheType:TKCacheType_File withKey:@"agreePolicyFlag"];
}

/**
 *  <AUTHOR> 2019年08月23日15:14:58
 *  加载隐私协议展示
 */
-(void)addPrivacyAgreement{
    NSString * agreePolicyFlag =(NSString *)[[TKCacheManager shareInstance] getCacheDataWithKey:@"agreePolicyFlag" cacheType:TKCacheType_File];

    if (![agreePolicyFlag isEqualToString:@"1"]) {
        privacyView=[[TKOpenPrivacyAgreementView alloc] initWithFrame:[UIScreen mainScreen].bounds];
        privacyView.delegate=self;
        [viewCtr.view addSubview:privacyView];
    }else{
        
        [self addTKOpenController];
    }
}

-(void)addTKOpenController{
    
    NSBundle *bundle = [NSBundle bundleWithURL:[[NSBundle mainBundle] URLForResource:[NSString stringWithFormat:@"%@", TK_OPEN_RESOURCE_NAME] withExtension:@"bundle"]];
    
    NSString *plistPath = [bundle pathForResource:@"config" ofType:@"plist"];
    
    NSMutableDictionary *cDic;
    
    if (plistPath) {
        
        cDic = [[NSMutableDictionary alloc] initWithContentsOfFile:plistPath];
    }
    
    cDic[@"h5Url"]=cDic[H5_URL];
    if([TKStringHelper isEmpty:cDic[@"h5Url"]]){
     //原生直接这里改h5地址，打包机走config.plist配置；避免给sdk时候有地址相关配置
        cDic[@"h5Url"]=@"https://opt-dev.thinkive.com:15149/tk-stkkh-view/views/index.html?showPrivacy=0";//开发环境
//        cDic[@"h5Url"]=@"https://operation.thinkive.com:18090/tk-stkkh-view/views/index.html?showPrivacy=0";//演示环境
    }

    
//    NSString *str = [TKAesHelper stringWithAesEncryptString:@"thinkivethinkivethinkive" withKey:[TKPasswordGenerator generatorPassword]];
    
    mCtl=[[TKOpenController alloc] initWithParams:cDic loginInfoParam:nil];
    //     mCtl.isShowLoadingCloseBtn=YES;
//    mCtl.isNoShowLoading=YES;
//    [mCtl loginInfoWithParam:@{@"token":@"123456",@"phoneNo":@"18888888888"}];
//    [mCtl loginOut];

//    [TKOpenController print3libSDKInfoLog];
    
//    mCtl.isImmersion=YES;
    mCtl.oDelegate=self;
    mCtl.tkDeviceInfoDelegate=self;
//    mCtl.isNeedTKAuthorIntroduce=YES;
//    mCtl.skinMode=@"1";
    
//    mCtl.loadingBgColorString=@"000000";
//    mCtl.gifImgName=@"photo_face.png";
    
//    //状态栏颜色
//    mCtl.statusBarBgColor = [TKUIHelper colorWithHexString:@"#0354c2"];
    
//    //原生导航头
//    //标题栏颜色
//    mCtl.titleColor=[TKUIHelper colorWithHexString:@"#FFFFFF"];
//    //返回按钮显示
//    mCtl.isShowBackBtn = YES;
//    //关闭按钮显示
//    mCtl.isShowCloseBtn = YES;
//    //标题栏文字
//    mCtl.title=@"手机开户";
//    //标题文字是否根据webview的Title而变化
//    mCtl.isChangeTitle = YES;
//    //如果Title不为空,返回或者关闭按钮的模式，默认是文本模式（0：文本，1：图片，2：文本+图片）
//    mCtl.btnMode = @"0";
    
//    mCtl.statusBarStyle = UIStatusBarStyleDefault;
//    mCtl.iphoneXBottomColor=[UIColor blackColor];

    
//    mCtl.progressColor = [UIColor clearColor];
//    [TKOpenViewStyleHelper shareInstance].isElder=YES;
    
    self.window.rootViewController = mCtl;
}

#pragma TKDFPrivacyAgreementDelegate
-(void)agreePolicy{
    [self addTKOpenController];
    //设置同意协议标识位
    [[TKCacheManager shareInstance] saveCacheData:@"1" cacheType:TKCacheType_File withKey:@"agreePolicyFlag"];
    
    // 通知底层库更新隐私协议标记
    [[TKPluginInvokeCenter shareInstance] callPlugin:@"50141" param:@{
        @"isUserAgreeRight" : @"1",
    } moduleName:@"open"];
    
    [privacyView removeFromSuperview];
}





#pragma mark - TKStatisticEventHelperDelegate
/// 回调统计事件
/// - Parameters:
///   - event: 事件类型
///   - params: 事件参数
- (void)statisticEventHelperDidCallBack:(TKStatisticEvent)event params:(NSDictionary *_Nullable)params {
    NSString *timeString;

    switch (event) {
        case TK_VIDEO_WITNESS_CONNECT_SUCCESS: // 视频见证_连接_成功
        {
            
        }
            break;
        case TK_VIDEO_WITNESS_QUEUE_HOLD_DOWN: // 视频见证_排队_用户挂断
        {


            // TODO 上报埋点事件

        }
            break;
        
        case TK_VIDEO_WITNESS_QUEUE_SUCCESS: // 视频见证_排队_成功
        {
            //将时间戳转换成指定格式的时间字符串
            NSString *timestampString = params[@"eventTime"];
            NSTimeInterval interval=[timestampString doubleValue] / 1000.0;
            NSDate *date = [NSDate dateWithTimeIntervalSince1970:interval];
            NSDateFormatter *formatter=[[NSDateFormatter alloc]init];
            [formatter setLocale:[NSLocale currentLocale]];
            [formatter setDateFormat:@"yyyy-MM-dd HH:mm:ss"];
            timeString = [formatter stringFromDate:date];

            // TODO 上报埋点事件

        }

            break;
        default:
            break;
    }
}

///**
// *
// * @description 开户sdk外部调用回调
// */
//- (void)interruptHandleExternalCall:(UIViewController*)hController withParams:(id)params{
//
//}

/**
 *
 * @description 开户sdk外部调用回调（和没有withActionType方法的二选一实现一个）
 */
- (void)interruptHandleExternalCall:(UIViewController*)hController withActionType:(int)actionType withActionParams:(id)actionParams{
    if (actionType == 5) {
         // 认证业务结束
         
        int error_no = [actionParams[@"error_no"] intValue]; //结果错误号 0表示人脸识别通过 1表示超出错误次数   -1表示用户主动返回 -2表示认证码过期
        NSString *error_info = actionParams[@"error_info"]; // 结果说明
        if (error_no==0){
            //0表示正常通过
        } else if (error_no==1){
            //1表示超出错误次数
        } else if (error_no==-1){
            //-1表示用户主动返回
        } else if (error_no==-2){
            //-2表示认证码过期
        }

     }
}


#pragma mark - TKDeviceInfoDelegate
/**
 *  获取设备的唯一性id，这里取MAC地址,大于7.0的取identifierForVendor
 *
 *  @return 设备平台信息
 */
- (NSString *) getDeviceMac{
    return @"DeviceMac";
}

/**
 *  获取设备的uuid
 *
 *  @return 获取设备的uuid
 */
- (NSString *)getDeviceUUID{
    return @"DeviceUUID";
}

/**
 *  获取运营商信息(IMSI)
 *
 *  @return 获取运营商信息(IMSI)
 */
- (NSString *)getPhoneIMSI{
    return @"PhoneIMSI";
}

/**
 *  <AUTHOR> 2015-04-20 19:04:50
 *
 *  获取手机设备的本地IP地址
 *
 *  @return 手机设备的IP地址
 */
- (NSString *)getLocalIP{
    return @"LocalIP";
}

- (void)applicationWillResignActive:(UIApplication *)application {
    // Sent when the application is about to move from active to inactive state. This can occur for certain types of temporary interruptions (such as an incoming phone call or SMS message) or when the user quits the application and it begins the transition to the background state.
    // Use this method to pause ongoing tasks, disable timers, and throttle down OpenGL ES frame rates. Games should use this method to pause the game.
}

- (void)applicationDidEnterBackground:(UIApplication *)application {
    // Use this method to release shared resources, save user data, invalidate timers, and store enough application state information to restore your application to its current state in case it is terminated later.
    // If your application supports background execution, this method is called instead of applicationWillTerminate: when the user quits.
    
    TKLogInfo(@"app enter background.");
}

- (void)applicationWillEnterForeground:(UIApplication *)application {
    // Called as part of the transition from the background to the inactive state; here you can undo many of the changes made on entering the background.
    
    TKLogInfo(@"app enter foreground.");

}

- (void)applicationDidBecomeActive:(UIApplication *)application {
    // Restart any tasks that were paused (or not yet started) while the application was inactive. If the application was previously in the background, optionally refresh the user interface.
}

- (void)applicationWillTerminate:(UIApplication *)application {
    // Called when the application is about to terminate. Save data if appropriate. See also applicationDidEnterBackground:.
    
    NSURLCache *cache = [NSURLCache sharedURLCache];
    
    [cache removeAllCachedResponses];
    
    [cache setDiskCapacity:0];
    
    [cache setMemoryCapacity:0];
    
    NSHTTPCookieStorage *storage = [NSHTTPCookieStorage sharedHTTPCookieStorage];
    
    for (NSHTTPCookie *cookie in [storage cookies]) {
        
        [storage deleteCookie:cookie];
    }
}


- (BOOL)application:(UIApplication *)application openURL:(NSURL *)url sourceApplication:(NSString *)sourceApplication annotation:(id)annotation{
    
    TKLogInfo(@"返回信息：%@",[[url absoluteString] stringByReplacingPercentEscapesUsingEncoding:NSUTF8StringEncoding]);
    
    return YES;
}

@end
