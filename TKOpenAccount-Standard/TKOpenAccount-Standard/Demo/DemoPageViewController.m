//
//  DemoPageViewController.m
//  TKOpenAccount-Standard
//
//  Created by <PERSON> on 2022/8/24.
//  Copyright © 2022 thinkive. All rights reserved.
//

#import "DemoPageViewController.h"
#import "DemoTableViewCell.h"

@interface DemoPageViewController ()<UITableViewDelegate, UITableViewDataSource>

@property (nonatomic, strong) UITableView *tableView;
@property (nonatomic, readwrite, strong) UIImageView *imageView;
@property (nonatomic, strong) NSMutableArray *dataSoureArray;
@property (nonatomic, strong) NSMutableDictionary *requestParam;//请求参数
@property (nonatomic, readwrite, assign) DemoPageType type;

@end

@implementation DemoPageViewController
- (instancetype)initWithParam:(NSMutableDictionary *)param type:(DemoPageType)type{
    self=[super init];
    if (self) {
        self.requestParam=param;
        self.type = type;
    }
    return self;
}

- (void)viewDidLoad {
    [super viewDidLoad];
    
    [self initData];
    [self setupUI];
}



// MARK: - setupUI
- (void)setupUI {
//    [self.navigationItem setTitle:@""];
//    [self.navigationController.navigationBar setTranslucent:YES];
    self.view.backgroundColor = UIColor.whiteColor;
    [self.view addSubview:self.tableView];
    [self.tableView reloadData];
    [self.view addSubview:self.imageView];
}

- (UITableView *)tableView {
    if (_tableView == nil) {
        
        CGFloat cellHeight = 60.0f;
        NSArray *tempArray = self.dataSoureArray.firstObject;
        
        _tableView = [[UITableView alloc] initWithFrame:CGRectMake(0, NAVBAR_HEIGHT + STATUSBAR_HEIGHT , UISCREEN_WIDTH, cellHeight * tempArray.count + 72) style:UITableViewStyleGrouped];
        _tableView.delegate = self;
        _tableView.dataSource = self;
        _tableView.showsVerticalScrollIndicator = NO;
        _tableView.separatorStyle = UITableViewCellSeparatorStyleSingleLine;
        _tableView.scrollEnabled = NO;
//        _tableView.contentInset = UIEdgeInsetsMake(NAVBAR_HEIGHT + STATUSBAR_HEIGHT, 0, IPHONEX_BUTTOM_HEIGHT, 0);
        _tableView.rowHeight = cellHeight;
        [_tableView registerNib:[UINib nibWithNibName:@"DemoTableViewCell" bundle:nil] forCellReuseIdentifier:@"DemoPageViewController"];
        _tableView.backgroundColor = UIColor.whiteColor;
    }
    return _tableView;
}


- (UIImageView *)imageView {
    if (_imageView == nil) {
        _imageView = [[UIImageView alloc] initWithFrame:CGRectMake(self.tableView.TKLeft, self.tableView.TKBottom + 20, self.tableView.TKWidth, 200)];
        _imageView.contentMode = UIViewContentModeScaleAspectFit;
    }
    return _imageView;
}

// MARK: - init data
- (void)initData {
    
    NSArray *sectionArray = nil;
    if (self.type == DemoPageTypeIDCard) {
        sectionArray = @[
            @[@"身份证人像面", @"身份证国徽面"],
    //        @[@"单人拍照", @"双人拍照"]
        ];
    } else if (self.type == DemoPageTypeBandCard) {
        sectionArray = @[
            @[@"银行卡拍照"]
        ];
    } else if (self.type == DemoPageTypeTakePhoto) {
        sectionArray = @[
            @[@"单人拍照", @"双人拍照"]
        ];
    } else if (self.type == DemoPageTypeLiveDetect) {
        sectionArray = @[
            @[@"人脸识别"]
        ];
    }
    
    
    
    for (NSInteger i = 0; i < sectionArray.count; i ++) {
        
        NSArray *titleArray = sectionArray[i];
        NSMutableArray *tempArr = [NSMutableArray array];
        for (int j = 0; j < titleArray.count; j++) {
            NSDictionary *dict = @{
                @"title" : titleArray[j],
            };
            [tempArr addObject:dict];
        }
        
        [self.dataSoureArray addObject:tempArr];
    }
}

- (NSMutableArray *)dataSoureArray {
    if (_dataSoureArray == nil) {
        _dataSoureArray = [[NSMutableArray alloc] init];
    }
    return _dataSoureArray;
}


// MARK: - UITableViewDelegate,UITableViewDataSource
- (NSInteger)numberOfSectionsInTableView:(UITableView *)tableView {
    return self.dataSoureArray.count;
}

- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    if (section >= self.dataSoureArray.count) return 1;
    
    NSArray *tempArray = self.dataSoureArray[section];
    return tempArray.count ? tempArray.count : 1;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    NSString *cellIndentifier = NSStringFromClass([self class]);

    UITableViewCell *cell = [tableView dequeueReusableCellWithIdentifier:cellIndentifier];
    if (cell == nil) {
//        cell = [[UITableViewCell alloc] initWithStyle:UITableViewCellStyleSubtitle reuseIdentifier:cellIndentifier];
        cell = [[DemoTableViewCell alloc] initWithStyle:UITableViewCellStyleSubtitle reuseIdentifier:cellIndentifier];
    }

    return cell;
}

- (void)tableView:(UITableView *)tableView willDisplayCell:(DemoTableViewCell *)cell forRowAtIndexPath:(NSIndexPath *)indexPath {
    
    NSArray *tempArray = self.dataSoureArray[indexPath.section];
    NSDictionary *dict = [tempArray objectAtIndex:indexPath.row];
    [cell.titleButton setTitle:[dict objectForKey:@"title"] forState:UIControlStateNormal];
//    cell.textLabel.text = [dict objectForKey:@"title"];
//    cell.detailTextLabel.text = [dict objectForKey:@"detailTitle"];
//    cell.imageView.image = [UIImage imageNamed:[dict objectForKey:@"image"]];
}

- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath {
    [tableView deselectRowAtIndexPath:indexPath animated:YES];
    
    [self handleAlertWithRowAtIndexPath:indexPath];
}

- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath {
    return 60;
}

- (void)handleAlertWithRowAtIndexPath:(NSIndexPath *)indexPath  {
    
    if (self.type == DemoPageTypeIDCard) {
        
        switch (indexPath.row) {
            case 0: // 身份证上传-正面
                [self IDCardTakePhoto:YES];
                break;
            case 1: // 身份证上传-反面
                [self IDCardTakePhoto:NO];
                break;
            default:
                break;
        }
    } else if (self.type == DemoPageTypeBandCard) {
        switch (indexPath.row) {
            case 0: // 银行卡拍照
                [self bankCardTakePhoto];
                break;
            default:
                break;
        }
    } else if (self.type == DemoPageTypeTakePhoto) {
        switch (indexPath.row) {
            case 0: // 单人拍照
                [self takePhoto:YES];
                break;
            case 1: // 双人拍照
                [self takePhoto:NO];
                break;
            default:
                break;
        }
    } else if (self.type == DemoPageTypeLiveDetect) {
        switch (indexPath.row) {
            case 0: // 单人拍照
                [self liveDetect];
                break;
            default:
                break;
        }
    }
}

- (void)IDCardTakePhoto:(BOOL)isFront {
    self.imageView.image = nil;
    
    // 让传入的参数
    NSMutableDictionary *param =[[NSMutableDictionary alloc] init];
    param[@"funcNo"]=@"60013";//功能号
    param[@"isUpload"]=@"0";//原生插件直接调用不走思迪上传
    param[@"action"]=@"phone";//pai:拍照界面
//    param[@"mainColor"]=@"#FD671A";//主色调16进制值，不传默认蓝色
//    param[@"imgType"]= isFront ? @"4" : @"5";//身份证正反面：4：身份证正面，5：身份证反面；
    param[@"imgType"]= @"4,5";//身份证正反面：4：身份证正面，5：身份证反面；
    param[@"isNeedSample"]=@"1";//照片结果展示是否需要显示示例部分:1：需要示例显示；其他不需要（默认不需要）
    param[@"isAlbum"]=@"1";
//    param[@"requestParam"]=@"test";
//    param[@"url"]=@"test";
    
    __weak typeof(self) weakSelf = self;
    [self callPlugin:@"60014" param:param callBackFunc:^(NSMutableDictionary *result) {
        NSString *errorNo = result[@"error_no"];
        if ([errorNo isEqualToString:@"0"]) {
            NSString *baseStrKey = isFront ? @"frontBase64" : @"backBase64";
            NSString *baseStr = [result[baseStrKey] stringByReplacingOccurrencesOfString:@"data:image/jpg;base64," withString:@""];
            NSData *base64Date = [TKBase64Helper dataWithDecodeBase64String:baseStr];
            UIImage *image = [UIImage imageWithData:base64Date];
            
            if (image) {
                weakSelf.imageView.image = image;
            } else {
                [TKAlertHelper showAlert:@"获取的图片为空" title:@"错误提示" okBtnText:@"确定" btnHandler:nil parentViewController:self];
            }
            
        } else {
            if (result[@"error_info"]) {
                
                [TKAlertHelper showAlert:result[@"error_info"] title:@"错误提示" okBtnText:@"确定" btnHandler:nil parentViewController:self];
            }
        }
    }];
}

- (void)bankCardTakePhoto {
    self.imageView.image = nil;
    
    // 让传入的参数
    NSMutableDictionary *param =[[NSMutableDictionary alloc] init];
//    param[@"funcNo"]=@"60037";//功能号
    param[@"funcNo"]=@"60037";//功能号
    param[@"mainColor"]=@"#2F85FF";//主色调16进制值，不传默认蓝色
    param[@"isAlbum"]=@"1";//是否显示相册按钮 0：不显示   1：显示（默认显示）
    param[@"isTake"]=@"1";

    __weak typeof(self) weakSelf = self;
    [self callPlugin:@"60016" param:param callBackFunc:^(NSMutableDictionary *result) {
        
        NSString *errorNo = result[@"error_no"];
        if ([errorNo isEqualToString:@"0"]) {
//            UIImage *image = [UIImage imageWithData:result[@"base64"]];
            
            NSString *baseStr = [result[@"base64"] stringByReplacingOccurrencesOfString:@"data:image/jpg;base64," withString:@""];
            NSData *base64Date = [TKBase64Helper dataWithDecodeBase64String:baseStr];
            UIImage *image = [UIImage imageWithData:base64Date];
            
            if (image) {
                weakSelf.imageView.image = image;
            } else {
                [TKAlertHelper showAlert:@"获取的图片为空" title:@"错误提示" okBtnText:@"确定" btnHandler:nil parentViewController:self];
            }
            
        } else {
            if (result[@"error_info"]) {
                
                dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                    [TKAlertHelper showAlert:result[@"error_info"] title:@"错误提示" okBtnText:@"确定" btnHandler:nil parentViewController:self];
                });
            }
        }
    }];
}

- (void)takePhoto:(BOOL)isSingle {
    self.imageView.image = nil;
    
    // 让传入的参数
    NSMutableDictionary *param =[[NSMutableDictionary alloc] init];
    param[@"funcNo"]=@"60028";//功能号
    param[@"mainColor"]=@"#2F85FF";//主色调16进制值，不传默认蓝色
    param[@"takeType"]= isSingle ? @"0" : @"1"; //0 ：单人框 （竖屏）2：单人无对准框（竖屏，可用于手持身份证拍照）；默认0
    param[@"isNeedPreview"]=@"1"; //是否需要原生拍照预览界面(目前只支持竖屏页面)

    __weak typeof(self) weakSelf = self;
    [self callPlugin:@"60028" param:param callBackFunc:^(NSMutableDictionary *result) {
        NSString *errorNo = result[@"error_no"];
        if ([errorNo isEqualToString:@"0"]) {
            NSString *baseStr = [result[@"base64"] stringByReplacingOccurrencesOfString:@"data:image/jpg;base64," withString:@""];
            NSData *base64Date = [TKBase64Helper dataWithDecodeBase64String:baseStr];
            UIImage *image = [UIImage imageWithData:base64Date];
            
            if (image) {
                weakSelf.imageView.image = image;
            } else {
                [TKAlertHelper showAlert:@"获取的图片为空" title:@"错误提示" okBtnText:@"确定" btnHandler:nil parentViewController:self];
            }
            
        } else {
            if (result[@"error_info"]) {
                
                dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                    [TKAlertHelper showAlert:result[@"error_info"] title:@"错误提示" okBtnText:@"确定" btnHandler:nil parentViewController:self];
                });
            }
        }
    }];
}

- (void)liveDetect {
    self.imageView.image = nil;
    
    NSDictionary *param = @{
        @"actionGroup" : @"0",
        @"url": @"https://opt-dev.thinkive.com:15149/auth-common-server/servlet/json",
        @"requestHeaders": self.requestParam[@"requestHeaders"] ? self.requestParam[@"requestHeaders"] : @{},
        @"isRestFull" : @"1",
        @"isURLSign" : @"1",
        @"requestSignKey" : @"f080556a782d50087beebb0fae7aabd2",
    };

    __weak typeof(self) weakSelf = self;
    [self callPlugin:@"60059" param:param callBackFunc:^(NSMutableDictionary *result) {
            
        NSString *errorNo = result[@"error_no"];
        if ([errorNo isEqualToString:@"0"]) {
            
            NSString *baseStr = [result[@"imageBase64"] stringByReplacingOccurrencesOfString:@"data:image/jpg;base64," withString:@""];
            NSData *base64Date = [TKBase64Helper dataWithDecodeBase64String:baseStr];
            UIImage *image = [UIImage imageWithData:base64Date];
            
            if (image) {
                weakSelf.imageView.image = image;
            } else {
                [TKAlertHelper showAlert:@"获取的图片为空" title:@"错误提示" okBtnText:@"确定" btnHandler:nil parentViewController:self];
            }
            
        } else if ([errorNo isEqualToString:@"-10"]) {
            if (result[@"error_info"]) {
                
                dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                    [TKAlertHelper showAlert:result[@"error_info"] title:@"错误提示" okBtnText:@"确定" btnHandler:^(NSInteger buttonIndex) {
                        
                        [self.navigationController popToRootViewControllerAnimated:YES];
                    } parentViewController:self];
                });
            }
        } else {
            if (result[@"error_info"]) {
                
                dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                    [TKAlertHelper showAlert:result[@"error_info"] title:@"错误提示" okBtnText:@"确定" btnHandler:nil parentViewController:self];
                });
            }
        }
    }];
}


- (void)callPlugin:(NSString *)pluginNumer param:(NSDictionary *)param callBackFunc:(TKPluginCallBackFunc)callBackFunc
{
    ResultVo *vo = [[TKPluginInvokeCenter shareInstance] callPlugin:pluginNumer param:param moduleName:@"open" isH5:NO callBackFunc:^(NSMutableDictionary *result) {
        
        if (callBackFunc) callBackFunc(result);
    }];
    
    if (vo.errorNo != 0) {
        NSString *errorMsg = [TKStringHelper isNotEmpty:vo.errorInfo] ? vo.errorInfo : @"运行插件出错";
        
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            [TKAlertHelper showAlert:[NSString stringWithFormat:@"错误信息:%@", errorMsg] title:@"错误提示" okBtnText:@"确定" btnHandler:nil parentViewController:self];
        });
    }
    
    TKLogInfo(@"调用插件号%@的插件，结果为%@", pluginNumer, vo);
}

@end
