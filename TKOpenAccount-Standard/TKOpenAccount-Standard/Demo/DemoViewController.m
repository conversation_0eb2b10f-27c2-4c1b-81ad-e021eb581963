//
//  DemoViewController.m
//  TKOpenAccount-Standard
//
//  Created by <PERSON> on 2022/8/24.
//  Copyright © 2022 thinkive. All rights reserved.
//

#import "DemoViewController.h"
#import "DemoTableViewCell.h"
#import "DemoPageViewController.h"

@interface DemoViewController ()<UITableViewDelegate, UITableViewDataSource>

@property (nonatomic, strong) UITableView *tableView;
@property (nonatomic, strong) NSMutableArray *dataSoureArray;
@property (nonatomic, strong) NSMutableDictionary *requestParam;//请求参数
@end

@implementation DemoViewController
-(instancetype)initWithParam:(NSMutableDictionary *)param{
    self=[super init];
    if (self) {
        self.requestParam=param;
    }
    return self;
}
- (void)viewDidLoad {
    [super viewDidLoad];
    
    [self initData];
    [self setupUI];
}



// MARK: - setupUI
- (void)setupUI {
//    [self.navigationItem setTitle:@""];
//    [self.navigationController.navigationBar setTranslucent:YES];
    self.view.backgroundColor = UIColor.whiteColor;
    [self.view addSubview:self.tableView];
    [self.tableView reloadData];
}

- (UITableView *)tableView {
    if (_tableView == nil) {
        _tableView = [[UITableView alloc] initWithFrame:CGRectMake(0, 0, UISCREEN_WIDTH, UISCREEN_HEIGHT) style:UITableViewStyleGrouped];
        _tableView.delegate = self;
        _tableView.dataSource = self;
        _tableView.showsVerticalScrollIndicator = NO;
        _tableView.separatorStyle = UITableViewCellSeparatorStyleSingleLine;
        _tableView.contentInset = UIEdgeInsetsMake(0, 0, IPHONEX_BUTTOM_HEIGHT, 0);
        _tableView.rowHeight = 60.0f;
        [_tableView registerNib:[UINib nibWithNibName:@"DemoTableViewCell" bundle:nil] forCellReuseIdentifier:@"DemoViewController"];
        _tableView.backgroundColor = UIColor.whiteColor;
    }
    return _tableView;
}



// MARK: - init data
- (void)initData {
//    NSArray *titleArray = @[@"身份证上传", @"银行卡拍照", @"大头照拍照", @"人脸识别", @"智能见证", @"数字人见证", @"视频见证"];
    NSArray *sectionArray = @[@[@"身份证上传"],
                            @[@"银行卡拍照", @"大头照拍照"],
                            @[@"人脸识别", @"智能见证", @"数字人见证"],
                            @[@"视频见证"]];
    
    for (NSInteger i = 0; i < sectionArray.count; i ++) {
        
        NSArray *titleArray = sectionArray[i];
        NSMutableArray *tempArr = [NSMutableArray array];
        for (int j = 0; j < titleArray.count; j++) {
            NSDictionary *dict = @{
                @"title" : titleArray[j],
            };
            [tempArr addObject:dict];
        }
        
        [self.dataSoureArray addObject:tempArr];
    }
    
}

- (NSMutableArray *)dataSoureArray {
    if (_dataSoureArray == nil) {
        _dataSoureArray = [[NSMutableArray alloc] init];
    }
    return _dataSoureArray;
}


// MARK: - UITableViewDelegate,UITableViewDataSource
- (NSInteger)numberOfSectionsInTableView:(UITableView *)tableView {
    return self.dataSoureArray.count;
}

- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    if (section >= self.dataSoureArray.count) return 1;
    
    NSArray *tempArray = self.dataSoureArray[section];
    return tempArray.count ? tempArray.count : 1;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    NSString *cellIndentifier = NSStringFromClass([self class]);

    UITableViewCell *cell = [tableView dequeueReusableCellWithIdentifier:cellIndentifier];
    if (cell == nil) {
//        cell = [[UITableViewCell alloc] initWithStyle:UITableViewCellStyleSubtitle reuseIdentifier:cellIndentifier];
        cell = [[DemoTableViewCell alloc] initWithStyle:UITableViewCellStyleSubtitle reuseIdentifier:cellIndentifier];
    }

    return cell;
}

- (void)tableView:(UITableView *)tableView willDisplayCell:(DemoTableViewCell *)cell forRowAtIndexPath:(NSIndexPath *)indexPath {
    
    NSArray *tempArray = self.dataSoureArray[indexPath.section];
    NSDictionary *dict = [tempArray objectAtIndex:indexPath.row];
    [cell.titleButton setTitle:[dict objectForKey:@"title"] forState:UIControlStateNormal];
//    cell.textLabel.text = [dict objectForKey:@"title"];
//    cell.detailTextLabel.text = [dict objectForKey:@"detailTitle"];
//    cell.imageView.image = [UIImage imageNamed:[dict objectForKey:@"image"]];
}

- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath {
    [tableView deselectRowAtIndexPath:indexPath animated:YES];
    
    [self handleAlertWithRowAtIndexPath:indexPath];
}

- (void)handleAlertWithRowAtIndexPath:(NSIndexPath *)indexPath  {
    
    switch (indexPath.section) {
        case 0: // 身份证上传
            [self jumpToDemoPage:DemoPageTypeIDCard];
            break;
        case 1:
            
            switch (indexPath.row) {
                case 0: // 银行卡拍照
                    [self jumpToDemoPage:DemoPageTypeBandCard];
                    break;
                case 1: // 大头照拍照
                {
                    [self jumpToDemoPage:DemoPageTypeTakePhoto];
                }
                    break;
                default:
                    break;
            }
            break;
        case 2:
            switch (indexPath.row) {
                case 0: // 人脸识别
                {
                    [self liveDetect];
                }
                    break;
                case 1: // 智能见证
                {
                    [self smartOneWayVideoRecord];
                }
                    break;
                case 2: // 数字人见证
                {
                    [self digitalManOneWayVideoRecord];
                }
                    break;
                default:
                    break;
            }
            break;
        case 3:
            [self artificialWitness];
            break;
        default:
            break;
    }
}

//- (void)IDCardTakePhoto {
//
//    [self jumpToDemoPage:DemoPageTypeIDCard];
//}
//
//- (void)bankCardTakePhoto {
//    [self jumpToDemoPage:DemoPageTypeBandCard];
//}
//
//- (void)takePhoto {
//    [self jumpToDemoPage:DemoPageTypeTakePhoto];
//}

- (void)liveDetect {
    [self jumpToDemoPage:DemoPageTypeLiveDetect];
}

- (void)smartOneWayVideoRecord {
    NSDictionary *param = @{
        @"questionJson" : @"[{\"tip_content\":\"尊敬的客户，请您在“滴”声后大声使用“是”或“否”回答以下问题。\"},{\"tip_content\":\"您是否为伍文峰本人，且已知晓证券市场风险，已阅读并充分理解网上开户协议条款，自愿在思迪证券开户？\",\"standardans\":\"^(是的|是|对|对的|4)\",\"prompt\":\"是的\\/不是\",\"failans\":\"^(\\\\S|\\\\s)*(不是的|不是)+(\\\\S|\\\\s)*\",\"wait_time\":\"5\"}]",
        @"url": @"https://opt-dev.thinkive.com:15149/auth-common-server/servlet/json",
        @"requestHeaders": self.requestParam[@"requestHeaders"] ? self.requestParam[@"requestHeaders"] : @{},
        @"uploadTipString": @"请您确认：我是***，已知晓证券市场风险，已阅读并充分理解网上开户协议条款，自愿选择在思迪证券开立账户，并承诺所提供的信息及证件真实、合法、有效。",
        @"isRestFull" : @"1",
        @"isURLSign" : @"1",
        @"requestSignKey" : @"f080556a782d50087beebb0fae7aabd2",
    };
    
    __weak typeof(self) weakSelf = self;
    [self callPlugin:@"60057" param:param callBackFunc:^(NSMutableDictionary *result) {
        NSString *errorNo = [result getStringWithKey:@"error_no"];
        NSString *errorInfo = [result getStringWithKey:@"error_info"];
        NSString *filePath = [result getStringWithKey:@"filePath"];
        
        if ([errorNo isEqualToString:@"0"]) {
            [TKAlertHelper showAlert:[NSString stringWithFormat:@"视频录制完成:%@",filePath] title:@"结果" okBtnText:@"确定" btnHandler:^(NSInteger buttonIndex) {
                
            } parentViewController:weakSelf];
        } else if ([errorNo isEqualToString:@"-10"]) {
            if (result[@"error_info"]) {
                
                dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                    [TKAlertHelper showAlert:result[@"error_info"] title:@"错误提示" okBtnText:@"确定" btnHandler:^(NSInteger buttonIndex) {
                        
                        [self.navigationController popToRootViewControllerAnimated:YES];
                    } parentViewController:self];
                });
            }
        } else {
            if (result[@"error_info"]) {
                
                dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                    [TKAlertHelper showAlert:[NSString stringWithFormat:@"错误号:%@\n错误信息:%@", errorNo, errorInfo] title:@"错误提示" okBtnText:@"确定" btnHandler:nil parentViewController:self];
                });
            }
        }
    }];
}

- (void)digitalManOneWayVideoRecord {
    NSDictionary *param = @{
        @"cscResultType": @(0),
        @"disableFaceDetect": @"1",
        @"disableFaceCompare": @"1",
        @"startRecordTimeOut": @(10),
        @"beforeVideoArray": @"[{\"fileName\":\"1.mp4\",\"tipTitle\":\"<font style=\\\"font-size:20px;\\\" color=\\\"#ff5900\\\">思先生您好，欢迎来到中信建投证券开户。</font>\",\"fileSource\":\"2\",\"tipContent\":\"思先生您好，欢迎来到中信建投证券开户。\"},{\"fileName\":\"2.mp4\",\"tipTitle\":\"<font style=\\\"font-size:20px;\\\" color=\\\"#ff5900\\\">请您尽量选择周边环境安静的场所进行录制。视频过程中，请您对准手机屏幕，保持全脸在人像框内，确保其他人不出现在视频中。</font>\",\"fileSource\":\"1\",\"tipContent\":\"请您尽量选择周边环境安静的场所进行录制。视频过程中，请您对准手机屏幕，保持全脸在人像框内，确保其他人不出现在视频中。\"},{\"fileName\":\"3.mp4\",\"tipTitle\":\"<font style=\\\"font-size:20px;\\\" color=\\\"#FFFFFF\\\">如您已准备好，请点击下方</font><font style=\\\"font-size:20px;\\\" color=\\\"#ff5900\\\">“开始录制”</font><font style=\\\"font-size:20px;\\\" color=\\\"#FFFFFF\\\">按钮录制视频。我将问您两个问题，请您根据实际情况进行回答。</font>\",\"fileSource\":\"1\",\"tipContent\":\"如您已准备好，请点击下方“开始录制”按钮录制视频。我将问您两个问题，请您根据实际情况进行回答。\"}]",
          @"questionArray": @"[{\"fileSource\":\"2\",\"tipContent\":\"第一个问题，请问您是否是思小迪本人在中信建投证券申请开户？请用“是的”或“不是”回答。\",\"fileName\":\"5.mp4\",\"waitTime\":\"10\",\"prompt\":\"<font style=\\\"font-size:20px;\\\" color=\\\"#FFFFFF\\\">请用</font><font style=\\\"font-size:20px;\\\" color=\\\"#ff5900\\\">“是的”</font><font style=\\\"font-size:20px;\\\" color=\\\"#FFFFFF\\\">或</font><font style=\\\"font-size:20px;\\\" color=\\\"#ff5900\\\">“不是”</font><font style=\\\"font-size:20px;\\\" color=\\\"#FFFFFF\\\">回答。</font>\",\"failans\":\"^(不是的|不是)$\",\"standardans\":\"^(是的|是|对的)$\",\"tipTitle\":\"<font style=\\\"font-size:20px;\\\" color=\\\"#FFFFFF\\\">1.请问您是否是思小迪本人在中信建投证券申请开户？</font>\",\"errorTip\":{\"fileName\":\"8.mp4\",\"tipTitle\":\"<font style=\\\"font-size:20px;\\\" color=\\\"#FFFFFF\\\">您好，办理开户业务需本人申请，请客户本人进行视频录制。本次视频结束。</font>\",\"fileSource\":\"1\",\"tipContent\":\"您好，办理开户业务需本人申请，请客户本人进行视频录制。本次视频结束。\"},\"noVoiceTip\":{\"fileName\":\"9.mp4\",\"tipTitle\":\"<font style=\\\"font-size:20px;\\\" color=\\\"#FFFFFF\\\">您好，我没有听清您的回答。请用</font><font style=\\\"font-size:20px;\\\" color=\\\"#ff5900\\\">“是的”</font><font style=\\\"font-size:20px;\\\" color=\\\"#FFFFFF\\\">或</font><font style=\\\"font-size:20px;\\\" color=\\\"#ff5900\\\">“不是”</font><font style=\\\"font-size:20px;\\\" color=\\\"#FFFFFF\\\">回答。</font>\",\"fileSource\":\"1\",\"tipContent\":\"您好，我没有听清您的回答。请用“是的”或“不是”回答。\"}},{\"fileSource\":\"1\",\"tipContent\":\"好的。第二个问题，请问您是否已知晓证券市场风险，已阅读并充分理解开户协议条款？请用“理解”或“不理解”回答。\",\"fileName\":\"6.mp4\",\"waitTime\":\"10\",\"prompt\":\"<font style=\\\"font-size:20px;\\\" color=\\\"#FFFFFF\\\">请用</font><font style=\\\"font-size:20px;\\\" color=\\\"#ff5900\\\">“理解”</font><font style=\\\"font-size:20px;\\\" color=\\\"#FFFFFF\\\">或</font><font style=\\\"font-size:20px;\\\" color=\\\"#ff5900\\\">“不理解”</font><font style=\\\"font-size:20px;\\\" color=\\\"#FFFFFF\\\">回答。</font>\",\"failans\":\"^(不是的|不是|不理解)$\",\"standardans\":\"^(是的|是|对的|理解)$\",\"tipTitle\":\"<font style=\\\"font-size:20px;\\\" color=\\\"#FFFFFF\\\">2.请问您是否已知晓证券市场风险，已阅读并充分理解开户协议条款？</font>\",\"errorTip\":{\"fileName\":\"10.mp4\",\"tipTitle\":\"<font style=\\\"font-size:20px;\\\" color=\\\"#FFFFFF\\\">您好，请您确认已知晓证券市场风险及已阅读且充分理解网上开户协议条款后进行开户。本次视频结束。</font>\",\"fileSource\":\"1\",\"tipContent\":\"您好，请您确认已知晓证券市场风险及已阅读且充分理解网上开户协议条款后进行开户。本次视频结束。\"},\"noVoiceTip\":{\"fileName\":\"11.mp4\",\"tipTitle\":\"<font style=\\\"font-size:20px;\\\" color=\\\"#FFFFFF\\\">您好，我没有听清您的回答。请用</font><font style=\\\"font-size:20px;\\\" color=\\\"#ff5900\\\">“理解”</font><font style=\\\"font-size:20px;\\\" color=\\\"#FFFFFF\\\">或</font><font style=\\\"font-size:20px;\\\" color=\\\"#ff5900\\\">“不理解”</font><font style=\\\"font-size:20px;\\\" color=\\\"#FFFFFF\\\">回答。</font>\",\"fileSource\":\"1\",\"tipContent\":\"您好，我没有听清您的回答。请用“理解”或“不理解”回答。\"}}]",
          @"afterVideoArray": @"[{\"fileName\":\"7.mp4\",\"tipTitle\":\"<font style=\\\"font-size:20px;\\\" color=\\\"#FFFFFF\\\">好的，视频录制完毕，感谢您的配合。</font>\",\"fileSource\":\"1\",\"tipContent\":\"好的，视频录制完毕，感谢您的配合。\"}]",
          @"errorTipJson": @"{\"overNoVoiceCountTip\":{\"fileName\":\"12.mp4\",\"tipTitle\":\"<font style=\\\"font-size:20px;\\\" color=\\\"#FFFFFF\\\">您好，我没有听清您的回答，请您选择周边安静的场所，重新申请视频。为了尽快帮您录制完成，我也帮您准备了人工视频方式，您可以点击“返回”按钮后进行选择。</font>\",\"fileSource\":\"1\",\"tipContent\":\"您好，我没有听清您的回答，请您选择周边安静的场所，重新申请视频。为了尽快帮您录制完成，我也帮您准备了人工视频方式，您可以点击“返回”按钮后进行选择。\"},\"overPresonCountTip\":{\"fileName\":\"13.mp4\",\"tipTitle\":\"<font style=\\\"font-size:20px;\\\" color=\\\"#FFFFFF\\\">您好，开户视频需由您本人独自完成录制，请确保视频中其他人不出现在镜头中。请确认好后重新发起录制！本次视频结束，一会儿见。</font>\",\"fileSource\":\"1\",\"tipContent\":\"您好，开户视频需由您本人独自完成录制，请确保视频中其他人不出现在镜头中。请确认好后重新发起录制！本次视频结束，一会儿见。\"},\"overCompareCountTip\":{\"fileName\":\"15.mp4\",\"tipTitle\":\"<font style=\\\"font-size:20px;\\\" color=\\\"#FFFFFF\\\">您好，本次视频头像比对未通过，请您不要遮挡面部、摘掉眼镜或口罩。请确认好后重新发起录制！本次视频结束，一会儿见。</font>\",\"fileSource\":\"1\",\"tipContent\":\"您好，本次视频头像比对未通过，请您不要遮挡面部、摘掉眼镜或口罩。请确认好后重新发起录制！本次视频结束，一会儿见。\"},\"overScreenCountTip\":{\"fileName\":\"14.mp4\",\"tipTitle\":\"<font style=\\\"font-size:20px;\\\" color=\\\"#FFFFFF\\\">您好，本次视频中您多次离开镜头，请您确保视频过程中面部始终保持在镜头内。请确认好后重新发起录制！本次视频结束，一会儿见。</font>\",\"fileSource\":\"1\",\"tipContent\":\"您好，本次视频中您多次离开镜头，请您确保视频过程中面部始终保持在镜头内。请确认好后重新发起录制！本次视频结束，一会儿见。\"}}",
          @"faceDetectCompositeFuncNo": @"15000059",
          @"url": @"https://opt-dev.thinkive.com:15149/auth-common-server/servlet/json",
        @"requestHeaders": self.requestParam[@"requestHeaders"] ? self.requestParam[@"requestHeaders"] : @{},
        @"isRestFull" : @"1",
        @"isURLSign" : @"1",
        @"requestSignKey" : @"f080556a782d50087beebb0fae7aabd2",
          @"funcNo": @"60072",
    };
    
    __weak typeof(self) weakSelf = self;
    [self callPlugin:@"60072" param:param callBackFunc:^(NSMutableDictionary *result) {
            
        NSString *errorNo = [result getStringWithKey:@"error_no"];
        NSString *errorInfo = [result getStringWithKey:@"error_info"];
        NSString *filePath = [result getStringWithKey:@"filePath"];
        
        if ([errorNo isEqualToString:@"0"]) {
            [TKAlertHelper showAlert:[NSString stringWithFormat:@"视频录制完成:%@",filePath] title:@"结果" okBtnText:@"确定" btnHandler:^(NSInteger buttonIndex) {
                
            } parentViewController:weakSelf];
        } else if ([errorNo isEqualToString:@"-10"]) {
            if (result[@"error_info"]) {
                
                dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                    [TKAlertHelper showAlert:result[@"error_info"] title:@"错误提示" okBtnText:@"确定" btnHandler:^(NSInteger buttonIndex) {
                        
                        [self.navigationController popToRootViewControllerAnimated:YES];
                    } parentViewController:self];
                });
            }
        } else {
            if (result[@"error_info"]) {
                
                dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                    [TKAlertHelper showAlert:[NSString stringWithFormat:@"错误号:%@\n错误信息:%@", errorNo, errorInfo] title:@"错误提示" okBtnText:@"确定" btnHandler:nil parentViewController:self];
                });
            }
        }
    }];
}

- (void)artificialWitness {
    // 让传入的参数
    NSMutableDictionary *param =[[NSMutableDictionary alloc] init];
    param[@"funcNo"]=@"60005";//功能号
    param[@"userId"]= [self.requestParam getStringWithKey:@"userId"];//用户ID
    param[@"userName"]=@"思小迪";//用户姓名
    param[@"orgId"]=@"2925";//营业部编号
//    param[@"mainColor"]=@"#FD671A";//主色调16进制值，不传默认蓝色
    param[@"netWorkStatus"]=@"WIFI";
    param[@"url"]=@"https://opt-dev.thinkive.com:15149/wa-queue-server/servlet/json";//连接排队的服务器地址
    param[@"isRejectToH5"]=@"1";//是否将内容返回:默认是0，1:将见证坐席内容全返給h5（4.0用）
    param[@"isNewView"]=@"0";//是否走新版视频界面：0：走新版界面，其他都走老板版界面
    param[@"isShowHeadRect"]=@"0";//是否走新版视频界面：0：走新版界面，其他都走老板版界面
    param[@"version"]=@"4.0";//走3.0还是4.0排队
    param[@"videoType"]=@"0";//视频类型:0:TChat视频，1:AnyChat视频（默认）
    param[@"biz_type"]=@"010001";
    param[@"origin"]=@"2";
//    param[@"isShowTruePosition"]=@"0";//4.0排队是否显示真实位置；因为默认排队只位置显示只减不增，被插队无法看到，需要客户知道被插队传：1：被插队也显真实位置，默认排队位置只减少不增加
    param[@"requestHeaders"]=self.requestParam[@"requestHeaders"];//微服务请求头（键值对对象，目前只支持4.0新界面）；微服务可能修改请求头
    [self callPlugin:@"60005" param:param callBackFunc:^(NSMutableDictionary *result) {
        NSString *errorNo = [result getStringWithKey:@"error_no"];
        NSString *errorInfo = [result getStringWithKey:@"error_info"];
        NSString *message = [result getStringWithKey:@"message"];
        
        [TKAlertHelper showAlert:[NSString stringWithFormat:@"错误号:%@\n错误信息:%@\n结果信息:%@", errorNo, errorInfo, message] title:@"结果" okBtnText:@"确定" btnHandler:^(NSInteger buttonIndex) {
            
        } parentViewController:self];
    }];
}

- (void)callPlugin:(NSString *)pluginNumer param:(NSDictionary *)param callBackFunc:(TKPluginCallBackFunc)callBackFunc
{
    ResultVo *vo = [[TKPluginInvokeCenter shareInstance] callPlugin:pluginNumer param:param moduleName:@"open" isH5:NO callBackFunc:^(NSMutableDictionary *result) {
        
        if (callBackFunc) callBackFunc(result);
    }];
    
    if (vo.errorNo != 0) {
        NSString *errorMsg = [TKStringHelper isNotEmpty:vo.errorInfo] ? vo.errorInfo : @"运行插件出错";
        
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            [TKAlertHelper showAlert:[NSString stringWithFormat:@"错误信息:%@", errorMsg] title:@"错误提示" okBtnText:@"确定" btnHandler:nil parentViewController:self];
        });
    }
    
    TKLogInfo(@"调用插件号%@的插件，结果为%@", pluginNumer, vo);
}

- (void)jumpToDemoPage:(DemoPageType)type
{
    DemoPageViewController *page = [[DemoPageViewController alloc] initWithParam:self.requestParam type:type];
    [self.navigationController pushViewController:page animated:YES];
}

@end
