//
//  TKOpenLoginViewController.m
//  TKOpenAccount-Standard
//
//  Created by <PERSON>ie on 2022/8/16.
//  Copyright © 2022 thinkive. All rights reserved.
//

#import "TKOpenLoginViewController.h"
#import "DemoViewController.h"
#import "TKTempService.h"

@interface TKOpenLoginViewController ()<UITextFieldDelegate>
@property (nonatomic, strong) TKNavBar *navBar;//导航头
@property (nonatomic, strong) UILabel *tipLabel;//头部提示语
@property (nonatomic, strong) UITextField *phoneNumberText;//手机输入框
@property (nonatomic, strong) UITextField *authCodeText;//验证码输入框
@property (nonatomic, strong) UIButton *loginBtn;//登陆按钮
@property (nonatomic, strong) TKLayerView *layerView;//toast提示
@property (nonatomic, strong) TKTempService *service;//网络请求工具类
@property (nonatomic, strong) NSMutableDictionary *requestParms;
@end

@implementation TKOpenLoginViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    self.view.backgroundColor=[UIColor whiteColor];
    self.service=[[TKTempService alloc] init];
    self.requestParms=[[NSMutableDictionary alloc] init];
    [self.view addSubview:self.navBar];
    [self.view addSubview:self.tipLabel];
    [self.view addSubview:self.phoneNumberText];
    [self.view addSubview:self.authCodeText];
    [self.view addSubview:self.loginBtn];
    
//    self.phoneNumberText.text = @"13112312360";
//    self.phoneNumberText.text = @"18307461111";
//    self.phoneNumberText.text = @"13750088002";
//    self.phoneNumberText.text = @"13661546773";
//    self.phoneNumberText.text = @"18811882707";
    self.phoneNumberText.text = @"18777777777";

}

-(void)viewDidAppear:(BOOL)animated{
    [super viewDidAppear:animated];
////    // 让传入的参数
//    NSMutableDictionary *param =[[NSMutableDictionary alloc] init];
//    param[@"funcNo"]=@"60007";//功能号
//    param[@"actionGroup"]=@"0,2";//0:眨眼 1:张嘴 2:点头 3:摇头 4:正脸  5:抬头 6:向左摇头 7:向右摇头;  0,1,2 这种表述多个动作组合；商汤：支持0、1、2、3
//    param[@"mainColor"]=@"#FD671A";//主色调16进制值，不传默认蓝色
//        
//    NSString *pluginNo = [param getStringWithKey:@"funcNo"];    // 取出插件号
//    if ([TKStringHelper isEmpty:pluginNo]) {
//        // 插件号不能为空
//        // 回调报错信息给h5
//    }
//    NSString *moduleName = [param getStringWithKey:@"moduleName"];
//    //插件调用
//    [[TKPluginInvokeCenter shareInstance] callPlugin:pluginNo param:param moduleName:[TKStringHelper isNotEmpty:moduleName] ? moduleName : @"open" callBackFunc:^(NSMutableDictionary *result) {
//        // 回调给原生的结果，可以直接回调给h5
//        TKLogDebug(@"result = %@", result);
//    }];
}


//结束触摸收起键盘
-(void)touchesEnded:(NSSet<UITouch *> *)touches withEvent:(UIEvent *)event{
  [self.view endEditing:YES];//收回键盘
}

#pragma mark UITextFieldDelegate
-(BOOL)textField:(UITextField *)textField shouldChangeCharactersInRange:(NSRange)range replacementString:(NSString *)string{
    //限制手机号输入长度
    if (textField==self.phoneNumberText) {
        if (range.location<11) {
            return YES;
        }else{
            return NO;
        }
    }
    return YES;
}

#pragma mark 事件方法

/**
 <AUTHOR> 2019年08月23日14:48:58
 @发送验证码事件
 */
-(void)authCodeAction{
//    [TKOpenViewStyleHelper shareInstance].isElder=YES;

    if (self.phoneNumberText.text.length<11) {
        [self.layerView showTip:@"手机号不得少于11位" position:TKLayerPosition_Center];
    }else{
        [self.view endEditing:YES];//收回键盘
        [self.layerView showLoading];

        NSMutableDictionary *param=[[NSMutableDictionary alloc] init];
        param[@"funcNo"]=@"login/sendSMS";
        param[@"flowType"]=@"TK-002";
        param[@"totalTime"]=@"10";
        param[@"opSource"]=@"2";
        param[@"bizType"]=@"010001";
        param[@"mobileNo"]=self.phoneNumberText.text;
        param[@"captcha"]=@"";
        param[@"captchaToken"]=@"";
        param[@"isRestFull"]=@"1";
        param[@"isURLSign"]=@"1";

        NSString *url=[NSString stringWithFormat:@"https://opt-dev.thinkive.com:15149/kh-stkkh-server/login/sendSMS"];
        
        __weak typeof(self) weakSelf = self;
        [self.service handleNetworkWithURL:url param:param callBackFunc:^(ResultVo *resultVo) {
            [self.layerView hideLoading];
//            NSArray *results = (NSArray *)resultVo.results;
            
            if (resultVo.errorNo == 0)
            {
//                weakSelf.requestParms[@"mobileCode"]=@"888888";//取验证码
                weakSelf.authCodeText.text = @"888888";
            }else{
                [weakSelf.layerView showTip:[NSString stringWithFormat:@"获取验证码失败(%@)", resultVo.errorInfo] position:TKLayerPosition_Center];
            }
        }];
    }
}

/**
 <AUTHOR> 2019年08月23日14:48:58
 @登陆事件
 */
-(void)loginAction{
    if (self.phoneNumberText.text.length<11) {
        [self.layerView showTip:@"手机号不得少于11位" position:TKLayerPosition_Center];
    }else{
        
        //验证短信验证码
        [self checkSMSCode];
    }
}

//验证短信验证码
-(void)checkSMSCode{
    NSMutableDictionary *checkCodeParam=[[NSMutableDictionary alloc] init];
    checkCodeParam[@"funcNo"]=@"login/validator";
    checkCodeParam[@"flowType"]=@"TK-002";
    checkCodeParam[@"totalTime"]=@"10";
    checkCodeParam[@"opSource"]=@"2";
    checkCodeParam[@"bizType"]=@"010001";
    checkCodeParam[@"mobileNo"]=self.phoneNumberText.text;
    checkCodeParam[@"mobileCode"]=self.authCodeText.text;
    checkCodeParam[@"authType"]=@"0";
    checkCodeParam[@"isRestFull"]=@"1";
    checkCodeParam[@"isURLSign"]=@"1";
    NSMutableDictionary *operateDeviceParam=[[NSMutableDictionary alloc] init];
    operateDeviceParam[@"operateDevice"]=[TKDeviceHelper getDevicePlatformInfo];
    operateDeviceParam[@"devicesysversion"]=[TKDeviceHelper getDeviceSysVersion];
    operateDeviceParam[@"deviceresoluation"]=[TKDeviceHelper getDeviceResoluationDescription];
    operateDeviceParam[@"sorftversion"]=[TKSystemHelper getAppVersion];
    operateDeviceParam[@"sorftversionsn"]=[TKSystemHelper getAppVersionSn];
    operateDeviceParam[@"sorftname"]=[TKSystemHelper getAppName];

    operateDeviceParam[@"devidebaseplatform"]=@"";

    operateDeviceParam[@"deviceinnerversion"]=@"";
    operateDeviceParam[@"deviceMEID"]=[TKDeviceHelper getDeviceUUID];
    operateDeviceParam[@"deviceIMEI"]=[TKDeviceHelper getDeviceUUID];
    operateDeviceParam[@"deviceIMSI"]=[TKNetHelper getPhoneIMSI];
    
    operateDeviceParam[@"deviceToken"]=@"";
    checkCodeParam[@"operateDevice"]=[TKDataHelper dictionaryToJson:operateDeviceParam];


    
    NSString *url=[NSString stringWithFormat:@"https://opt-dev.thinkive.com:15149/kh-stkkh-server/login/validator"];
    
    __weak typeof(self) weakSelf = self;
    [self.service handleNetworkWithURL:url param:checkCodeParam callBackFunc:^(ResultVo *resultVo) {
        
        NSArray *results = (NSArray *)resultVo.results;
        
        if (resultVo.errorNo == 0)
        {
            NSDictionary *dic =   (NSDictionary*)[results objectAtIndex:0];
            weakSelf.requestParms[@"userId"] = dic[@"userId"];
            [weakSelf loginOpenSystem];
        }else{
            [weakSelf.layerView hideLoading];
            [weakSelf.layerView showTip:[NSString stringWithFormat:@"校验验证码失败(%@)", resultVo.errorInfo] position:TKLayerPosition_Center];
        }
    }];
}

//登陆开户系统
-(void)loginOpenSystem{
    NSMutableDictionary *param=[[NSMutableDictionary alloc] init];
    param[@"funcNo"]=@"check/loginAcc";
    param[@"flowType"]=@"TK-002";
    param[@"totalTime"]=@"10";
    param[@"opSource"]=@"2";
    param[@"bizType"]=@"010001";
    param[@"mobileNo"]=self.phoneNumberText.text;
    param[@"userId"]=self.requestParms[@"userId"];
    param[@"isRestFull"]=@"1";
    param[@"isURLSign"]=@"1";


    NSString *url=[NSString stringWithFormat:@"https://opt-dev.thinkive.com:15149/kh-stkkh-server/check/loginAcc"];
    
    __weak typeof(self) weakSelf = self;
    [self.service handleNetworkWithURL:url param:param callBackFunc:^(ResultVo *resultVo) {
        [weakSelf.layerView hideLoading];
//        NSArray *results = (NSArray *)resultVo.results;
        
        if (resultVo.errorNo == 0)
        {
            [weakSelf registerToken];
        }else{
            [weakSelf.layerView showTip:[NSString stringWithFormat:@"登陆开户系统失败(%@)", resultVo.errorInfo] position:TKLayerPosition_Center];
        }
    }];
}
//注册jwt_token
-(void)registerToken{
    NSMutableDictionary *param=[[NSMutableDictionary alloc] init];
    param[@"funcNo"]=@"witness/two/register";
    param[@"flowType"]=@"TK-002";
    param[@"totalTime"]=@"10";
    param[@"opSource"]=@"2";
    param[@"bizType"]=@"010001";
    param[@"userId"]=self.requestParms[@"userId"];
    param[@"isRestFull"]=@"1";
    param[@"isURLSign"]=@"1";
    

    NSString *url=[NSString stringWithFormat:@"https://opt-dev.thinkive.com:15149/kh-stkkh-server/witness/two/register"];
    
    __weak typeof(self) weakSelf = self;
    [self.service handleNetworkWithURL:url param:param callBackFunc:^(ResultVo *resultVo) {
        [weakSelf.layerView hideLoading];
        NSArray *results = (NSArray *)resultVo.results;
        
        if (resultVo.errorNo == 0)
        {
            NSDictionary *dic =   (NSDictionary*)[results objectAtIndex:0];
            if (dic[@"token"]) {
                
                weakSelf.requestParms[@"userId"] = dic[@"token"];
            }
            //登陆成功场景
//            weakSelf.requestParms[@"url"]=@"https://gfo-fts-test.ciccwm.com/wa-queue-server-web/servlet/json";//连接排队的服务器地址
            if (dic[@"jwtToken"]) {
                weakSelf.requestParms[@"requestHeaders"]=@{@"tk-jwt-authorization":dic[@"jwtToken"]};//微服务请求头（键值对对象，目前只支持4.0新界面）；微服务可能修改请求头
            }
            
            DemoViewController *vc = [[DemoViewController alloc] initWithParam:weakSelf.requestParms];
            vc.modalPresentationStyle=UIModalPresentationFullScreen;
            [weakSelf.navigationController pushViewController:vc animated:YES];
        }else{
            [weakSelf.layerView showTip:[NSString stringWithFormat:@"获取token失败(%@)", resultVo.errorInfo] position:TKLayerPosition_Center];
        }
    }];
}
/**
 <AUTHOR> 2019年08月23日14:48:58
 @取消事件
 */
-(void)backAction{
    exit(1);
}

#pragma mark lazyloading
/**
 *  <AUTHOR> 2022年08月16日09:40:41
 *  @return 原生导航头
 */
-(TKNavBar *)navBar{
    if (!_navBar) {
        _navBar=[[TKNavBar alloc] initWithFrame:CGRectMake(0, STATUSBAR_HEIGHT, UISCREEN_WIDTH, NAVBAR_HEIGHT)];
        _navBar.barTintColor=[TKUIHelper colorWithHexString:@"#FFFFFF"];
        [_navBar setTitleTextAttributes:@{NSFontAttributeName:[UIFont fontWithName:@"PingFangSC-Regular" size:18.0f],NSForegroundColorAttributeName:[UIColor blackColor]}];
        //创建导航栏内容
        UINavigationItem *navigationItem = [[UINavigationItem alloc]init];
        navigationItem.title=@"验证手机号";
        [_navBar pushNavigationItem:navigationItem animated:NO];
               
        //设置返回按钮
        [navigationItem setTKBackItemWithTarget:self action:@selector(backAction) image:@"tk_nav_back"];
    }
    return _navBar;
}


/**
 *  <AUTHOR> 2022年08月16日09:40:41
 *  @return 头部提示语
 */
-(UILabel *)tipLabel{
    if (!_tipLabel) {
        float height=40;
        _tipLabel=[[UILabel alloc] initWithFrame:CGRectMake(0, self.navBar.TKBottom, self.view.TKWidth, height)];
        _tipLabel.backgroundColor=[TKUIHelper colorWithHexString:@"#F3F3F3"];
        _tipLabel.textColor=[TKUIHelper colorWithHexString:@"#333333"];
        _tipLabel.text=@"    请填写本人手机号";
    }
    return _tipLabel;
}


/**
 *  <AUTHOR> 2022年08月16日10:55:19
 *  @return 手机输入框
 */
-(UITextField *)phoneNumberText{
    if (!_phoneNumberText) {
        float x=15;
        float width=self.view.TKWidth-2*x;
        _phoneNumberText=[[UITextField alloc] initWithFrame:CGRectMake(x, self.tipLabel.TKBottom, width, 60)];
        _phoneNumberText.keyboardType=UIKeyboardTypeNumberPad;
        [_phoneNumberText setBackgroundColor:[UIColor whiteColor]];
        [_phoneNumberText setPlaceholder:@"请输入手机号"];
        UILabel *label=[[UILabel alloc] init];
        label.text=@"手机号码    ";
        label.textColor=[TKUIHelper colorWithHexString:@"#000000"];
        label.backgroundColor=[UIColor clearColor];
        _phoneNumberText.leftView=label;
        _phoneNumberText.leftViewMode=UITextFieldViewModeAlways;
        _phoneNumberText.delegate=self;
    }
    return _phoneNumberText;
}



/**
 *  <AUTHOR> 2022年08月16日10:55:19
 *  @return 验证码输入框
 */
-(UITextField *)authCodeText{
    if (!_authCodeText) {
        float x=15;
        float width=self.view.TKWidth-2*x;
        _authCodeText=[[UITextField alloc] initWithFrame:CGRectMake(x, self.phoneNumberText.TKBottom, width, 60)];
        _authCodeText.keyboardType=UIKeyboardTypeNumberPad;
        [_authCodeText setBackgroundColor:[UIColor whiteColor]];
        [_authCodeText setPlaceholder:@"请输入验证码"];
        UILabel *label=[[UILabel alloc] init];
        label.text=@"验证码        ";
        label.textColor=[TKUIHelper colorWithHexString:@"#000000"];
        label.backgroundColor=[UIColor clearColor];
        _authCodeText.leftView=label;
        _authCodeText.leftViewMode=UITextFieldViewModeAlways;
        
        UIButton *btn=[[UIButton alloc] init];
        [btn setTitle:@"获取验证码" forState:UIControlStateNormal];
        [btn setTitleColor:[TKUIHelper colorWithHexString:@"##387CFF"] forState:UIControlStateNormal];
        btn.titleLabel.font=[UIFont systemFontOfSize:14.0f];
        btn.backgroundColor=[UIColor clearColor];
        [btn addTarget:self action:@selector(authCodeAction) forControlEvents:UIControlEventTouchUpInside];
        _authCodeText.rightView=btn;
        _authCodeText.rightViewMode=UITextFieldViewModeAlways;
    }
    return _authCodeText;
}


/**
*  <AUTHOR> 2022年08月16日15:21:02
*  @return 登陆按钮
*/
-(UIButton *)loginBtn{
    if (!_loginBtn) {
        float loginBtnX=16;
        float loginBtnWidth=self.view.TKWidth-2*loginBtnX;
        float loginBtnHeight=44;
        float loginBtnY=self.authCodeText.TKBottom+30;
        _loginBtn=[[UIButton alloc] initWithFrame:CGRectMake(loginBtnX, loginBtnY, loginBtnWidth, loginBtnHeight)];
        
        NSMutableArray *btoColors = [NSMutableArray arrayWithObjects:(id)[TKUIHelper colorWithHexString:@"#1061FF" alpha:1.0f].CGColor,(id)[TKUIHelper colorWithHexString:@"#1061FF" alpha:0.6f].CGColor, nil];
        CAGradientLayer *btoGradientLayer = [CAGradientLayer layer];
        btoGradientLayer.frame = CGRectMake(0, 0, loginBtnWidth, loginBtnHeight);
        btoGradientLayer.startPoint = CGPointMake(0, 0.5);
        btoGradientLayer.endPoint = CGPointMake(1, 0.5);
        [btoGradientLayer setColors:[NSArray arrayWithArray:btoColors]];
        btoGradientLayer.cornerRadius=loginBtnHeight/2.0f;
        [_loginBtn.layer addSublayer:btoGradientLayer]; //设置颜色渐变
        

        [_loginBtn setTitle:@"立即登陆" forState:UIControlStateNormal];
        _loginBtn.titleLabel.font = [UIFont fontWithName:@"PingFang SC" size:16];
        
        [_loginBtn setTitleColor:[TKUIHelper colorWithHexString:@"#FFFFFF"] forState:UIControlStateNormal];
        _loginBtn.layer.cornerRadius=loginBtnHeight/2.0f;
        
        [_loginBtn addTarget:self action:@selector(loginAction) forControlEvents:UIControlEventTouchUpInside];
    }
    return _loginBtn;
}

/**
 <AUTHOR> 2022年08月16日15:41:35
 @return 提示layer
 */
-(TKLayerView *)layerView{
    if (!_layerView) {
        _layerView=[[TKLayerView alloc] initContentView:self.view withBtnTextColor:@"#ffffff"];
    }
    return _layerView;
}

@end
