<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="20037" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_1" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="20020"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="System colors in document resources" minToolsVersion="11.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <view contentMode="scaleToFill" restorationIdentifier="DemoViewController" id="iN0-l3-epB" customClass="DemoTableViewCell">
            <rect key="frame" x="0.0" y="0.0" width="376" height="88"/>
            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
            <subviews>
                <button opaque="NO" userInteractionEnabled="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="NGA-7Q-Rbl">
                    <rect key="frame" x="60" y="21.5" width="256" height="45"/>
                    <color key="backgroundColor" red="0.20392156859999999" green="0.62745098040000002" blue="0.98431372549999996" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                    <constraints>
                        <constraint firstAttribute="height" constant="45" id="cLa-Ga-3o6"/>
                    </constraints>
                    <state key="normal" title="Button"/>
                    <buttonConfiguration key="configuration" style="plain" title="Button">
                        <fontDescription key="titleFontDescription" type="system" pointSize="18"/>
                        <color key="baseForegroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                    </buttonConfiguration>
                </button>
            </subviews>
            <viewLayoutGuide key="safeArea" id="vUN-kp-3ea"/>
            <color key="backgroundColor" systemColor="systemGray4Color"/>
            <constraints>
                <constraint firstItem="NGA-7Q-Rbl" firstAttribute="centerX" secondItem="iN0-l3-epB" secondAttribute="centerX" id="T1c-7f-8T1"/>
                <constraint firstItem="NGA-7Q-Rbl" firstAttribute="centerY" secondItem="iN0-l3-epB" secondAttribute="centerY" id="Y0K-DP-nbB"/>
                <constraint firstItem="NGA-7Q-Rbl" firstAttribute="leading" secondItem="vUN-kp-3ea" secondAttribute="leading" constant="60" id="kiM-WZ-0hL"/>
                <constraint firstItem="vUN-kp-3ea" firstAttribute="trailing" secondItem="NGA-7Q-Rbl" secondAttribute="trailing" constant="60" id="mR5-wR-eGc"/>
            </constraints>
            <freeformSimulatedSizeMetrics key="simulatedDestinationMetrics"/>
            <connections>
                <outlet property="titleButton" destination="NGA-7Q-Rbl" id="iwM-DC-4fr"/>
            </connections>
            <point key="canvasLocation" x="104.34782608695653" y="118.52678571428571"/>
        </view>
    </objects>
    <resources>
        <systemColor name="systemGray4Color">
            <color red="0.81960784313725488" green="0.81960784313725488" blue="0.83921568627450982" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </systemColor>
    </resources>
</document>
