//
//  DemoTableViewCell.m
//  TKOpenAccount-Standard
//
//  Created by <PERSON> on 2022/8/26.
//  Copyright © 2022 thinkive. All rights reserved.
//

#import "DemoTableViewCell.h"

@implementation DemoTableViewCell

- (void)awakeFromNib {
    [super awakeFromNib];
    
    NSMutableArray *btoColors = [NSMutableArray arrayWithObjects:(id)[TKUIHelper colorWithHexString:@"#1061FF" alpha:1.0f].CGColor,(id)[TKUIHelper colorWithHexString:@"#1061FF" alpha:0.1f].CGColor, nil];
    CAGradientLayer *btoGradientLayer = [CAGradientLayer layer];
    btoGradientLayer.frame = self.titleButton.bounds;
    btoGradientLayer.startPoint = CGPointMake(0, 0.5);
    btoGradientLayer.endPoint = CGPointMake(1, 0.5);
    [btoGradientLayer setColors:[NSArray arrayWithArray:btoColors]];
    [self.titleButton.layer addSublayer:btoGradientLayer]; //设置颜色渐变
    self.titleButton.layer.cornerRadius = self.titleButton.TKHeight * 0.5;
    self.titleButton.clipsToBounds = YES;
}

- (void)setSelected:(BOOL)selected animated:(BOOL)animated {
    [super setSelected:selected animated:animated];

    // Configure the view for the selected state
}

@end
