//
//  SelectViewController.m
//  TKOpenAccount-Standard
//
//  Created by appple on 2021/7/23.
//  Copyright © 2021 thinkive. All rights reserved.
//

#define H5_URL @"H5Url"

#import "SelectViewController.h"
#import "TKOpenController.h"

@interface SelectViewController ()

@property (unsafe_unretained, nonatomic) IBOutlet UITextField *textField;

@end

@implementation SelectViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    // Do any additional setup after loading the view.
}

/*
#pragma mark - Navigation

// In a storyboard-based application, you will often want to do a little preparation before navigation
- (void)prepareForSegue:(UIStoryboardSegue *)segue sender:(id)sender {
    // Get the new view controller using [segue destinationViewController].
    // Pass the selected object to the new view controller.
}
*/
- (IBAction)buttonAction:(UIButton *)sender {
    NSBundle *bundle = [NSBundle bundleWithURL:[[NSBundle mainBundle] URLForResource:[NSString stringWithFormat:@"%@", TK_OPEN_RESOURCE_NAME] withExtension:@"bundle"]];
    
    NSString *plistPath = [bundle pathForResource:@"config" ofType:@"plist"];
    
    NSMutableDictionary *cDic;
    
    if (plistPath) {
        
        cDic = [[NSMutableDictionary alloc] initWithContentsOfFile:plistPath];
    }
    NSString *urlString = self.textField.text;
    if ([TKStringHelper isEmpty:urlString]) {
        urlString = [cDic getStringWithKey:H5_URL];
    }
    cDic[@"h5Url"]=urlString;
    TKOpenController *mCtl = [[TKOpenController alloc] initWithParams:cDic];
    mCtl.isUseFullScreenH5 = YES;
    self.view.window.rootViewController = mCtl;
}

- (void)touchesBegan:(NSSet<UITouch *> *)touches withEvent:(UIEvent *)event {
    [super touchesBegan:touches withEvent:event];
    [self.view endEditing:YES];
}

@end
