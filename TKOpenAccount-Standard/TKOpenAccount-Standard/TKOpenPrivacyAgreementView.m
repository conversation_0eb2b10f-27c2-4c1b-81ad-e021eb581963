//
//  TKOpenPrivacyAgreementView.m
//  TKOpenAccount-Standard
//  东方隐私协议视图
//  Created by <PERSON>ie on 2019/8/23.
//  Copyright © 2019 thinkive. All rights reserved.
//

#import "TKOpenPrivacyAgreementView.h"
#import "TTTAttributedLabel.h"

//#define TKOPEN_PRIVACY_TITLE @"隐私协议"

//开户协议
#define TKOPEN_PRIVACY_URLSTRING  @"《xx证券开户平台隐私政策》"
#define TKOPEN_PRIVACY_URL @"https://operation.thinkive.com:18090/kh-privacyagreement-view/index.html"
#define TKOPEN_PRIVACY_STRING [NSString stringWithFormat:@"欢迎您使⽤xx证券开户APP。为加强对您个⼈信息的保护，我们依据最新的法律法规及监管政策要求更新了隐私政策。我们将在充分保证您知情权且获得您明确授权后收集、使⽤您的个⼈信息。请务必仔细阅读并确认%@。我们将严格按照政策内容使⽤和保护您的个⼈信息，感谢您的信任。",TKOPEN_PRIVACY_URLSTRING]

////期货协议
//#define TKOPEN_PRIVACY_URLSTRING  @"《xx期货开户平台隐私政策》"
////#define TKOPEN_PRIVACY_URL_IP @"https://opt-dev.thinkive.com:15149"   // 期货开发环境
////#define TKOPEN_PRIVACY_URL_IP @"https://opt-test.thinkive.com:15149"   // 期货测试环境
//#define TKOPEN_PRIVACY_URL_IP @"https://operation.thinkive.com:10000"   // 期货演示环境
////https://operation.thinkive.com:10000/bc-futures-view/views/index.html#/appPrivacyAgreement
//#define TKOPEN_PRIVACY_URL [NSString stringWithFormat:@"%@/bc-futures-view/views/index.html#/appPrivacyAgreement",TKOPEN_PRIVACY_URL_IP]
//#define TKOPEN_PRIVACY_STRING [NSString stringWithFormat:@"欢迎您使用xx期货开户APP。为加强对您个人信息的保护，我们依据最新的法律法规及监管政策要求更新了隐私政策。我们将在充分保证您知情权且获得您明确授权后收集、使用您的个人信息。请务必仔细阅读并确认%@。我们将严格按照政策内容使用和保护您的个人信息，感谢您的信任。",TKOPEN_PRIVACY_URLSTRING]

#define TKOPEN_PRIVACY_CANCEL @"不同意"
#define TKOPEN_PRIVACY_OK @"同意"
#define TKOPEN_PRIVACY_EXIT @"继续退出"
#define TKOPEN_PRIVACY_KNOW @"阅读协议"
#define TKOPEN_PRIVACY_MAIN_COLOR  @"#0354C2"
#define TKOPEN_PRIVACY_CANCEL_TIP @"尊敬的用户，按照相关法律及规定，不同意隐私政策我们将无法为您提供服务。"

@interface TKOpenPrivacyAgreementView()<TKWebViewDelegate,TTTAttributedLabelDelegate>
@property (nonatomic, strong) UIImageView *bgImgView;//背景图
@property (nonatomic, strong) UIView *tipBgView;//协议提示窗口背景视图
@property (nonatomic, strong) UILabel *titleLabel;//标题文本
//@property (nonatomic, strong) TKWebView *tipTextWebView;//隐私政策pdf文档展示的webview
@property (nonatomic, strong) UIScrollView *scrollView;//隐私文本底部滚动视图
@property (nonatomic, strong) TTTAttributedLabel *tipTextView;//隐私政策
@property (nonatomic, strong) UIButton *cancelBtn;//取消按钮
@property (nonatomic, strong) UIButton *okBtn;//同意按钮

@property (nonatomic, strong) UIView *cancelTipBgView;//取消提示窗口背景视图
@property (nonatomic, strong) UILabel *cancelTitleLabel;//标题文本
@property (nonatomic, strong) UILabel *cancelTipLabel;//取消文本提示
//@property (nonatomic, strong) UIView *horizontalLine;//取消横线
//@property (nonatomic, strong) UIView *verticalLine;//取消竖线
@property (nonatomic, strong) UIButton *exitBtn;//退出按钮
@property (nonatomic, strong) UIButton *knowBtn;//知晓按钮
@property (nonatomic, strong) NSTimer *timer;
@end

@implementation TKOpenPrivacyAgreementView

-(instancetype)initWithFrame:(CGRect)frame{
    self=[super initWithFrame:frame];
    if (self) {
        [self viewInit];
    }
    return self;
}

/**
 <AUTHOR> 2019年08月23日11:21:24
 @初始化隐私协议提示层
 */
-(void)viewInit{
    [self addSubview:self.bgImgView];
    [self addSubview:self.tipBgView];
    [self.tipBgView addSubview:self.titleLabel];
    [self.tipBgView addSubview:self.scrollView];
    [self.scrollView addSubview:self.tipTextView];
    [self.tipBgView addSubview:self.cancelBtn];
    [self.tipBgView addSubview:self.okBtn];
}

-(void)showTextView{
    //隐藏隐式动画
    [CATransaction begin];
    [CATransaction setDisableActions:YES];
    [self.scrollView flashScrollIndicators];
    [CATransaction commit];
}

#pragma mark - TTTAttributedLabelDelegate
-(void)attributedLabel:(TTTAttributedLabel *)label didSelectLinkWithURL:(NSURL *)url{
    TKBaseWebViewController *webviewController = [[TKBaseWebViewController alloc] initWithName:@"privacy"];
    webviewController.webViewUrl = url.absoluteString;
    webviewController.title = [url.absoluteString isEqualToString:TKOPEN_PRIVACY_URL] ? @"隐私政策" : @"隐私政策";
    webviewController.isShowBackBtn = YES;
    webviewController.btnMode = @"2";
    webviewController.statusBarBgColor = [UIColor whiteColor];
    webviewController.statusBarStyle = UIStatusBarStyleDefault;
    webviewController.titleColor = [UIColor blackColor];
    
    // 通过添加子控制器的方式来打开隐私页面
    [[TKAppEngine shareInstance].rootViewCtr.currentViewCtrl presentViewController:webviewController animated:YES completion:nil];

}




#pragma mark 事件方法
/**
 <AUTHOR> 2019年08月23日14:48:58
 @取消事件
 */
-(void)cancelAction{

    [self.tipBgView setHidden:YES];
    [self addSubview:self.cancelTipBgView];
    [self.cancelTipBgView addSubview:self.cancelTitleLabel];
    [self.cancelTipBgView addSubview:self.cancelTipLabel];
//    [self.cancelTipBgView addSubview:self.horizontalLine];
//    [self.cancelTipBgView addSubview:self.verticalLine];
//    [self.cancelTipBgView setTKHeight:self.verticalLine.frame.origin.y+self.verticalLine.frame.size.height];
    self.cancelTipBgView.center=self.center;
    [self.cancelTipBgView addSubview:self.exitBtn];
    [self.cancelTipBgView addSubview:self.knowBtn];
}
/**
 <AUTHOR> 2019年08月23日14:48:58
 @确定事件
 */
-(void)okAction{
    if (self.timer) {
        [self.timer invalidate];
        self.timer = nil;
    }
    if (self.delegate&&[self.delegate respondsToSelector:@selector(agreePolicy)]) {
        [self.delegate agreePolicy];
    }
}

/**
 <AUTHOR> 2021年08月27日16:16:24
 @退出
 */
-(void)exitAction{
    if (self.timer) {
        [self.timer invalidate];
        self.timer = nil;
    }
    exit(1);
}

/**
 <AUTHOR> 2021年08月27日16:16:24
 @知晓
 */
-(void)knowAction{
    
    [self.cancelTipBgView removeFromSuperview];
    _cancelTipBgView=nil;
    [self.tipBgView setHidden:NO];
}

#pragma mark lazyloading

/**
 *  <AUTHOR> 2021年08月27日11:28:44
 *  背景图
 *  @return 背景图
 */
-(UIImageView *)bgImgView{
    if (!_bgImgView) {
        _bgImgView=[[UIImageView alloc] initWithFrame:self.frame];
        _bgImgView.image=[TKImageHelper defaultLaunchImage];
        UIView *colorView=[[UIView alloc] initWithFrame:self.frame];
        colorView.backgroundColor=[TKUIHelper colorWithHexString:@"#000000" alpha:0.3f];
        [_bgImgView addSubview:colorView];
    }
    return _bgImgView;
}

/**
 <AUTHOR> 2019年08月23日11:17:19
 @初始化懒加载提示窗口背景视图
 @return 提示窗口背景视图
 */
-(UIView *)tipBgView{
    if (!_tipBgView) {
        float width=315;
        float height=402;
        float x=(self.frame.size.width-width)/2;
        float y=(self.frame.size.height-height)/2;;
        _tipBgView=[[UIView alloc] initWithFrame:CGRectMake(x, y, width, height)];
        _tipBgView.backgroundColor=[UIColor whiteColor];
        _tipBgView.layer.cornerRadius=8.0f;
        _tipBgView.layer.masksToBounds=YES;
    }
    return _tipBgView;
}

/**
 <AUTHOR> 2019年08月23日11:17:19
 @初始化懒加载标题文件
 @return 标题文本
 */
-(UILabel *)titleLabel{
    if (!_titleLabel) {
        float x=0;
        float y=24;
        float height=26;
        float width=self.tipBgView.frame.size.width;
        _titleLabel=[[UILabel alloc] initWithFrame:CGRectMake(x, y, width, height)];
        _titleLabel.text = @"隐私协议";
        _titleLabel.textAlignment=NSTextAlignmentCenter;
        _titleLabel.font = [UIFont fontWithName:@"PingFang SC" size:18];
        _titleLabel.textColor = [UIColor blackColor];
        _titleLabel.backgroundColor =[UIColor clearColor];
    }
    return _titleLabel;
}



/**
 *  <AUTHOR> 2019年08月23日13:25:07
 *  隐私文本底部滚动视图
 *  @return  隐私文本底部滚动视图
 */
-(UIScrollView *)scrollView{
    if (!_scrollView) {
        float x=24;
        float y=self.titleLabel.TKBottom+8;
        float width=self.tipBgView.frame.size.width-2*x;
        float height=240;
        _scrollView=[[UIScrollView alloc] initWithFrame:CGRectMake(x, y, width, height)];

        _scrollView.backgroundColor=[UIColor whiteColor];
    }
    return _scrollView;
}

/**
 *  <AUTHOR> 2019年08月23日13:25:07
 *  隐私政策提示文本内容
 *  @return  隐私政策提示文本内容
 */
-(TTTAttributedLabel *)tipTextView{
    if (!_tipTextView) {
        _tipTextView =[[TTTAttributedLabel alloc] initWithFrame:CGRectMake(0, 0, 10, 10)];
        _tipTextView.font = [UIFont fontWithName:@"PingFang SC" size:14];;
        _tipTextView.numberOfLines = 0;
        _tipTextView.lineBreakMode = NSLineBreakByWordWrapping;
        _tipTextView.verticalAlignment = TTTAttributedLabelVerticalAlignmentTop;
        _tipTextView.delegate = self;
        
        
        // 《用户协议与隐私政策》
        NSRange range = [TKOPEN_PRIVACY_STRING rangeOfString:TKOPEN_PRIVACY_URLSTRING];
        NSMutableAttributedString *attr = [[NSMutableAttributedString alloc] initWithString:TKOPEN_PRIVACY_STRING];

        [attr addAttribute:NSFontAttributeName value:[UIFont fontWithName:@"PingFang SC" size:14] range:NSMakeRange(0, TKOPEN_PRIVACY_STRING.length)];
        [attr addAttribute:NSForegroundColorAttributeName value:[TKUIHelper colorWithHexString:@"#666666"] range:NSMakeRange(0, TKOPEN_PRIVACY_STRING.length)];
        
//        NSMutableParagraphStyle *paragraphStyle = [NSMutableParagraphStyle new];
//        paragraphStyle.lineSpacing = 10;

//        [attr setAttributes:@{NSFontAttributeName:[UIFont systemFontOfSize:16],NSParagraphStyleAttributeName:paragraphStyle} range:NSMakeRange(0, TKOPEN_PRIVACY_STRING.length)];
   
        [_tipTextView setText:attr];
        _tipTextView.linkAttributes = @{NSForegroundColorAttributeName:[TKUIHelper colorWithHexString:@"#0354C2"],NSFontAttributeName:[UIFont fontWithName:@"PingFang SC" size:14]};
        [_tipTextView addLinkToURL:[NSURL URLWithString:TKOPEN_PRIVACY_URL] withRange:range];
            

        float width=self.scrollView.frame.size.width;
        float height=240;
            //文本size
        CGSize labelSize = [TTTAttributedLabel sizeThatFitsAttributedString:_tipTextView.attributedText withConstraints:CGSizeMake(width, MAXFLOAT) limitedToNumberOfLines:1000000];
        _scrollView.contentSize=CGSizeMake(width, labelSize.height);
        _tipTextView.frame=CGRectMake(0, 0, width, labelSize.height);
        if (labelSize.height<240) {
            float difference=240-labelSize.height;
            [self.tipBgView setTKHeight:self.tipBgView.frame.size.height-difference];
            height=labelSize.height;
        }
        _scrollView.frame=CGRectMake(_scrollView.frame.origin.x, _scrollView.frame.origin.y, width, height);
        if (self.timer) {
            [self.timer invalidate];
            self.timer = nil;
        }
        self.timer=[NSTimer scheduledTimerWithTimeInterval:0.5 target:self selector:@selector(showTextView) userInfo:nil repeats:YES];
    }
    return _tipTextView;
}

/**
 *  <AUTHOR> 2019年08月23日14:34:16
 *  取消按钮
 *  @return 取消按钮
 */
-(UIButton *)cancelBtn{
    if (!_cancelBtn) {
        float x=30;
        float height=40;
        float y=self.scrollView.TKBottom+24;
        float width=120;

        _cancelBtn=[[UIButton alloc] initWithFrame:CGRectMake(x, y, width, height)];
        
//        if (self.requestParams[@"mainColor"]) {
//            [_continueScanBtn setTitleColor:[TKUIHelper colorWithHexString:self.requestParams[@"mainColor"]] forState:UIControlStateNormal];
//            [_continueScanBtn setBackgroundColor:[TKUIHelper colorWithHexString:self.requestParams[@"mainColor"] alpha:0.05f]];
//        }else{
            [_cancelBtn setTitleColor:[TKUIHelper colorWithHexString:@"#1061FF"] forState:UIControlStateNormal];
            [_cancelBtn setBackgroundColor:[TKUIHelper colorWithHexString:@"#E8F0FF"]];
//        }

        
        [_cancelBtn setTitle:TKOPEN_PRIVACY_CANCEL forState:UIControlStateNormal];
        _cancelBtn.titleLabel.font=[UIFont fontWithName:@"PingFang SC" size:14];

        [_cancelBtn addTarget:self action:@selector(cancelAction) forControlEvents:UIControlEventTouchUpInside ];
        _cancelBtn.layer.cornerRadius=height/2;
  
    }
    return _cancelBtn;
}


/**
 *  <AUTHOR> 2019年08月23日14:42:01
 *  同意按钮
 *  @return 同意按钮
 */
-(UIButton *)okBtn{
    if (!_okBtn) {
        
        float height=self.cancelBtn.frame.size.height;
        float y=self.cancelBtn.frame.origin.y;
        float width=self.cancelBtn.frame.size.width;
        float x=self.tipBgView.frame.size.width-30-width;
        _okBtn=[[UIButton alloc] initWithFrame:CGRectMake(x, y, width, height)];
        
//        if (self.requestParams[@"mainColor"]) {
//            [_okBtn setBackgroundColor:[TKUIHelper colorWithHexString:self.requestParams[@"mainColor"]]];
//        }else{
            NSMutableArray *btoColors = [NSMutableArray arrayWithObjects:(id)[TKUIHelper colorWithHexString:@"#1061FF" alpha:1.0f].CGColor,(id)[TKUIHelper colorWithHexString:@"#1061FF" alpha:0.6f].CGColor, nil];
            CAGradientLayer *btoGradientLayer = [CAGradientLayer layer];
            btoGradientLayer.frame = CGRectMake(0, 0, width, height);
            btoGradientLayer.startPoint = CGPointMake(0, 0.5);
            btoGradientLayer.endPoint = CGPointMake(1, 0.5);
            [btoGradientLayer setColors:[NSArray arrayWithArray:btoColors]];
            btoGradientLayer.cornerRadius=height/2.0f;
            [_okBtn.layer addSublayer:btoGradientLayer]; //设置颜色渐变
//        }

        
        [_okBtn setTitle:TKOPEN_PRIVACY_OK forState:UIControlStateNormal];
        _okBtn.titleLabel.font=[UIFont fontWithName:@"PingFang SC" size:14];
        [_okBtn setTitleColor:[TKUIHelper colorWithHexString:@"#FFFFFF"] forState:UIControlStateNormal];
        [_okBtn addTarget:self action:@selector(okAction) forControlEvents:UIControlEventTouchUpInside ];
        _okBtn.layer.cornerRadius=height/2;
    }
    return _okBtn;
}


/**
 <AUTHOR> 2019年08月23日11:17:19
 @初始化懒加载取消提示窗口背景视图
 @return 取消提示窗口背景视图
 */
-(UIView *)cancelTipBgView{
    if (!_cancelTipBgView) {
        float width=300;
        float height=200;
        float x=(self.TKWidth-width)/2;
        float y=(self.TKHeight-height)/2;;
        _cancelTipBgView=[[UIView alloc] initWithFrame:CGRectMake(x, y, width, height)];
        _cancelTipBgView.backgroundColor=[UIColor whiteColor];
        _cancelTipBgView.layer.cornerRadius=8.0f;
        _cancelTipBgView.layer.masksToBounds=YES;
    }
    return _cancelTipBgView;
}

/**
 <AUTHOR> 2019年08月23日11:17:19
 @初始化懒加载取消标题文件
 @return 取消标题文本
 */
-(UILabel *)cancelTitleLabel{
    if (!_cancelTitleLabel) {
        float x=0;
        float y=24;
        float height=26;
        float width=self.cancelTipBgView.frame.size.width;
        _cancelTitleLabel=[[UILabel alloc] initWithFrame:CGRectMake(x, y, width, height)];
        _cancelTitleLabel.text = @"温馨提示";
        _cancelTitleLabel.textAlignment=NSTextAlignmentCenter;
        _cancelTitleLabel.font = [UIFont fontWithName:@"PingFang SC" size:18];
        _cancelTitleLabel.textColor = [UIColor blackColor];
        _cancelTitleLabel.backgroundColor =[UIColor clearColor];
//        [TKUIHelper colorWithHexString:TKOPEN_PRIVACY_MAIN_COLOR];
//        [UIColor colorWithRed:226/255.0 green:46/255.0 blue:48/255.0 alpha:1/1.0];
    }
    return _cancelTitleLabel;
}

/**
 <AUTHOR> 2019年08月23日11:17:19
 @初始化懒加载取消文本提示
 @return 取消文本提示
 */
-(UILabel *)cancelTipLabel{
    if (!_cancelTipLabel) {
        _cancelTipLabel=[[UILabel alloc] init];
        _cancelTipLabel.font = [UIFont fontWithName:@"PingFang SC" size:14];
        _cancelTipLabel.text=TKOPEN_PRIVACY_CANCEL_TIP;
        _cancelTipLabel.textColor=[TKUIHelper colorWithHexString:@"#666666"];
        _cancelTipLabel.numberOfLines=0;
        float x=24;
        float y=self.titleLabel.TKBottom+8;
        float width=self.cancelTipBgView.TKWidth-2*x;
       CGSize lableSize = [_cancelTipLabel sizeThatFits:CGSizeMake(width, MAXFLOAT)];
        _cancelTipLabel.frame=CGRectMake(x, y, width, lableSize.height);
    }
    return _cancelTipLabel;
}



///**
// <AUTHOR> 2019年08月23日11:17:19
// @初始化懒加载取消横线
// @return 取消横线
// */
//-(UIView *)horizontalLine{
//    if (!_horizontalLine) {
//        float height=0.5f;
//        float x=0;
//        float y=self.cancelTipLabel.frame.size.height+self.cancelTipLabel.frame.origin.y+20;
//        float width=self.cancelTipBgView.frame.size.width;
//        _horizontalLine=[[UIView alloc] initWithFrame:CGRectMake(x, y, width, height)];
//        _horizontalLine.backgroundColor=[TKUIHelper colorWithHexString:@"#EAEAEF"];
//    }
//    return _horizontalLine;
//}
//
//
///**
// <AUTHOR> 2019年08月23日11:17:19
// @初始化懒加载取消横线
// @return 取消横线
// */
//-(UIView *)verticalLine{
//    if (!_verticalLine) {
//        float width=0.5f;
//        float height=48;
//        float x=(self.cancelTipBgView.frame.size.width-width)/2.0f;
//        float y=self.horizontalLine.frame.size.height+self.horizontalLine.frame.origin.y;
//
//        _verticalLine=[[UIView alloc] initWithFrame:CGRectMake(x, y, width, height)];
//        _verticalLine.backgroundColor=[TKUIHelper colorWithHexString:@"#EAEAEF"];
//    }
//    return _verticalLine;
//}



/**
 *  <AUTHOR> 2021年08月27日16:11:19
 *  退出按钮
 *  @return 退出按钮
 */
-(UIButton *)exitBtn{
    if (!_exitBtn) {
        float x=24;
        float height=40;
        float y=self.cancelTipLabel.TKBottom+26;
        float width=120;

        _exitBtn=[[UIButton alloc] initWithFrame:CGRectMake(x, y, width, height)];
        
        //        if (self.requestParams[@"mainColor"]) {
        //            [_exitBtn setTitleColor:[TKUIHelper colorWithHexString:self.requestParams[@"mainColor"]] forState:UIControlStateNormal];
        //            [_exitBtn setBackgroundColor:[TKUIHelper colorWithHexString:self.requestParams[@"mainColor"] alpha:0.05f]];
        //        }else{
                    [_exitBtn setTitleColor:[TKUIHelper colorWithHexString:@"#1061FF"] forState:UIControlStateNormal];
                    [_exitBtn setBackgroundColor:[TKUIHelper colorWithHexString:@"#E8F0FF"]];
        //        }

        
        [_exitBtn setTitle:TKOPEN_PRIVACY_EXIT forState:UIControlStateNormal];
        _exitBtn.titleLabel.font=[UIFont fontWithName:@"PingFang SC" size:14];
//        [_exitBtn setTitleColor:[TKUIHelper colorWithHexString:TKOPEN_PRIVACY_MAIN_COLOR] forState:UIControlStateNormal];
        _exitBtn.layer.cornerRadius=height/2;
        [_exitBtn addTarget:self action:@selector(exitAction) forControlEvents:UIControlEventTouchUpInside ];
    }
    return _exitBtn;
}



/**
 *  <AUTHOR> 2021年08月27日16:30:32
 *  知晓按钮
 *  @return 知晓按钮
 */
-(UIButton *)knowBtn{
    if (!_knowBtn) {
        
        float height=self.exitBtn.TKHeight;
        float y=self.exitBtn.TKTop;
        float width=self.exitBtn.TKWidth;
        float x=self.cancelTipBgView.TKWidth-30-width;;
        
        _knowBtn=[[UIButton alloc] initWithFrame:CGRectMake(x, y, width, height)];
        //        if (self.requestParams[@"mainColor"]) {
        //            [_knowBtn setBackgroundColor:[TKUIHelper colorWithHexString:self.requestParams[@"mainColor"]]];
        //        }else{
                    NSMutableArray *btoColors = [NSMutableArray arrayWithObjects:(id)[TKUIHelper colorWithHexString:@"#1061FF" alpha:1.0f].CGColor,(id)[TKUIHelper colorWithHexString:@"#1061FF" alpha:0.6f].CGColor, nil];
                    CAGradientLayer *btoGradientLayer = [CAGradientLayer layer];
                    btoGradientLayer.frame = CGRectMake(0, 0, width, height);
                    btoGradientLayer.startPoint = CGPointMake(0, 0.5);
                    btoGradientLayer.endPoint = CGPointMake(1, 0.5);
                    [btoGradientLayer setColors:[NSArray arrayWithArray:btoColors]];
                    btoGradientLayer.cornerRadius=height/2.0f;
                    [_knowBtn.layer addSublayer:btoGradientLayer]; //设置颜色渐变
        //        }
        
        
        [_knowBtn setTitle:TKOPEN_PRIVACY_KNOW forState:UIControlStateNormal];
        _knowBtn.titleLabel.font=[UIFont fontWithName:@"PingFang SC" size:14];
        [_knowBtn setTitleColor:[TKUIHelper colorWithHexString:@"#FFFFFF"] forState:UIControlStateNormal];
        [_knowBtn addTarget:self action:@selector(knowAction) forControlEvents:UIControlEventTouchUpInside ];
    }
    return _knowBtn;
}
@end
