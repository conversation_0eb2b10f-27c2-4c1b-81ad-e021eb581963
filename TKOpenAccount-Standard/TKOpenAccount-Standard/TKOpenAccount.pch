//
//  TKOpenAccount.pch
//  TKOpenAccount-Standard
//
//  Created by <PERSON><PERSON><PERSON> on 15/5/9.
//  Copyright (c) 2015年 thinkive. All rights reserved.
//

#ifndef TKOpenAccount_Standard_TKOpenAccount_pch
#define TKOpenAccount_Standard_TKOpenAccount_pch

// Include any system framework and library headers here that should be included in all compilation units.
// You will also need to set the Prefix Header build setting of one or more of your targets to reference this file.

#endif

#import <Availability.h>

#ifndef __IPHONE_5_0
#warning "This project uses features only available in iOS SDK 5.0 and later."
#endif

#ifdef __OBJC__
#import <UIKit/UIKit.h>
#import <Foundation/Foundation.h>
#import <TKWebViewApp/TKWebViewApp.h>
#import "UIViewController+TKAuthorityKit.h"
#import "TKCommonUtil.h"
#import "TKOpenViewStyleHelper.h"
#import "TKStatisticEventHelper.h"


#define speechRecognizeToolType 1 // 语音合成识别；1-阿里；2腾讯；3-科大讯飞； 4-同花顺语音；5-科大讯飞非AI
//#define TK_OPEN_Authority_UIAlertView 1 //没有权限授权情况下是否走UIAlertView弹窗（联储重写了UIAlertView要走）
//#define TK_HH_KVOCR 1    //是否走合合深度学习KV版本；不是的话注释掉这个

#define TK_ONE_WAY_TEMP_MOVE_NAME @"oneWayVideo"
#define TKSmartOpenVolume  0.3   //单向开户的音量检测阈值
#define TKCARD_MAIN_COLOR @"#2F85FF" //13，14身份证插件默认主色调

#define IMAGE_COMPRESS_SIZE 250.0

#define TK_OPEN_RESOURCE_NAME @"TKOpenResource"

#define TK_OPEN_TOKEN_INVALID_NOTIFICATION @"tkOpenTokenInvaidNotification" // 单向token相关的网络请求过期

#define TK_THIRD_WEBVIEW_NOTIFICATION @"tkThirdWebviewNotification"

#define TK_INTERRUPTSDK_NOTIFICATION @"tkInterruptSDKNotification"

#define INTSIG_CARD_RECOGNIZE_APP_KEY @"newAuth27f15f31f0e6167d" //思迪合合测试证件识别key

#define TK_CHANGE_SCREEN_SHOT_RECORD_STATUS @"tkChangeScreenShotRecordStatus" //改变截屏录屏监听状态

//#define INTSIG_CARD_RECOGNIZE_APP_KEY @"EJEBA2JyQgdEBX7FdVC6YRJb" //合合证件识别key
#endif


