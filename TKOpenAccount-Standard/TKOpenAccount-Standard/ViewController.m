//
//  ViewController.m
//  TKOpenAccount-Standard
//
//  Created by <PERSON><PERSON><PERSON> on 15/5/9.
//  Copyright (c) 2015年 thinkive. All rights reserved.
//

#import "ViewController.h"

// 必须导入的文件
#import <TKWebViewApp/TKWebViewApp.h>

// 为仿制而添加的适配器。集成正式的SDK时，可以去掉
#import "TKTempService.h"
#import "TKChatVideoRecordViewController.h"

#import "TKChatLiveDetectViewController.h"

#import "TKSmartVirtualManViewController.h"
#import "TKOpenController.h"
#import "TKDoubleChatVideoRecordViewController.h"
#import "TKStatisticEventHelper.h"
//#import "TKNewOneWayVideoViewController.h"
#import <AVFoundation/AVFoundation.h>

@interface ViewController ()<TKChatVideoRecordResultDelegate, TKChatLiveDetectResultDelegate /*, TKSmartVirtualManViewControllerResultDelegate*/, TKDoubleChatVideoRecordResultDelegate, TKStatisticEventHelperDelegate>
{
    
    dispatch_queue_t _audioDownloadQueue;
}
@property (nonatomic, readwrite, strong) UITextField *textField;
@property (nonatomic, readwrite, strong) UILabel *label;
@property (nonatomic, readwrite, strong) UILabel *label2;

@property (nonatomic, readwrite, strong) TKDownloadSessionManager *downloadSessionManager; // 下载管理者
@property (nonatomic, readwrite, strong) TKDownloadModel *currentDownloadModel;  // 当前正在下载文件模型

@end

@implementation ViewController

- (void)dealloc {
    TKLogInfo(@"ViewController dealloc");
}

- (UIStatusBarStyle)preferredStatusBarStyle {
    if (@available(iOS 13.0, *)) {
        return UIStatusBarStyleDarkContent;
    } else {
        return UIStatusBarStyleDefault;
    }
}

-(void)viewDidAppear:(BOOL)animated{
    [super viewDidAppear:animated];
    
    UIApplication.sharedApplication.statusBarStyle = TKUIStatusBarStyleDefault;
    
//    // 让传入的参数
//    NSMutableDictionary *param =[[NSMutableDictionary alloc] init];
//    param[@"funcNo"]=@"60013";//功能号
//    param[@"isUpload"]=@"0";//原生插件直接调用不走思迪上传
//    param[@"action"]=@"pai";//pai:拍照界面
//    param[@"mainColor"]=@"#FD671A";//主色调16进制值，不传默认蓝色
//    param[@"imgType"]=@"4";//身份证正反面：4：身份证正面，5：身份证反面；
//    param[@"isNeedSample"]=@"1";//照片结果展示是否需要显示示例部分:1：需要示例显示；其他不需要（默认不需要）
//
//    NSString *pluginNo = [param getStringWithKey:@"funcNo"];    // 取出插件号
//    if ([TKStringHelper isEmpty:pluginNo]) {
//        // 插件号不能为空
//        // 回调报错信息给h5
//    }
//    NSString *moduleName = [param getStringWithKey:@"moduleName"];
//    //插件调用
//    [[TKPluginInvokeCenter shareInstance] callPlugin:pluginNo param:param moduleName:[TKStringHelper isNotEmpty:moduleName] ? moduleName : @"open" callBackFunc:^(NSMutableDictionary *result) {
//        // 回调给原生的结果，可以直接回调给h5
//        TKLogDebug(@"result = %@", result);
//    }];
    
    
//    // 让传入的参数
//    NSMutableDictionary *param =[[NSMutableDictionary alloc] init];
//    param[@"funcNo"]=@"60014";//功能号
//    param[@"isUpload"]=@"0";//原生插件直接调用不走思迪上传
//    //因为合合OCR不支持本地图片识别，所以隐藏相册和拍照按钮，只用扫描，不然会出现没有识别结果只有图片情况
//    param[@"isAlbum"]=@"0";//是否显示相册按钮；0：不显示  1：显示（默认显示）
//    param[@"isTake"]=@"0";//是否显示拍照按钮0：不显示  1：显示（默认显示）
//    param[@"timeOut"]=@"10000";//扫描超时时长；单位秒，默认30秒；为了避免超时提示出现去拍照情况，这里时间设置长点
//    param[@"mainColor"]=@"#FD671A";//主色调16进制值，不传默认蓝色
//    param[@"imgType"]=@"4";//身份证正反面：4：身份证正面，5：身份证反面；
//    param[@"isNeedSample"]=@"1";//照片结果展示是否需要显示示例部分:1：需要示例显示；其他不需要（默认不需要）
//        
//    NSString *pluginNo = [param getStringWithKey:@"funcNo"];    // 取出插件号
//    if ([TKStringHelper isEmpty:pluginNo]) {
//        // 插件号不能为空
//        // 回调报错信息给h5
//    }
//    NSString *moduleName = [param getStringWithKey:@"moduleName"];
//    //插件调用
//    [[TKPluginInvokeCenter shareInstance] callPlugin:pluginNo param:param moduleName:[TKStringHelper isNotEmpty:moduleName] ? moduleName : @"open" callBackFunc:^(NSMutableDictionary *result) {
//        // 回调给原生的结果，可以直接回调给h5
//        TKLogDebug(@"result = %@", result);
//    }];
    
//    NSString *htmlString= @"<p><div><div style=\"font-size:22px;\"style=\"font-size:22px;\" color='#FFFFFF'><font style=\"font-size:22px;\"style=\"font-size:22px;\" color=\"#FF5900\">“理解”<\/font>或<font style=\"font-size:22px;\"color=\"#FF5900\">“不理解”<\/font><\/div><\/div></p>";
//    htmlString=[TKCommonUtil switchLabelToSpan:htmlString];
//    TKLogDebug(@"-----");
}

- (void)viewDidLoad {
    [super viewDidLoad];
    
    _audioDownloadQueue = dispatch_queue_create("com.thinkive.TKChatLocalVideoRecordManager.audioDownloadQueue", DISPATCH_QUEUE_SERIAL);
    
//    [self getToken];
//    return;
    
//    [self createAuth:@"com.hxsc.tzt.cn|com.zztzt.hxsckh"];
    //    [self goOpenView];
    
//    [TKStatisticEventHelper shareInstance].delegate = self;
//    UIButton *btn = [UIButton buttonWithType:UIButtonTypeCustom];
//    [btn setTitle:@"数据统计" forState:UIControlStateNormal];
//    btn.frame = CGRectMake(100, 50, 200, 100);
//    btn.backgroundColor = UIColor.blueColor;
//    [btn addTarget:self action:@selector(testStatistic) forControlEvents:UIControlEventTouchUpInside];
//    [self.view addSubview:btn];
    
#if 1
    // 本地录制
//    UIButton *btn = [UIButton buttonWithType:UIButtonTypeCustom];
//    [btn setTitle:@"本地录制" forState:UIControlStateNormal];
//    btn.frame = CGRectMake(100, 50, 200, 100);
//    btn.backgroundColor = UIColor.blueColor;
//    [btn addTarget:self action:@selector(startLocalVideoRecord) forControlEvents:UIControlEventTouchUpInside];
//    [self.view addSubview:btn];
    
    // 服务端录制
    UIButton *btn = [UIButton buttonWithType:UIButtonTypeCustom];
//    [btn setTitle:@"本地录制" forState:UIControlStateNormal];
    [btn setTitle:@"服务端录制" forState:UIControlStateNormal];
    btn.frame = CGRectMake(100, 50, 200, 100);
    btn.backgroundColor = UIColor.blueColor;
//    [btn addTarget:self action:@selector(startLocalVideoRecord) forControlEvents:UIControlEventTouchUpInside];
    [btn addTarget:self action:@selector(startVideoRecord) forControlEvents:UIControlEventTouchUpInside];
    [self.view addSubview:btn];
    
    
    // 双人录制
//    UIButton *btn = [UIButton buttonWithType:UIButtonTypeCustom];
//    [btn setTitle:@"开始双人本地录制" forState:UIControlStateNormal];
//    btn.frame = CGRectMake(100, 50, 200, 100);
//    btn.backgroundColor = UIColor.blueColor;
//    [btn addTarget:self action:@selector(startDoubleVideoRecord) forControlEvents:UIControlEventTouchUpInside];
//    [self.view addSubview:btn];

    // 双人服务端录制
//    UIButton *btn = [UIButton buttonWithType:UIButtonTypeCustom];
//    [btn setTitle:@"开始双人服务端录制" forState:UIControlStateNormal];
//    btn.frame = CGRectMake(100, 50, 200, 100);
//    btn.backgroundColor = UIColor.blueColor;
//    [btn addTarget:self action:@selector(startDoubleTChatVideoRecord) forControlEvents:UIControlEventTouchUpInside];
//    [self.view addSubview:btn];
//    [btn setTitle:@"服务端录制" forState:UIControlStateNormal];
//    [btn addTarget:self action:@selector(startVideoRecord) forControlEvents:UIControlEventTouchUpInside];
    
    // 双人录制
    // 虚拟人录制
//    UIButton *btn = [UIButton buttonWithType:UIButtonTypeCustom];
//    [btn setTitle:@"开始虚拟人录制" forState:UIControlStateNormal];
//    btn.frame = CGRectMake(100, 50, 200, 100);
//    btn.backgroundColor = UIColor.blueColor;
//    [btn addTarget:self action:@selector(startSmartVirtualManVideoRecord) forControlEvents:UIControlEventTouchUpInside];
//    [self.view addSubview:btn];

//    // 双人服务端录制
//    UIButton *btn = [UIButton buttonWithType:UIButtonTypeCustom];
//    [btn setTitle:@"开始双人服务端录制" forState:UIControlStateNormal];
//    btn.frame = CGRectMake(100, 50, 200, 100);
//    btn.backgroundColor = UIColor.blueColor;
//    [btn addTarget:self action:@selector(startDoubleTChatVideoRecord) forControlEvents:UIControlEventTouchUpInside];
//    [self.view addSubview:btn];

    UILabel *resultLabel = [UILabel new];
    resultLabel.frame = CGRectMake(10, btn.frame.origin.y + 100, 300, 200);
    resultLabel.numberOfLines = 0;
    self.label = resultLabel;
    [self.view addSubview:resultLabel];

    // 服务端活体识别
    UIButton *btn2 = [UIButton buttonWithType:UIButtonTypeCustom];
    [btn2 setTitle:@"开始服务端活体识别" forState:UIControlStateNormal];
    btn2.frame = CGRectMake(100, 350, 200, 100);
    btn2.backgroundColor = UIColor.blueColor;
    [btn2 addTarget:self action:@selector(startChatLiveDetect) forControlEvents:UIControlEventTouchUpInside];
    [self.view addSubview:btn2];


    UILabel *resultLabel2 = [UILabel new];
    resultLabel2.frame = CGRectMake(10, btn2.frame.origin.y + 100, 300, 200);
    resultLabel2.numberOfLines = 0;
    self.label2 = resultLabel2;
    [self.view addSubview:resultLabel2];

#else

    CGFloat textFieldWidth = 300;
    CGFloat textFieldX = (self.view.TKWidth - textFieldWidth) * 0.5;
    self.textField = [[UITextField alloc] initWithFrame:CGRectMake(textFieldX, 200, textFieldWidth, 80)];
    
//    self.textField.text = @"https://khh5test.csc108.com:8084/auth-living-view/views/index.html#/witnessVirtual";
//    self.textField.text = @"https://khh5test.csc108.com:8084/demo/index.html";
//    self.textField.text = @"https://khtest.csc108.com/index_account_qtdj_index.html?tk_moduleName=openAccount&appType=tdx&channel=tdx&shortcode=BBvY3i&tk_moduleName=openAccount&currentTimeMillis=*************#/account_index";


    self.textField.backgroundColor = [TKUIHelper colorWithHexString:@"9a9a9a"];
    self.textField.clearButtonMode = UITextFieldViewModeWhileEditing;
    self.textField.borderStyle = UITextBorderStyleRoundedRect;
    self.textField.placeholder = @"请输入h5地址。默认https://www.baidu.com";
    [self.view addSubview:self.textField];
    
    // 智能虚拟人录制
    UIButton *btn2 = [UIButton buttonWithType:UIButtonTypeCustom];
    [btn2 setTitle:@"Go" forState:UIControlStateNormal];
    btn2.frame = CGRectMake((self.view.TKWidth - 200) * 0.5, 320, 200, 60);
    btn2.backgroundColor = [TKUIHelper colorWithHexString:@"34a0fb"];
    btn2.layer.cornerRadius = 10;
    btn2.clipsToBounds = YES;
    [btn2 addTarget:self action:@selector(openOpenControllerBy:) forControlEvents:UIControlEventTouchUpInside];
    [self.view addSubview:btn2];


    UILabel *resultLabel2 = [UILabel new];
    resultLabel2.frame = CGRectMake(10, btn2.frame.origin.y + 100, 300, 200);
    resultLabel2.numberOfLines = 0;
    self.label2 = resultLabel2;
    [self.view addSubview:resultLabel2];
    
#endif
}

-(void)goOpenView{
    float left=20;
    UILabel *tipLabel = [[UILabel alloc] initWithFrame:CGRectMake(left, STATUSBAR_HEIGHT, 100, 30)];
    tipLabel.text=@"目标地址:";
    tipLabel.font=[UIFont systemFontOfSize:20];
    tipLabel.textColor=[UIColor blackColor];
    tipLabel.backgroundColor=[UIColor clearColor];
    tipLabel.textAlignment=NSTextAlignmentLeft;
    [self.view addSubview:tipLabel];
    
    //地址输入框
    NSBundle *bundle = [NSBundle bundleWithURL:[[NSBundle mainBundle] URLForResource:[NSString stringWithFormat:@"%@", TK_OPEN_RESOURCE_NAME] withExtension:@"bundle"]];
    
    NSString *plistPath = [bundle pathForResource:@"config" ofType:@"plist"];
    
    NSMutableDictionary *cDic;
    
    if (plistPath) {
        
        cDic = [[NSMutableDictionary alloc] initWithContentsOfFile:plistPath];
    }
    
    
    UITextField *urlTextField=[[UITextField alloc] initWithFrame:CGRectMake(15, tipLabel.TKBottom, self.view.TKWidth-30, 30)];
    urlTextField.text=cDic[@"H5Url"];
    urlTextField.textColor=[UIColor blackColor];
    urlTextField.backgroundColor=[UIColor clearColor];
    urlTextField.tag=1111;
    urlTextField.clearButtonMode=UITextFieldViewModeWhileEditing;
    [self.view addSubview:urlTextField];
    UIView *lineView=[[UIView alloc] initWithFrame:CGRectMake(left, urlTextField.TKBottom, urlTextField.TKWidth, 1)];
    lineView.backgroundColor=[UIColor grayColor];
    [self.view addSubview:lineView];
    
    UIButton *goBtn=[[UIButton alloc] initWithFrame:CGRectMake(left, lineView.TKBottom+10, 100, 50)];
    [goBtn setTitle:@"GO" forState:UIControlStateNormal];
    [goBtn setTitleColor:[UIColor blackColor] forState:UIControlStateNormal];
    [goBtn setBackgroundColor:[UIColor grayColor]];
    [goBtn addTarget:self action:@selector(goOpenCtr) forControlEvents:UIControlEventTouchUpInside];
    [self.view addSubview:goBtn];
}
-(void)goOpenCtr{
    UITextField *urlTextField=(UITextField *)[self.view viewWithTag:1111];
    NSMutableDictionary *cDic=[[NSMutableDictionary alloc] init];
    cDic[@"h5Url"]=urlTextField.text;
    TKOpenController *mCtl=[[TKOpenController alloc] initWithParams:cDic loginInfoParam:@{@"xiaomi_id":@"Cuq55nJEmNioAqC5Bi+8RV9B1PMqG0poFCaU21HnVs5riHcdJm0G+8lhCN22t1kI08t1CwqTJJ6ETxECZf1Rh7XNMSP1TqkpU4SSMz3SlxZw+Ny/baQg2oveCr6IFf5S6RvmN3Uz198rxKC+O9T+yKxmCUuwGW3rSHptvUz+bZU="}];
    [self presentViewController:mCtl animated:YES completion:nil];
}

//结束触摸
- (void)touchesEnded:(NSSet *)touches withEvent:(UIEvent *)event {
    //isExclusiveTouch一个布尔值来指示接收机处理触摸事件。
    //没有触摸_textUser进入if内操作
    if (![(UITextField *)[self.view viewWithTag:1111] isExclusiveTouch]) {
        //resignFirstResponder取消第一响应者状态的。如果对textfield使用的话，那么调用这个方法，textfield的第一响应者状态就会取消，然后键盘就消失了。
        [(UITextField *)[self.view viewWithTag:1111] resignFirstResponder];
    }
}

- (void)testStatistic
{
//    [TKStatisticEventHelper sendEvent:TKPrivateEventVideoWitness subEventName:TKPrivateSubEventVideoWitnessEnqueue result:TKPrivateEventResultCancel eventDic:@{}];
//    [TKStatisticEventHelper sendEvent:TKPrivateEventVideoWitness subEventName:TKPrivateSubEventVideoWitnessPrepareVideo progress:TKPrivateEventProgressStart eventDic:@{}];
//    [TKStatisticEventHelper sendEvent:TKPrivateEventVideoWitness subEventName:TKPrivateSubEventNone result:TKPrivateEventResultCancel eventDic:@{}];
//    [TKStatisticEventHelper sendEvent:TKPrivateEventVideoWitness subEventName:TKPrivateSubEventNone result:TKPrivateEventResultSuccess eventDic:@{}];
//    [TKStatisticEventHelper sendEvent:TKPrivateEventVideoWitness subEventName:TKPrivateSubEventNone result:TKPrivateEventResultFail eventDic:@{@"extParam" : @{@"123": @"456"}}];
//    [TKStatisticEventHelper sendEvent:TKPrivateEventVideoWitness subEventName:TKPrivateSubEventNone result:TKPrivateEventResultError eventDic:@{@"extParams" : @{@"123": @"456"}}];
    
//    NSString *lpMsgBuf =@"123";
//    NSMutableDictionary *tempDic = [NSMutableDictionary dictionary];
//    tempDic[@"message"] = lpMsgBuf;
//    tempDic[@"extParams"] = @{@"eventUrl" : @"https://opt-dev.thinkive.com:15149/kh-stats-server/data/event", @"userId" : @"10000064", @"devicePlatform" : @"ios"};
//    [TKStatisticEventHelper sendEvent:TKPrivateEventVideoWitness subEventName:TKPrivateSubEventVideoWitnessReceiveCMD result:TKPrivateEventResultNone eventDic:tempDic];
    
//    ResultVo *vo = [[TKPluginInvokeCenter shareInstance] callPlugin:@"60071" param:@{@"eventName" : @"test", @"params" : @{@"123" : @"456"}} moduleName:@"mall"];
//    TKLogDebug(@"");
    
}

- (void)getVideoUrl
{
    NSString *url = @"http://regard01.95579.com:3000/wa-engine-server/script/voice/token";
    NSString *voiceUrl = @"https://regard01.95579.com/filecenter/file/tyspzt/2023/08/20230824/response.wav";
    url = [TKCommonUtil url:url appendingParamStr:[NSString stringWithFormat:@"voiceUrl=%@&%@=%@",
                                             voiceUrl,
                                             @"tk-jwt-authorization",
                                             @"Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJzdWIiOiIwMTAwMDEwIiwiaXNzIjoidGhpbmtpdmUiLCJmbG93Tm8iOiIxMjMxMjMiLCJleHAiOjE2OTQxNjM2NDksInVzZXJOYW1lIjoiMTIzMTIzIn0.gaLbNiiLzJuCfHEI3R_d8Nb93DitmYCBWeldQ4kQry0"]];
    
    NSMutableDictionary *params = [NSMutableDictionary dictionary];
    
    NSDictionary *param = [TKStringHelper getUrlParam:voiceUrl];
    TKLogInfo(@"t = %@", [param getStringWithKey:@"t"]);
    
    [[TKTempService new] getTokenWithURL:url param:params callBackFunc:^(ResultVo *resultVo) {
        
        NSArray *results = (NSArray *)resultVo.results;
        if (resultVo.errorNo == 0) {
            NSDictionary *dic = results.firstObject;
            
//            NSDictionary *data = dic[@"data"];
//            NSString *authorization = [NSString stringWithFormat:@"Bearer %@", data[@"authorization"]];
            
            NSString *time = [dic getStringWithKey:@"time"];
            NSString *token = [dic getStringWithKey:@"token"];
            NSString *newVoiceUrl = [TKCommonUtil url:voiceUrl appendingParamStr:[NSString stringWithFormat:@"t=%@&token=%@",time,token]];
            
            NSDictionary *param = [TKStringHelper getUrlParam:newVoiceUrl];
            TKLogInfo(@"t = %@", [param getStringWithKey:@"t"]);
            
//            if (callBackFunc) {
//                callBackFunc(authorization);
//            }
            TKLogInfo(@"请求视频地址成功");
        } else {
            TKLogInfo(@"请求视频地址出错");
        }
    }];
}


#pragma mark - 本地录制
- (void)startLocalVideoRecord
{
    __weak typeof(self) weakSelf = self;
    //麦克风相机权限校验
    [self tkIsMicrophonePermissions:^{
                            
        [weakSelf tkIsCameraPermissions:^{
            
            [[TKCommonUtil shareInstance] getMute:^(BOOL flag) {
                
                dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                    
                    NSMutableDictionary *param = [NSMutableDictionary dictionaryWithDictionary:[self getTipsParam]];
                    param[@"aliYunToken"] = @"default";
                    param[@"aliYunKey"] = @"default";
                    param[@"aliYunUrl"] = @"wss://dev-tchat.thinkive.com:15070/ws/v1";
                    param[@"aliTTSSampleRate"] = @"8000";
//                    param[@"enableHeadphone"] = @"1";
                    param[@"iflyAsrParameter"] = @"extend_params={\"params\":\"eos=10000,bos=10000\"},appid=test1234,url=**************:8185,time_out=10,svc=iat,auf=audio/L16;rate=16000,aue=raw,type=1,uid=660Y5r,mi=40";
                    param[@"iflyTtsParameter"] = @"vid=65620,auf=4,aue=raw,svc=tts,type=1,uid=660Y5r,appid=test1234,tts_data_notify=1,url=**************:8085";
//                    param[@"tipSpeed"] = @"0.3";
//                    param[@"url"] = @"http://regard01.95579.com:3000/auth-common-server/servlet/json";
                    param[@"requestHeaders"] = @{@"tk-jwt-authorization" : [NSString stringWithFormat:@"%@", @"Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJzdWIiOiIwMTAwMDEwIiwiaXNzIjoidGhpbmtpdmUiLCJmbG93Tm8iOiIxODQ1NjQ1NjQ1NSIsImV4cCI6MTcwMTMzNTg1NSwidXNlck5hbWUiOiIxODQ1NjQ1NjQ1NSJ9.08-7nplkQKwO91ori6ipYXXWX76CMPOhbdvYBB86cRI"], @"test":@"123"};
                    param[@"voiceTokenUrl"] = @"http://regard01.95579.com:3000/wa-engine-server/script/voice/token";
                    param[@"startAutoRecordTime"] = @"5";
                    param[@"resultType"] = @"2";
                    param[@"continueOnAnswerTimeout"] = @"1";
                    param[@"maxFailureCountPerAsrNoVoice"] = @"2";
                    param[@"totalFailureCount"] = @"1";
                    
                    [[TKPluginInvokeCenter shareInstance] callPlugin:@"60026" param:param
                                                          moduleName:@"open" isH5:NO callBackFunc:^(NSMutableDictionary *result) {
                        TKLogInfo(@"result %@", result);
                    }];
                });
            }];
        }];
                                    
    }];
}


- (void)startDoubleVideoRecord
{
    __weak typeof(self) weakSelf = self;
    //麦克风相机权限校验
    [self tkIsMicrophonePermissions:^{
                            
        [weakSelf tkIsCameraPermissions:^{
            
            [[TKCommonUtil shareInstance] getMute:^(BOOL flag) {
                
                dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                    
                    NSMutableDictionary *param = [NSMutableDictionary dictionaryWithDictionary:[self getTipsParam]];
                    param[@"aliYunKey"] = @"default";
                    param[@"aliYunToken"] = @"default";
                    param[@"aliYunUrl"] = @"wss://dev-tchat.thinkive.com:15070/ws/v1";
                    
                    [[TKPluginInvokeCenter shareInstance] callPlugin:@"60089"
                                                               param:param
                                                          moduleName:@"open"];
                });
            }];
        }];
                                    
    }];
}

#pragma mark - 智能虚拟人
- (void)startSmartVirtualManVideoRecord
{
    __weak typeof(self) weakSelf = self;
    //麦克风相机权限校验
    [self tkIsMicrophonePermissions:^{
                            
        [weakSelf tkIsCameraPermissions:^{
            
            [[TKCommonUtil shareInstance] getMute:^(BOOL flag) {
//                if (flag) {
//                    UIAlertView *alert=[[UIAlertView alloc] initWithTitle:@"请勿将手机静音" message:@"请将手机开启响铃模式" delegate:nil cancelButtonTitle:@"确定" otherButtonTitles:nil];
//                    [alert show];
//                }else{
                    // 获取authorization
                    [weakSelf sendRequestToGetTokenWithCallBackFunc:^(NSString *authorization) {
                        
                        // 模拟服务端录制，内部调整为本地智能单向录制
                        [weakSelf openSmartVirtualManVideoRecord:authorization];
//                        [weakSelf openOpenControllerBy:authorization];
                    }];
//                    [weakSelf openSmartVirtualManVideoRecord:@""];
//                }
            }];
        }];
                                    
    }];
}

- (void)openOpenControllerBy:(NSString *)authorization
{
    
    NSMutableDictionary *pDic = [[NSMutableDictionary alloc] init];

    // url
    [pDic setValue:[TKStringHelper isNotEmpty:self.textField.text] ? self.textField.text : @"https://www.baidu.com" forKey:@"h5Url"];//替换券商部署的h5地址
  
    NSMutableDictionary *loginInfo=[[NSMutableDictionary alloc] init];
    loginInfo[@"flow_no"] = @"121212"; // 仅展示录制页面
    // 授权
    loginInfo[@"tk-jwt-authorization"] = [TKStringHelper isEmpty:authorization] ? @"" : authorization;
    loginInfo[@"sub"] = @"123"; // 业务参数
    loginInfo[@"maxFailureCountPerFaceCompare"] = @"999"; // 业务参数
    loginInfo[@"tips"] = [TKDataHelper dictionaryToJson:[self getTipsParam]];
    
    TKOpenController *mCtl = [[TKOpenController alloc] initWithParams:pDic loginInfoParam:loginInfo];
    mCtl.statusBarBgColor = [UIColor whiteColor];//状态栏颜色
    mCtl.oDelegate = self;
    mCtl.statusBarStyle = UIStatusBarStyleDefault;//状态栏样式
    mCtl.isImmersion = YES;

    [self presentViewController:mCtl animated:YES completion:nil];
}

- (void)openSmartVirtualManVideoRecord:(NSString *)authorization
{
    NSMutableDictionary *param = [NSMutableDictionary dictionaryWithDictionary:[self getTipsParam]];
    // 请求入参
    param[@"uploadTipString"]=@"请您确认：我是XXX，已知晓证券市场风险，已阅读并充分理解网上开户协议条款，自愿选择在思迪证券开立账户，并承诺所提供的信息及证件真实、合法、有效。"; //确认上传提示内容，为空情况下确认上传不做提示
    //人脸在框检测地址，不穿的话人脸检测综合功能号和人脸在框检测功能号也不用穿不做在框检测
//    param[@"url"] = @"https://soft.thinkive.com:15098/auth-common-server/servlet/json";
    param[@"url"] = @"https://khh5test.csc108.com:8084/auth-common-server/servlet/json";

    //人脸检测综合功能号
    param[@"faceDetectCompositeFuncNo"] = @"15000059";
//    param[@"mainColor"] = @"#EC2E31";
    param[@"maxFailureCountPerFaceCompare"] = @"9999";
    param[@"tips"] = [TKDataHelper dictionaryToJson:[self getTipsParam]];
    param[@"totalNoVoiceFailureCount"] = @"1";
    
    
    // !!!
    // 请求头参数
    param[@"requestHeaders"] = @{@"tk-jwt-authorization" : [NSString stringWithFormat:@"%@", authorization]};

    // 创建tchat录制控制器
    TKSmartVirtualManViewController *oneWayCtr = [[TKSmartVirtualManViewController alloc] initWithParam:param];
    oneWayCtr.delegate = self;
    [[TKAppEngine shareInstance].rootViewCtr.currentViewCtrl presentViewController:oneWayCtr animated:YES completion:nil];
}

- (void)openSmartVideoRecord:(NSString *)authorization
{
//    NSMutableDictionary *param = [NSMutableDictionary dictionaryWithDictionary:[self getTipsParam]];
//    // 请求入参
//    param[@"uploadTipString"]=@"请您确认：我是XXX，已知晓证券市场风险，已阅读并充分理解网上开户协议条款，自愿选择在思迪证券开立账户，并承诺所提供的信息及证件真实、合法、有效。"; //确认上传提示内容，为空情况下确认上传不做提示
//    //人脸在框检测地址，不穿的话人脸检测综合功能号和人脸在框检测功能号也不用穿不做在框检测
//    param[@"url"] = @"https://soft.thinkive.com:15098/auth-common-server/servlet/json";
////    param[@"url"] = @"https://khh5test.csc108.com:8084/auth-common-server/servlet/json";
//
//    //人脸检测综合功能号
//    param[@"faceDetectCompositeFuncNo"] = @"15000059";
//    param[@"mainColor"] = @"#EC2E31";
//    param[@"maxFailureCountPerFaceCompare"] = @"9999";
//    param[@"tips"] = [TKDataHelper dictionaryToJson:[self getTipsParam]];
//
//    // !!!
//    // 请求头参数
//    param[@"requestHeaders"] = @{@"tk-jwt-authorization" : [NSString stringWithFormat:@"%@", authorization]};
//
//    // 创建tchat录制控制器
//    TKSmartVirtualManViewController *oneWayCtr = [[TKSmartVirtualManViewController alloc] initWithParam:param];
//    oneWayCtr.delegate = self;
//    [[TKAppEngine shareInstance].rootViewCtr.currentViewCtrl presentViewController:oneWayCtr animated:YES completion:nil];
}

- (NSMutableDictionary *)getTipsParam
{
    NSMutableDictionary *param = [NSMutableDictionary dictionary];
    
    //  提示语音
    NSMutableArray *beforeVideoArray = [[NSMutableArray alloc] init];
    NSMutableDictionary *beforeVideoDic1 = [[NSMutableDictionary alloc] init];
    //提示问题会语音播报
    beforeVideoDic1[@"fileSource"]=@"2";
    beforeVideoDic1[@"fileName"]=@"1.mp4";
    beforeVideoDic1[@"tipContent"]=@"宋先生您好，欢迎来到中信建投证券开户。";
//    beforeVideoDic1[@"tipContent"]=@"您好";
    beforeVideoDic1[@"tipTitle"] = @"<span style=\"font-size:16px;\" color=\"#ff5900\">宋先生您好，欢迎来到中信建投证券开户。</span>";
    [beforeVideoArray addObject:beforeVideoDic1];

    NSMutableDictionary *beforeVideoDic2 = [[NSMutableDictionary alloc] init];
    //提示问题会语音播报
    beforeVideoDic2[@"fileSource"]=@"1";
    beforeVideoDic2[@"fileName"]=@"2.mp4";
    beforeVideoDic2[@"tipContent"]=@"请您尽量选择周边环境安静的场所进行录制。视频过程中，请您对准手机屏幕，保持全脸在人像框内，确保其他人不出现在视频中。";
//    beforeVideoDic2[@"tipContent"]=@"请您";
    beforeVideoDic2[@"tipTitle"]=@"<font style=\"font-size:30px;\" color='#FF5900'>请您尽量选择周边环境安静的场所进行录制。视频过程中，请您对准手机屏幕，保持全脸在人像框内，确保其他人不出现在视频中。<\/font>";
//    [beforeVideoArray addObject:beforeVideoDic2];

    NSMutableDictionary *beforeVideoDic3 = [[NSMutableDictionary alloc] init];
    //提示问题会语音播报
    beforeVideoDic3[@"fileSource"]=@"1";
    beforeVideoDic3[@"fileName"]=@"3.mp4";
    beforeVideoDic3[@"tipContent"]=@"接下来，我将问您两个问题，请您根据实际情况进行回答。";
    beforeVideoDic3[@"tipTitle"]=@"<font style=\"font-size:25px;\" color='#FF5900'>接下来，我将问您两个问题，请您根据实际情况进行回答。<\/font>";
//    [beforeVideoArray addObject:beforeVideoDic3];
    
    NSMutableDictionary *beforeVideoDic4 = [[NSMutableDictionary alloc] init];
    //提示问题会语音播报
    beforeVideoDic4[@"fileSource"]=@"1";
    beforeVideoDic4[@"fileName"]=@"4.mp4";
    beforeVideoDic4[@"tipContent"] = @"如您已准备好，请点击下方“开始录制”按钮录制视频。";
    beforeVideoDic4[@"tipTitle"] = @"<font style=\"font-size:13px;\" color='#FFFFFF'>如您已准备好，请点击下方<font style=\"font-size:22px;\" color='#FF5900'>“开始录制”<\/font>按钮录制视频。<\/font>";
//    [beforeVideoArray addObject:beforeVideoDic4];
    
//    param[@"beforeVideoArray"] = beforeVideoArray;
//    param[@"beforeVideoArray"] = [TKDataHelper arrayToJson:beforeVideoArray];

    //问题数组
    NSMutableArray *questionArray = [[NSMutableArray alloc] init];
    NSMutableDictionary *questionDic1 = [[NSMutableDictionary alloc] init];
    //提示问题会语音播报
    questionDic1[@"fileSource"]=@"2";
    questionDic1[@"fileName"]=@"5.mp4";
    questionDic1[@"tipContent"]=@"第一个问题，请问您是否是宋朝增本人在中信建投证券申请开户？请用“是”或“不是”回答。";
//    questionDic1[@"tipContent"]=@"第一";
//    questionDic1[@"tipTitle"]=@"<font style=\"font-size:22px;\" color='#FFFFFF'>第一个问题，请问您是否是宋朝增本人在中信建投证券申请开户？请用“是”或“不是”回答。第一个问题，请问您是否是宋朝增本人在中信建投证券申请开户？请用“是”或“不是”回答。第一个问题，请问您是否是宋朝增本人在中信建投证券申请开户？请用“是”或“不是”回答。第一个问题，请问您是否是宋朝增本人在中信建投证券申请开户？请用“是”或“不是”回答。<\/font>";   // 回答提示
    questionDic1[@"prompt"] = @"<font style=\"font-size:22px;\" color='#FFFFFF'><font style=\"font-size:22px;\" color=\"#FF5900\">“是”<\/font>或<font style=\"font-size:22px;\" color=\"#FF5900\">“不是”<\/font><\/font>";   // 回答提示
//    questionDic1[@"tipContent"]=@"十二、投资者需关注创业板交易的单笔申报限制数量、有效竞价范围等与深圳证券交易所其他板块交易存在的差异，避免产生无效申报。十三、投资者需关注创业板交易方式包括竞价交易、盘后定价交易及大宗交易，不同交易方式的交易时间、申报要求、成交原则等存在差异。";
//    questionDic1[@"tipTitle"] = @"<font style=\"font-size:13px;\" color='#FFFFFF'>如您已准备好，请点击下方如您已准备好，请点击下方如您已准备好，请点击下方如您已准备好，请点击下方如您已准备好，请点击下方如您已准备好，请点击下方如您已准备好，请点击下方如您已准备好，请点击下方如您已准备好，请点击下方如您已准备好，请点击下方如您已准备好，请点击下方如您已准备好，请点击下方如您已准备好，请点击下方如您已准备好，请点击下方如您已准备好，请点击下方如您已准备好，请点击下方如您已准备好，请点击下方如您已准备好，请点击下方如您已准备好，请点击下方如您已准备好，请点击下方如您已准备好，请点击下方<font style=\"font-size:22px;\" color='#FF5900'>“开始录制”<\/font>按钮录制视频。<\/font>";
//    questionDic1[@"tipContentList"]=@[
//        @"十二、投资者需关注创业板交易的单笔申报限制数量、有效竞价范围等与深圳证券交易所其他板块交易存在的差异，避免产生无效申报。",
//        @"十三、投资者需关注创业板交易方式包括竞价交易、盘后定价交易及大宗交易，不同交易方式的交易时间、申报要求、成交原则等存在差异。",
//        @"十四、按照《创业板首次公开发行股票注册管理办法（试行）》发行上市的股票，上市首日即可作为融资融券标的，投资者应注意相关风险。",
//        @"十五、创业板股票交易盘中临时停牌情形和严重异常波动股票核查制度与深圳证券交易所其他板块规定不同，投资者应当关注与此相关的风险。",
//    ];
//    questionDic1[@"tipContentList"]=@[
//        @"第一个",
//        @"第二个",
//        @"第三个",
//        @"第四个",
//    ];
    questionDic1[@"standardans"] = @"^(是的|是|对的|嗯)$";   //需要回答的问题，正确回答的正则
    questionDic1[@"failans"] = @"^(不是的|不是)$"; //需要回答的问题，错误回答的正则
    questionDic1[@"waitTime"] = @"5";  //该问题预留的回答时间
    questionDic1[@"waitTime"] = @"5";  //该问题预留的回答时间
    questionDic1[@"questionOneWordSpeed"] = @"0.1";  //文字滚动速度
    questionDic1[@"tipSpeed"] = @"1.2";  //阿里语音合成速度
    // 错误提示
    NSMutableDictionary *errorTip1 = [[NSMutableDictionary alloc] init];
    errorTip1[@"fileSource"]=@"1";
    errorTip1[@"fileName"]=@"8.mp4";
    errorTip1[@"tipContent"]=@"您好，由于办理开立业务需本人申请，请客户本人进行视频录制。稍后请您点击屏幕下方“开始录制”按钮重新录制。";
    errorTip1[@"tipTitle"]=@"<font style=\"font-size:22px;\" color='#FFFFFF'>您好，由于办理开立业务需本人申请，请客户本人进行视频录制。稍后请您点击屏幕下方“开始录制”按钮重新录制。<\/font>";
    questionDic1[@"errorTip"] = errorTip1;  //错误提示
    // 没有声音提示
    NSMutableDictionary *noVoiceTip1 = [[NSMutableDictionary alloc] init];
    noVoiceTip1[@"fileSource"]=@"1";
    noVoiceTip1[@"fileName"]=@"9.mp4";
    noVoiceTip1[@"tipContent"]=@"您好，未听清您的回复，请您用“是”或“否”重新回答。";
//    noVoiceTip1[@"tipTitle"]=@"<font style=\"font-size:22px;\" color='#FFFFFF'>您好，未听清您的回复，请您用“是”或“否”重新回答。<\/font>";
    questionDic1[@"noVoiceTip"] = noVoiceTip1;  //错误提示
    // 正确回答提示
//    NSMutableDictionary *standardansTip1 = [[NSMutableDictionary alloc] init];
//    standardansTip1[@"fileSource"]=@"1";
//    standardansTip1[@"fileName"]=@"6.mp4";
//    standardansTip1[@"tipContent"]=@"好的。谢谢您";
//    questionDic1[@"standardansTip"] = standardansTip1;
    
    [questionArray addObject:questionDic1];

    NSMutableDictionary *questionDic2 = [[NSMutableDictionary alloc] init];
    //提示问题会语音播报
    questionDic2[@"fileSource"]=@"1";
    questionDic2[@"fileName"]=@"6.mp4";
    questionDic2[@"tipContent"]=@"好的。谢谢您。第二个问题，请问您是否已知晓证券市场风险，已阅读并充分理解开户协议条款？请用“理解”或“不理解”回答。";
    questionDic2[@"tipContent"]=@"好的。第二";
    questionDic2[@"tipTitle"]=@"<font style=\"font-size:22px;\" color='#FFFFFF'>好的。谢谢您。第二个问题，请问您是否已知晓证券市场风险，已阅读并充分理解开户协议条款？请用“理解”或“不理解”回答。<\/font>";
    questionDic2[@"prompt"] = @"<div style=\"font-size:22px;\"style=\"font-size:22px;\" color='#FFFFFF'><font style=\"font-size:22px;\"style=\"font-size:22px;\" color=\"#FF5900\">“理解”<\/font>或<font style=\"font-size:22px;\"color=\"#FF5900\">“不理解”<\/font><\/font>";   // 回答提示
    questionDic2[@"standardans"] = @"^(是的|是|对的|理解)$";   //需要回答的问题，正确回答的正则
    questionDic2[@"failans"] = @"^(不是的|不是|不理解)$"; //需要回答的问题，错误回答的正则
    questionDic2[@"waitTime"] = @"5";  //该问题预留的回答时间
    // 错误提示
    NSMutableDictionary *errorTip2 = [[NSMutableDictionary alloc] init];
    errorTip2[@"fileSource"]=@"1";
    errorTip2[@"fileName"]=@"10.mp4";
    errorTip2[@"tipContent"]=@"您好，请您确认已知晓证券市场风险及已阅读且充分理解网上开户协议条款后，稍后请您点击屏幕下方“开始录制”按钮重新录制。";
//    errorTip2[@"tipTitle"]=@"<font style=\"font-size:22px;\"color='#FFFFFF'>您好，请您确认已知晓证券市场风险及已阅读且充分理解网上开户协议条款后，稍后请您点击屏幕下方“开始录制”按钮重新录制。<\/font>";
    questionDic2[@"errorTip"] = errorTip2;  //错误提示
    // 没有声音提示
    NSMutableDictionary *noVoiceTip2 = [[NSMutableDictionary alloc] init];
    noVoiceTip2[@"fileSource"]=@"1";
    noVoiceTip2[@"fileName"]=@"11.mp4";
    noVoiceTip2[@"tipContent"]=@"您好，未听清您的回复，请您用“理解”或“不理解”重新回答。";
//    noVoiceTip2[@"tipTitle"]=@"<font style=\"font-size:22px;\"color='#FFFFFF'>您好，未听清您的回复，请您用“理解”或“不理解”重新回答。<\/font>";
    questionDic2[@"noVoiceTip"] = noVoiceTip2;  //错误提示
    // 正确回答提示
//    NSMutableDictionary *standardansTip2 = [[NSMutableDictionary alloc] init];
//    standardansTip2[@"fileSource"]=@"1";
//    standardansTip2[@"fileName"]=@"zxjt_poc_normal5";
//    standardansTip2[@"tipContent"]=@"好的。";
//    questionDic2[@"standardansTip"] = standardansTip2;  //错误提示
    
//    [questionArray addObject:questionDic2];
    
    
    NSMutableDictionary *questionDic3 = [[NSMutableDictionary alloc] init];
    //提示问题会语音播报
    questionDic3[@"fileSource"]=@"2";
    questionDic3[@"fileName"]=@"6.mp4";
//    questionDic3[@"tipContent"]=@"请展示您本人的身份证明文件原价及公司营业执照。";
    questionDic3[@"tipContent"]=@"请展示111";
//    questionDic3[@"tipTitle"]=@"请展示您本人的身份证明文件原价及公司营业执照。";
    questionDic3[@"prompt"] = @"展示完成后，请点击<font style=\"font-size:20px;\"color='#0000FF'>\"继续播放\"</font>按钮，进行下一条话术播放";   // 回答提示
    questionDic3[@"standardans"] = @"^((?!不).)*[是对4事四思室市氏士似]((?!不).)*$";   //需要回答的问题，正确回答的正则
    questionDic3[@"failans"] = @"^(不是的|不是|不理解)$"; //需要回答的问题，错误回答的正则
    questionDic3[@"waitTime"] = @"5";  //该问题预留的回答时间
//    questionDic3[@"pauseTime"] = @"10";  // 播报完停顿时间（秒）。默认0。v5.3.0版本后支持
//    questionDic3[@"longPauseTime"] = @"1";  // 是否长期停顿状态，1：是 2|空：否。默认0。v5.3.0版本后支持
//    questionDic3[@"longPauseBtn"] = @"继续播放111";  // 长期停顿显示按钮文案，默认“继续播报”。v5.3.0版本后支持

    // 错误提示
    NSMutableDictionary *errorTip3 = [[NSMutableDictionary alloc] init];
    errorTip3[@"fileSource"]=@"1";
    errorTip3[@"fileName"]=@"10.mp4";
    errorTip3[@"tipContent"]=@"您好，请您确认已知晓证券市场风险及已阅读且充分理解网上开户协议条款后，稍后请您点击屏幕下方“开始录制”按钮重新录制。";
//    errorTip3[@"tipTitle"]=@"<font style=\"font-size:22px;\"color='#FFFFFF'>您好，请您确认已知晓证券市场风险及已阅读且充分理解网上开户协议条款后，稍后请您点击屏幕下方“开始录制”按钮重新录制。<\/font>";
    questionDic3[@"errorTip"] = errorTip3;  //错误提示
    // 没有声音提示
    NSMutableDictionary *noVoiceTip3 = [[NSMutableDictionary alloc] init];
    noVoiceTip3[@"fileSource"]=@"1";
    noVoiceTip3[@"fileName"]=@"11.mp4";
    noVoiceTip3[@"tipContent"]=@"您好，未听清您的回复，请您用“理解”或“不理解”重新回答。";
//    noVoiceTip3[@"tipTitle"]=@"<font style=\"font-size:22px;\"color='#FFFFFF'>您好，未听清您的回复，请您用“理解”或“不理解”重新回答。<\/font>";
    questionDic3[@"noVoiceTip"] = noVoiceTip3;  //错误提示
    // 正确回答提示
//    NSMutableDictionary *standardansTip3 = [[NSMutableDictionary alloc] init];
//    standardansTip3[@"fileSource"]=@"1";
//    standardansTip3[@"fileName"]=@"zxjt_poc_normal5";
//    standardansTip3[@"tipContent"]=@"好的。";
//    questionDic2[@"standardansTip"] = standardansTip3;  //错误提示
    
//    [questionArray addObject:questionDic3];
    
    NSMutableDictionary *questionDic4 = [[NSMutableDictionary alloc] init];
    //提示问题会语音播报
    questionDic4[@"fileSource"]=@"2";
    questionDic4[@"fileName"]=@"6.mp4";
//    questionDic4[@"tipContent"]=@"请展示您本人的身份证明文件原价及公司营业执照。";
    questionDic4[@"tipContent"]=@"请展示";
//    questionDic4[@"tipTitle"]=@"请展示您本人的身份证明文件原价及公司营业执照。";
    questionDic4[@"prompt"] = @"展示完成后，请点击<font style=\"font-size:20px;\"color='#0000FF'>\"继续播放\"</font>按钮，进行下一条话术播放";   // 回答提示
//    questionDic4[@"standardans"] = @"^(是的|是|对的|理解)$";   //需要回答的问题，正确回答的正则
//    questionDic4[@"failans"] = @"^(不是的|不是|不理解)$"; //需要回答的问题，错误回答的正则
//    questionDic4[@"waitTime"] = @"5";  //该问题预留的回答时间
    questionDic4[@"pauseTime"] = @"3";  // 播报完停顿时间（秒）。默认0。v5.3.0版本后支持
    questionDic4[@"longPauseTime"] = @"0";  // 是否长期停顿状态，1：是 2|空：否。默认0。v5.3.0版本后支持
    questionDic4[@"longPauseBtn"] = @"继续播放";  // 长期停顿显示按钮文案，默认“继续播报”。v5.3.0版本后支持
    
    [questionArray addObject:questionDic4];
    
    NSMutableDictionary *questionDic5 = [[NSMutableDictionary alloc] init];
    //提示问题会语音播报
    questionDic5[@"fileSource"]=@"1";
    questionDic5[@"fileName"]=@"6.mp4";
    questionDic5[@"tipContent"]=@"请用理解";
    questionDic5[@"prompt"] = @"<span style=\"font-size:22px; color='#FFFFFF'><font style=\"font-size:22px; color=\"#FF5900\">“理解”</font>或<font style=\"font-size:22px;\"color=\"#FF5900\">“不理解”</font></span>";   // 回答提示
    questionDic5[@"standardans"] = @"^(是的|是|对的|理解)$";   //需要回答的问题，正确回答的正则
    questionDic5[@"failans"] = @"^(不是的|不是|不理解)$"; //需要回答的问题，错误回答的正则
    questionDic5[@"waitTime"] = @"5";  //该问题预留的回答时间
    
//    [questionArray addObject:questionDic5];
    

    NSMutableDictionary *questionDic6 = [[NSMutableDictionary alloc] init];
    //提示问题会语音播报
    questionDic6[@"type"]=@"0";
//    questionDic6[@"readPromptBtnTitle"]=@"开始阅读1";
    questionDic6[@"tipContent"]=@"现在向您展示关干盘京盛信4期私慕证券投资基金的相关情况及风险揭示信息。请您点击下方“开始朗读”按钮。";
    questionDic6[@"readContent"]=@"<font style=\"text-align: center; font-size: 22px;color:black\">您购买的产品盘京盛信4期B私募证券投资基，管理人是上海盘京投资管理中心（有限合伙人），托管人是中信证券，代销机构是长江证券，产品购买起点100万元（不含申购费用），产品存续期10年，每半年开放一次（产品成立日为2019年6月20日），详细信息请查阅合同“八、基金的申购、赎回及转让”您购买的产品盘京盛信4期B私募证券投资基，管理人是上海盘京投资管理中心（有限合伙人），托管人是中信证券，代销机构是长江证券，产品购买起点100万元（不含申购费用），产品存续期10年，每半年开放一次（产品成立日为2019年6月20日），详细信息请查阅合同“八、基金的申购、赎回及转让”<\/font>";
//    questionDic6[@"readConfirmBtnTitle"]=@"我已阅读1";
//    questionDic6[@"readTitle"]=@"请完整阅读1";
//    questionDic6[@"readTime"]=@"9";
//    [questionArray addObject:questionDic6];
    
    NSMutableDictionary *questionDic7 = [[NSMutableDictionary alloc] init];
    //提示问题会语音播报
    questionDic7[@"type"]=@"2"; // 0-一问一答；1-朗读 2-阅读
//    questionDic7[@"readPromptBtnTitle"]=@"开始朗读1";
    questionDic7[@"tipContent"]=@"请您点击下方“开始朗读”按钮。并使用普通话朗读";
    questionDic7[@"readContent"]=@"<font style=\"text-align: center; font-size: 22px;color:black\">我是宋小迪，我已知晓证券市场风险，已阅读且充分理解开户协议条款并自愿在思迪证券开户。<\/font>";
//    questionDic7[@"readConfirmBtnTitle"]=@"我已朗读1";
//    questionDic7[@"readTitle"]=@"请使用普通话朗读1";
//    questionDic7[@"readTime"]=@"8";
//    [questionArray addObject:questionDic7];
    
    NSMutableDictionary *questionDic8 = [[NSMutableDictionary alloc] init];
    //提示问题会语音播报
    questionDic8[@"isNeedVoiceToken"]=@"0";
    questionDic8[@"type"]=@"0"; // 0-一问一答；1-朗读 2-阅读
//    questionDic8[@"tipSpeed"]=@"1.5";
//    questionDic8[@"readPromptBtnTitle"]=@"开始朗读1";
//    questionDic8[@"tipContent"]=@"1.您购买的产品安诚数盈长兴私募证券投资基金，管理人是浙江安诚数盈投资管理有限公司，托管人是长江证券，代销机构是长江证券，产品购买起点100万元（不含手续费），固定存续期为10年，固定开放日为：本基金开放日为每个交易日。本基金每笔份额锁定期为36个月，即自认购/申购之日（认购日为基金成立日、申购日 为申购所对应份额确认日）起36个月（每月按30个自然日计算）内不允许基金份额持有人进行赎回。详细信息请您查阅合同“七、私募基金的申购、赎回和转让”";
    questionDic8[@"tipContent"]=@"1.您购买的产品安诚数盈长兴私募证券投资基金，管理人是浙江安诚数盈投资管理有限公司，托管人是长江证券，代销机构是长江证券，产品购买起点100万元（不含手续费），固定存续期为10年，固定开放日为：本基金开放日为每个交易日。本基金每笔份额锁定期为36个月，即自认购/申购之日（认购日为基金成立日、申购日 为申购所对应份额确认日）起36个月（每月按30个自然日计算）内不允许基金份额持有人进行赎回。详细信息请您查阅合同“七、私募基金的申购、赎回和转让”。</p>";
    questionDic8[@"tipTitle"]=@"<p>1.您购买的产品安诚数盈长兴私募证券投资基金，管理人是浙江安诚数盈投资管理有限公司，托管人是长江证券，代销机构是长江证券，产品购买起点100万元（不含手续费），固定存续期为10年，固定开放日为：本基金开放日为每个交易日。本基金每笔份额锁定期为36个月，即自认购/申购之日（认购日为基金成立日、申购日 为申购所对应份额确认日）起36个月（每月按30个自然日计算）内不允许基金份额持有人进行赎回。详细信息请您查阅合同“七、私募基金的申购、赎回和转让”。</p>";
//    questionDic8[@"voiceUrl"]=@"https://regard01.95579.com/filecenter/file/tyspzt/2023/08/20230824/response.wav";
//    questionDic8[@"readConfirmBtnTitle"]=@"我已朗读1";
//    questionDic8[@"readTitle"]=@"请使用普通话朗读1";
//    questionDic8[@"readTime"]=@"8";
//    [questionArray addObject:questionDic8];
    
    NSMutableDictionary *questionDic9 = [[NSMutableDictionary alloc] init];
    //提示问题会语音播报
    questionDic9[@"isNeedVoiceToken"]=@"1";
    questionDic9[@"type"]=@"0"; // 0-一问一答；1-朗读 2-阅读
    questionDic9[@"tipSpeed"]=@"1.2";
//    questionDic9[@"wordSpeed"]=@"0.5";
//    questionDic9[@"readPromptBtnTitle"]=@"开始朗读1";
//    questionDic9[@"tipContent"]=@"请您确认";
//    questionDic9[@"tipContent"]=@"请您确认：购买兴聚财富9号私募证券投资基金是您本人的真实意愿，您已阅读了产品合同及风险揭示书，清楚长江证券与产品管理人的委托代理关系，对产品要素、风险收益特征、风险等级匹配情况、业务办理流程、客户权益等信息均已了解。以上情况您是否确认已经清楚";
//    questionDic9[@"tipTitle"]=@"<p>请您确认：购买兴聚财富9号私募证券投资基金是您本人的真实意愿，您已阅读了产品合同及风险揭示书，清楚长江证券与产品管理人的委托代理关系，对产品要素、风险收益特征、风险等级匹配情况、业务办理流程、客户权益等信息均已了解。以上情况您是否确认已经清楚</p>";
//    questionDic9[@"tipPlayitem"]=@"{\"0\":{\"e\":\"91\",\"e2\":\"31\",\"s\":\"0\",\"s2\":\"0\"},\"148912\":{\"e\":\"139\",\"e2\":\"47\",\"s\":\"91\",\"s2\":\"31\"},\"221188\":{\"e\":\"199\",\"e2\":\"67\",\"s\":\"139\",\"s2\":\"47\"},\"314806\":{\"e\":\"322\",\"e2\":\"108\",\"s\":\"199\",\"s2\":\"67\"},\"518022\":{\"e\":\"361\",\"e2\":\"121\",\"s\":\"322\",\"s2\":\"108\"}}";
//    questionDic9[@"voiceUrl"]=@"http://regard01.95579.com:3000/wa-engine-server/file/download?mode=1&src=/file-wa/voice/20231229/0c03dc149c9f420a9ef3c6d67ba0a18d.wav&tk-jwt-authorization=Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJzdWIiOiIwMTAwMDEwIiwiaXNzIjoidGhpbmtpdmUiLCJmbG93Tm8iOiIxODQ1NjQ1NjQ1NSIsImV4cCI6MTcwNDI2MjE4MiwidXNlck5hbWUiOiIxODQ1NjQ1NjQ1NSJ9.p2XG8kf5OmuWSwy6mB7q0RonZ9sh_TB4bppHb20KlTo";
    questionDic9[@"prompt"] = @"请用“是”或“不是”回答请用“是”或“不是”回答请用“是”或“不是”回答请用“是”或“不是”回答请用“是”或“不是”回答请用“是”或“不是”回答";   // 回答提示
    questionDic9[@"standardans"] = @"是|是的|四|事";   //需要回答的问题，正确回答的正则
    questionDic9[@"failans"] = @"不是|否|不对|错"; //需要回答的问题，错误回答的正则
    questionDic9[@"waitTime"] = @"10";  //该问题预留的回答时间
    questionDic9[@"readConfirmBtnTitle"]=@"我已朗读1";
    questionDic9[@"readTitle"]=@"请使用普通话朗读1";
    questionDic9[@"readTime"]=@"8";
    questionDic9[@"isNeedVoiceToken"]=@"0";
    // 没有声音提示
    NSMutableDictionary *noVoiceTip9 = [[NSMutableDictionary alloc] init];
    noVoiceTip9[@"fileSource"]=@"1";
    noVoiceTip9[@"fileName"]=@"11.mp4";
    noVoiceTip9[@"tipContent"]=@"您好，未听清您的回复，请您用“理解”或“不理解”重新回答。";
//    noVoiceTip9[@"tipTitle"]=@"<font style=\"font-size:22px;\"color='#FFFFFF'>您好，未听清您的回复，请您用“理解”或“不理解”重新回答。<\/font>";
    questionDic9[@"noVoiceTip"] = noVoiceTip9;  //错误提示
    
//    [questionArray addObject:questionDic9];
    
    NSMutableDictionary *questionDic10 = [[NSMutableDictionary alloc] init];
    //提示问题会语音播报
    questionDic10[@"isNeedVoiceToken"]=@"1";
    questionDic10[@"type"]=@"1"; // 0-一问一答；1-朗读 2-阅读
    questionDic10[@"tipSpeed"]=@"<null>";
//    questionDic10[@"readPromptBtnTitle"]=@"开始朗读1";
//    questionDic10[@"tipContent"]=@"请朗读以下文案";
//    questionDic10[@"tipTitle"]=@"<p>请朗读以下文案<br/></p>";
//    questionDic10[@"voiceUrl"]=@"https://regard01.95579.com/filecenter/file/tyspzt/2023/08/20230824/response.wav";
//    questionDic10[@"readConfirmBtnTitle"]=@"我已朗读1";
    questionDic10[@"readContent"]=@"<p>我已阅读了产品合同及风险揭示书，清楚长江证券与产品管理人的委托代理关系，对产品要素、风险收益特征、风险等级匹配情况、业务办理流程、客户权益等信息均已了解<br/></p>";
//    questionDic10[@"readTitle"]=@"请使用普通话朗读1";
    questionDic10[@"readTime"]=@"5";
//    [questionArray addObject:questionDic10];
//    param[@"questionArray"] = questionArray;
    param[@"questionArray"] = [TKDataHelper arrayToJson:questionArray];
    
    //  结束提示语音
    NSMutableArray *afterVideoArray = [[NSMutableArray alloc] init];
    NSMutableDictionary *afterVideoDic1 = [[NSMutableDictionary alloc] init];
    //提示问题会语音播报
    afterVideoDic1[@"fileSource"]=@"1";
    afterVideoDic1[@"fileName"]=@"7.mp4";
    afterVideoDic1[@"tipContent"]=@"好的，视频录制完毕，感谢您的配合。";
//    afterVideoDic1[@"tipTitle"]=@"<font style=\"font-size:22px;\"color='#FFFFFF'>好的，视频录制完毕，感谢您的配合。<\/font>";
    [afterVideoArray addObject:afterVideoDic1];
    
//    param[@"afterVideoArray"] = afterVideoArray;
//    param[@"afterVideoArray"] = [TKDataHelper arrayToJson:afterVideoArray];
    
    //  全局错误提示语音
    NSMutableDictionary *errorTipDic = [[NSMutableDictionary alloc] init];
    NSMutableDictionary *overNoVoiceCountTip = [[NSMutableDictionary alloc] init];
    // 多次错误
    overNoVoiceCountTip[@"fileSource"]=@"1";
    overNoVoiceCountTip[@"fileName"]=@"12.mp4";
    overNoVoiceCountTip[@"tipContent"]=@"您好，由于无法识别您本次的回答，请您选择周边安静的场所，重新申请视频。为了尽快帮您录制完成，我也帮您准备了人工视频方式，您可以点击“返回”按钮后进行选择。";
//    overNoVoiceCountTip[@"tipTitle"]=@"<font style=\"font-size:22px;\"color='#FFFFFF'>您好，由于无法识别您本次的回答，请您选择周边安静的场所，重新申请视频。为了尽快帮您录制完成，我也帮您准备了人工视频方式，您可以点击“返回”按钮后进行选择。<\/font>";
    errorTipDic[@"overNoVoiceCountTip"] = overNoVoiceCountTip;
    
    // 多张人脸
    NSMutableDictionary *overPresonCountTip = [[NSMutableDictionary alloc] init];
    overPresonCountTip[@"fileSource"]=@"1";
    overPresonCountTip[@"fileName"]=@"13.mp4";
    overPresonCountTip[@"tipContent"]=@"您好，由于需您本人独自完成视频录制，请您重新录制视频，并确保视频中其他人不出现在镜头中。感谢您的配合，一会儿见！";
//    overPresonCountTip[@"tipTitle"]=@"<font style=\"font-size:22px;\"color='#FFFFFF'>您好，由于需您本人独自完成视频录制，请您重新录制视频，并确保视频中其他人不出现在镜头中。感谢您的配合，一会儿见！<\/font>";
    errorTipDic[@"overPresonCountTip"] = overPresonCountTip;
    
    // 多次超出屏幕
    NSMutableDictionary *overScreenCountTip = [[NSMutableDictionary alloc] init];
    overScreenCountTip[@"fileSource"]=@"1";
    overScreenCountTip[@"fileName"]=@"14.mp4";
    overScreenCountTip[@"tipContent"]=@"您好，由于本次视频中您多次离开镜头，请您重新录制视频，并确保过程中面部始终保持在镜头内。感谢您的配合，一会儿见！";
//    overScreenCountTip[@"tipTitle"]=@"<font style=\"font-size:22px;\"color='#FFFFFF'>您好，由于本次视频中您多次离开镜头，请您重新录制视频，并确保过程中面部始终保持在镜头内。感谢您的配合，一会儿见！<\/font>";
    errorTipDic[@"overScreenCountTip"] = overScreenCountTip;
    
    // 多次人脸比对失败
    NSMutableDictionary *overCompareCountTip = [[NSMutableDictionary alloc] init];
    overCompareCountTip[@"fileSource"]=@"1";
    overCompareCountTip[@"fileName"]=@"15.mp4";
    overCompareCountTip[@"tipContent"]=@"您好，由于本次视频中您多次离开镜头，请您重新录制视频，并确保过程中面部始终保持在镜头内。感谢您的配合，一会儿见！";
//    overCompareCountTip[@"tipTitle"]=@"<font style=\"font-size:22px;\"color='#FFFFFF'>您好，由于本次视频中您多次离开镜头，请您重新录制视频，并确保过程中面部始终保持在镜头内。感谢您的配合，一会儿见！<\/font>";
    errorTipDic[@"overCompareCountTip"] = overCompareCountTip;
    
//    param[@"errorTipJson"] = errorTipDic;
//    param[@"errorTipJson"] = [TKDataHelper dictionaryToJson:errorTipDic];
    
    return param;
}

#pragma mark - 授权
///生成凡泰授权，授权内容日志打印
/// @param originAuthString 授权的bundleid，多个以|隔开，不穿就是默认授权
-(void)createAuth:(NSString *)originAuthString{
    //生成授权文件方法
    //授权key
    NSString *tkKey=[TKUUIDHelper uuid];
    TKLogInfo(@"TKOpenAuthorization.lic:tkKey:%@",tkKey);
    //授权key的SM4值
    NSString *sm4Key=[TKSM4Helper stringWithSM4EncryptString:tkKey withKey:[TKPasswordGenerator generatorPassword]];
    //（授权key+框架密钥)的md5值的前32位，
    NSString *md532Lenght=[[TKMd5Helper md5Encrypt:[NSString stringWithFormat:@"%@%@",tkKey,[TKPasswordGenerator generatorPassword]]] substringToIndex:32];
    //需要授权的信息(默认授权内容)
    NSString *authString=[NSString stringWithFormat:@"%@#%@|%@",tkKey,@"com.thinkive.openaccountAdhoc",@"com.thinkive.mobile.account"];
    //传入的授权内容不为空
    if ([TKStringHelper isNotEmpty:originAuthString]) {
        authString=[NSString stringWithFormat:@"%@#%@",tkKey,originAuthString];
    }
    //授权密文
    NSString *authCipherString=[TKSM4Helper stringWithSM4EncryptString:authString withKey:md532Lenght];
    TKLogInfo(@"TKOpenAuthorization.lic授权密文:%@\n%@",sm4Key,authCipherString);
}


#pragma mark - 服务端双人录制组件开发
- (void)startDoubleTChatVideoRecord {
    
    __weak typeof(self) weakSelf = self;
    //麦克风相机权限校验
    [self tkIsMicrophonePermissions:^{
                            
        [weakSelf tkIsCameraPermissions:^{
            
            [weakSelf sendRequestToGetTokenWithCallBackFunc:^(NSString *authorization) {
                
                // 模拟服务端录制，内部调整为本地智能单向录制
                [weakSelf openDoubleOneWayVideoRecord:authorization];
            }];
        }];
                                    
    }];
    
}

// 模拟服务端录制，内部调整为本地智能单向录制
- (void)openDoubleOneWayVideoRecord:(NSString *)authorization
{
    NSMutableDictionary *param = [NSMutableDictionary dictionaryWithDictionary:[self getTipsParam]];

    // 请求入参
    param[@"uploadTipString"]=@"请您确认：我是XXX，已知晓证券市场风险，已阅读并充分理解网上开户协议条款，自愿选择在思迪证券开立账户，并承诺所提供的信息及证件真实、合法、有效。"; //确认上传提示内容，为空情况下确认上传不做提示
    //人脸在框检测地址，不穿的话人脸检测综合功能号和人脸在框检测功能号也不用穿不做在框检测
    param[@"url"] = @"https://opt-dev.thinkive.com:15149/auth-common-server/servlet/json";
//    param[@"url"] = @"https://khh5test.csc108.com:8084/auth-common-server/servlet/json";
//    param[@"url"] = @"https://khh5prodtest.csc108.com/auth-common-server/servlet/json";

    //人脸检测综合功能号
    param[@"faceDetectCompositeFuncNo"] = @"15000006";
    //人脸在框检测功能号
    param[@"faceDetectFuncNo"] = @"15000000";
//    param[@"rate"] = @"1"; // 语音播报速度
//    param[@"videoPreviewUrl"] = @"https://soft.thinkive.com:15098/auth-common-server/servlet/VideoPlaytAction"; // 预览视频地址
//    param[@"disableFaceDetect"] = @"1";

    // !!!
    // 请求头参数
    param[@"requestHeaders"] = @{@"tk-jwt-authorization" : [NSString stringWithFormat:@"%@", authorization]};
    
//    //人脸检测综合功能号
//    param[@"faceDetectCompositeFuncNo"] = @"15000059";
//    param[@"mainColor"] = @"#EC2E31";
//    param[@"requestSignKey"] = @"f080556a782d50087beebb0fae7aabd2";
//    param[@"requestSignKey"] = @"uFSCpZUT24jPeofBtkFTEXIKNZAKWw1vXD/ABnpapSUeT9CVhloh0e0kmq2PagBU";
//    param[@"requestSignAppId"] = @"default";
//    param[@"isRestFull"] = @"1";
//    param[@"tips"] = [TKDataHelper dictionaryToJson:[self getTipsParam]];
    
    param[@"maxFailureCountPerFaceCompare"] = @"9999";
    param[@"isNeedSwitchCamera"] = @"1";
    ResultVo *vo = [[TKPluginInvokeCenter shareInstance] callPlugin:@"60077" param:param moduleName:@"mall"];
    TKLogInfo(@"");
    
//    // 创建tchat录制控制器
//    TKDoubleChatVideoRecordViewController *vc = [[TKDoubleChatVideoRecordViewController alloc] initWithParam:param];
//    vc.delegate = self;
//    [[TKAppEngine shareInstance].rootViewCtr.currentViewCtrl presentViewController:vc animated:YES completion:nil];
}


#pragma mark - 服务端组件开发
- (void)startVideoRecord {
    
    __weak typeof(self) weakSelf = self;
    //麦克风相机权限校验
    [self tkIsMicrophonePermissions:^{
                            
        [weakSelf tkIsCameraPermissions:^{
            
            [weakSelf sendRequestToGetTokenWithCallBackFunc:^(NSString *authorization) {
                
//            NSString *authorization = @"";
                // 模拟服务端录制，内部调整为本地智能单向录制
                [weakSelf openOneWayVideoRecord:authorization];
            }];
        }];
                                    
    }];
    
}

// demo为完整演示，做了authorization请求。请集成到项目中时，自行请求，并把authorization传给sdk.
- (void)sendRequestToGetTokenWithCallBackFunc:(void(^)(NSString *authorization))callBackFunc
{
    NSString *url = @"https://opt-dev.thinkive.com:15149/auth-hello-server/jwt/create?flowNo=123123";   // 标准版环境
//    NSString *url = @"https://khh5test.csc108.com:8084/stkkh-hello/jwt/create?flowNo=18229709218&userName=TOM1"; // 中信建投环境
//    NSString *url = @"https://khh5prodtest.csc108.com/stkkh-hello/jwt/create?flowNo=23&userName=%E6%B5%8B%E8%AF%95"; // 中信建投环境

    NSMutableDictionary *param = [NSMutableDictionary dictionary];
    
    [[TKTempService new] getTokenWithURL:url param:param callBackFunc:^(ResultVo *resultVo) {
        
        NSArray *results = (NSArray *)resultVo.results;
        if (resultVo.errorNo == 0) {
            NSDictionary *dic = results.firstObject;
            
//            NSDictionary *data = dic[@"data"];
//            NSString *authorization = [NSString stringWithFormat:@"Bearer %@", data[@"authorization"]];
            
            NSString *authorization = dic[@"authorization"];
            authorization = [NSString stringWithFormat:@"Bearer %@", authorization];
            
            if (callBackFunc) {
                callBackFunc(authorization);
            }
        } else {
            TKLogInfo(@"请求授权参数出错");
        }
    }];
}

// 模拟服务端录制，内部调整为本地智能单向录制
- (void)openOneWayVideoRecord:(NSString *)authorization
{
    NSMutableDictionary *param = [NSMutableDictionary dictionaryWithDictionary:[self getTipsParam]];

    // 请求入参
    param[@"uploadTipString"]=@"请您确认：我是XXX，已知晓证券市场风险，已阅读并充分理解网上开户协议条款，自愿选择在思迪证券开立账户，并承诺所提供的信息及证件真实、合法、有效。"; //确认上传提示内容，为空情况下确认上传不做提示
    //人脸在框检测地址，不穿的话人脸检测综合功能号和人脸在框检测功能号也不用穿不做在框检测
    param[@"url"] = @"https://opt-dev.thinkive.com:15149/auth-common-server/servlet/json";
//    param[@"url"] = @"https://khh5test.csc108.com:8084/auth-common-server/servlet/json";
//    param[@"url"] = @"https://khh5prodtest.csc108.com/auth-common-server/servlet/json";

    //人脸检测综合功能号
    param[@"faceDetectCompositeFuncNo"] = @"15000006";
    //人脸在框检测功能号
    param[@"faceDetectFuncNo"] = @"15000000";
//    param[@"rate"] = @"1"; // 语音播报速度
//    param[@"videoPreviewUrl"] = @"https://tk.thinkive.com:18099/servlet/VideoPlaytAction;jsessionid=477E3EC0CA84EE29FF625E18D800B0CD";
//    param[@"videoPreviewUrl"] = @"http://xc-tchat.thinkive.com:9600/VideoPlaytAction";

    // 预览视频地址
    param[@"disableFaceDetect"] = @"1";
    param[@"maxFailureCountPerFaceDetect"] = @"999";
    param[@"maxFailureCountPerFaceCompare"] = @"999";

    //问题数组
    NSMutableArray *questionArray = [[NSMutableArray alloc] init];
    NSMutableDictionary *questionDic1 = [[NSMutableDictionary alloc] init];
    //提示问题会语音播报
//    questionDic1[@"tip_content"]=@"尊敬的客户，请您在\"滴\"声后大声使用\"是\"或\"否\"回答以下问题。";
    questionDic1[@"tip_content"]=@"尊敬的客户，请您在'滴'声后大声使用'是'或'否'回答以下问题。";
//    questionDic1[@"tip_content"]=@"尊敬的客户，请您在\"滴\"声后大声使用\"是\"或\"否\"回答以下问题。尊敬的客户，请您在\"滴\"声后大声使用\"是\"或\"否\"回答以下问题。尊敬的客户，请您在\"滴\"声后大声使用\"是\"或\"否\"回答以下问题。尊敬的客户，请您在\"滴\"声后大声使用\"是\"或\"否\"回答以下问题。";
//    questionDic1[@"tip_content"]=@"尊敬";
//    [questionArray addObject:questionDic1];

    NSMutableDictionary *questionDic2 = [[NSMutableDictionary alloc] init];
    //提示问题会语音播报，只传播报说明该问题不需要回答
    questionDic2[@"tip_content"] = @"您是否为XXX本人，且已知晓证券市场风险，已阅读并充分理解网上开户协议条款，自愿在思迪证券开户？";
    questionDic2[@"tip_title"] = @"您是否为测试本人，且已知晓证券市场风险，已阅读并充分理解网上开户协议条款，自愿在思迪证券开户？";
    questionDic2[@"tip_content"] = @"您是否";
    //需要回答的问题，正确回答的正则
    questionDic2[@"standardans"] = @"^((?!不).)*([是对4事四思室市氏士似]|已知道|已知晓|已清楚|好的|知道|清楚|明白|可以)((?!不).)*$";
    //需要回答的问题，错误回答的正则
    questionDic2[@"failans"] = @"/[不否部布步]";
    //该问题预留的回答时间
    questionDic2[@"wait_time"] = @(5);
    //该问题回答的文字提示
    questionDic2[@"prompt"] = @"是的/不是";
    [questionArray addObject:questionDic2];


    NSMutableDictionary *questionDic3 = [[NSMutableDictionary alloc] init];
    //提示问题会语音播报，只传播报说明该问题不需要回答
//    questionDic3[@"tip_content"] = @"您是否为XXX本人，且已知晓证券市场风险，已阅读并充分理解网上开户协议条款，自愿在思迪证券开户？";
    questionDic3[@"tip_content"] = @"测试的";
    //需要回答的问题，正确回答的正则
    questionDic3[@"standardans"] = @"^(是的|是|对|对的|4)";
    //需要回答的问题，错误回答的正则
    questionDic3[@"failans"] = @"^(\\S|\\s)*(不是的|不是)+(\\S|\\s)*";
    //该问题预留的回答时间
    questionDic3[@"wait_time"] = @(5);
    //该问题回答的文字提示
    questionDic3[@"prompt"] = @"是的/不是";
//    [questionArray addObject:questionDic3];


    NSMutableDictionary *questionDic4 = [[NSMutableDictionary alloc] init];
    //提示问题会语音播报，只传播报说明该问题不需要回答
//    questionDic4[@"tip_content"] = @"您是否为XXX本人，且已知晓证券市场风险，已阅读并充分理解网上开户协议条款，自愿在思迪证券开户？";
    questionDic4[@"tip_content"] = @"问题2";
    //需要回答的问题，正确回答的正则
    questionDic4[@"standardans"] = @"^(是的|是|对|对的|4)";
    //需要回答的问题，错误回答的正则
    questionDic4[@"failans"] = @"^(\\S|\\s)*(不是的|不是)+(\\S|\\s)*";
    //该问题预留的回答时间
    questionDic4[@"wait_time"] = @(5);
    //该问题回答的文字提示
    questionDic4[@"prompt"] = @"是的/不是";
//    [questionArray addObject:questionDic4];
    param[@"questionJson"] = questionArray;

    // !!!
    // 请求头参数
    param[@"requestHeaders"] = @{@"tk-jwt-authorization" : [NSString stringWithFormat:@"%@", authorization]};
    
//    NSMutableDictionary *param = [NSMutableDictionary dictionaryWithDictionary:[self getTipsParam]];
//    // 请求入参
//    param[@"uploadTipString"]=@"请您确认：我是XXX，已知晓证券市场风险，已阅读并充分理解网上开户协议条款，自愿选择在思迪证券开立账户，并承诺所提供的信息及证件真实、合法、有效。"; //确认上传提示内容，为空情况下确认上传不做提示
//    //人脸在框检测地址，不穿的话人脸检测综合功能号和人脸在框检测功能号也不用穿不做在框检测
////    param[@"url"] = @"https://soft.thinkive.com:15036/auth-common-server/servlet/json";
//    param[@"url"] = @"https://soft.thinkive.com:15098/auth-common-server/servlet/json";
//
//    //人脸检测综合功能号
//    param[@"faceDetectCompositeFuncNo"] = @"15000059";
//    param[@"mainColor"] = @"#EC2E31";
    param[@"maxFailureCountPerFaceCompare"] = @"9999";
//    param[@"tips"] = [TKDataHelper dictionaryToJson:[self getTipsParam]];
//
//    // !!!
//    // 请求头参数
//    param[@"requestHeaders"] = @{@"tk-jwt-authorization" : [NSString stringWithFormat:@"%@", authorization]};
    
//    ResultVo *vo = [[TKPluginInvokeCenter shareInstance] callPlugin:@"60026" param:param moduleName:@"mall"];
//    TKLogDebug(@"");
    
    ResultVo *vo = [[TKPluginInvokeCenter shareInstance] callPlugin:@"60057" param:param moduleName:@"mall"];
    TKLogInfo(@"");
    
//    // 创建tchat录制控制器
//    TKChatVideoRecordViewController *vc = [[TKChatVideoRecordViewController alloc] initWithParam:param];
//    vc.delegate = self;
//    [[TKAppEngine shareInstance].rootViewCtr.currentViewCtrl presentViewController:vc animated:YES completion:nil];
}

- (void)touchesBegan:(NSSet<UITouch *> *)touches withEvent:(UIEvent *)event {
    TKLogInfo(@"touchesBegan");
}


#pragma mark - 服务端活体识别组件开发
- (void)startChatLiveDetect {
    
    __weak typeof(self) weakSelf = self;
    //麦克风相机权限校验
                            
        [weakSelf tkIsCameraPermissions:^{
            
            // 获取authorization
            [weakSelf sendRequestToGetTokenWithCallBackFunc:^(NSString *authorization) {
                
                // 打开服务端活体识别
                [weakSelf openChatLiveDectet:authorization];
            }];
        }];
    
}

// 模拟服务端录制，内部调整为本地智能单向录制
- (void)openChatLiveDectet:(NSString *)authorization
{
    NSMutableDictionary *param = [NSMutableDictionary dictionary];
    
    // 请求入参
    param[@"uploadTipString"]=@"请您确认：我是XXX，已知晓证券市场风险，已阅读并充分理解网上开户协议条款，自愿选择在思迪证券开立账户，并承诺所提供的信息及证件真实、合法、有效。"; //确认上传提示内容，为空情况下确认上传不做提示
    //人脸在框检测地址，不穿的话人脸检测综合功能号和人脸在框检测功能号也不用穿不做在框检测
//    param[@"url"] = @"https://soft.thinkive.com:15036/auth-common-server/servlet/json";
//    param[@"url"] = @"https://soft.thinkive.com:15098/auth-common-server/servlet/json";
    param[@"url"] = @"https://opt-dev.thinkive.com:15149/auth-common-server/servlet/json";
//    param[@"url"] = @"https://khh5test.csc108.com:8084/auth-common-server/servlet/json";
//    param[@"url"] = @"https://khh5prodtest.csc108.com/auth-common-server/servlet/json";
    
    //人脸检测综合功能号
    param[@"faceDetectCompositeFuncNo"] = @"15000006";
    //人脸在框检测功能号
    param[@"faceDetectFuncNo"] = @"15000000";
    // 动作数组
    param[@"actionGroup"] = @"1";

    // !!!
    // 请求头参数
    param[@"requestHeaders"] = @{@"tk-jwt-authorization" : [NSString stringWithFormat:@"%@", authorization]};
    
    // 创建tchat录制控制器
//    TKChatLiveDetectViewController *vc = [[TKChatLiveDetectViewController alloc] initWithParam:param];
//    vc.delegate = self;
//    [[TKAppEngine shareInstance].rootViewCtr.currentViewCtrl presentViewController:vc animated:YES completion:nil];
    ResultVo *vo = [[TKPluginInvokeCenter shareInstance] callPlugin:@"60059" param:param moduleName:@"mall"];
    TKLogInfo(@"");
    
    TKLogInfo(@"创建tchat活体控制器");
}


//结果信息
- (void)tkChatLiveDetectDidComplete:(NSMutableDictionary *)result {
    
//    result[@"error_no"];
//    0：识别成功
//     -1:  用户取消，
//     -2：初始化视频出错
//     -3：视频录制异常中断
//    -4：人脸在框检测错误
//    -5：人脸比对误
//    -6 :  语音识别没有声音
//    -7 ： 语音识别错误回答
//    -8 ： 视频录制其他错误
    
    
//    result[@"error_info"];
//    0：识别成功
//    -1:  用户主动返回
//     -2：初始化视频出错
//    -3：请您保持全脸在人像框内 | 检测不到人脸 | 人脸数量超限 | 视频过程中请勿接听电话 | 视频过程中请勿切换App或锁屏
//    -4 ：由于长时间未检测到面部在框，本次视频录制失败，请重新录制。| 由于长时间未完成视频录制，我们为您切换到一对一人工见证服务。
//    -5 ： 人脸比对不通过，请确保为本人录制，本次视频录制失败，请重新录制。| 由于长时间未完成视频录制，我们为您切换到一对一人工见证服务。
//    -6 ： 由于长时间未检测到您的声音，本次视频见证失败，请重新录制。| 由于长时间未完成视频录制，我们为您切换到一对一人工见证服务。
//    -7 ： 回答不通过，本次视频见证失败，您可重新发起录制。| 由于长时间未完成视频录制，我们为您切换到一对一人工见证服务。
//    -8 ： 视频录制异常退出
    
//    result[@"start_time"];//文件开始录制时间,成功才会有该值， 开始视频见证时间字符串    格式：2019-06-24 20:49:37
    
//    result[@"video_length"];//视频时长,成功才会有该值，单位秒
    
//    result[@"end_time"];//文件结束录制时间,成功才会有该值， 结束视频见证时间字符串    格式：2019-06-24 20:49:37
    
//    result[@"filePath"];//视频文件路径

    
    self.label2.text = [NSString stringWithFormat:@"\n活体结果：\n状态码：%@\n结果信息：%@\n文件长度:%@\n文件路径:%@", [result getStringWithKey:@"error_no"], [result getStringWithKey:@"error_info"], [result getStringWithKey:@"video_length"], [result getStringWithKey:@"filePath"]];
}

#pragma mark - TKChatVideoRecordResultDelegate
//结果信息
- (void)tkChatVideoRecordDidComplete:(NSMutableDictionary *)result {
    
//    result[@"error_no"];
//    0：识别成功
//     -1:  用户取消，
//     -2：初始化视频出错
//     -3：视频录制异常中断
//    -4：人脸在框检测错误
//    -5：人脸比对误
//    -6 :  语音识别没有声音
//    -7 ： 语音识别错误回答
//    -8 ： 视频录制其他错误
    
    
//    result[@"error_info"];
//    0：识别成功
//    -1:  用户主动返回
//     -2：初始化视频出错
//    -3：请您保持全脸在人像框内 | 检测不到人脸 | 人脸数量超限 | 视频过程中请勿接听电话 | 视频过程中请勿切换App或锁屏
//    -4 ：由于长时间未检测到面部在框，本次视频录制失败，请重新录制。| 由于长时间未完成视频录制，我们为您切换到一对一人工见证服务。
//    -5 ： 人脸比对不通过，请确保为本人录制，本次视频录制失败，请重新录制。| 由于长时间未完成视频录制，我们为您切换到一对一人工见证服务。
//    -6 ： 由于长时间未检测到您的声音，本次视频见证失败，请重新录制。| 由于长时间未完成视频录制，我们为您切换到一对一人工见证服务。
//    -7 ： 回答不通过，本次视频见证失败，您可重新发起录制。| 由于长时间未完成视频录制，我们为您切换到一对一人工见证服务。
//    -8 ： 视频录制异常退出
    
//    result[@"start_time"];//文件开始录制时间,成功才会有该值， 开始视频见证时间字符串    格式：2019-06-24 20:49:37
    
//    result[@"video_length"];//视频时长,成功才会有该值，单位秒
    
//    result[@"end_time"];//文件结束录制时间,成功才会有该值， 结束视频见证时间字符串    格式：2019-06-24 20:49:37
    
//    result[@"filePath"];//视频文件路径

    
    self.label.text = [NSString stringWithFormat:@"\n录制结果：\n状态码：%@\n结果信息：%@\n文件长度:%@\n文件路径:%@", [result getStringWithKey:@"error_no"], [result getStringWithKey:@"error_info"], [result getStringWithKey:@"video_length"], [result getStringWithKey:@"filePath"]];
}


#pragma mark - TKVirtualManOneVideoViewControllerResultDelegate
//结果信息
- (void)tkVirtualManOneWayVideoDidComplete:(NSMutableDictionary *)result {
    TKLogInfo(@"tkVirtualManOneWayVideoDidComplete result = %@", result);
}

#pragma mark - TKSmartVirtualManViewControllerResultDelegate
//结果信息
- (void)tkSmartVirtualManVideoDidComplete:(NSMutableDictionary *)result {
    TKLogInfo(@"tkSmartVirtualManVideoDidComplete result = %@", result);
}

#pragma mark - TKDoubleChatVideoRecordResultDelegate
//结果信息
- (void)tkDoubleChatVideoRecordDidComplete:(NSMutableDictionary *)result {
    TKLogInfo(@"tkDoubleChatVideoRecordDidComplete result = %@", result);
}


#pragma mark - TKStatisticEventHelperDelegate
/// H5回调代理事件
/// - Parameters:
///   - event: 事件类型。字符串
///   - params: 事件参数
- (void)h5StatisticEventHelperDidCallBack:(NSString *)event params:(NSDictionary *_Nullable)params {
    // code
    TKLogInfo(@"event = %@, params = %@", event, params);
}

@end



